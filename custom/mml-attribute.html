<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/globals.css">
  <title>Add a data-mathml attribute to each math element</title>
  <script class="show">
  MathJax = {
    startup: {
      ready() {
        //
        //  Do the usual startup ready actions (create document, input/output jax, etc).
        //
        MathJax.startup.defaultReady();
        const toMML = MathJax.startup.toMML.bind(MathJax.startup);
        //
        //  Add a post-filter to the output jax to add the extra attributes,
        //    after removing data-semantic and other speech-related attributes.
        //
        MathJax.startup.output.postFilters.add(({math, data}) => {
          const original = math.math || (math.inputJax.processStrings ? '' : math.start.node.outerHTML);
          const mml = toMML(math.root).replace(/ (?:data-(?:semantic|latex|speech|braille)|aria-|has-).*?=".*?"/g, '');
          data.setAttribute('data-original', original);
          data.setAttribute('data-mathml', '\n' + mml);
        });
        //
        // Wait for the typesetting to finish, and then show the
        //   container element in the code area below the text.
        //   
        MathJax.startup.promise.then(() => {
          const pre = document.querySelector('#DOM');
          const html = Array.from(MathJax.startup.document.math)[0].typesetRoot.outerHTML;
          pre.innerHTML = html
            .replace(/\n/g, '\n    ')                     // indent the MathML a bit
            .replace(/><.*<\//, '>\n  ...\n</')           // remove the contents of the container
            .replace(/ CtxtMenu_Attached_\d+/, '')        // remove unwanted class and attributes
            .replace(/ (?:data-(?:semantic|speech|braille|latex)|aria-|has-|ctxtmenu).*?=".*?"/g, '')
            .replace(/ (.+?="[^]*?")/g, '\n  $1')         // put each attribute on a separate line
            .replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;')
            .replace(/ (data-(?:mathml|original))/g, ' <b>$1</b>');  //  make the new attributes bold
        });
      },
    }
  };
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js"></script>
</head>
<body>

<div id="frame">

<h1>Add a <code>data</code> attributes to each math element</h1>

<div class="show display">
 
  <p> This math will have a <code>data-mathml</code> attribute
  containing the MathML representation of the expression, and a
  <code>data-original</code> attribute holding the original TeX format
  of the expression.  The results are shown below the typeset
  expression.  The semantic-enrichment and speech attributes are
  removed for easier viewing.  Note that <code>&lt;</code>,
  <code>&gt;</code> and <code>&amp;</code> are encoded as entities
  within the attribute values when the tag is serialized, but
  <code>node.getAttribute('data-mathml')</code> would give a string
  where the entities would be replaced by their usual characters.</p>

  $$\sqrt{\frac{1-x}{1+x}}$$

  <pre class="code" id="DOM">
  </pre>
 
</div>
</div>

<div class="explain inset">

<p>This example shows how to automatically generate a
<code>data-mathml</code> attribute on the HTML output for each math
expression that contains the serialized MathML version of the
expression.  It also adds a <code>data-original</code> attribute that
gives the original form of the expression (i.e., the original TeX or
MathML markup).  These attributes could be picked up by other tools
that may need access to the mathematics after it has been converted to
HTML or SVG tags, for example.</p>

<p>The process uses the <code>startup.ready()</code> function to do
the normal startup, then attaches a post-filter to the output jax that
looks up the original format of the math, sets that
<code>data-original</code> attribute to that, and uses the
<code>startup.toMML()</code> function to set the
<code>data-mathml</code> attribute to the serialized MathML for the
expression using the internal MathML representation generated by
MathJax.  Some attributes are removed to simplify the structure.</p>

<p>Finally, we wait for the initial typesetting to comple using the
<code>MathJax.startup.promise</code>, and when it dos, we transfer the
<code>mjx-container</code> node to the output area, removing some
attributes and showing each attribute on a separate line for
clarity.</p>

</div>

<script src="../scripts/source.js"></script>

</body>
</html>
