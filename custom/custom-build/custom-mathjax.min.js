(()=>{var __webpack_modules__={78:(t,e,r)=>{"use strict";var n=r(165).freeze;e.XML_ENTITIES=n({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),e.HTML_ENTITIES=n({Aacute:"\xc1",aacute:"\xe1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223e",acd:"\u223f",acE:"\u223e\u0333",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"\u0410",acy:"\u0430",AElig:"\xc6",aelig:"\xe6",af:"\u2061",Afr:"\u{1d504}",afr:"\u{1d51e}",Agrave:"\xc0",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03b1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2a3f",AMP:"&",amp:"&",And:"\u2a53",and:"\u2227",andand:"\u2a55",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1d538}",aopf:"\u{1d552}",ap:"\u2248",apacir:"\u2a6f",apE:"\u2a70",ape:"\u224a",apid:"\u224b",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224a",Aring:"\xc5",aring:"\xe5",Ascr:"\u{1d49c}",ascr:"\u{1d4b6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224d",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",Backslash:"\u2216",Barv:"\u2ae7",barvee:"\u22bd",Barwed:"\u2306",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",Because:"\u2235",because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",Bernoullis:"\u212c",Beta:"\u0392",beta:"\u03b2",beth:"\u2136",between:"\u226c",Bfr:"\u{1d505}",bfr:"\u{1d51f}",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bNot:"\u2aed",bnot:"\u2310",Bopf:"\u{1d539}",bopf:"\u{1d553}",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxbox:"\u29c9",boxDL:"\u2557",boxDl:"\u2556",boxdL:"\u2555",boxdl:"\u2510",boxDR:"\u2554",boxDr:"\u2553",boxdR:"\u2552",boxdr:"\u250c",boxH:"\u2550",boxh:"\u2500",boxHD:"\u2566",boxHd:"\u2564",boxhD:"\u2565",boxhd:"\u252c",boxHU:"\u2569",boxHu:"\u2567",boxhU:"\u2568",boxhu:"\u2534",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxUL:"\u255d",boxUl:"\u255c",boxuL:"\u255b",boxul:"\u2518",boxUR:"\u255a",boxUr:"\u2559",boxuR:"\u2558",boxur:"\u2514",boxV:"\u2551",boxv:"\u2502",boxVH:"\u256c",boxVh:"\u256b",boxvH:"\u256a",boxvh:"\u253c",boxVL:"\u2563",boxVl:"\u2562",boxvL:"\u2561",boxvl:"\u2524",boxVR:"\u2560",boxVr:"\u255f",boxvR:"\u255e",boxvr:"\u251c",bprime:"\u2035",Breve:"\u02d8",breve:"\u02d8",brvbar:"\xa6",Bscr:"\u212c",bscr:"\u{1d4b7}",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsol:"\\",bsolb:"\u29c5",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",Bumpeq:"\u224e",bumpeq:"\u224f",Cacute:"\u0106",cacute:"\u0107",Cap:"\u22d2",cap:"\u2229",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",capcup:"\u2a47",capdot:"\u2a40",CapitalDifferentialD:"\u2145",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",Cayleys:"\u212d",ccaps:"\u2a4d",Ccaron:"\u010c",ccaron:"\u010d",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2a4c",ccupssm:"\u2a50",Cdot:"\u010a",cdot:"\u010b",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"\u29b2",cent:"\xa2",CenterDot:"\xb7",centerdot:"\xb7",Cfr:"\u212d",cfr:"\u{1d520}",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03a7",chi:"\u03c7",cir:"\u25cb",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",CircleDot:"\u2299",circledR:"\xae",circledS:"\u24c8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cirE:"\u29c3",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",Colon:"\u2237",colon:":",Colone:"\u2a74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",Congruent:"\u2261",Conint:"\u222f",conint:"\u222e",ContourIntegral:"\u222e",Copf:"\u2102",copf:"\u{1d554}",coprod:"\u2210",Coproduct:"\u2210",COPY:"\xa9",copy:"\xa9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21b5",Cross:"\u2a2f",cross:"\u2717",Cscr:"\u{1d49e}",cscr:"\u{1d4b8}",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",Cup:"\u22d3",cup:"\u222a",cupbrcap:"\u2a48",CupCap:"\u224d",cupcap:"\u2a46",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",Dagger:"\u2021",dagger:"\u2020",daleth:"\u2138",Darr:"\u21a1",dArr:"\u21d3",darr:"\u2193",dash:"\u2010",Dashv:"\u2ae4",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",Dcaron:"\u010e",dcaron:"\u010f",Dcy:"\u0414",dcy:"\u0434",DD:"\u2145",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21ca",DDotrahd:"\u2911",ddotseq:"\u2a77",deg:"\xb0",Del:"\u2207",Delta:"\u0394",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",Dfr:"\u{1d507}",dfr:"\u{1d521}",dHar:"\u2965",dharl:"\u21c3",dharr:"\u21c2",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",diam:"\u22c4",Diamond:"\u22c4",diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",DifferentialD:"\u2146",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",Dopf:"\u{1d53b}",dopf:"\u{1d555}",Dot:"\xa8",dot:"\u02d9",DotDot:"\u20dc",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",Downarrow:"\u21d3",downarrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVector:"\u21bd",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295f",DownRightVector:"\u21c1",DownRightVectorBar:"\u2957",DownTee:"\u22a4",DownTeeArrow:"\u21a7",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",Dscr:"\u{1d49f}",dscr:"\u{1d4b9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29f6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",DZcy:"\u040f",dzcy:"\u045f",dzigrarr:"\u27ff",Eacute:"\xc9",eacute:"\xe9",easter:"\u2a6e",Ecaron:"\u011a",ecaron:"\u011b",ecir:"\u2256",Ecirc:"\xca",ecirc:"\xea",ecolon:"\u2255",Ecy:"\u042d",ecy:"\u044d",eDDot:"\u2a77",Edot:"\u0116",eDot:"\u2251",edot:"\u0117",ee:"\u2147",efDot:"\u2252",Efr:"\u{1d508}",efr:"\u{1d522}",eg:"\u2a9a",Egrave:"\xc8",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",Element:"\u2208",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25fb",emptyv:"\u2205",EmptyVerySmallSquare:"\u25ab",emsp:"\u2003",emsp13:"\u2004",emsp14:"\u2005",ENG:"\u014a",eng:"\u014b",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1d53c}",eopf:"\u{1d556}",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",Epsilon:"\u0395",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",Equal:"\u2a75",equals:"=",EqualTilde:"\u2242",equest:"\u225f",Equilibrium:"\u21cc",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erarr:"\u2971",erDot:"\u2253",Escr:"\u2130",escr:"\u212f",esdot:"\u2250",Esim:"\u2a73",esim:"\u2242",Eta:"\u0397",eta:"\u03b7",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",ExponentialE:"\u2147",exponentiale:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",Ffr:"\u{1d509}",ffr:"\u{1d523}",filig:"\ufb01",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",Fopf:"\u{1d53d}",fopf:"\u{1d557}",ForAll:"\u2200",forall:"\u2200",fork:"\u22d4",forkv:"\u2ad9",Fouriertrf:"\u2131",fpartint:"\u2a0d",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",Fscr:"\u2131",fscr:"\u{1d4bb}",gacute:"\u01f5",Gamma:"\u0393",gamma:"\u03b3",Gammad:"\u03dc",gammad:"\u03dd",gap:"\u2a86",Gbreve:"\u011e",gbreve:"\u011f",Gcedil:"\u0122",Gcirc:"\u011c",gcirc:"\u011d",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",gE:"\u2267",ge:"\u2265",gEl:"\u2a8c",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",ges:"\u2a7e",gescc:"\u2aa9",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",Gfr:"\u{1d50a}",gfr:"\u{1d524}",Gg:"\u22d9",gg:"\u226b",ggg:"\u22d9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gl:"\u2277",gla:"\u2aa5",glE:"\u2a92",glj:"\u2aa4",gnap:"\u2a8a",gnapprox:"\u2a8a",gnE:"\u2269",gne:"\u2a88",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",Gopf:"\u{1d53e}",gopf:"\u{1d558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\u{1d4a2}",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",Gt:"\u226b",GT:">",gt:">",gtcc:"\u2aa7",gtcir:"\u2a7a",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",Hacek:"\u02c7",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",HARDcy:"\u042a",hardcy:"\u044a",hArr:"\u21d4",harr:"\u2194",harrcir:"\u2948",harrw:"\u21ad",Hat:"^",hbar:"\u210f",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",Hfr:"\u210c",hfr:"\u{1d525}",HilbertSpace:"\u210b",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",Hopf:"\u210d",hopf:"\u{1d559}",horbar:"\u2015",HorizontalLine:"\u2500",Hscr:"\u210b",hscr:"\u{1d4bd}",hslash:"\u210f",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224e",HumpEqual:"\u224f",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xcd",iacute:"\xed",ic:"\u2063",Icirc:"\xce",icirc:"\xee",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xa1",iff:"\u21d4",Ifr:"\u2111",ifr:"\u{1d526}",Igrave:"\xcc",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Im:"\u2111",Imacr:"\u012a",imacr:"\u012b",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22b7",imped:"\u01b5",Implies:"\u21d2",in:"\u2208",incare:"\u2105",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",Int:"\u222c",int:"\u222b",intcal:"\u22ba",integers:"\u2124",Integral:"\u222b",intercal:"\u22ba",Intersection:"\u22c2",intlarhk:"\u2a17",intprod:"\u2a3c",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012e",iogon:"\u012f",Iopf:"\u{1d540}",iopf:"\u{1d55a}",Iota:"\u0399",iota:"\u03b9",iprod:"\u2a3c",iquest:"\xbf",Iscr:"\u2110",iscr:"\u{1d4be}",isin:"\u2208",isindot:"\u22f5",isinE:"\u22f9",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xcf",iuml:"\xef",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1d50d}",jfr:"\u{1d527}",jmath:"\u0237",Jopf:"\u{1d541}",jopf:"\u{1d55b}",Jscr:"\u{1d4a5}",jscr:"\u{1d4bf}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039a",kappa:"\u03ba",kappav:"\u03f0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041a",kcy:"\u043a",Kfr:"\u{1d50e}",kfr:"\u{1d528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040c",kjcy:"\u045c",Kopf:"\u{1d542}",kopf:"\u{1d55c}",Kscr:"\u{1d4a6}",kscr:"\u{1d4c0}",lAarr:"\u21da",Lacute:"\u0139",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",Lambda:"\u039b",lambda:"\u03bb",Lang:"\u27ea",lang:"\u27e8",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",Laplacetrf:"\u2112",laquo:"\xab",Larr:"\u219e",lArr:"\u21d0",larr:"\u2190",larrb:"\u21e4",larrbfs:"\u291f",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",lat:"\u2aab",lAtail:"\u291b",latail:"\u2919",late:"\u2aad",lates:"\u2aad\ufe00",lBarr:"\u290e",lbarr:"\u290c",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",Lcaron:"\u013d",lcaron:"\u013e",Lcedil:"\u013b",lcedil:"\u013c",lceil:"\u2308",lcub:"{",Lcy:"\u041b",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",lE:"\u2266",le:"\u2264",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",Leftarrow:"\u21d0",leftarrow:"\u2190",LeftArrowBar:"\u21e4",LeftArrowRightArrow:"\u21c6",leftarrowtail:"\u21a2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21c3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230a",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",LeftRightArrow:"\u2194",Leftrightarrow:"\u21d4",leftrightarrow:"\u2194",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",LeftRightVector:"\u294e",LeftTee:"\u22a3",LeftTeeArrow:"\u21a4",LeftTeeVector:"\u295a",leftthreetimes:"\u22cb",LeftTriangle:"\u22b2",LeftTriangleBar:"\u29cf",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21bf",LeftUpVectorBar:"\u2958",LeftVector:"\u21bc",LeftVectorBar:"\u2952",lEg:"\u2a8b",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",les:"\u2a7d",lescc:"\u2aa8",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2aa1",lesssim:"\u2272",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",lfisht:"\u297c",lfloor:"\u230a",Lfr:"\u{1d50f}",lfr:"\u{1d529}",lg:"\u2276",lgE:"\u2a91",lHar:"\u2962",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",Ll:"\u22d8",ll:"\u226a",llarr:"\u21c7",llcorner:"\u231e",Lleftarrow:"\u21da",llhard:"\u296b",lltri:"\u25fa",Lmidot:"\u013f",lmidot:"\u0140",lmoust:"\u23b0",lmoustache:"\u23b0",lnap:"\u2a89",lnapprox:"\u2a89",lnE:"\u2268",lne:"\u2a87",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",LongLeftArrow:"\u27f5",Longleftarrow:"\u27f8",longleftarrow:"\u27f5",LongLeftRightArrow:"\u27f7",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",LongRightArrow:"\u27f6",Longrightarrow:"\u27f9",longrightarrow:"\u27f6",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",Lopf:"\u{1d543}",lopf:"\u{1d55d}",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",Lscr:"\u2112",lscr:"\u{1d4c1}",Lsh:"\u21b0",lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",Lstrok:"\u0141",lstrok:"\u0142",Lt:"\u226a",LT:"<",lt:"<",ltcc:"\u2aa6",ltcir:"\u2a79",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",ltrPar:"\u2996",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",Mcy:"\u041c",mcy:"\u043c",mdash:"\u2014",mDDot:"\u223a",measuredangle:"\u2221",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\u{1d510}",mfr:"\u{1d52a}",mho:"\u2127",micro:"\xb5",mid:"\u2223",midast:"*",midcir:"\u2af0",middot:"\xb7",minus:"\u2212",minusb:"\u229f",minusd:"\u2238",minusdu:"\u2a2a",MinusPlus:"\u2213",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",Mopf:"\u{1d544}",mopf:"\u{1d55e}",mp:"\u2213",Mscr:"\u2133",mscr:"\u{1d4c2}",mstpos:"\u223e",Mu:"\u039c",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266e",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",Ncy:"\u041d",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",nearhk:"\u2924",neArr:"\u21d7",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1d511}",nfr:"\u{1d52b}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",nGg:"\u22d9\u0338",ngsim:"\u2275",nGt:"\u226b\u20d2",ngt:"\u226f",ngtr:"\u226f",nGtv:"\u226b\u0338",nhArr:"\u21ce",nharr:"\u21ae",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",NJcy:"\u040a",njcy:"\u045a",nlArr:"\u21cd",nlarr:"\u219a",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nLeftarrow:"\u21cd",nleftarrow:"\u219a",nLeftrightarrow:"\u21ce",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nLl:"\u22d8\u0338",nlsim:"\u2274",nLt:"\u226a\u20d2",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nLtv:"\u226a\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Nopf:"\u2115",nopf:"\u{1d55f}",Not:"\u2aec",not:"\xac",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",notin:"\u2209",notindot:"\u22f5\u0338",notinE:"\u22f9\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",NotLeftTriangle:"\u22ea",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangle:"\u22eb",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarr:"\u219b",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nRightarrow:"\u21cf",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",Nscr:"\u{1d4a9}",nscr:"\u{1d4c3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",Nu:"\u039d",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224d\u20d2",nVDash:"\u22af",nVdash:"\u22ae",nvDash:"\u22ad",nvdash:"\u22ac",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvHarr:"\u2904",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwarhk:"\u2923",nwArr:"\u21d6",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xd3",oacute:"\xf3",oast:"\u229b",ocir:"\u229a",Ocirc:"\xd4",ocirc:"\xf4",Ocy:"\u041e",ocy:"\u043e",odash:"\u229d",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29bf",Ofr:"\u{1d512}",ofr:"\u{1d52c}",ogon:"\u02db",Ograve:"\xd2",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",Omacr:"\u014c",omacr:"\u014d",Omega:"\u03a9",omega:"\u03c9",Omicron:"\u039f",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",Oopf:"\u{1d546}",oopf:"\u{1d560}",opar:"\u29b7",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",operp:"\u29b9",oplus:"\u2295",Or:"\u2a54",or:"\u2228",orarr:"\u21bb",ord:"\u2a5d",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oS:"\u24c8",Oscr:"\u{1d4aa}",oscr:"\u2134",Oslash:"\xd8",oslash:"\xf8",osol:"\u2298",Otilde:"\xd5",otilde:"\xf5",Otimes:"\u2a37",otimes:"\u2297",otimesas:"\u2a36",Ouml:"\xd6",ouml:"\xf6",ovbar:"\u233d",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",PartialD:"\u2202",Pcy:"\u041f",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",Pfr:"\u{1d513}",pfr:"\u{1d52d}",Phi:"\u03a6",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",Pi:"\u03a0",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",Poincareplane:"\u210c",pointint:"\u2a15",Popf:"\u2119",popf:"\u{1d561}",pound:"\xa3",Pr:"\u2abb",pr:"\u227a",prap:"\u2ab7",prcue:"\u227c",prE:"\u2ab3",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",precsim:"\u227e",Prime:"\u2033",prime:"\u2032",primes:"\u2119",prnap:"\u2ab9",prnE:"\u2ab5",prnsim:"\u22e8",prod:"\u220f",Product:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",Proportion:"\u2237",Proportional:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",Pscr:"\u{1d4ab}",pscr:"\u{1d4c5}",Psi:"\u03a8",psi:"\u03c8",puncsp:"\u2008",Qfr:"\u{1d514}",qfr:"\u{1d52e}",qint:"\u2a0c",Qopf:"\u211a",qopf:"\u{1d562}",qprime:"\u2057",Qscr:"\u{1d4ac}",qscr:"\u{1d4c6}",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",QUOT:'"',quot:'"',rAarr:"\u21db",race:"\u223d\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",Rang:"\u27eb",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",Rarr:"\u21a0",rArr:"\u21d2",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21a3",rarrw:"\u219d",rAtail:"\u291c",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",RBarr:"\u2910",rBarr:"\u290f",rbarr:"\u290d",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",Re:"\u211c",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",REG:"\xae",reg:"\xae",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",rfisht:"\u297d",rfloor:"\u230b",Rfr:"\u211c",rfr:"\u{1d52f}",rHar:"\u2964",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",Rho:"\u03a1",rho:"\u03c1",rhov:"\u03f1",RightAngleBracket:"\u27e9",RightArrow:"\u2192",Rightarrow:"\u21d2",rightarrow:"\u2192",RightArrowBar:"\u21e5",RightArrowLeftArrow:"\u21c4",rightarrowtail:"\u21a3",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVector:"\u21c2",RightDownVectorBar:"\u2955",RightFloor:"\u230b",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTeeVector:"\u295b",rightthreetimes:"\u22cc",RightTriangle:"\u22b3",RightTriangleBar:"\u29d0",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVector:"\u21be",RightUpVectorBar:"\u2954",RightVector:"\u21c0",RightVectorBar:"\u2953",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoust:"\u23b1",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",Ropf:"\u211d",ropf:"\u{1d563}",roplus:"\u2a2e",rotimes:"\u2a35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",Rrightarrow:"\u21db",rsaquo:"\u203a",Rscr:"\u211b",rscr:"\u{1d4c7}",Rsh:"\u21b1",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",RuleDelayed:"\u29f4",ruluhar:"\u2968",rx:"\u211e",Sacute:"\u015a",sacute:"\u015b",sbquo:"\u201a",Sc:"\u2abc",sc:"\u227b",scap:"\u2ab8",Scaron:"\u0160",scaron:"\u0161",sccue:"\u227d",scE:"\u2ab4",sce:"\u2ab0",Scedil:"\u015e",scedil:"\u015f",Scirc:"\u015c",scirc:"\u015d",scnap:"\u2aba",scnE:"\u2ab6",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",Scy:"\u0421",scy:"\u0441",sdot:"\u22c5",sdotb:"\u22a1",sdote:"\u2a66",searhk:"\u2925",seArr:"\u21d8",searr:"\u2198",searrow:"\u2198",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1d516}",sfr:"\u{1d530}",sfrown:"\u2322",sharp:"\u266f",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xad",Sigma:"\u03a3",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",SOFTcy:"\u042c",softcy:"\u044c",sol:"/",solb:"\u29c4",solbar:"\u233f",Sopf:"\u{1d54a}",sopf:"\u{1d564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",Sqrt:"\u221a",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25a1",Square:"\u25a1",square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25aa",squf:"\u25aa",srarr:"\u2192",Sscr:"\u{1d4ae}",sscr:"\u{1d4c8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",Star:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",Sub:"\u22d0",sub:"\u2282",subdot:"\u2abd",subE:"\u2ac5",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",Subset:"\u22d0",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2ac5",SubsetEqual:"\u2286",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succ:"\u227b",succapprox:"\u2ab8",succcurlyeq:"\u227d",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",SuchThat:"\u220b",Sum:"\u2211",sum:"\u2211",sung:"\u266a",Sup:"\u22d1",sup:"\u2283",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supdot:"\u2abe",supdsub:"\u2ad8",supE:"\u2ac6",supe:"\u2287",supedot:"\u2ac4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",Supset:"\u22d1",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swarhk:"\u2926",swArr:"\u21d9",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292a",szlig:"\xdf",Tab:"\t",target:"\u2316",Tau:"\u03a4",tau:"\u03c4",tbrk:"\u23b4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",Tfr:"\u{1d517}",tfr:"\u{1d531}",there4:"\u2234",Therefore:"\u2234",therefore:"\u2234",Theta:"\u0398",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",ThickSpace:"\u205f\u200a",thinsp:"\u2009",ThinSpace:"\u2009",thkap:"\u2248",thksim:"\u223c",THORN:"\xde",thorn:"\xfe",Tilde:"\u223c",tilde:"\u02dc",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",times:"\xd7",timesb:"\u22a0",timesbar:"\u2a31",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",top:"\u22a4",topbot:"\u2336",topcir:"\u2af1",Topf:"\u{1d54b}",topf:"\u{1d565}",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",TRADE:"\u2122",trade:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",TripleDot:"\u20db",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",Tscr:"\u{1d4af}",tscr:"\u{1d4c9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040b",tshcy:"\u045b",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",Uacute:"\xda",uacute:"\xfa",Uarr:"\u219f",uArr:"\u21d1",uarr:"\u2191",Uarrocir:"\u2949",Ubrcy:"\u040e",ubrcy:"\u045e",Ubreve:"\u016c",ubreve:"\u016d",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21c5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",Ufr:"\u{1d518}",ufr:"\u{1d532}",Ugrave:"\xd9",ugrave:"\xf9",uHar:"\u2963",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",Umacr:"\u016a",umacr:"\u016b",uml:"\xa8",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1d54c}",uopf:"\u{1d566}",UpArrow:"\u2191",Uparrow:"\u21d1",uparrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21c5",UpDownArrow:"\u2195",Updownarrow:"\u21d5",updownarrow:"\u2195",UpEquilibrium:"\u296e",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03d2",upsi:"\u03c5",upsih:"\u03d2",Upsilon:"\u03a5",upsilon:"\u03c5",UpTee:"\u22a5",UpTeeArrow:"\u21a5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",Uring:"\u016e",uring:"\u016f",urtri:"\u25f9",Uscr:"\u{1d4b0}",uscr:"\u{1d4ca}",utdot:"\u22f0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",Uuml:"\xdc",uuml:"\xfc",uwangle:"\u29a7",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",vArr:"\u21d5",varr:"\u2195",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",Vbar:"\u2aeb",vBar:"\u2ae8",vBarv:"\u2ae9",Vcy:"\u0412",vcy:"\u0432",VDash:"\u22ab",Vdash:"\u22a9",vDash:"\u22a8",vdash:"\u22a2",Vdashl:"\u2ae6",Vee:"\u22c1",vee:"\u2228",veebar:"\u22bb",veeeq:"\u225a",vellip:"\u22ee",Verbar:"\u2016",verbar:"|",Vert:"\u2016",vert:"|",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\u{1d519}",vfr:"\u{1d533}",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",Vopf:"\u{1d54d}",vopf:"\u{1d567}",vprop:"\u221d",vrtri:"\u22b3",Vscr:"\u{1d4b1}",vscr:"\u{1d4cb}",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",Vvdash:"\u22aa",vzigzag:"\u299a",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2a5f",Wedge:"\u22c0",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1d51a}",wfr:"\u{1d534}",Wopf:"\u{1d54e}",wopf:"\u{1d568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1d4b2}",wscr:"\u{1d4cc}",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",Xfr:"\u{1d51b}",xfr:"\u{1d535}",xhArr:"\u27fa",xharr:"\u27f7",Xi:"\u039e",xi:"\u03be",xlArr:"\u27f8",xlarr:"\u27f5",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",Xopf:"\u{1d54f}",xopf:"\u{1d569}",xoplus:"\u2a01",xotime:"\u2a02",xrArr:"\u27f9",xrarr:"\u27f6",Xscr:"\u{1d4b3}",xscr:"\u{1d4cd}",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",Yacute:"\xdd",yacute:"\xfd",YAcy:"\u042f",yacy:"\u044f",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042b",ycy:"\u044b",yen:"\xa5",Yfr:"\u{1d51c}",yfr:"\u{1d536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1d550}",yopf:"\u{1d56a}",Yscr:"\u{1d4b4}",yscr:"\u{1d4ce}",YUcy:"\u042e",yucy:"\u044e",Yuml:"\u0178",yuml:"\xff",Zacute:"\u0179",zacute:"\u017a",Zcaron:"\u017d",zcaron:"\u017e",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017b",zdot:"\u017c",zeetrf:"\u2128",ZeroWidthSpace:"\u200b",Zeta:"\u0396",zeta:"\u03b6",Zfr:"\u2128",zfr:"\u{1d537}",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21dd",Zopf:"\u2124",zopf:"\u{1d56b}",Zscr:"\u{1d4b5}",zscr:"\u{1d4cf}",zwj:"\u200d",zwnj:"\u200c"}),e.entityMap=e.HTML_ENTITIES},128:(t,e,r)=>{"use strict";var n,s;r.d(e,{Al:()=>c,Gb:()=>a,Oe:()=>u});var i=r(665),o=r(164);const a=n||(n=r.t(i,2)),c=(new a.DOMImplementation).createDocument("",""),l=(null===(s||(s=r.t(o,2)))||void 0===(s||(s=r.t(o,2)))?void 0:o.install)||window.wgxpath.install,h=function(){const t={document:{},XPathResult:{}};return l(t),t.document.XPathResult=t.XPathResult,t.document}(),u={currentDocument:c,evaluate:h.evaluate,result:h.XPathResult,createNSResolver:h.createNSResolver}},164:(t,e,r)=>{(function(){"use strict";var e=this;function r(t){return"string"==typeof t}function n(t,e,r){return t.call.apply(t.bind,arguments)}function s(t,e,r){if(!t)throw Error();if(2<arguments.length){var n=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,n),t.apply(e,r)}}return function(){return t.apply(e,arguments)}}function i(t,e,r){return(i=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?n:s).apply(null,arguments)}function o(t){var e=ot;function r(){}r.prototype=e.prototype,t.G=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.F=function(t,r,n){for(var s=Array(arguments.length-2),i=2;i<arguments.length;i++)s[i-2]=arguments[i];return e.prototype[r].apply(t,s)}}var a=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};function c(t,e){return-1!=t.indexOf(e)}function l(t,e){return t<e?-1:t>e?1:0}var h,u=Array.prototype.indexOf?function(t,e,r){return Array.prototype.indexOf.call(t,e,r)}:function(t,e,n){if(n=null==n?0:0>n?Math.max(0,t.length+n):n,r(t))return r(e)&&1==e.length?t.indexOf(e,n):-1;for(;n<t.length;n++)if(n in t&&t[n]===e)return n;return-1},d=Array.prototype.forEach?function(t,e,r){Array.prototype.forEach.call(t,e,r)}:function(t,e,n){for(var s=t.length,i=r(t)?t.split(""):t,o=0;o<s;o++)o in i&&e.call(n,i[o],o,t)},p=Array.prototype.filter?function(t,e,r){return Array.prototype.filter.call(t,e,r)}:function(t,e,n){for(var s=t.length,i=[],o=0,a=r(t)?t.split(""):t,c=0;c<s;c++)if(c in a){var l=a[c];e.call(n,l,c,t)&&(i[o++]=l)}return i},m=Array.prototype.reduce?function(t,e,r,n){return n&&(e=i(e,n)),Array.prototype.reduce.call(t,e,r)}:function(t,e,r,n){var s=r;return d(t,(function(r,i){s=e.call(n,s,r,i,t)})),s},f=Array.prototype.some?function(t,e,r){return Array.prototype.some.call(t,e,r)}:function(t,e,n){for(var s=t.length,i=r(t)?t.split(""):t,o=0;o<s;o++)if(o in i&&e.call(n,i[o],o,t))return!0;return!1};t:{var g=e.navigator;if(g){var E=g.userAgent;if(E){h=E;break t}}h=""}var N,b,T=c(h,"Opera")||c(h,"OPR"),y=c(h,"Trident")||c(h,"MSIE"),I=c(h,"Edge"),A=c(h,"Gecko")&&!(c(h.toLowerCase(),"webkit")&&!c(h,"Edge"))&&!(c(h,"Trident")||c(h,"MSIE"))&&!c(h,"Edge"),R=c(h.toLowerCase(),"webkit")&&!c(h,"Edge");function L(){var t=e.document;return t?t.documentMode:void 0}t:{var C="",O=(b=h,A?/rv\:([^\);]+)(\)|;)/.exec(b):I?/Edge\/([\d\.]+)/.exec(b):y?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(b):R?/WebKit\/(\S+)/.exec(b):T?/(?:Version)[ \/]?(\S+)/.exec(b):void 0);if(O&&(C=O?O[1]:""),y){var v=L();if(null!=v&&v>parseFloat(C)){N=String(v);break t}}N=C}var S={};function w(t){if(!S[t]){for(var e=0,r=a(String(N)).split("."),n=a(String(t)).split("."),s=Math.max(r.length,n.length),i=0;0==e&&i<s;i++){var o=r[i]||"",c=n[i]||"",h=/(\d*)(\D*)/g,u=/(\d*)(\D*)/g;do{var d=h.exec(o)||["","",""],p=u.exec(c)||["","",""];if(0==d[0].length&&0==p[0].length)break;e=l(0==d[1].length?0:parseInt(d[1],10),0==p[1].length?0:parseInt(p[1],10))||l(0==d[2].length,0==p[2].length)||l(d[2],p[2])}while(0==e)}S[t]=0<=e}}var M=e.document,x=M&&y?L()||("CSS1Compat"==M.compatMode?parseInt(N,10):5):void 0,P=y&&!(9<=Number(x)),D=y&&!(8<=Number(x));function k(t,e,r,n){this.a=t,this.nodeName=r,this.nodeValue=n,this.nodeType=2,this.parentNode=this.ownerElement=e}function _(t,e){var r=D&&"href"==e.nodeName?t.getAttribute(e.nodeName,2):e.nodeValue;return new k(e,t,e.nodeName,r)}function F(t){var e=null;if(1==(r=t.nodeType)&&(e=null==(e=null==(e=t.textContent)||null==e?t.innerText:e)||null==e?"":e),"string"!=typeof e)if(P&&"title"==t.nodeName.toLowerCase()&&1==r)e=t.text;else if(9==r||1==r){t=9==r?t.documentElement:t.firstChild;var r=0,n=[];for(e="";t;){do{1!=t.nodeType&&(e+=t.nodeValue),P&&"title"==t.nodeName.toLowerCase()&&(e+=t.text),n[r++]=t}while(t=t.firstChild);for(;r&&!(t=n[--r].nextSibling););}}else e=t.nodeValue;return""+e}function B(t,e,r){if(null===e)return!0;try{if(!t.getAttribute)return!1}catch(t){return!1}return D&&"class"==e&&(e="className"),null==r?!!t.getAttribute(e):t.getAttribute(e,2)==r}function U(t,e,n,s,i){return(P?q:H).call(null,t,e,r(n)?n:null,r(s)?s:null,i||new $)}function q(t,e,r,n,s){if(t instanceof Dt||8==t.b||r&&null===t.b){var i=e.all;if(!i)return s;if("*"!=(t=V(t))&&!(i=e.getElementsByTagName(t)))return s;if(r){for(var o=[],a=0;e=i[a++];)B(e,r,n)&&o.push(e);i=o}for(a=0;e=i[a++];)"*"==t&&"!"==e.tagName||tt(s,e);return s}return X(t,e,r,n,s),s}function H(t,e,r,n,s){return e.getElementsByName&&n&&"name"==r&&!y?(e=e.getElementsByName(n),d(e,(function(e){t.a(e)&&tt(s,e)}))):e.getElementsByClassName&&n&&"class"==r?(e=e.getElementsByClassName(n),d(e,(function(e){e.className==n&&t.a(e)&&tt(s,e)}))):t instanceof Lt?X(t,e,r,n,s):e.getElementsByTagName&&(e=e.getElementsByTagName(t.f()),d(e,(function(t){B(t,r,n)&&tt(s,t)}))),s}function G(t,e,r,n,s){var i;if((t instanceof Dt||8==t.b||r&&null===t.b)&&(i=e.childNodes)){var o=V(t);return"*"==o||(i=p(i,(function(t){return t.tagName&&t.tagName.toLowerCase()==o})),i)?(r&&(i=p(i,(function(t){return B(t,r,n)}))),d(i,(function(t){"*"==o&&("!"==t.tagName||"*"==o&&1!=t.nodeType)||tt(s,t)})),s):s}return j(t,e,r,n,s)}function j(t,e,r,n,s){for(e=e.firstChild;e;e=e.nextSibling)B(e,r,n)&&t.a(e)&&tt(s,e);return s}function X(t,e,r,n,s){for(e=e.firstChild;e;e=e.nextSibling)B(e,r,n)&&t.a(e)&&tt(s,e),X(t,e,r,n,s)}function V(t){if(t instanceof Lt){if(8==t.b)return"!";if(null===t.b)return"*"}return t.f()}function W(t,e){if(!t||!e)return!1;if(t.contains&&1==e.nodeType)return t==e||t.contains(e);if(void 0!==t.compareDocumentPosition)return t==e||!!(16&t.compareDocumentPosition(e));for(;e&&t!=e;)e=e.parentNode;return e==t}function K(t,r){if(t==r)return 0;if(t.compareDocumentPosition)return 2&t.compareDocumentPosition(r)?1:-1;if(y&&!(9<=Number(x))){if(9==t.nodeType)return-1;if(9==r.nodeType)return 1}if("sourceIndex"in t||t.parentNode&&"sourceIndex"in t.parentNode){var n=1==t.nodeType,s=1==r.nodeType;if(n&&s)return t.sourceIndex-r.sourceIndex;var i=t.parentNode,o=r.parentNode;return i==o?Y(t,r):!n&&W(i,r)?-1*z(t,r):!s&&W(o,t)?z(r,t):(n?t.sourceIndex:i.sourceIndex)-(s?r.sourceIndex:o.sourceIndex)}return(n=(s=9==t.nodeType?t:t.ownerDocument||t.document).createRange()).selectNode(t),n.collapse(!0),(s=s.createRange()).selectNode(r),s.collapse(!0),n.compareBoundaryPoints(e.Range.START_TO_END,s)}function z(t,e){var r=t.parentNode;if(r==e)return-1;for(var n=e;n.parentNode!=r;)n=n.parentNode;return Y(n,t)}function Y(t,e){for(var r=e;r=r.previousSibling;)if(r==t)return-1;return 1}function $(){this.b=this.a=null,this.l=0}function J(t){this.node=t,this.a=this.b=null}function Q(t,e){if(!t.a)return e;if(!e.a)return t;for(var r=t.a,n=e.a,s=null,i=null,o=0;r&&n;){i=r.node;var a=n.node;i==a||i instanceof k&&a instanceof k&&i.a==a.a?(i=r,r=r.a,n=n.a):0<K(r.node,n.node)?(i=n,n=n.a):(i=r,r=r.a),(i.b=s)?s.a=i:t.a=i,s=i,o++}for(i=r||n;i;)i.b=s,s=s.a=i,o++,i=i.a;return t.b=s,t.l=o,t}function Z(t,e){var r=new J(e);r.a=t.a,t.b?t.a.b=r:t.a=t.b=r,t.a=r,t.l++}function tt(t,e){var r=new J(e);r.b=t.b,t.a?t.b.a=r:t.a=t.b=r,t.b=r,t.l++}function et(t){return(t=t.a)?t.node:null}function rt(t){return(t=et(t))?F(t):""}function nt(t,e){return new st(t,!!e)}function st(t,e){this.f=t,this.b=(this.c=e)?t.b:t.a,this.a=null}function it(t){var e=t.b;if(null==e)return null;var r=t.a=e;return t.b=t.c?e.b:e.a,r.node}function ot(t){this.i=t,this.b=this.g=!1,this.f=null}function at(t){return"\n  "+t.toString().split("\n").join("\n  ")}function ct(t,e){t.g=e}function lt(t,e){t.b=e}function ht(t,e){var r=t.a(e);return r instanceof $?+rt(r):+r}function ut(t,e){var r=t.a(e);return r instanceof $?rt(r):""+r}function dt(t,e){var r=t.a(e);return r instanceof $?!!r.l:!!r}function pt(t,e,r){ot.call(this,t.i),this.c=t,this.h=e,this.o=r,this.g=e.g||r.g,this.b=e.b||r.b,this.c==Nt&&(r.b||r.g||4==r.i||0==r.i||!e.f?e.b||e.g||4==e.i||0==e.i||!r.f||(this.f={name:r.f.name,s:e}):this.f={name:e.f.name,s:r})}function mt(t,e,r,n,s){var i;if(e=e.a(n),r=r.a(n),e instanceof $&&r instanceof $){for(n=it(e=nt(e));n;n=it(e))for(i=it(s=nt(r));i;i=it(s))if(t(F(n),F(i)))return!0;return!1}if(e instanceof $||r instanceof $){e instanceof $?(s=e,n=r):(s=r,n=e);for(var o=typeof n,a=it(i=nt(s));a;a=it(i)){switch(o){case"number":a=+F(a);break;case"boolean":a=!!F(a);break;case"string":a=F(a);break;default:throw Error("Illegal primitive type for comparison.")}if(s==e&&t(a,n)||s==r&&t(n,a))return!0}return!1}return s?"boolean"==typeof e||"boolean"==typeof r?t(!!e,!!r):"number"==typeof e||"number"==typeof r?t(+e,+r):t(e,r):t(+e,+r)}function ft(t,e,r,n){this.a=t,this.w=e,this.i=r,this.m=n}!A&&!y||y&&9<=Number(x)||A&&w("1.9.1"),y&&w("9"),o(pt),pt.prototype.a=function(t){return this.c.m(this.h,this.o,t)},pt.prototype.toString=function(){return"Binary Expression: "+this.c+at(this.h)+at(this.o)},ft.prototype.toString=function(){return this.a};var gt={};function Et(t,e,r,n){if(gt.hasOwnProperty(t))throw Error("Binary operator already created: "+t);return t=new ft(t,e,r,n),gt[t.toString()]=t}Et("div",6,1,(function(t,e,r){return ht(t,r)/ht(e,r)})),Et("mod",6,1,(function(t,e,r){return ht(t,r)%ht(e,r)})),Et("*",6,1,(function(t,e,r){return ht(t,r)*ht(e,r)})),Et("+",5,1,(function(t,e,r){return ht(t,r)+ht(e,r)})),Et("-",5,1,(function(t,e,r){return ht(t,r)-ht(e,r)})),Et("<",4,2,(function(t,e,r){return mt((function(t,e){return t<e}),t,e,r)})),Et(">",4,2,(function(t,e,r){return mt((function(t,e){return t>e}),t,e,r)})),Et("<=",4,2,(function(t,e,r){return mt((function(t,e){return t<=e}),t,e,r)})),Et(">=",4,2,(function(t,e,r){return mt((function(t,e){return t>=e}),t,e,r)}));var Nt=Et("=",3,2,(function(t,e,r){return mt((function(t,e){return t==e}),t,e,r,!0)}));function bt(t,e,r){this.a=t,this.b=e||1,this.f=r||1}function Tt(t,e){if(e.a.length&&4!=t.i)throw Error("Primary expression must evaluate to nodeset if filter has predicate(s).");ot.call(this,t.i),this.c=t,this.h=e,this.g=t.g,this.b=t.b}function yt(t,e){if(e.length<t.A)throw Error("Function "+t.j+" expects at least"+t.A+" arguments, "+e.length+" given");if(null!==t.v&&e.length>t.v)throw Error("Function "+t.j+" expects at most "+t.v+" arguments, "+e.length+" given");t.B&&d(e,(function(e,r){if(4!=e.i)throw Error("Argument "+r+" to function "+t.j+" is not of type Nodeset: "+e)})),ot.call(this,t.i),this.h=t,this.c=e,ct(this,t.g||f(e,(function(t){return t.g}))),lt(this,t.D&&!e.length||t.C&&!!e.length||f(e,(function(t){return t.b})))}function It(t,e,r,n,s,i,o,a,c){this.j=t,this.i=e,this.g=r,this.D=n,this.C=s,this.m=i,this.A=o,this.v=void 0!==a?a:o,this.B=!!c}Et("!=",3,2,(function(t,e,r){return mt((function(t,e){return t!=e}),t,e,r,!0)})),Et("and",2,2,(function(t,e,r){return dt(t,r)&&dt(e,r)})),Et("or",1,2,(function(t,e,r){return dt(t,r)||dt(e,r)})),o(Tt),Tt.prototype.a=function(t){return t=this.c.a(t),Ht(this.h,t)},Tt.prototype.toString=function(){return"Filter:"+at(this.c)+at(this.h)},o(yt),yt.prototype.a=function(t){return this.h.m.apply(null,function(t){return Array.prototype.concat.apply(Array.prototype,arguments)}(t,this.c))},yt.prototype.toString=function(){var t="Function: "+this.h;if(this.c.length){var e=m(this.c,(function(t,e){return t+at(e)}),"Arguments:");t=t+at(e)}return t},It.prototype.toString=function(){return this.j};var At={};function Rt(t,e,r,n,s,i,o,a){if(At.hasOwnProperty(t))throw Error("Function already created: "+t+".");At[t]=new It(t,e,r,n,!1,s,i,o,a)}function Lt(t,e){switch(this.h=t,this.c=void 0!==e?e:null,this.b=null,t){case"comment":this.b=8;break;case"text":this.b=3;break;case"processing-instruction":this.b=7;break;case"node":break;default:throw Error("Unexpected argument")}}function Ct(t){return"comment"==t||"text"==t||"processing-instruction"==t||"node"==t}function Ot(t){this.b=t,this.a=0}Rt("boolean",2,!1,!1,(function(t,e){return dt(e,t)}),1),Rt("ceiling",1,!1,!1,(function(t,e){return Math.ceil(ht(e,t))}),1),Rt("concat",3,!1,!1,(function(t,e){return m(function(t,e,r){return 2>=arguments.length?Array.prototype.slice.call(t,e):Array.prototype.slice.call(t,e,r)}(arguments,1),(function(e,r){return e+ut(r,t)}),"")}),2,null),Rt("contains",2,!1,!1,(function(t,e,r){return c(ut(e,t),ut(r,t))}),2),Rt("count",1,!1,!1,(function(t,e){return e.a(t).l}),1,1,!0),Rt("false",2,!1,!1,(function(){return!1}),0),Rt("floor",1,!1,!1,(function(t,e){return Math.floor(ht(e,t))}),1),Rt("id",4,!1,!1,(function(t,e){function n(t){if(P){var e=s.all[t];if(e){if(e.nodeType&&t==e.id)return e;if(e.length)return function(t,e){var n;t:{n=t.length;for(var s=r(t)?t.split(""):t,i=0;i<n;i++)if(i in s&&e.call(void 0,s[i],i,t)){n=i;break t}n=-1}return 0>n?null:r(t)?t.charAt(n):t[n]}(e,(function(e){return t==e.id}))}return null}return s.getElementById(t)}var s=9==(i=t.a).nodeType?i:i.ownerDocument,i=ut(e,t).split(/\s+/),o=[];d(i,(function(t){!(t=n(t))||0<=u(o,t)||o.push(t)})),o.sort(K);var a=new $;return d(o,(function(t){tt(a,t)})),a}),1),Rt("lang",2,!1,!1,(function(){return!1}),1),Rt("last",1,!0,!1,(function(t){if(1!=arguments.length)throw Error("Function last expects ()");return t.f}),0),Rt("local-name",3,!1,!0,(function(t,e){var r=e?et(e.a(t)):t.a;return r?r.localName||r.nodeName.toLowerCase():""}),0,1,!0),Rt("name",3,!1,!0,(function(t,e){var r=e?et(e.a(t)):t.a;return r?r.nodeName.toLowerCase():""}),0,1,!0),Rt("namespace-uri",3,!0,!1,(function(){return""}),0,1,!0),Rt("normalize-space",3,!1,!0,(function(t,e){return(e?ut(e,t):F(t.a)).replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")}),0,1),Rt("not",2,!1,!1,(function(t,e){return!dt(e,t)}),1),Rt("number",1,!1,!0,(function(t,e){return e?ht(e,t):+F(t.a)}),0,1),Rt("position",1,!0,!1,(function(t){return t.b}),0),Rt("round",1,!1,!1,(function(t,e){return Math.round(ht(e,t))}),1),Rt("starts-with",2,!1,!1,(function(t,e,r){return e=ut(e,t),t=ut(r,t),0==e.lastIndexOf(t,0)}),2),Rt("string",3,!1,!0,(function(t,e){return e?ut(e,t):F(t.a)}),0,1),Rt("string-length",1,!1,!0,(function(t,e){return(e?ut(e,t):F(t.a)).length}),0,1),Rt("substring",3,!1,!1,(function(t,e,r,n){if(r=ht(r,t),isNaN(r)||1/0==r||-1/0==r)return"";if(n=n?ht(n,t):1/0,isNaN(n)||-1/0===n)return"";r=Math.round(r)-1;var s=Math.max(r,0);return t=ut(e,t),1/0==n?t.substring(s):t.substring(s,r+Math.round(n))}),2,3),Rt("substring-after",3,!1,!1,(function(t,e,r){return e=ut(e,t),t=ut(r,t),-1==(r=e.indexOf(t))?"":e.substring(r+t.length)}),2),Rt("substring-before",3,!1,!1,(function(t,e,r){return e=ut(e,t),t=ut(r,t),-1==(t=e.indexOf(t))?"":e.substring(0,t)}),2),Rt("sum",1,!1,!1,(function(t,e){for(var r=nt(e.a(t)),n=0,s=it(r);s;s=it(r))n+=+F(s);return n}),1,1,!0),Rt("translate",3,!1,!1,(function(t,e,r,n){e=ut(e,t),r=ut(r,t);var s=ut(n,t);for(t={},n=0;n<r.length;n++){var i=r.charAt(n);i in t||(t[i]=s.charAt(n))}for(r="",n=0;n<e.length;n++)r+=(i=e.charAt(n))in t?t[i]:i;return r}),3),Rt("true",2,!1,!1,(function(){return!0}),0),Lt.prototype.a=function(t){return null===this.b||this.b==t.nodeType},Lt.prototype.f=function(){return this.h},Lt.prototype.toString=function(){var t="Kind Test: "+this.h;return null===this.c||(t+=at(this.c)),t};var vt=/\$?(?:(?![0-9-\.])(?:\*|[\w-\.]+):)?(?![0-9-\.])(?:\*|[\w-\.]+)|\/\/|\.\.|::|\d+(?:\.\d*)?|\.\d+|"[^"]*"|'[^']*'|[!<>]=|\s+|./g,St=/^\s/;function wt(t,e){return t.b[t.a+(e||0)]}function Mt(t){return t.b[t.a++]}function xt(t){return t.b.length<=t.a}function Pt(t){ot.call(this,3),this.c=t.substring(1,t.length-1)}function Dt(t,e){var r;this.j=t.toLowerCase(),r="*"==this.j?"*":"http://www.w3.org/1999/xhtml",this.c=e?e.toLowerCase():r}function kt(t,e){if(ot.call(this,t.i),this.h=t,this.c=e,this.g=t.g,this.b=t.b,1==this.c.length){var r=this.c[0];r.u||r.c!=Wt||"*"!=(r=r.o).f()&&(this.f={name:r.f(),s:null})}}function _t(){ot.call(this,4)}function Ft(){ot.call(this,4)}function Bt(t){return"/"==t||"//"==t}function Ut(t){ot.call(this,4),this.c=t,ct(this,f(this.c,(function(t){return t.g}))),lt(this,f(this.c,(function(t){return t.b})))}function qt(t,e){this.a=t,this.b=!!e}function Ht(t,e,r){for(r=r||0;r<t.a.length;r++)for(var n,s=t.a[r],i=nt(e),o=e.l,a=0;n=it(i);a++){var c=t.b?o-a:a+1;if("number"==typeof(n=s.a(new bt(n,c,o))))c=c==n;else if("string"==typeof n||"boolean"==typeof n)c=!!n;else{if(!(n instanceof $))throw Error("Predicate.evaluate returned an unexpected type.");c=0<n.l}if(!c){if(n=(c=i).f,!(h=c.a))throw Error("Next must be called at least once before remove.");var l=h.b,h=h.a;l?l.a=h:n.a=h,h?h.b=l:n.b=l,n.l--,c.a=null}}return e}function Gt(t,e,r,n){ot.call(this,4),this.c=t,this.o=e,this.h=r||new qt([]),this.u=!!n,e=0<(e=this.h).a.length?e.a[0].f:null,t.b&&e&&(t=e.name,t=P?t.toLowerCase():t,this.f={name:t,s:e.s});t:{for(t=this.h,e=0;e<t.a.length;e++)if((r=t.a[e]).g||1==r.i||0==r.i){t=!0;break t}t=!1}this.g=t}function jt(t,e,r,n){this.j=t,this.f=e,this.a=r,this.b=n}o(Pt),Pt.prototype.a=function(){return this.c},Pt.prototype.toString=function(){return"Literal: "+this.c},Dt.prototype.a=function(t){var e=t.nodeType;return(1==e||2==e)&&(e=void 0!==t.localName?t.localName:t.nodeName,("*"==this.j||this.j==e.toLowerCase())&&("*"==this.c||this.c==(t.namespaceURI?t.namespaceURI.toLowerCase():"http://www.w3.org/1999/xhtml")))},Dt.prototype.f=function(){return this.j},Dt.prototype.toString=function(){return"Name Test: "+("http://www.w3.org/1999/xhtml"==this.c?"":this.c+":")+this.j},o(kt),o(_t),_t.prototype.a=function(t){var e=new $;return 9==(t=t.a).nodeType?tt(e,t):tt(e,t.ownerDocument),e},_t.prototype.toString=function(){return"Root Helper Expression"},o(Ft),Ft.prototype.a=function(t){var e=new $;return tt(e,t.a),e},Ft.prototype.toString=function(){return"Context Helper Expression"},kt.prototype.a=function(t){var e=this.h.a(t);if(!(e instanceof $))throw Error("Filter expression must evaluate to nodeset.");for(var r=0,n=(t=this.c).length;r<n&&e.l;r++){var s,i=t[r],o=nt(e,i.c.a);if(i.g||i.c!=Yt)if(i.g||i.c!=Jt)for(s=it(o),e=i.a(new bt(s));null!=(s=it(o));)e=Q(e,s=i.a(new bt(s)));else s=it(o),e=i.a(new bt(s));else{for(s=it(o);(e=it(o))&&(!s.contains||s.contains(e))&&8&e.compareDocumentPosition(s);s=e);e=i.a(new bt(s))}}return e},kt.prototype.toString=function(){var t;if(t="Path Expression:"+at(this.h),this.c.length){var e=m(this.c,(function(t,e){return t+at(e)}),"Steps:");t+=at(e)}return t},o(Ut),Ut.prototype.a=function(t){var e=new $;return d(this.c,(function(r){if(!((r=r.a(t))instanceof $))throw Error("Path expression must evaluate to NodeSet.");e=Q(e,r)})),e},Ut.prototype.toString=function(){return m(this.c,(function(t,e){return t+at(e)}),"Union Expression:")},qt.prototype.toString=function(){return m(this.a,(function(t,e){return t+at(e)}),"Predicates:")},o(Gt),Gt.prototype.a=function(t){var e=t.a,r=null,n=null,s=null,i=0;if((r=this.f)&&(n=r.name,s=r.s?ut(r.s,t):null,i=1),this.u)if(this.g||this.c!=Kt)if(e=it(t=nt(new Gt(zt,new Lt("node")).a(t))))for(r=this.m(e,n,s,i);null!=(e=it(t));)r=Q(r,this.m(e,n,s,i));else r=new $;else r=U(this.o,e,n,s),r=Ht(this.h,r,i);else r=this.m(t.a,n,s,i);return r},Gt.prototype.m=function(t,e,r,n){return t=this.c.f(this.o,t,e,r),Ht(this.h,t,n)},Gt.prototype.toString=function(){var t;if(t="Step:"+at("Operator: "+(this.u?"//":"/")),this.c.j&&(t+=at("Axis: "+this.c)),t+=at(this.o),this.h.a.length){var e=m(this.h.a,(function(t,e){return t+at(e)}),"Predicates:");t+=at(e)}return t},jt.prototype.toString=function(){return this.j};var Xt={};function Vt(t,e,r,n){if(Xt.hasOwnProperty(t))throw Error("Axis already created: "+t);return e=new jt(t,e,r,!!n),Xt[t]=e}Vt("ancestor",(function(t,e){for(var r=new $,n=e;n=n.parentNode;)t.a(n)&&Z(r,n);return r}),!0),Vt("ancestor-or-self",(function(t,e){var r=new $,n=e;do{t.a(n)&&Z(r,n)}while(n=n.parentNode);return r}),!0);var Wt=Vt("attribute",(function(t,e){var r=new $;if("style"==(i=t.f())&&P&&e.style)return tt(r,new k(e.style,e,"style",e.style.cssText)),r;var n=e.attributes;if(n)if(t instanceof Lt&&null===t.b||"*"==i)for(var s,i=0;s=n[i];i++)P?s.nodeValue&&tt(r,_(e,s)):tt(r,s);else(s=n.getNamedItem(i))&&(P?s.nodeValue&&tt(r,_(e,s)):tt(r,s));return r}),!1),Kt=Vt("child",(function(t,e,n,s,i){return(P?G:j).call(null,t,e,r(n)?n:null,r(s)?s:null,i||new $)}),!1,!0);Vt("descendant",U,!1,!0);var zt=Vt("descendant-or-self",(function(t,e,r,n){var s=new $;return B(e,r,n)&&t.a(e)&&tt(s,e),U(t,e,r,n,s)}),!1,!0),Yt=Vt("following",(function(t,e,r,n){var s=new $;do{for(var i=e;i=i.nextSibling;)B(i,r,n)&&t.a(i)&&tt(s,i),s=U(t,i,r,n,s)}while(e=e.parentNode);return s}),!1,!0);Vt("following-sibling",(function(t,e){for(var r=new $,n=e;n=n.nextSibling;)t.a(n)&&tt(r,n);return r}),!1),Vt("namespace",(function(){return new $}),!1);var $t=Vt("parent",(function(t,e){var r=new $;if(9==e.nodeType)return r;if(2==e.nodeType)return tt(r,e.ownerElement),r;var n=e.parentNode;return t.a(n)&&tt(r,n),r}),!1),Jt=Vt("preceding",(function(t,e,r,n){var s=new $,i=[];do{i.unshift(e)}while(e=e.parentNode);for(var o=1,a=i.length;o<a;o++){var c=[];for(e=i[o];e=e.previousSibling;)c.unshift(e);for(var l=0,h=c.length;l<h;l++)B(e=c[l],r,n)&&t.a(e)&&tt(s,e),s=U(t,e,r,n,s)}return s}),!0,!0);Vt("preceding-sibling",(function(t,e){for(var r=new $,n=e;n=n.previousSibling;)t.a(n)&&Z(r,n);return r}),!0);var Qt=Vt("self",(function(t,e){var r=new $;return t.a(e)&&tt(r,e),r}),!1);function Zt(t){ot.call(this,1),this.c=t,this.g=t.g,this.b=t.b}function te(t){ot.call(this,1),this.c=t}function ee(t,e){this.a=t,this.b=e}function re(t){for(var e,r=[];;){ne(t,"Missing right hand side of binary expression."),e=he(t);var n=Mt(t.a);if(!n)break;var s=(n=gt[n]||null)&&n.w;if(!s){t.a.a--;break}for(;r.length&&s<=r[r.length-1].w;)e=new pt(r.pop(),r.pop(),e);r.push(e,n)}for(;r.length;)e=new pt(r.pop(),r.pop(),e);return e}function ne(t,e){if(xt(t.a))throw Error(e)}function se(t,e){var r=Mt(t.a);if(r!=e)throw Error("Bad token, expected: "+e+" got: "+r)}function ie(t){if(")"!=(t=Mt(t.a)))throw Error("Bad token: "+t)}function oe(t){if(2>(t=Mt(t.a)).length)throw Error("Unclosed literal string");return new Pt(t)}function ae(t){var e,r,n=[];if(Bt(wt(t.a))){if(e=Mt(t.a),r=wt(t.a),"/"==e&&(xt(t.a)||"."!=r&&".."!=r&&"@"!=r&&"*"!=r&&!/(?![0-9])[\w]/.test(r)))return new _t;r=new _t,ne(t,"Missing next location step."),e=ce(t,e),n.push(e)}else{t:{switch(r=(e=wt(t.a)).charAt(0)){case"$":throw Error("Variable reference not allowed in HTML XPath");case"(":Mt(t.a),e=re(t),ne(t,'unclosed "("'),se(t,")");break;case'"':case"'":e=oe(t);break;default:if(isNaN(+e)){if(Ct(e)||!/(?![0-9])[\w]/.test(r)||"("!=wt(t.a,1)){e=null;break t}for(e=Mt(t.a),e=At[e]||null,Mt(t.a),r=[];")"!=wt(t.a)&&(ne(t,"Missing function argument list."),r.push(re(t)),","==wt(t.a));)Mt(t.a);ne(t,"Unclosed function argument list."),ie(t),e=new yt(e,r)}else e=new te(+Mt(t.a))}"["==wt(t.a)&&(e=new Tt(e,r=new qt(le(t))))}if(e){if(!Bt(wt(t.a)))return e;r=e}else e=ce(t,"/"),r=new Ft,n.push(e)}for(;Bt(wt(t.a));)e=Mt(t.a),ne(t,"Missing next location step."),e=ce(t,e),n.push(e);return new kt(r,n)}function ce(t,e){var r,n,s,i;if("/"!=e&&"//"!=e)throw Error('Step op should be "/" or "//"');if("."==wt(t.a))return n=new Gt(Qt,new Lt("node")),Mt(t.a),n;if(".."==wt(t.a))return n=new Gt($t,new Lt("node")),Mt(t.a),n;if("@"==wt(t.a))i=Wt,Mt(t.a),ne(t,"Missing attribute name");else if("::"==wt(t.a,1)){if(!/(?![0-9])[\w]/.test(wt(t.a).charAt(0)))throw Error("Bad token: "+Mt(t.a));if(r=Mt(t.a),!(i=Xt[r]||null))throw Error("No axis with name: "+r);Mt(t.a),ne(t,"Missing node name")}else i=Kt;if(r=wt(t.a),!/(?![0-9])[\w\*]/.test(r.charAt(0)))throw Error("Bad token: "+Mt(t.a));if("("==wt(t.a,1)){if(!Ct(r))throw Error("Invalid node type: "+r);if(!Ct(r=Mt(t.a)))throw Error("Invalid type name: "+r);se(t,"("),ne(t,"Bad nodetype");var o=null;'"'!=(s=wt(t.a).charAt(0))&&"'"!=s||(o=oe(t)),ne(t,"Bad nodetype"),ie(t),r=new Lt(r,o)}else if(-1==(s=(r=Mt(t.a)).indexOf(":")))r=new Dt(r);else{var a;if("*"==(o=r.substring(0,s)))a="*";else if(!(a=t.b(o)))throw Error("Namespace prefix not declared: "+o);r=new Dt(r=r.substr(s+1),a)}return s=new qt(le(t),i.a),n||new Gt(i,r,s,"//"==e)}function le(t){for(var e=[];"["==wt(t.a);){Mt(t.a),ne(t,"Missing predicate expression.");var r=re(t);e.push(r),ne(t,"Unclosed predicate expression."),se(t,"]")}return e}function he(t){if("-"==wt(t.a))return Mt(t.a),new Zt(he(t));var e=ae(t);if("|"!=wt(t.a))t=e;else{for(e=[e];"|"==Mt(t.a);)ne(t,"Missing next union location path."),e.push(ae(t));t.a.a--,t=new Ut(e)}return t}function ue(t){switch(t.nodeType){case 1:return function(t,e){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}(pe,t);case 9:return ue(t.documentElement);case 11:case 10:case 6:case 12:return de;default:return t.parentNode?ue(t.parentNode):de}}function de(){return null}function pe(t,e){if(t.prefix==e)return t.namespaceURI||"http://www.w3.org/1999/xhtml";var r=t.getAttributeNode("xmlns:"+e);return r&&r.specified?r.value||null:t.parentNode&&9!=t.parentNode.nodeType?pe(t.parentNode,e):null}function me(t,e){if(!t.length)throw Error("Empty XPath expression.");var r=function(t){t=t.match(vt);for(var e=0;e<t.length;e++)St.test(t[e])&&t.splice(e,1);return new Ot(t)}(t);if(xt(r))throw Error("Invalid XPath expression.");e?"function"==function(t){var e=typeof t;if("object"==e){if(!t)return"null";if(t instanceof Array)return"array";if(t instanceof Object)return e;var r=Object.prototype.toString.call(t);if("[object Window]"==r)return"object";if("[object Array]"==r||"number"==typeof t.length&&void 0!==t.splice&&void 0!==t.propertyIsEnumerable&&!t.propertyIsEnumerable("splice"))return"array";if("[object Function]"==r||void 0!==t.call&&void 0!==t.propertyIsEnumerable&&!t.propertyIsEnumerable("call"))return"function"}else if("function"==e&&void 0===t.call)return"object";return e}(e)||(e=i(e.lookupNamespaceURI,e)):e=function(){return null};var n=re(new ee(r,e));if(!xt(r))throw Error("Bad token: "+Mt(r));this.evaluate=function(t,e){return new fe(n.a(new bt(t)),e)}}function fe(t,e){if(0==e)if(t instanceof $)e=4;else if("string"==typeof t)e=2;else if("number"==typeof t)e=1;else{if("boolean"!=typeof t)throw Error("Unexpected evaluation result.");e=3}if(2!=e&&1!=e&&3!=e&&!(t instanceof $))throw Error("value could not be converted to the specified type");var r;switch(this.resultType=e,e){case 2:this.stringValue=t instanceof $?rt(t):""+t;break;case 1:this.numberValue=t instanceof $?+rt(t):+t;break;case 3:this.booleanValue=t instanceof $?0<t.l:!!t;break;case 4:case 5:case 6:case 7:var n=nt(t);r=[];for(var s=it(n);s;s=it(n))r.push(s instanceof k?s.a:s);this.snapshotLength=t.l,this.invalidIteratorState=!1;break;case 8:case 9:n=et(t),this.singleNodeValue=n instanceof k?n.a:n;break;default:throw Error("Unknown XPathResult type.")}var i=0;this.iterateNext=function(){if(4!=e&&5!=e)throw Error("iterateNext called with wrong result type");return i>=r.length?null:r[i++]},this.snapshotItem=function(t){if(6!=e&&7!=e)throw Error("snapshotItem called with wrong result type");return t>=r.length||0>t?null:r[t]}}function ge(t){this.lookupNamespaceURI=ue(t)}function Ee(t,r){var n=t||e,s=n.Document&&n.Document.prototype||n.document;s.evaluate&&!r||(n.XPathResult=fe,s.evaluate=function(t,e,r,n){return new me(t,r).evaluate(e,n)},s.createExpression=function(t,e){return new me(t,e)},s.createNSResolver=function(t){return new ge(t)})}o(Zt),Zt.prototype.a=function(t){return-ht(this.c,t)},Zt.prototype.toString=function(){return"Unary Expression: -"+at(this.c)},o(te),te.prototype.a=function(){return this.c},te.prototype.toString=function(){return"Number: "+this.c},fe.ANY_TYPE=0,fe.NUMBER_TYPE=1,fe.STRING_TYPE=2,fe.BOOLEAN_TYPE=3,fe.UNORDERED_NODE_ITERATOR_TYPE=4,fe.ORDERED_NODE_ITERATOR_TYPE=5,fe.UNORDERED_NODE_SNAPSHOT_TYPE=6,fe.ORDERED_NODE_SNAPSHOT_TYPE=7,fe.ANY_UNORDERED_NODE_TYPE=8,fe.FIRST_ORDERED_NODE_TYPE=9;var Ne,be=["wgxpath","install"],Te=e;be[0]in Te||!Te.execScript||Te.execScript("var "+be[0]);for(;be.length&&(Ne=be.shift());)be.length||void 0===Ee?Te=Te[Ne]?Te[Ne]:Te[Ne]={}:Te[Ne]=Ee;t.exports.install=Ee,t.exports.XPathResultType={ANY_TYPE:0,NUMBER_TYPE:1,STRING_TYPE:2,BOOLEAN_TYPE:3,UNORDERED_NODE_ITERATOR_TYPE:4,ORDERED_NODE_ITERATOR_TYPE:5,UNORDERED_NODE_SNAPSHOT_TYPE:6,ORDERED_NODE_SNAPSHOT_TYPE:7,ANY_UNORDERED_NODE_TYPE:8,FIRST_ORDERED_NODE_TYPE:9}}).call(r.g)},165:(t,e)=>{"use strict";function r(t,e){return void 0===e&&(e=Object),e&&"function"==typeof e.getOwnPropertyDescriptors&&(t=e.create(null,e.getOwnPropertyDescriptors(t))),e&&"function"==typeof e.freeze?e.freeze(t):t}function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var s=r({allowfullscreen:!0,async:!0,autofocus:!0,autoplay:!0,checked:!0,controls:!0,default:!0,defer:!0,disabled:!0,formnovalidate:!0,hidden:!0,ismap:!0,itemscope:!0,loop:!0,multiple:!0,muted:!0,nomodule:!0,novalidate:!0,open:!0,playsinline:!0,readonly:!0,required:!0,reversed:!0,selected:!0});var i=r({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});var o=r({script:!1,style:!1,textarea:!0,title:!0});function a(t){return t===c.HTML}var c=r({HTML:"text/html",XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),l=Object.keys(c).map((function(t){return c[t]}));var h=r({HTML:"http://www.w3.org/1999/xhtml",SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});e.assign=function(t,e){if(null===t||"object"!=typeof t)throw new TypeError("target is not an object");for(var r in e)n(e,r)&&(t[r]=e[r]);return t},e.find=function(t,e,r){if(void 0===r&&(r=Array.prototype),t&&"function"==typeof r.find)return r.find.call(t,e);for(var s=0;s<t.length;s++)if(n(t,s)){var i=t[s];if(e.call(void 0,i,s,t))return i}},e.freeze=r,e.HTML_BOOLEAN_ATTRIBUTES=s,e.HTML_RAW_TEXT_ELEMENTS=o,e.HTML_VOID_ELEMENTS=i,e.hasDefaultHTMLNamespace=function(t){return a(t)||t===c.XML_XHTML_APPLICATION},e.hasOwn=n,e.isHTMLBooleanAttribute=function(t){return n(s,t.toLowerCase())},e.isHTMLRawTextElement=function(t){var e=t.toLowerCase();return n(o,e)&&!o[e]},e.isHTMLEscapableRawTextElement=function(t){var e=t.toLowerCase();return n(o,e)&&o[e]},e.isHTMLMimeType=a,e.isHTMLVoidElement=function(t){return n(i,t.toLowerCase())},e.isValidMimeType=function(t){return l.indexOf(t)>-1},e.MIME_TYPE=c,e.NAMESPACE=h},342:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{f:()=>SystemExternal});var _variables_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(550),_lib_external_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(128);const windowSupported=!("undefined"==typeof window),documentSupported=windowSupported&&!(void 0===window.document),webworker=!("undefined"==typeof DedicatedWorkerGlobalScope),nodeRequire=()=>{try{return eval("require")}catch(t){return t=>null}},SystemExternal={extRequire:t=>"undefined"!=typeof process&&"undefined"!=typeof require?nodeRequire()(t):null,windowSupported,documentSupported,webworker,xmldom:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Gb,document:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Al,fs:documentSupported||webworker?null:nodeRequire()("fs"),url:_variables_js__WEBPACK_IMPORTED_MODULE_0__.u.url,jsonPath:function(){if(documentSupported||webworker)return _variables_js__WEBPACK_IMPORTED_MODULE_0__.u.url;if(process.env.SRE_JSON_PATH||global.SRE_JSON_PATH)return process.env.SRE_JSON_PATH||global.SRE_JSON_PATH;try{return nodeRequire().resolve("speech-rule-engine").replace(/sre\.js$/,"")+"mathmaps"}catch(t){}try{return nodeRequire().resolve(".").replace(/sre\.js$/,"")+"mathmaps"}catch(t){}return"undefined"!=typeof __dirname?__dirname+(__dirname.match(/lib?$/)?"/mathmaps":"/lib/mathmaps"):process.cwd()+"/lib/mathmaps"}(),xpath:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Oe}},360:(t,e)=>{"use strict";function r(t){try{"function"!=typeof t&&(t=RegExp);var e=new t("\u{1d306}","u").exec("\u{1d306}");return!!e&&2===e[0].length}catch(t){}return!1}var n=r();function s(t){if("["!==t.source[0])throw new Error(t+" can not be used with chars");return t.source.slice(1,t.source.lastIndexOf("]"))}function i(t,e){if("["!==t.source[0])throw new Error("/"+t.source+"/ can not be used with chars_without");if(!e||"string"!=typeof e)throw new Error(JSON.stringify(e)+" is not a valid search");if(-1===t.source.indexOf(e))throw new Error('"'+e+'" is not is /'+t.source+"/");if("-"===e&&1!==t.source.indexOf(e))throw new Error('"'+e+'" is not at the first postion of /'+t.source+"/");return new RegExp(t.source.replace(e,""),n?"u":"")}function o(t){var e=this;return new RegExp(Array.prototype.slice.call(arguments).map((function(t){var r="string"==typeof t;if(r&&void 0===e&&"|"===t)throw new Error("use regg instead of reg to wrap expressions with `|`!");return r?t:t.source})).join(""),n?"mu":"m")}function a(t){if(0===arguments.length)throw new Error("no parameters provided");return o.apply(a,["(?:"].concat(Array.prototype.slice.call(arguments),[")"]))}var c=/[-\x09\x0A\x0D\x20-\x2C\x2E-\uD7FF\uE000-\uFFFD]/;n&&(c=o("[",s(c),"\\u{10000}-\\u{10FFFF}","]"));var l=/[\x20\x09\x0D\x0A]/,h=s(l),u=o(l,"+"),d=o(l,"*"),p=/[:_a-zA-Z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/;n&&(p=o("[",s(p),"\\u{10000}-\\u{10FFFF}","]"));var m=o("[",s(p),s(/[-.0-9\xB7]/),s(/[\u0300-\u036F\u203F-\u2040]/),"]"),f=o(p,m,"*"),g=o(m,"+"),E=o("&",f,";"),N=a(/&#[0-9]+;|&#x[0-9a-fA-F]+;/),b=a(E,"|",N),T=o("%",f,";"),y=a(o('"',a(/[^%&"]/,"|",T,"|",b),"*",'"'),"|",o("'",a(/[^%&']/,"|",T,"|",b),"*","'")),I=a('"',a(/[^<&"]/,"|",b),"*",'"',"|","'",a(/[^<&']/,"|",b),"*","'"),A=o(i(p,":"),i(m,":"),"*"),R=o(A,a(":",A),"?"),L=o("^",R,"$"),C=o("(",R,")"),O=a(/"[^"]*"|'[^']*'/),v=o(/^<\?/,"(",f,")",a(u,"(",c,"*?)"),"?",/\?>/),S=/[\x20\x0D\x0Aa-zA-Z0-9-'()+,./:=?;!*#@$_%]/,w=a('"',S,'*"',"|","'",i(S,"'"),"*'"),M="\x3c!--",x=o(M,a(i(c,"-"),"|",o("-",i(c,"-"))),"*","--\x3e"),P="#PCDATA",D=a(o(/\(/,d,P,a(d,/\|/,d,R),"*",d,/\)\*/),"|",o(/\(/,d,P,d,/\)/)),k=a("EMPTY","|","ANY","|",D,"|",o(/\([^>]+\)/,/[?*+]?/)),_=o("<!ELEMENT",u,a(R,"|",T),u,a(k,"|",T),d,">"),F=o("NOTATION",u,/\(/,d,f,a(d,/\|/,d,f),"*",d,/\)/),B=o(/\(/,d,g,a(d,/\|/,d,g),"*",d,/\)/),U=a(F,"|",B),q=a(/CDATA|ID|IDREF|IDREFS|ENTITY|ENTITIES|NMTOKEN|NMTOKENS/,"|",U),H=a(/#REQUIRED|#IMPLIED/,"|",a(a("#FIXED",u),"?",I)),G=o("<!ATTLIST",u,f,a(u,f,u,q,u,H),"*",d,">"),j="about:legacy-compat",X=a('"'+j+'"',"|","'"+j+"'"),V="SYSTEM",W="PUBLIC",K=a(a(V,u,O),"|",a(W,u,w,u,O)),z=o("^",a(a(V,u,"(?<SystemLiteralOnly>",O,")"),"|",a(W,u,"(?<PubidLiteral>",w,")",u,"(?<SystemLiteral>",O,")"))),Y=a(u,"NDATA",u,f),$="<!ENTITY",J=o($,u,f,u,a(y,"|",a(K,Y,"?")),d,">"),Q=a(y,"|",K),Z=a(J,"|",o($,u,"%",u,f,u,Q,d,">")),tt=o(W,u,w),et=o("<!NOTATION",u,f,u,a(K,"|",tt),d,">"),rt=o(d,"=",d),nt=/1[.]\d+/,st=o(u,"version",rt,a("'",nt,"'","|",'"',nt,'"')),it=/[A-Za-z][-A-Za-z0-9._]*/,ot=o(/^<\?xml/,st,a(u,"encoding",rt,a('"',it,'"',"|","'",it,"'")),"?",a(u,"standalone",rt,a("'",a("yes","|","no"),"'","|",'"',a("yes","|","no"),'"')),"?",d,/\?>/),at=o(c,"*?",/\]\]>/),ct=o(/<!\[CDATA\[/,at);e.chars=s,e.chars_without=i,e.detectUnicodeSupport=r,e.reg=o,e.regg=a,e.ABOUT_LEGACY_COMPAT=j,e.ABOUT_LEGACY_COMPAT_SystemLiteral=X,e.AttlistDecl=G,e.CDATA_START="<![CDATA[",e.CDATA_END="]]>",e.CDSect=ct,e.Char=c,e.Comment=x,e.COMMENT_START=M,e.COMMENT_END="--\x3e",e.DOCTYPE_DECL_START="<!DOCTYPE",e.elementdecl=_,e.EntityDecl=Z,e.EntityValue=y,e.ExternalID=K,e.ExternalID_match=z,e.Name=f,e.NotationDecl=et,e.Reference=b,e.PEReference=T,e.PI=v,e.PUBLIC=W,e.PubidLiteral=w,e.QName=R,e.QName_exact=L,e.QName_group=C,e.S=u,e.SChar_s=h,e.S_OPT=d,e.SYSTEM=V,e.SystemLiteral=O,e.UNICODE_REPLACEMENT_CHARACTER="\ufffd",e.UNICODE_SUPPORT=n,e.XMLDecl=ot},550:(t,e,r)=>{"use strict";r.d(e,{u:()=>n});class n{static ensureLocale(t,e){return n.LOCALES.get(t)?t:(console.error(`Locale ${t} does not exist! Using ${n.LOCALES.get(e)} instead.`),e)}}n.VERSION="5.0.0-alpha.8",n.LOCALES=new Map([["af","Africaans"],["ca","Catalan"],["da","Danish"],["de","German"],["en","English"],["es","Spanish"],["euro","Euro"],["fr","French"],["hi","Hindi"],["it","Italian"],["ko","Korean"],["nb","Bokm\xe5l"],["nn","Nynorsk"],["sv","Swedish"],["nemeth","Nemeth"]]),n.mathjaxVersion="4.0.0-beta.8",n.url="https://cdn.jsdelivr.net/npm/speech-rule-engine@"+n.VERSION+"/lib/mathmaps"},661:(t,e,r)=>{"use strict";var n=r(165),s=r(360),i=r(857),o=n.isHTMLEscapableRawTextElement,a=n.isHTMLMimeType,c=n.isHTMLRawTextElement,l=n.hasOwn,h=n.NAMESPACE,u=i.ParseError,d=i.DOMException;function p(){}p.prototype={parse:function(t,e,r){var i=this.domBuilder;i.startDocument(),b(e,e=Object.create(null)),function(t,e,r,i,o){var c=a(i.mimeType);t.indexOf(s.UNICODE_REPLACEMENT_CHARACTER)>=0&&o.warning("Unicode replacement character detected, source encoding issues?");function h(t){if(t>65535){var e=55296+((t-=65536)>>10),r=56320+(1023&t);return String.fromCharCode(e,r)}return String.fromCharCode(t)}function p(t){var e=";"===t[t.length-1]?t:t+";";if(!c&&e!==t)return o.error("EntityRef: expecting ;"),t;var n=s.Reference.exec(e);if(!n||n[0].length!==e.length)return o.error("entity not matching Reference production: "+t),t;var i=e.slice(1,-1);return l(r,i)?r[i]:"#"===i.charAt(0)?h(parseInt(i.substring(1).replace("x","0x"))):(o.error("entity not found:"+t),t)}function b(e){if(e>w){var r=t.substring(w,e).replace(m,p);C&&O(w),i.characters(r,0,e-w),w=e}}var T=0,R=0,L=/\r\n?|\n|$/g,C=i.locator;function O(e,r){for(;e>=R&&(r=L.exec(t));)T=R,R=r.index+r[0].length,C.lineNumber++;C.columnNumber=e-T+1}var v=[{currentNSMap:e}],S=[],w=0;for(;;){try{var M=t.indexOf("<",w);if(M<0){if(!c&&S.length>0)return o.fatalError("unclosed xml tag(s): "+S.join(", "));if(!t.substring(w).match(/^\s*$/)){var x=i.doc,P=x.createTextNode(t.substring(w));if(x.documentElement)return o.error("Extra content at the end of the document");x.appendChild(P),i.currentElement=P}return}if(M>w){var D=t.substring(w,M);c||0!==S.length||(D=D.replace(new RegExp(s.S_OPT.source,"g"),""))&&o.error("Unexpected content outside root element: '"+D+"'"),b(M)}switch(t.charAt(M+1)){case"/":var k=t.indexOf(">",M+2),_=t.substring(M+2,k>0?k:void 0);if(!_)return o.fatalError("end tag name missing");var F=k>0&&s.reg("^",s.QName_group,s.S_OPT,"$").exec(_);if(!F)return o.fatalError('end tag name contains invalid characters: "'+_+'"');if(!i.currentElement&&!i.doc.documentElement)return;var B=S[S.length-1]||i.currentElement.tagName||i.doc.documentElement.tagName||"";if(B!==F[1]){var U=F[1].toLowerCase();if(!c||B.toLowerCase()!==U)return o.fatalError('Opening and ending tag mismatch: "'+B+'" != "'+_+'"')}var q=v.pop();S.pop();var H=q.localNSMap;if(i.endElement(q.uri,q.localName,B),H)for(var G in H)l(H,G)&&i.endPrefixMapping(G);k++;break;case"?":C&&O(M),k=I(t,M,i,o);break;case"!":C&&O(M),k=y(t,M,i,o,c);break;default:C&&O(M);var j=new A,X=v[v.length-1].currentNSMap,V=(k=g(t,M,j,X,p,o,c),j.length);if(j.closed||(c&&n.isHTMLVoidElement(j.tagName)?j.closed=!0:S.push(j.tagName)),C&&V){for(var W=f(C,{}),K=0;K<V;K++){var z=j[K];O(z.offset),z.locator=f(C,{})}i.locator=W,E(j,i,X)&&v.push(j),i.locator=C}else E(j,i,X)&&v.push(j);c&&!j.closed?k=N(t,k,j.tagName,p,i):k++}}catch(t){if(t instanceof u)throw t;if(t instanceof d)throw new u(t.name+": "+t.message,i.locator,t);o.error("element parse error: "+t),k=-1}k>w?w=k:b(Math.max(M,w)+1)}}(t,e,r,i,this.errorHandler),i.endDocument()}};var m=/&#?\w+;?/g;function f(t,e){return e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber,e}function g(t,e,r,n,s,i,o){function a(t,e,n){return l(r.attributeNames,t)?i.fatalError("Attribute "+t+" redefined"):!o&&e.indexOf("<")>=0?i.fatalError("Unescaped '<' not allowed in attributes values"):void r.addValue(t,e.replace(/[\t\n\r]/g," ").replace(m,s),n)}for(var c,h=++e,u=0;;){var d=t.charAt(h);switch(d){case"=":if(1===u)c=t.slice(e,h),u=3;else{if(2!==u)throw new Error("attribute equal must after attrName");u=3}break;case"'":case'"':if(3===u||1===u){if(1===u&&(i.warning('attribute value must after "="'),c=t.slice(e,h)),e=h+1,!((h=t.indexOf(d,e))>0))throw new Error("attribute value no end '"+d+"' match");a(c,p=t.slice(e,h),e-1),u=5}else{if(4!=u)throw new Error('attribute value must after "="');a(c,p=t.slice(e,h),e),i.warning('attribute "'+c+'" missed start quot('+d+")!!"),e=h+1,u=5}break;case"/":switch(u){case 0:r.setTagName(t.slice(e,h));case 5:case 6:case 7:u=7,r.closed=!0;case 4:case 1:break;case 2:r.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return i.error("unexpected end of input"),0==u&&r.setTagName(t.slice(e,h)),h;case">":switch(u){case 0:r.setTagName(t.slice(e,h));case 5:case 6:case 7:break;case 4:case 1:"/"===(p=t.slice(e,h)).slice(-1)&&(r.closed=!0,p=p.slice(0,-1));case 2:2===u&&(p=c),4==u?(i.warning('attribute "'+p+'" missed quot(")!'),a(c,p,e)):(o||i.warning('attribute "'+p+'" missed value!! "'+p+'" instead!!'),a(p,p,e));break;case 3:if(!o)return i.fatalError("AttValue: ' or \" expected")}return h;case"\x80":d=" ";default:if(d<=" ")switch(u){case 0:r.setTagName(t.slice(e,h)),u=6;break;case 1:c=t.slice(e,h),u=2;break;case 4:var p=t.slice(e,h);i.warning('attribute "'+p+'" missed quot(")!!'),a(c,p,e);case 5:u=6}else switch(u){case 2:o||i.warning('attribute "'+c+'" missed value!! "'+c+'" instead2!!'),a(c,c,e),e=h,u=1;break;case 5:i.warning('attribute space is required"'+c+'"!!');case 6:u=1,e=h;break;case 3:u=4,e=h;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}h++}}function E(t,e,r){for(var n=t.tagName,s=null,i=t.length;i--;){var o=t[i],a=o.qName,c=o.value;if((m=a.indexOf(":"))>0)var u=o.prefix=a.slice(0,m),d=a.slice(m+1),p="xmlns"===u&&d;else d=a,u=null,p="xmlns"===a&&"";o.localName=d,!1!==p&&(null==s&&(s=Object.create(null),b(r,r=Object.create(null))),r[p]=s[p]=c,o.uri=h.XMLNS,e.startPrefixMapping(p,c))}for(i=t.length;i--;)(o=t[i]).prefix&&("xml"===o.prefix&&(o.uri=h.XML),"xmlns"!==o.prefix&&(o.uri=r[o.prefix]));var m;(m=n.indexOf(":"))>0?(u=t.prefix=n.slice(0,m),d=t.localName=n.slice(m+1)):(u=null,d=t.localName=n);var f=t.uri=r[u||""];if(e.startElement(f,d,n,t),!t.closed)return t.currentNSMap=r,t.localNSMap=s,!0;if(e.endElement(f,d,n),s)for(u in s)l(s,u)&&e.endPrefixMapping(u)}function N(t,e,r,n,s){var i=o(r);if(i||c(r)){var a=t.indexOf("</"+r+">",e),l=t.substring(e+1,a);return i&&(l=l.replace(m,n)),s.characters(l,0,l.length),a}return e+1}function b(t,e){for(var r in t)l(t,r)&&(e[r]=t[r])}function T(t,e){var r=e;function n(e){return e=e||0,t.charAt(r+e)}function i(t){r+=t=t||1}function o(){return t.substring(r)}return{char:n,getIndex:function(){return r},getMatch:function(t){var e=s.reg("^",t).exec(o());return e?(i(e[0].length),e[0]):null},getSource:function(){return t},skip:i,skipBlanks:function(){for(var e=0;r<t.length;){var s=n();if(" "!==s&&"\n"!==s&&"\t"!==s&&"\r"!==s)return e;e++,i()}return-1},substringFromIndex:o,substringStartsWith:function(e){return t.substring(r,r+e.length)===e},substringStartsWithCaseInsensitive:function(e){return t.substring(r,r+e.length).toUpperCase()===e.toUpperCase()}}}function y(t,e,r,n,i){var o=T(t,e);switch(i?o.char(2).toUpperCase():o.char(2)){case"-":var a=o.getMatch(s.Comment);return a?(r.comment(a,s.COMMENT_START.length,a.length-s.COMMENT_START.length-s.COMMENT_END.length),o.getIndex()):n.fatalError("comment is not well-formed at position "+o.getIndex());case"[":var c=o.getMatch(s.CDSect);return c?i||r.currentElement?(r.startCDATA(),r.characters(c,s.CDATA_START.length,c.length-s.CDATA_START.length-s.CDATA_END.length),r.endCDATA(),o.getIndex()):n.fatalError("CDATA outside of element"):n.fatalError("Invalid CDATA starting at position "+e);case"D":if(r.doc&&r.doc.documentElement)return n.fatalError("Doctype not allowed inside or after documentElement at position "+o.getIndex());if(i?!o.substringStartsWithCaseInsensitive(s.DOCTYPE_DECL_START):!o.substringStartsWith(s.DOCTYPE_DECL_START))return n.fatalError("Expected "+s.DOCTYPE_DECL_START+" at position "+o.getIndex());if(o.skip(s.DOCTYPE_DECL_START.length),o.skipBlanks()<1)return n.fatalError("Expected whitespace after "+s.DOCTYPE_DECL_START+" at position "+o.getIndex());var l={name:void 0,publicId:void 0,systemId:void 0,internalSubset:void 0};if(l.name=o.getMatch(s.Name),!l.name)return n.fatalError("doctype name missing or contains unexpected characters at position "+o.getIndex());if(i&&"html"!==l.name.toLowerCase()&&n.warning("Unexpected DOCTYPE in HTML document at position "+o.getIndex()),o.skipBlanks(),o.substringStartsWith(s.PUBLIC)||o.substringStartsWith(s.SYSTEM)){var h=s.ExternalID_match.exec(o.substringFromIndex());if(!h)return n.fatalError("doctype external id is not well-formed at position "+o.getIndex());void 0!==h.groups.SystemLiteralOnly?l.systemId=h.groups.SystemLiteralOnly:(l.systemId=h.groups.SystemLiteral,l.publicId=h.groups.PubidLiteral),o.skip(h[0].length)}else if(i&&o.substringStartsWithCaseInsensitive(s.SYSTEM)){if(o.skip(s.SYSTEM.length),o.skipBlanks()<1)return n.fatalError("Expected whitespace after "+s.SYSTEM+" at position "+o.getIndex());if(l.systemId=o.getMatch(s.ABOUT_LEGACY_COMPAT_SystemLiteral),!l.systemId)return n.fatalError("Expected "+s.ABOUT_LEGACY_COMPAT+" in single or double quotes after "+s.SYSTEM+" at position "+o.getIndex())}return i&&l.systemId&&!s.ABOUT_LEGACY_COMPAT_SystemLiteral.test(l.systemId)&&n.warning("Unexpected doctype.systemId in HTML document at position "+o.getIndex()),i||(o.skipBlanks(),l.internalSubset=function(t,e){function r(t,e){var r=s.PI.exec(t.substringFromIndex());return r?"xml"===r[1].toLowerCase()?e.fatalError("xml declaration is only allowed at the start of the document, but found at position "+t.getIndex()):(t.skip(r[0].length),r[0]):e.fatalError("processing instruction is not well-formed at position "+t.getIndex())}var n=t.getSource();if("["===t.char()){t.skip(1);for(var i=t.getIndex();t.getIndex()<n.length;){if(t.skipBlanks(),"]"===t.char()){var o=n.substring(i,t.getIndex());return t.skip(1),o}var a=null;if("<"===t.char()&&"!"===t.char(1))switch(t.char(2)){case"E":"L"===t.char(3)?a=t.getMatch(s.elementdecl):"N"===t.char(3)&&(a=t.getMatch(s.EntityDecl));break;case"A":a=t.getMatch(s.AttlistDecl);break;case"N":a=t.getMatch(s.NotationDecl);break;case"-":a=t.getMatch(s.Comment)}else if("<"===t.char()&&"?"===t.char(1))a=r(t,e);else{if("%"!==t.char())return e.fatalError("Error detected in Markup declaration");a=t.getMatch(s.PEReference)}if(!a)return e.fatalError("Error in internal subset at position "+t.getIndex())}return e.fatalError("doctype internal subset is not well-formed, missing ]")}}(o,n)),o.skipBlanks(),">"!==o.char()?n.fatalError("doctype not terminated with > at position "+o.getIndex()):(o.skip(1),r.startDTD(l.name,l.publicId,l.systemId,l.internalSubset),r.endDTD(),o.getIndex());default:return n.fatalError('Not well-formed XML starting with "<!" at position '+e)}}function I(t,e,r,n){var i=t.substring(e).match(s.PI);if(!i)return n.fatalError("Invalid processing instruction starting at position "+e);if("xml"===i[1].toLowerCase()){if(e>0)return n.fatalError("processing instruction at position "+e+" is an xml declaration which is only at the start of the document");if(!s.XMLDecl.test(t.substring(e)))return n.fatalError("xml declaration is not well-formed")}return r.processingInstruction(i[1],i[2]),e+i[0].length}function A(){this.attributeNames=Object.create(null)}A.prototype={setTagName:function(t){if(!s.QName_exact.test(t))throw new Error("invalid tagName:"+t);this.tagName=t},addValue:function(t,e,r){if(!s.QName_exact.test(t))throw new Error("invalid attribute:"+t);this.attributeNames[t]=this.length,this[this.length++]={qName:t,value:e,offset:r}},length:0,getLocalName:function(t){return this[t].localName},getLocator:function(t){return this[t].locator},getQName:function(t){return this[t].qName},getURI:function(t){return this[t].uri},getValue:function(t){return this[t].value}},e.XMLReader=p,e.parseUtils=T,e.parseDoctypeCommentOrCData=y},665:(t,e,r)=>{"use strict";var n=r(165);e.assign=n.assign,e.hasDefaultHTMLNamespace=n.hasDefaultHTMLNamespace,e.isHTMLMimeType=n.isHTMLMimeType,e.isValidMimeType=n.isValidMimeType,e.MIME_TYPE=n.MIME_TYPE,e.NAMESPACE=n.NAMESPACE;var s=r(857);e.DOMException=s.DOMException,e.DOMExceptionName=s.DOMExceptionName,e.ExceptionCode=s.ExceptionCode,e.ParseError=s.ParseError;var i=r(905);e.Attr=i.Attr,e.CDATASection=i.CDATASection,e.CharacterData=i.CharacterData,e.Comment=i.Comment,e.Document=i.Document,e.DocumentFragment=i.DocumentFragment,e.DocumentType=i.DocumentType,e.DOMImplementation=i.DOMImplementation,e.Element=i.Element,e.Entity=i.Entity,e.EntityReference=i.EntityReference,e.LiveNodeList=i.LiveNodeList,e.NamedNodeMap=i.NamedNodeMap,e.Node=i.Node,e.NodeList=i.NodeList,e.Notation=i.Notation,e.ProcessingInstruction=i.ProcessingInstruction,e.Text=i.Text,e.XMLSerializer=i.XMLSerializer;var o=r(897);e.DOMParser=o.DOMParser,e.normalizeLineEndings=o.normalizeLineEndings,e.onErrorStopParsing=o.onErrorStopParsing,e.onWarningStopParsing=o.onWarningStopParsing},857:(t,e,r)=>{"use strict";function n(t,e){t.prototype=Object.create(Error.prototype,{constructor:{value:t},name:{value:t.name,enumerable:!0,writable:e}})}var s=r(165).freeze({Error:"Error",IndexSizeError:"IndexSizeError",DomstringSizeError:"DomstringSizeError",HierarchyRequestError:"HierarchyRequestError",WrongDocumentError:"WrongDocumentError",InvalidCharacterError:"InvalidCharacterError",NoDataAllowedError:"NoDataAllowedError",NoModificationAllowedError:"NoModificationAllowedError",NotFoundError:"NotFoundError",NotSupportedError:"NotSupportedError",InUseAttributeError:"InUseAttributeError",InvalidStateError:"InvalidStateError",SyntaxError:"SyntaxError",InvalidModificationError:"InvalidModificationError",NamespaceError:"NamespaceError",InvalidAccessError:"InvalidAccessError",ValidationError:"ValidationError",TypeMismatchError:"TypeMismatchError",SecurityError:"SecurityError",NetworkError:"NetworkError",AbortError:"AbortError",URLMismatchError:"URLMismatchError",QuotaExceededError:"QuotaExceededError",TimeoutError:"TimeoutError",InvalidNodeTypeError:"InvalidNodeTypeError",DataCloneError:"DataCloneError",EncodingError:"EncodingError",NotReadableError:"NotReadableError",UnknownError:"UnknownError",ConstraintError:"ConstraintError",DataError:"DataError",TransactionInactiveError:"TransactionInactiveError",ReadOnlyError:"ReadOnlyError",VersionError:"VersionError",OperationError:"OperationError",NotAllowedError:"NotAllowedError",OptOutError:"OptOutError"}),i=Object.keys(s);function o(t){return"number"==typeof t&&t>=1&&t<=25}function a(t,e){var r;o(t)?(this.name=i[t],this.message=e||""):(this.message=t,this.name="string"==typeof(r=e)&&r.substring(r.length-s.Error.length)===s.Error?e:s.Error),Error.captureStackTrace&&Error.captureStackTrace(this,a)}n(a,!0),Object.defineProperties(a.prototype,{code:{enumerable:!0,get:function(){var t=i.indexOf(this.name);return o(t)?t:0}}});for(var c={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25},l=Object.entries(c),h=0;h<l.length;h++){a[l[h][0]]=l[h][1]}function u(t,e){this.message=t,this.locator=e,Error.captureStackTrace&&Error.captureStackTrace(this,u)}n(u),e.DOMException=a,e.DOMExceptionName=s,e.ExceptionCode=c,e.ParseError=u},897:(t,e,r)=>{"use strict";var n=r(165),s=r(905),i=r(857),o=r(78),a=r(661),c=s.DOMImplementation,l=n.hasDefaultHTMLNamespace,h=n.isHTMLMimeType,u=n.isValidMimeType,d=n.MIME_TYPE,p=n.NAMESPACE,m=i.ParseError,f=a.XMLReader;function g(t){return t.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028\u2029]/g,"\n")}function E(t){if(void 0===(t=t||{}).locator&&(t.locator=!0),this.assign=t.assign||n.assign,this.domHandler=t.domHandler||N,this.onError=t.onError||t.errorHandler,t.errorHandler&&"function"!=typeof t.errorHandler)throw new TypeError("errorHandler object is no longer supported, switch to onError!");t.errorHandler&&t.errorHandler("warning","The `errorHandler` option has been deprecated, use `onError` instead!",this),this.normalizeLineEndings=t.normalizeLineEndings||g,this.locator=!!t.locator,this.xmlns=this.assign(Object.create(null),t.xmlns)}function N(t){var e=t||{};this.mimeType=e.mimeType||d.XML_APPLICATION,this.defaultNamespace=e.defaultNamespace||null,this.cdata=!1,this.currentElement=void 0,this.doc=void 0,this.locator=void 0,this.onError=e.onError}function b(t,e){e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber}function T(t,e,r){return"string"==typeof t?t.substr(e,r):t.length>=e+r||e?new java.lang.String(t,e,r)+"":t}function y(t,e){t.currentElement?t.currentElement.appendChild(e):t.doc.appendChild(e)}E.prototype.parseFromString=function(t,e){if(!u(e))throw new TypeError('DOMParser.parseFromString: the provided mimeType "'+e+'" is not valid.');var r=this.assign(Object.create(null),this.xmlns),s=o.XML_ENTITIES,i=r[""]||null;l(e)?(s=o.HTML_ENTITIES,i=p.HTML):e===d.XML_SVG_IMAGE&&(i=p.SVG),r[""]=i,r.xml=r.xml||p.XML;var a=new this.domHandler({mimeType:e,defaultNamespace:i,onError:this.onError}),c=this.locator?{}:void 0;this.locator&&a.setDocumentLocator(c);var h=new f;return h.errorHandler=a,h.domBuilder=a,!n.isHTMLMimeType(e)&&"string"!=typeof t&&h.errorHandler.fatalError("source is not a string"),h.parse(this.normalizeLineEndings(String(t)),r,s),a.doc.documentElement||h.errorHandler.fatalError("missing root element"),a.doc},N.prototype={startDocument:function(){var t=new c;this.doc=h(this.mimeType)?t.createHTMLDocument(!1):t.createDocument(this.defaultNamespace,"")},startElement:function(t,e,r,n){var s=this.doc,i=s.createElementNS(t,r||e),o=n.length;y(this,i),this.currentElement=i,this.locator&&b(this.locator,i);for(var a=0;a<o;a++){t=n.getURI(a);var c=n.getValue(a),l=(r=n.getQName(a),s.createAttributeNS(t,r));this.locator&&b(n.getLocator(a),l),l.value=l.nodeValue=c,i.setAttributeNode(l)}},endElement:function(t,e,r){this.currentElement=this.currentElement.parentNode},startPrefixMapping:function(t,e){},endPrefixMapping:function(t){},processingInstruction:function(t,e){var r=this.doc.createProcessingInstruction(t,e);this.locator&&b(this.locator,r),y(this,r)},ignorableWhitespace:function(t,e,r){},characters:function(t,e,r){if(t=T.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(t);else n=this.doc.createTextNode(t);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(t)&&this.doc.appendChild(n),this.locator&&b(this.locator,n)}},skippedEntity:function(t){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(t){t&&(t.lineNumber=0),this.locator=t},comment:function(t,e,r){t=T.apply(this,arguments);var n=this.doc.createComment(t);this.locator&&b(this.locator,n),y(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(t,e,r,n){var s=this.doc.implementation;if(s&&s.createDocumentType){var i=s.createDocumentType(t,e,r,n);this.locator&&b(this.locator,i),y(this,i),this.doc.doctype=i}},reportError:function(t,e){if("function"==typeof this.onError)try{this.onError(t,e,this)}catch(r){throw new m("Reporting "+t+' "'+e+'" caused '+r,this.locator)}else console.error("[xmldom "+t+"]\t"+e,function(t){if(t)return"\n@#[line:"+t.lineNumber+",col:"+t.columnNumber+"]"}(this.locator))},warning:function(t){this.reportError("warning",t)},error:function(t){this.reportError("error",t)},fatalError:function(t){throw this.reportError("fatalError",t),new m(t,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,(function(t){N.prototype[t]=function(){return null}})),e.__DOMHandler=N,e.DOMParser=E,e.normalizeLineEndings=g,e.onErrorStopParsing=function(t){if("error"===t)throw"onErrorStopParsing"},e.onWarningStopParsing=function(){throw"onWarningStopParsing"}},905:(t,e,r)=>{"use strict";var n=r(165),s=n.find,i=n.hasDefaultHTMLNamespace,o=n.hasOwn,a=n.isHTMLMimeType,c=n.isHTMLRawTextElement,l=n.isHTMLVoidElement,h=n.MIME_TYPE,u=n.NAMESPACE,d=Symbol(),p=r(857),m=p.DOMException,f=p.DOMExceptionName,g=r(360);function E(t){if(t!==d)throw new TypeError("Illegal constructor")}function N(t){return""!==t}function b(t,e){return o(t,e)||(t[e]=!0),t}function T(t){if(!t)return[];var e=function(t){return t?t.split(/[\t\n\f\r ]+/).filter(N):[]}(t);return Object.keys(e.reduce(b,{}))}function y(t){if(!g.QName_exact.test(t))throw new m(m.INVALID_CHARACTER_ERR,'invalid character in qualified name "'+t+'"')}function I(t,e){y(e),t=t||null;var r=null,s=e;if(e.indexOf(":")>=0){var i=e.split(":");r=i[0],s=i[1]}if(null!==r&&null===t)throw new m(m.NAMESPACE_ERR,"prefix is non-null and namespace is null");if("xml"===r&&t!==n.NAMESPACE.XML)throw new m(m.NAMESPACE_ERR,'prefix is "xml" and namespace is not the XML namespace');if(("xmlns"===r||"xmlns"===e)&&t!==n.NAMESPACE.XMLNS)throw new m(m.NAMESPACE_ERR,'either qualifiedName or prefix is "xmlns" and namespace is not the XMLNS namespace');if(t===n.NAMESPACE.XMLNS&&"xmlns"!==r&&"xmlns"!==e)throw new m(m.NAMESPACE_ERR,'namespace is the XMLNS namespace and neither qualifiedName nor prefix is "xmlns"');return[t,r,s]}function A(t,e){for(var r in t)o(t,r)&&(e[r]=t[r])}function R(t,e){var r=t.prototype;if(!(r instanceof e)){function n(){}n.prototype=e.prototype,A(r,n=new n),t.prototype=r=n}r.constructor!=t&&("function"!=typeof t&&console.error("unknown Class:"+t),r.constructor=t)}var L={},C=L.ELEMENT_NODE=1,O=L.ATTRIBUTE_NODE=2,v=L.TEXT_NODE=3,S=L.CDATA_SECTION_NODE=4,w=L.ENTITY_REFERENCE_NODE=5,M=L.ENTITY_NODE=6,x=L.PROCESSING_INSTRUCTION_NODE=7,P=L.COMMENT_NODE=8,D=L.DOCUMENT_NODE=9,k=L.DOCUMENT_TYPE_NODE=10,_=L.DOCUMENT_FRAGMENT_NODE=11,F=L.NOTATION_NODE=12,B=n.freeze({DOCUMENT_POSITION_DISCONNECTED:1,DOCUMENT_POSITION_PRECEDING:2,DOCUMENT_POSITION_FOLLOWING:4,DOCUMENT_POSITION_CONTAINS:8,DOCUMENT_POSITION_CONTAINED_BY:16,DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:32});function U(t,e){if(e.length<t.length)return U(e,t);var r=null;for(var n in t){if(t[n]!==e[n])return r;r=t[n]}return r}function q(t){return t.guid||(t.guid=Math.random()),t.guid}function H(){}function G(t,e){this._node=t,this._refresh=e,j(this)}function j(t){var e=t._node._inc||t._node.ownerDocument._inc;if(t._inc!==e){var r=t._refresh(t._node);if(wt(t,"length",r.length),!t.$$length||r.length<t.$$length)for(var n=r.length;n in t;n++)o(t,n)&&delete t[n];A(r,t),t._inc=e}}function X(){}function V(t,e){for(var r=0;r<t.length;){if(t[r]===e)return r;r++}}function W(t,e,r,n){if(n?e[V(e,n)]=r:(e[e.length]=r,e.length++),t){r.ownerElement=t;var s=t.ownerDocument;s&&(n&&Z(s,t,n),function(t,e,r){t&&t._inc++;var n=r.namespaceURI;n===u.XMLNS&&(e._nsMap[r.prefix?r.localName:""]=r.value)}(s,t,r))}}function K(t,e,r){var n=V(e,r);if(n>=0){for(var s=e.length-1;n<=s;)e[n]=e[++n];if(e.length=s,t){var i=t.ownerDocument;i&&Z(i,t,r),r.ownerElement=null}}}function z(){}function Y(t){E(t)}function $(t){return("<"==t?"&lt;":">"==t&&"&gt;")||"&"==t&&"&amp;"||'"'==t&&"&quot;"||"&#"+t.charCodeAt()+";"}function J(t,e){if(e(t))return!0;if(t=t.firstChild)do{if(J(t,e))return!0}while(t=t.nextSibling)}function Q(t,e){E(t);var r=e||{};this.ownerDocument=this,this.contentType=r.contentType||h.XML_APPLICATION,this.type=a(this.contentType)?"html":"xml"}function Z(t,e,r,n){t&&t._inc++,r.namespaceURI===u.XMLNS&&delete e._nsMap[r.prefix?r.localName:""]}function tt(t,e,r){if(t&&t._inc){t._inc++;var n=e.childNodes;if(r&&!r.nextSibling)n[n.length++]=r;else{for(var s=e.firstChild,i=0;s;)n[i++]=s,s=s.nextSibling;n.length=i,delete n[n.length]}}}function et(t,e){if(t!==e.parentNode)throw new m(m.NOT_FOUND_ERR,"child's parent is not parent");var r=e.previousSibling,n=e.nextSibling;return r?r.nextSibling=n:t.firstChild=n,n?n.previousSibling=r:t.lastChild=r,tt(t.ownerDocument,t),e.parentNode=null,e.previousSibling=null,e.nextSibling=null,e}function rt(t){return t&&t.nodeType===Y.DOCUMENT_TYPE_NODE}function nt(t){return t&&t.nodeType===Y.ELEMENT_NODE}function st(t){return t&&t.nodeType===Y.TEXT_NODE}function it(t,e){var r=t.childNodes||[];if(s(r,nt)||rt(e))return!1;var n=s(r,rt);return!(e&&n&&r.indexOf(n)>r.indexOf(e))}function ot(t,e){var r=t.childNodes||[];if(s(r,(function(t){return nt(t)&&t!==e})))return!1;var n=s(r,rt);return!(e&&n&&r.indexOf(n)>r.indexOf(e))}function at(t,e,r){if(!function(t){return t&&(t.nodeType===Y.DOCUMENT_NODE||t.nodeType===Y.DOCUMENT_FRAGMENT_NODE||t.nodeType===Y.ELEMENT_NODE)}(t))throw new m(m.HIERARCHY_REQUEST_ERR,"Unexpected parent node type "+t.nodeType);if(r&&r.parentNode!==t)throw new m(m.NOT_FOUND_ERR,"child not in parent");if(!function(t){return t&&(t.nodeType===Y.CDATA_SECTION_NODE||t.nodeType===Y.COMMENT_NODE||t.nodeType===Y.DOCUMENT_FRAGMENT_NODE||t.nodeType===Y.DOCUMENT_TYPE_NODE||t.nodeType===Y.ELEMENT_NODE||t.nodeType===Y.PROCESSING_INSTRUCTION_NODE||t.nodeType===Y.TEXT_NODE)}(e)||rt(e)&&t.nodeType!==Y.DOCUMENT_NODE)throw new m(m.HIERARCHY_REQUEST_ERR,"Unexpected node type "+e.nodeType+" for parent node type "+t.nodeType)}function ct(t,e,r){var n=t.childNodes||[],i=e.childNodes||[];if(e.nodeType===Y.DOCUMENT_FRAGMENT_NODE){var o=i.filter(nt);if(o.length>1||s(i,st))throw new m(m.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===o.length&&!it(t,r))throw new m(m.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(nt(e)&&!it(t,r))throw new m(m.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(rt(e)){if(s(n,rt))throw new m(m.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");var a=s(n,nt);if(r&&n.indexOf(a)<n.indexOf(r))throw new m(m.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element");if(!r&&a)throw new m(m.HIERARCHY_REQUEST_ERR,"Doctype can not be appended since element is present")}}function lt(t,e,r){var n=t.childNodes||[],i=e.childNodes||[];if(e.nodeType===Y.DOCUMENT_FRAGMENT_NODE){var o=i.filter(nt);if(o.length>1||s(i,st))throw new m(m.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===o.length&&!ot(t,r))throw new m(m.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(nt(e)&&!ot(t,r))throw new m(m.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(rt(e)){function c(t){return rt(t)&&t!==r}if(s(n,c))throw new m(m.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");var a=s(n,nt);if(r&&n.indexOf(a)<n.indexOf(r))throw new m(m.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element")}}function ht(t,e,r,n){at(t,e,r),t.nodeType===Y.DOCUMENT_NODE&&(n||ct)(t,e,r);var s=e.parentNode;if(s&&s.removeChild(e),e.nodeType===_){var i=e.firstChild;if(null==i)return e;var o=e.lastChild}else i=o=e;var a=r?r.previousSibling:t.lastChild;i.previousSibling=a,o.nextSibling=r,a?a.nextSibling=i:t.firstChild=i,null==r?t.lastChild=o:r.previousSibling=o;do{i.parentNode=t}while(i!==o&&(i=i.nextSibling));return tt(t.ownerDocument||t,t,e),e.nodeType==_&&(e.firstChild=e.lastChild=null),e}function ut(t){E(t),this._nsMap=Object.create(null)}function dt(t){E(t),this.namespaceURI=null,this.prefix=null,this.ownerElement=null}function pt(t){E(t)}function mt(t){E(t)}function ft(t){E(t)}function gt(t){E(t)}function Et(t){E(t)}function Nt(t){E(t)}function bt(t){E(t)}function Tt(t){E(t)}function yt(t){E(t)}function It(t){E(t)}function At(){}function Rt(t){var e=[],r=this.nodeType===D&&this.documentElement||this,n=r.prefix,s=r.namespaceURI;if(s&&null==n&&null==(n=r.lookupPrefix(s)))var i=[{namespace:s,prefix:null}];return Ot(this,e,t,i),e.join("")}function Lt(t,e,r){var n=t.prefix||"",s=t.namespaceURI;if(!s)return!1;if("xml"===n&&s===u.XML||s===u.XMLNS)return!1;for(var i=r.length;i--;){var o=r[i];if(o.prefix===n)return o.namespace!==s}return!0}function Ct(t,e,r){t.push(" ",e,'="',r.replace(/[<>&"\t\n\r]/g,$),'"')}function Ot(t,e,r,n){n||(n=[]);var s="html"===(t.nodeType===D?t:t.ownerDocument).type;if(r){if(!(t=r(t)))return;if("string"==typeof t)return void e.push(t)}switch(t.nodeType){case C:var i=t.attributes,o=i.length,a=t.firstChild,h=t.tagName,d=h;if(!s&&!t.prefix&&t.namespaceURI){for(var p,m=0;m<i.length;m++)if("xmlns"===i.item(m).name){p=i.item(m).value;break}if(!p)for(var f=n.length-1;f>=0;f--){if(""===(E=n[f]).prefix&&E.namespace===t.namespaceURI){p=E.namespace;break}}if(p!==t.namespaceURI)for(f=n.length-1;f>=0;f--){var E;if((E=n[f]).namespace===t.namespaceURI){E.prefix&&(d=E.prefix+":"+h);break}}}e.push("<",d);for(var N=0;N<o;N++){"xmlns"==(b=i.item(N)).prefix?n.push({prefix:b.localName,namespace:b.value}):"xmlns"==b.nodeName&&n.push({prefix:"",namespace:b.value})}for(N=0;N<o;N++){var b,T,y;if(Lt(b=i.item(N),0,n))Ct(e,(T=b.prefix||"")?"xmlns:"+T:"xmlns",y=b.namespaceURI),n.push({prefix:T,namespace:y});Ot(b,e,r,n)}if(h===d&&Lt(t,0,n))Ct(e,(T=t.prefix||"")?"xmlns:"+T:"xmlns",y=t.namespaceURI),n.push({prefix:T,namespace:y});var I=!a;if(I&&(s||t.namespaceURI===u.HTML)&&(I=l(h)),I)e.push("/>");else{if(e.push(">"),s&&c(h))for(;a;)a.data?e.push(a.data):Ot(a,e,r,n.slice()),a=a.nextSibling;else for(;a;)Ot(a,e,r,n.slice()),a=a.nextSibling;e.push("</",d,">")}return;case D:case _:for(a=t.firstChild;a;)Ot(a,e,r,n.slice()),a=a.nextSibling;return;case O:return Ct(e,t.name,t.value);case v:return e.push(t.data.replace(/[<&>]/g,$));case S:return e.push(g.CDATA_START,t.data,g.CDATA_END);case P:return e.push(g.COMMENT_START,t.data,g.COMMENT_END);case k:var A=t.publicId,R=t.systemId;return e.push(g.DOCTYPE_DECL_START," ",t.name),A?(e.push(" ",g.PUBLIC," ",A),R&&"."!==R&&e.push(" ",R)):R&&"."!==R&&e.push(" ",g.SYSTEM," ",R),t.internalSubset&&e.push(" [",t.internalSubset,"]"),void e.push(">");case x:return e.push("<?",t.target," ",t.data,"?>");case w:return e.push("&",t.nodeName,";");default:e.push("??",t.nodeName)}}function vt(t,e,r){var n;switch(e.nodeType){case C:(n=e.cloneNode(!1)).ownerDocument=t;case _:break;case O:r=!0}if(n||(n=e.cloneNode(!1)),n.ownerDocument=t,n.parentNode=null,r)for(var s=e.firstChild;s;)n.appendChild(vt(t,s,r)),s=s.nextSibling;return n}function St(t,e,r){var n=new e.constructor(d);for(var s in e)if(o(e,s)){var i=e[s];"object"!=typeof i&&i!=n[s]&&(n[s]=i)}switch(e.childNodes&&(n.childNodes=new H),n.ownerDocument=t,n.nodeType){case C:var a=e.attributes,c=n.attributes=new X,l=a.length;c._ownerElement=n;for(var h=0;h<l;h++)n.setAttributeNode(St(t,a.item(h),!0));break;case O:r=!0}if(r)for(var u=e.firstChild;u;)n.appendChild(St(t,u,r)),u=u.nextSibling;return n}function wt(t,e,r){t[e]=r}H.prototype={length:0,item:function(t){return t>=0&&t<this.length?this[t]:null},toString:function(t){for(var e=[],r=0;r<this.length;r++)Ot(this[r],e,t);return e.join("")},filter:function(t){return Array.prototype.filter.call(this,t)},indexOf:function(t){return Array.prototype.indexOf.call(this,t)}},H.prototype[Symbol.iterator]=function(){var t=this,e=0;return{next:function(){return e<t.length?{value:t[e++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},G.prototype.item=function(t){return j(this),this[t]||null},R(G,H),X.prototype={length:0,item:H.prototype.item,getNamedItem:function(t){this._ownerElement&&this._ownerElement._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase());for(var e=0;e<this.length;){var r=this[e];if(r.nodeName===t)return r;e++}return null},setNamedItem:function(t){var e=t.ownerElement;if(e&&e!==this._ownerElement)throw new m(m.INUSE_ATTRIBUTE_ERR);var r=this.getNamedItemNS(t.namespaceURI,t.localName);return r===t?t:(W(this._ownerElement,this,t,r),r)},setNamedItemNS:function(t){return this.setNamedItem(t)},removeNamedItem:function(t){var e=this.getNamedItem(t);if(!e)throw new m(m.NOT_FOUND_ERR,t);return K(this._ownerElement,this,e),e},removeNamedItemNS:function(t,e){var r=this.getNamedItemNS(t,e);if(!r)throw new m(m.NOT_FOUND_ERR,t?t+" : "+e:e);return K(this._ownerElement,this,r),r},getNamedItemNS:function(t,e){t||(t=null);for(var r=0;r<this.length;){var n=this[r];if(n.localName===e&&n.namespaceURI===t)return n;r++}return null}},X.prototype[Symbol.iterator]=function(){var t=this,e=0;return{next:function(){return e<t.length?{value:t[e++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},z.prototype={hasFeature:function(t,e){return!0},createDocument:function(t,e,r){var n=h.XML_APPLICATION;t===u.HTML?n=h.XML_XHTML_APPLICATION:t===u.SVG&&(n=h.XML_SVG_IMAGE);var s=new Q(d,{contentType:n});if(s.implementation=this,s.childNodes=new H,s.doctype=r||null,r&&s.appendChild(r),e){var i=s.createElementNS(t,e);s.appendChild(i)}return s},createDocumentType:function(t,e,r,n){y(t);var s=new Et(d);return s.name=t,s.nodeName=t,s.publicId=e||"",s.systemId=r||"",s.internalSubset=n||"",s.childNodes=new H,s},createHTMLDocument:function(t){var e=new Q(d,{contentType:h.HTML});if(e.implementation=this,e.childNodes=new H,!1!==t){e.doctype=this.createDocumentType("html"),e.doctype.ownerDocument=e,e.appendChild(e.doctype);var r=e.createElement("html");e.appendChild(r);var n=e.createElement("head");if(r.appendChild(n),"string"==typeof t){var s=e.createElement("title");s.appendChild(e.createTextNode(t)),n.appendChild(s)}r.appendChild(e.createElement("body"))}return e}},Y.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,parentNode:null,get parentElement(){return this.parentNode&&this.parentNode.nodeType===this.ELEMENT_NODE?this.parentNode:null},childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,baseURI:"about:blank",get isConnected(){var t=this.getRootNode();return t&&t.nodeType===t.DOCUMENT_NODE},contains:function(t){if(!t)return!1;var e=t;do{if(this===e)return!0;e=t.parentNode}while(e);return!1},getRootNode:function(t){var e=this;do{if(!e.parentNode)return e;e=e.parentNode}while(e)},isEqualNode:function(t){if(!t)return!1;if(this.nodeType!==t.nodeType)return!1;switch(this.nodeType){case this.DOCUMENT_TYPE_NODE:if(this.name!==t.name)return!1;if(this.publicId!==t.publicId)return!1;if(this.systemId!==t.systemId)return!1;break;case this.ELEMENT_NODE:if(this.namespaceURI!==t.namespaceURI)return!1;if(this.prefix!==t.prefix)return!1;if(this.localName!==t.localName)return!1;if(this.attributes.length!==t.attributes.length)return!1;for(var e=0;e<this.attributes.length;e++){var r=this.attributes.item(e);if(!r.isEqualNode(t.getAttributeNodeNS(r.namespaceURI,r.localName)))return!1}break;case this.ATTRIBUTE_NODE:if(this.namespaceURI!==t.namespaceURI)return!1;if(this.localName!==t.localName)return!1;if(this.value!==t.value)return!1;break;case this.PROCESSING_INSTRUCTION_NODE:if(this.target!==t.target||this.data!==t.data)return!1;break;case this.TEXT_NODE:case this.COMMENT_NODE:if(this.data!==t.data)return!1}if(this.childNodes.length!==t.childNodes.length)return!1;for(e=0;e<this.childNodes.length;e++)if(!this.childNodes[e].isEqualNode(t.childNodes[e]))return!1;return!0},isSameNode:function(t){return this===t},insertBefore:function(t,e){return ht(this,t,e)},replaceChild:function(t,e){ht(this,t,e,lt),e&&this.removeChild(e)},removeChild:function(t){return et(this,t)},appendChild:function(t){return this.insertBefore(t,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(t){return St(this.ownerDocument||this,this,t)},normalize:function(){for(var t=this.firstChild;t;){var e=t.nextSibling;e&&e.nodeType==v&&t.nodeType==v?(this.removeChild(e),t.appendData(e.data)):(t.normalize(),t=e)}},isSupported:function(t,e){return this.ownerDocument.implementation.hasFeature(t,e)},lookupPrefix:function(t){for(var e=this;e;){var r=e._nsMap;if(r)for(var n in r)if(o(r,n)&&r[n]===t)return n;e=e.nodeType==O?e.ownerDocument:e.parentNode}return null},lookupNamespaceURI:function(t){for(var e=this;e;){var r=e._nsMap;if(r&&o(r,t))return r[t];e=e.nodeType==O?e.ownerDocument:e.parentNode}return null},isDefaultNamespace:function(t){return null==this.lookupPrefix(t)},compareDocumentPosition:function(t){if(this===t)return 0;var e=t,r=this,n=null,s=null;if(e instanceof dt&&(e=(n=e).ownerElement),r instanceof dt&&(r=(s=r).ownerElement,n&&e&&r===e))for(var i,o=0;i=r.attributes[o];o++){if(i===n)return B.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+B.DOCUMENT_POSITION_PRECEDING;if(i===s)return B.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+B.DOCUMENT_POSITION_FOLLOWING}if(!e||!r||r.ownerDocument!==e.ownerDocument)return B.DOCUMENT_POSITION_DISCONNECTED+B.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+(q(r.ownerDocument)>q(e.ownerDocument)?B.DOCUMENT_POSITION_FOLLOWING:B.DOCUMENT_POSITION_PRECEDING);if(s&&e===r)return B.DOCUMENT_POSITION_CONTAINS+B.DOCUMENT_POSITION_PRECEDING;if(n&&e===r)return B.DOCUMENT_POSITION_CONTAINED_BY+B.DOCUMENT_POSITION_FOLLOWING;for(var a=[],c=e.parentNode;c;){if(!s&&c===r)return B.DOCUMENT_POSITION_CONTAINED_BY+B.DOCUMENT_POSITION_FOLLOWING;a.push(c),c=c.parentNode}a.reverse();for(var l=[],h=r.parentNode;h;){if(!n&&h===e)return B.DOCUMENT_POSITION_CONTAINS+B.DOCUMENT_POSITION_PRECEDING;l.push(h),h=h.parentNode}l.reverse();var u=U(a,l);for(var d in u.childNodes){var p=u.childNodes[d];if(p===r)return B.DOCUMENT_POSITION_FOLLOWING;if(p===e)return B.DOCUMENT_POSITION_PRECEDING;if(l.indexOf(p)>=0)return B.DOCUMENT_POSITION_FOLLOWING;if(a.indexOf(p)>=0)return B.DOCUMENT_POSITION_PRECEDING}return 0}},A(L,Y),A(L,Y.prototype),A(B,Y),A(B,Y.prototype),Q.prototype={implementation:null,nodeName:"#document",nodeType:D,doctype:null,documentElement:null,_inc:1,insertBefore:function(t,e){if(t.nodeType===_){for(var r=t.firstChild;r;){var n=r.nextSibling;this.insertBefore(r,e),r=n}return t}return ht(this,t,e),t.ownerDocument=this,null===this.documentElement&&t.nodeType===C&&(this.documentElement=t),t},removeChild:function(t){var e=et(this,t);return e===this.documentElement&&(this.documentElement=null),e},replaceChild:function(t,e){ht(this,t,e,lt),t.ownerDocument=this,e&&this.removeChild(e),nt(t)&&(this.documentElement=t)},importNode:function(t,e){return vt(this,t,e)},getElementById:function(t){var e=null;return J(this.documentElement,(function(r){if(r.nodeType==C&&r.getAttribute("id")==t)return e=r,!0})),e},createElement:function(t){var e=new ut(d);return e.ownerDocument=this,"html"===this.type&&(t=t.toLowerCase()),i(this.contentType)&&(e.namespaceURI=u.HTML),e.nodeName=t,e.tagName=t,e.localName=t,e.childNodes=new H,(e.attributes=new X)._ownerElement=e,e},createDocumentFragment:function(){var t=new yt(d);return t.ownerDocument=this,t.childNodes=new H,t},createTextNode:function(t){var e=new mt(d);return e.ownerDocument=this,e.childNodes=new H,e.appendData(t),e},createComment:function(t){var e=new ft(d);return e.ownerDocument=this,e.childNodes=new H,e.appendData(t),e},createCDATASection:function(t){var e=new gt(d);return e.ownerDocument=this,e.childNodes=new H,e.appendData(t),e},createProcessingInstruction:function(t,e){var r=new It(d);return r.ownerDocument=this,r.childNodes=new H,r.nodeName=r.target=t,r.nodeValue=r.data=e,r},createAttribute:function(t){if(!g.QName_exact.test(t))throw new m(m.INVALID_CHARACTER_ERR,'invalid character in name "'+t+'"');return"html"===this.type&&(t=t.toLowerCase()),this._createAttribute(t)},_createAttribute:function(t){var e=new dt(d);return e.ownerDocument=this,e.childNodes=new H,e.name=t,e.nodeName=t,e.localName=t,e.specified=!0,e},createEntityReference:function(t){if(!g.Name.test(t))throw new m(m.INVALID_CHARACTER_ERR,'not a valid xml name "'+t+'"');if("html"===this.type)throw new m("document is an html document",f.NotSupportedError);var e=new Tt(d);return e.ownerDocument=this,e.childNodes=new H,e.nodeName=t,e},createElementNS:function(t,e){var r=I(t,e),n=new ut(d),s=n.attributes=new X;return n.childNodes=new H,n.ownerDocument=this,n.nodeName=e,n.tagName=e,n.namespaceURI=r[0],n.prefix=r[1],n.localName=r[2],s._ownerElement=n,n},createAttributeNS:function(t,e){var r=I(t,e),n=new dt(d);return n.ownerDocument=this,n.childNodes=new H,n.nodeName=e,n.name=e,n.specified=!0,n.namespaceURI=r[0],n.prefix=r[1],n.localName=r[2],n}},R(Q,Y),ut.prototype={nodeType:C,attributes:null,getQualifiedName:function(){return this.prefix?this.prefix+":"+this.localName:this.localName},_isInHTMLDocumentAndNamespace:function(){return"html"===this.ownerDocument.type&&this.namespaceURI===u.HTML},hasAttributes:function(){return!(!this.attributes||!this.attributes.length)},hasAttribute:function(t){return!!this.getAttributeNode(t)},getAttribute:function(t){var e=this.getAttributeNode(t);return e?e.value:null},getAttributeNode:function(t){return this._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase()),this.attributes.getNamedItem(t)},setAttribute:function(t,e){this._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase());var r=this.getAttributeNode(t);r?r.value=r.nodeValue=""+e:((r=this.ownerDocument._createAttribute(t)).value=r.nodeValue=""+e,this.setAttributeNode(r))},removeAttribute:function(t){var e=this.getAttributeNode(t);e&&this.removeAttributeNode(e)},setAttributeNode:function(t){return this.attributes.setNamedItem(t)},setAttributeNodeNS:function(t){return this.attributes.setNamedItemNS(t)},removeAttributeNode:function(t){return this.attributes.removeNamedItem(t.nodeName)},removeAttributeNS:function(t,e){var r=this.getAttributeNodeNS(t,e);r&&this.removeAttributeNode(r)},hasAttributeNS:function(t,e){return null!=this.getAttributeNodeNS(t,e)},getAttributeNS:function(t,e){var r=this.getAttributeNodeNS(t,e);return r?r.value:null},setAttributeNS:function(t,e,r){var n=I(t,e)[2],s=this.getAttributeNodeNS(t,n);s?s.value=s.nodeValue=""+r:((s=this.ownerDocument.createAttributeNS(t,e)).value=s.nodeValue=""+r,this.setAttributeNode(s))},getAttributeNodeNS:function(t,e){return this.attributes.getNamedItemNS(t,e)},getElementsByClassName:function(t){var e=T(t);return new G(this,(function(r){var n=[];return e.length>0&&J(r,(function(s){if(s!==r&&s.nodeType===C){var i=s.getAttribute("class");if(i){var o=t===i;if(!o){var a=T(i);o=e.every((c=a,function(t){return c&&-1!==c.indexOf(t)}))}o&&n.push(s)}}var c})),n}))},getElementsByTagName:function(t){var e="html"===(this.nodeType===D?this:this.ownerDocument).type,r=t.toLowerCase();return new G(this,(function(n){var s=[];return J(n,(function(i){i!==n&&i.nodeType===C&&(("*"===t||i.getQualifiedName()===(e&&i.namespaceURI===u.HTML?r:t))&&s.push(i))})),s}))},getElementsByTagNameNS:function(t,e){return new G(this,(function(r){var n=[];return J(r,(function(s){s===r||s.nodeType!==C||"*"!==t&&s.namespaceURI!==t||"*"!==e&&s.localName!=e||n.push(s)})),n}))}},Q.prototype.getElementsByClassName=ut.prototype.getElementsByClassName,Q.prototype.getElementsByTagName=ut.prototype.getElementsByTagName,Q.prototype.getElementsByTagNameNS=ut.prototype.getElementsByTagNameNS,R(ut,Y),dt.prototype.nodeType=O,R(dt,Y),pt.prototype={data:"",substringData:function(t,e){return this.data.substring(t,t+e)},appendData:function(t){t=this.data+t,this.nodeValue=this.data=t,this.length=t.length},insertData:function(t,e){this.replaceData(t,0,e)},deleteData:function(t,e){this.replaceData(t,e,"")},replaceData:function(t,e,r){r=this.data.substring(0,t)+r+this.data.substring(t+e),this.nodeValue=this.data=r,this.length=r.length}},R(pt,Y),mt.prototype={nodeName:"#text",nodeType:v,splitText:function(t){var e=this.data,r=e.substring(t);e=e.substring(0,t),this.data=this.nodeValue=e,this.length=e.length;var n=this.ownerDocument.createTextNode(r);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},R(mt,pt),ft.prototype={nodeName:"#comment",nodeType:P},R(ft,pt),gt.prototype={nodeName:"#cdata-section",nodeType:S},R(gt,mt),Et.prototype.nodeType=k,R(Et,Y),Nt.prototype.nodeType=F,R(Nt,Y),bt.prototype.nodeType=M,R(bt,Y),Tt.prototype.nodeType=w,R(Tt,Y),yt.prototype.nodeName="#document-fragment",yt.prototype.nodeType=_,R(yt,Y),It.prototype.nodeType=x,R(It,pt),At.prototype.serializeToString=function(t,e){return Rt.call(t,e)},Y.prototype.toString=Rt;try{if(Object.defineProperty){function Mt(t){switch(t.nodeType){case C:case _:var e=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&e.push(Mt(t)),t=t.nextSibling;return e.join("");default:return t.nodeValue}}Object.defineProperty(G.prototype,"length",{get:function(){return j(this),this.$$length}}),Object.defineProperty(Y.prototype,"textContent",{get:function(){return Mt(this)},set:function(t){switch(this.nodeType){case C:case _:for(;this.firstChild;)this.removeChild(this.firstChild);(t||String(t))&&this.appendChild(this.ownerDocument.createTextNode(t));break;default:this.data=t,this.value=t,this.nodeValue=t}}}),wt=function(t,e,r){t["$$"+e]=r}}}catch(xt){}e._updateLiveList=j,e.Attr=dt,e.CDATASection=gt,e.CharacterData=pt,e.Comment=ft,e.Document=Q,e.DocumentFragment=yt,e.DocumentType=Et,e.DOMImplementation=z,e.Element=ut,e.Entity=bt,e.EntityReference=Tt,e.LiveNodeList=G,e.NamedNodeMap=X,e.Node=Y,e.NodeList=H,e.Notation=Nt,e.Text=mt,e.ProcessingInstruction=It,e.XMLSerializer=At}},__webpack_module_cache__={},leafPrototypes,getProto;function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var r=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](r,r.exports,__webpack_require__),r.exports}getProto=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,__webpack_require__.t=function(t,e){if(1&e&&(t=this(t)),8&e)return t;if("object"==typeof t&&t){if(4&e&&t.__esModule)return t;if(16&e&&"function"==typeof t.then)return t}var r=Object.create(null);__webpack_require__.r(r);var n={};leafPrototypes=leafPrototypes||[null,getProto({}),getProto([]),getProto(getProto)];for(var s=2&e&&t;("object"==typeof s||"function"==typeof s)&&!~leafPrototypes.indexOf(s);s=getProto(s))Object.getOwnPropertyNames(s).forEach((e=>n[e]=()=>t[e]));return n.default=()=>t,__webpack_require__.d(r,n),r},__webpack_require__.d=(t,e)=>{for(var r in e)__webpack_require__.o(e,r)&&!__webpack_require__.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),__webpack_require__.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";const t="4.0.0";class e{constructor(){this.items=[],this.items=[]}[Symbol.iterator](){let t=0;const e=this.items;return{next:()=>({value:e[t++],done:t>e.length})}}add(t,r=e.DEFAULTPRIORITY){let n=this.items.length;do{n--}while(n>=0&&r<this.items[n].priority);return this.items.splice(n+1,0,{item:t,priority:r}),t}remove(t){let e=this.items.length;do{e--}while(e>=0&&this.items[e].item!==t);return e>=0&&this.items.splice(e,1),this}}e.DEFAULTPRIORITY=5;function r(t){return new Promise((function e(r,n){const s=t=>{var s;t.retry instanceof Promise?t.retry.then((()=>e(r,n))).catch((t=>n(t))):(null===(s=t.restart)||void 0===s?void 0:s.isCallback)?MathJax.Callback.After((()=>e(r,n)),t.restart):n(t)};try{const e=t();e instanceof Promise?e.then((t=>r(t))).catch((t=>s(t))):r(e)}catch(t){s(t)}}))}function n(t){const e=new Error("MathJax retry -- an asynchronous action is required; try using one of the promise-based functions and await its resolution.");throw e.retry=t,e}const s="undefined"!=typeof window,i={window:s?window:null,document:s?window.document:null,os:(()=>{if(s&&window.navigator){const t=window.navigator.appVersion,e=[["Win","Windows"],["Mac","MacOS"],["X11","Unix"],["Linux","Unix"]];for(const[r,n]of e)if(t.includes(r))return n;if(window.navigator.userAgent.includes("Android"))return"Unix"}return"unknown"})()},o={version:t,context:i,handlers:new class extends e{register(t){return this.add(t,t.priority)}unregister(t){this.remove(t)}handlesDocument(t){for(const e of this){const r=e.item;if(r.handlesDocument(t))return r}throw new Error("Can't find handler for document")}document(t,e=null){return this.handlesDocument(t).create(t,e)}},document:function(t,e){return o.handlers.document(t,e)},handleRetriesFor:r,retryAfter:n,asyncLoad:null,asyncIsSynchronous:!1},a={}.constructor;function c(t){return"object"==typeof t&&null!==t&&(t.constructor===a||t.constructor===d)}const l="[+]",h="[-]",u={invalidOption:"warn",optionError:(t,e)=>{if("fatal"===u.invalidOption)throw new Error(t);console.warn("MathJax: "+t)}};class d{}function p(t){return Object.assign(Object.create(d.prototype),t)}function m(t){return Array.isArray(t)?t:[t]}function f(t){return t?Object.keys(t).concat(Object.getOwnPropertySymbols(t)):[]}function g(t){const e={};for(const r of f(t)){const n=Object.getOwnPropertyDescriptor(t,r),s=n.value;Array.isArray(s)?n.value=E([],s,!1):c(s)&&(n.value=g(s)),n.enumerable&&(e[r]=n)}return Object.defineProperties(t.constructor===d?p({}):{},e)}function E(t,e,r=!0){for(let n of f(e)){if(r&&void 0===t[n]&&t.constructor!==d){"symbol"==typeof n&&(n=n.toString()),u.optionError(`Invalid option "${n}" (no default value).`,n);continue}const s=e[n];let i=t[n];if(!c(s)||null===i||"object"!=typeof i&&"function"!=typeof i)Array.isArray(s)?(t[n]=[],E(t[n],s,!1)):c(s)?t[n]=g(s):t[n]=s;else{const e=f(s);Array.isArray(i)&&(1===e.length&&(e[0]===l||e[0]===h)&&Array.isArray(s[e[0]])||2===e.length&&e.sort().join(",")===l+","+h&&Array.isArray(s[l])&&Array.isArray(s[h]))?(s[h]&&(i=t[n]=i.filter((t=>s[h].indexOf(t)<0))),s[l]&&(t[n]=[...i,...s[l]])):E(i,s,r)}}return t}function N(t,...e){return e.forEach((e=>E(t,e,!1))),t}function b(t,...e){return e.forEach((e=>E(t,e,!0))),t}function T(t,...e){const r=[];for(const n of e){const e={},s={};for(const r of Object.keys(t||{}))(void 0===n[r]?s:e)[r]=t[r];r.push(e),t=s}return r.unshift(t),r}function y(t,e,r=null){return Object.hasOwn(e,t)?e[t]:r}class I extends e{constructor(t=null){super(),t&&this.addList(t)}addList(t){for(const e of t)Array.isArray(e)?this.add(e[0],e[1]):this.add(e)}execute(...t){for(const e of this){if(!1===e.item(...t))return!1}return!0}asyncExecute(...t){let e=-1;const r=this.items;return new Promise(((n,s)=>{!function i(){for(;++e<r.length;){const o=r[e].item(...t);if(o instanceof Promise)return void o.then(i).catch((t=>s(t)));if(!1===o)return void n(!1)}n(!0)}()}))}}class A{constructor(t={}){this.adaptor=null,this.mmlFactory=null;const e=this.constructor;this.options=b(N({},e.OPTIONS),t),this.preFilters=new I(this.options.preFilters),this.postFilters=new I(this.options.postFilters)}get name(){return this.constructor.NAME}setAdaptor(t){this.adaptor=t}setMmlFactory(t){this.mmlFactory=t}initialize(){}reset(...t){}get processStrings(){return!0}findMath(t,e){return[]}executeFilters(t,e,r,n){const s={math:e,document:r,data:n};return t.execute(s),s.data}}A.NAME="generic",A.OPTIONS={preFilters:[],postFilters:[]};class R{constructor(t){const e=this.constructor;this.options=b(N({},e.OPTIONS),t)}}function L(t,e){return t.length!==e.length?e.length-t.length:t===e?0:t<e?-1:1}function C(t){return t.replace(/([\^$(){}.+*?\-|[\]:\\])/g,"\\$1")}function O(t){return t.trim().split(/\s+/)}function v(t,e,r,n,s,i,o=null){return{open:t,math:e,close:r,n,start:{n:s},end:{n:i},display:o}}R.OPTIONS={};class S{get isEscaped(){return null===this.display}constructor(t,e,r=!0,n={i:0,n:0,delim:""},s={i:0,n:0,delim:""}){this.root=null,this.typesetRoot=null,this.metrics={},this.inputData={},this.outputData={},this._state=w.UNPROCESSED,this.math=t,this.inputJax=e,this.display=r,this.start=n,this.end=s,this.root=null,this.typesetRoot=null,this.metrics={},this.inputData={},this.outputData={}}render(t){t.renderActions.renderMath(this,t)}rerender(t,e=w.RERENDER){this.state()>=e&&this.state(e-1),t.renderActions.renderMath(this,t,e)}convert(t,e=w.LAST){t.renderActions.renderConvert(this,t,e)}compile(t){this.state()<w.COMPILED&&(this.root=this.inputJax.compile(this,t),this.state(w.COMPILED))}typeset(t){this.state()<w.TYPESET&&(this.typesetRoot=t.outputJax[this.isEscaped?"escaped":"typeset"](this,t),this.state(w.TYPESET))}updateDocument(t){}removeFromDocument(t=!1){this.clear()}setMetrics(t,e,r,n){this.metrics={em:t,ex:e,containerWidth:r,scale:n}}state(t=null,e=!1){return null!=t&&(t<w.INSERTED&&this._state>=w.INSERTED&&this.removeFromDocument(e),t<w.TYPESET&&this._state>=w.TYPESET&&(this.outputData={}),t<w.COMPILED&&this._state>=w.COMPILED&&(this.inputData={}),this._state=t),this._state}reset(t=!1){this.state(w.UNPROCESSED,t)}clear(){}}const w={UNPROCESSED:0,FINDMATH:10,COMPILED:20,CONVERT:100,METRICS:110,RERENDER:125,TYPESET:150,INSERTED:200,LAST:1e4};function M(t,e){if(t in w)throw Error("State "+t+" already exists");w[t]=e}class x extends R{constructor(t){super(t),this.getPatterns()}getPatterns(){const t=this.options,e=[],r=[],n=[];this.end={},this.env=this.sub=0;let s=1;t.inlineMath.forEach((t=>this.addPattern(e,t,!1))),t.displayMath.forEach((t=>this.addPattern(e,t,!0))),e.length&&r.push(e.sort(L).join("|")),t.processEnvironments&&(r.push("\\\\begin\\s*\\{([^}]*)\\}"),this.env=s,s++),t.processEscapes&&n.push("\\\\([\\\\$])"),t.processRefs&&n.push("(\\\\(?:eq)?ref\\s*\\{[^}]*\\})"),n.length&&(r.push("("+n.join("|")+")"),this.sub=s),this.start=new RegExp(r.join("|"),"g"),this.hasPatterns=r.length>0}addPattern(t,e,r){const[n,s]=e;t.push(C(n)),this.end[n]=[s,r,this.endPattern(s)]}endPattern(t,e){return new RegExp((e||C(t))+"|\\\\(?:[a-zA-Z]|.)|[{}]","g")}findEnd(t,e,r,n){const[s,i,o]=n,a=o.lastIndex=r.index+r[0].length;let c,l=0;for(;c=o.exec(t);){if((c[1]||c[0])===s&&0===l)return v(r[0],t.substring(a,c.index),c[0],e,r.index,c.index+c[0].length,i);"{"===c[0]?l++:"}"===c[0]&&l&&l--}return null}findMathInString(t,e,r){let n,s;for(this.start.lastIndex=0;n=this.start.exec(r);){if(void 0!==n[this.env]&&this.env){const t="\\\\end\\s*(\\{"+C(n[this.env])+"\\})";s=this.findEnd(r,e,n,["{"+n[this.env]+"}",!0,this.endPattern(null,t)]),s&&(s.math=s.open+s.math+s.close,s.open=s.close="")}else if(void 0!==n[this.sub]&&this.sub){const t=n[this.sub],r=n.index+n[this.sub].length;s=2===t.length?v("\\",t.substring(1),"",e,n.index,r):v("",t,"",e,n.index,r,!1)}else s=this.findEnd(r,e,n,this.end[n[0]]);s&&(t.push(s),this.start.lastIndex=s.end.n)}}findMath(t){const e=[];if(this.hasPatterns)for(let r=0,n=t.length;r<n;r++)this.findMathInString(e,r,t[r]);return e}}x.OPTIONS={inlineMath:[["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],processEscapes:!0,processEnvironments:!0,processRefs:!0};const P="_inherit_";class D{constructor(t,e){this.global=e,this.defaults=Object.create(e),this.inherited=Object.create(this.defaults),this.attributes=Object.create(this.inherited),Object.assign(this.defaults,t)}set(t,e){this.attributes[t]=e}setList(t){Object.assign(this.attributes,t)}unset(t){delete this.attributes[t]}get(t){let e=this.attributes[t];return e===P&&(e=this.global[t]),e}getExplicit(t){return this.hasExplicit(t)?this.attributes[t]:void 0}hasExplicit(t){return Object.hasOwn(this.attributes,t)}hasOneOf(t){for(const e of t)if(this.hasExplicit(e))return!0;return!1}getList(...t){const e={};for(const r of t)e[r]=this.get(r);return e}setInherited(t,e){this.inherited[t]=e}getInherited(t){return this.inherited[t]}getDefault(t){return this.defaults[t]}isSet(t){return Object.hasOwn(this.attributes,t)||Object.hasOwn(this.inherited,t)}hasDefault(t){return t in this.defaults}getExplicitNames(){return Object.keys(this.attributes)}getInheritedNames(){return Object.keys(this.inherited)}getDefaultNames(){return Object.keys(this.defaults)}getGlobalNames(){return Object.keys(this.global)}getAllAttributes(){return this.attributes}getAllInherited(){return this.inherited}getAllDefaults(){return this.defaults}getAllGlobals(){return this.global}}class k{constructor(t,e={},r=[]){this.factory=t,this.parent=null,this.properties={},this.childNodes=[];for(const t of Object.keys(e))this.setProperty(t,e[t]);r.length&&this.setChildren(r)}get kind(){return"unknown"}setProperty(t,e){this.properties[t]=e}getProperty(t){return this.properties[t]}getPropertyNames(){return Object.keys(this.properties)}getAllProperties(){return this.properties}removeProperty(...t){for(const e of t)delete this.properties[e]}isKind(t){return this.factory.nodeIsKind(this,t)}setChildren(t){this.childNodes=[];for(const e of t)this.appendChild(e)}appendChild(t){return this.childNodes.push(t),t.parent=this,t}replaceChild(t,e){const r=this.childIndex(e);return null!==r&&(this.childNodes[r]=t,t.parent=this,e.parent===this&&(e.parent=null)),t}removeChild(t){const e=this.childIndex(t);return null!==e&&(this.childNodes.splice(e,1),t.parent=null),t}childIndex(t){const e=this.childNodes.indexOf(t);return-1===e?null:e}copy(){const t=this.factory.create(this.kind);t.properties=Object.assign({},this.properties);for(const e of this.childNodes||[])e&&t.appendChild(e.copy());return t}findNodes(t){const e=[];return this.walkTree((r=>{r.isKind(t)&&e.push(r)})),e}walkTree(t,e){t(this,e);for(const r of this.childNodes)r&&r.walkTree(t,e);return e}toString(){return this.kind+"("+this.childNodes.join(",")+")"}}class _ extends k{setChildren(t){}appendChild(t){return t}replaceChild(t,e){return e}childIndex(t){return null}walkTree(t,e){return t(this,e),e}toString(){return this.kind}}const F={ORD:0,OP:1,BIN:2,REL:3,OPEN:4,CLOSE:5,PUNCT:6,INNER:7,NONE:-1},B=["ORD","OP","BIN","REL","OPEN","CLOSE","PUNCT","INNER"],U=["","thinmathspace","mediummathspace","thickmathspace"],q=[[0,-1,2,3,0,0,0,1],[-1,-1,0,3,0,0,0,1],[2,2,0,0,2,0,0,2],[3,3,0,0,3,0,0,3],[0,0,0,0,0,0,0,0],[0,-1,2,3,0,0,0,1],[1,1,0,1,1,1,1,1],[1,-1,2,3,1,0,1,1]],H=new Set(["normal","bold","italic","bold-italic","double-struck","fraktur","bold-fraktur","script","bold-script","sans-serif","bold-sans-serif","sans-serif-italic","sans-serif-bold-italic","monospace","inital","tailed","looped","stretched"]),G=["indentalign","indentalignfirst","indentshift","indentshiftfirst"];class j extends k{constructor(t,e={},r=[]){super(t),this.prevClass=null,this.prevLevel=null,this.texclass=null,this.arity<0&&(this.childNodes=[t.create("inferredMrow")],this.childNodes[0].parent=this),this.setChildren(r),this.attributes=new D(t.getNodeClass(this.kind).defaults,t.getNodeClass("math").defaults),this.attributes.setList(e)}copy(t=!1){const e=this.factory.create(this.kind);if(e.properties=Object.assign({},this.properties),this.attributes){const r=this.attributes.getAllAttributes();for(const n of Object.keys(r))("id"!==n||t)&&e.attributes.set(n,r[n])}if(this.childNodes&&this.childNodes.length){let t=this.childNodes;1===t.length&&t[0].isInferred&&(t=t[0].childNodes);for(const r of t)r?e.appendChild(r.copy()):e.childNodes.push(null)}return e}get texClass(){return this.texclass}set texClass(t){this.texclass=t}get isToken(){return!1}get isEmbellished(){return!1}get isSpacelike(){return!1}get linebreakContainer(){return!1}get linebreakAlign(){return"data-align"}get arity(){return 1/0}get isInferred(){return!1}get Parent(){let t=this.parent;for(;t&&t.notParent;)t=t.Parent;return t}get notParent(){return!1}setChildren(t){return this.arity<0?this.childNodes[0].setChildren(t):super.setChildren(t)}appendChild(t){if(this.arity<0)return this.childNodes[0].appendChild(t),t;if(t.isInferred){if(this.arity===1/0)return t.childNodes.forEach((t=>super.appendChild(t))),t;const e=t;(t=this.factory.create("mrow")).setChildren(e.childNodes),t.attributes=e.attributes;for(const r of e.getPropertyNames())t.setProperty(r,e.getProperty(r))}return super.appendChild(t)}replaceChild(t,e){return this.arity<0?(this.childNodes[0].replaceChild(t,e),t):super.replaceChild(t,e)}core(){return this}coreMO(){return this}coreIndex(){return 0}childPosition(){let t=null,e=this.parent;for(;e&&e.notParent;)t=e,e=e.parent;if(t=t||this,e){let r=0;for(const n of e.childNodes){if(n===t)return r;r++}}return null}setTeXclass(t){return this.getPrevClass(t),null!=this.texClass?this:t}updateTeXclass(t){t&&(this.prevClass=t.prevClass,this.prevLevel=t.prevLevel,t.prevClass=t.prevLevel=null,this.texClass=t.texClass)}getPrevClass(t){t&&(this.prevClass=t.texClass,this.prevLevel=t.attributes.get("scriptlevel"))}texSpacing(){const t=null!=this.prevClass?this.prevClass:F.NONE,e=this.texClass||F.ORD;if(t===F.NONE||e===F.NONE)return"";const r=q[t][e];return(this.prevLevel>0||this.attributes.get("scriptlevel")>0)&&r>=0?"":U[Math.abs(r)]}hasSpacingAttributes(){return this.isEmbellished&&this.coreMO().hasSpacingAttributes()}setInheritedAttributes(t={},e=!1,r=0,n=!1){var s,i,o;const a=this.attributes.getAllDefaults();for(const e of Object.keys(t)){if(Object.hasOwn(a,e)||Object.hasOwn(j.alwaysInherit,e)){const[r,n]=t[e];(null===(i=null===(s=j.noInherit[r])||void 0===s?void 0:s[this.kind])||void 0===i?void 0:i[e])||this.attributes.setInherited(e,n)}(null===(o=j.stopInherit[this.kind])||void 0===o?void 0:o[e])&&delete(t=Object.assign({},t))[e]}void 0===this.attributes.getExplicit("displaystyle")&&this.attributes.setInherited("displaystyle",e);void 0===this.attributes.getExplicit("scriptlevel")&&this.attributes.setInherited("scriptlevel",r),n&&this.setProperty("texprimestyle",n);const c=this.arity;if(c>=0&&c!==1/0&&(1===c&&0===this.childNodes.length||1!==c&&this.childNodes.length!==c))if(c<this.childNodes.length)this.childNodes=this.childNodes.slice(0,c);else for(;this.childNodes.length<c;)this.appendChild(this.factory.create("mrow"));if(this.linebreakContainer&&!this.isEmbellished){const e=this.linebreakAlign;if(e){const r=this.attributes.get(e)||"left";t=this.addInheritedAttributes(t,{indentalign:r,indentshift:"0",indentalignfirst:r,indentshiftfirst:"0",indentalignlast:"indentalign",indentshiftlast:"indentshift"})}}this.setChildInheritedAttributes(t,e,r,n)}setChildInheritedAttributes(t,e,r,n){for(const s of this.childNodes)s.setInheritedAttributes(t,e,r,n)}addInheritedAttributes(t,e){const r=Object.assign({},t);for(const t of Object.keys(e))"displaystyle"!==t&&"scriptlevel"!==t&&"style"!==t&&(r[t]=[this.kind,e[t]]);return r}inheritAttributesFrom(t){const e=t.attributes,r=e.get("displaystyle"),n=e.get("scriptlevel"),s=e.isSet("mathsize")?{mathsize:["math",e.get("mathsize")]}:{},i=t.getProperty("texprimestyle")||!1;this.setInheritedAttributes(s,r,n,i)}verifyTree(t=null){if(null===t)return;this.verifyAttributes(t);const e=this.arity;t.checkArity&&e>=0&&e!==1/0&&(1===e&&0===this.childNodes.length||1!==e&&this.childNodes.length!==e)&&this.mError('Wrong number of children for "'+this.kind+'" node',t,!0),this.verifyChildren(t)}verifyAttributes(t){if(t.checkAttributes){const e=this.attributes,r=[];for(const t of e.getExplicitNames())"data-"===t.substring(0,5)||void 0!==e.getDefault(t)||t.match(/^(?:class|style|id|(?:xlink:)?href)$/)||r.push(t);r.length&&this.mError("Unknown attributes for "+this.kind+" node: "+r.join(", "),t)}if(t.checkMathvariants){const e=this.attributes.getExplicit("mathvariant");!e||H.has(e)||this.getProperty("ignore-variant")||this.mError(`Invalid mathvariant: ${e}`,t,!0)}}verifyChildren(t){for(const e of this.childNodes)e.verifyTree(t)}mError(t,e,r=!1){if(this.parent&&this.parent.isKind("merror"))return null;const n=this.factory.create("merror");if(n.attributes.set("data-mjx-message",t),e.fullErrors||r){const r=this.factory.create("mtext"),s=this.factory.create("text");s.setText(e.fullErrors?t:this.kind),r.appendChild(s),n.appendChild(r),this.parent.replaceChild(n,this),e.fullErrors||n.attributes.set("title",t)}else this.parent.replaceChild(n,this),n.appendChild(this);return n}}j.defaults={mathbackground:P,mathcolor:P,mathsize:P,dir:P},j.noInherit={mstyle:{mpadded:{width:!0,height:!0,depth:!0,lspace:!0,voffset:!0},mtable:{width:!0,height:!0,depth:!0,align:!0}},maligngroup:{mrow:{groupalign:!0},mtable:{groupalign:!0}},mtr:{msqrt:{"data-vertical-align":!0},mroot:{"data-vertical-align":!0}},mlabeledtr:{msqrt:{"data-vertical-align":!0},mroot:{"data-vertical-align":!0}}},j.stopInherit={mtd:{columnalign:!0,rowalign:!0,groupalign:!0}},j.alwaysInherit={scriptminsize:!0,scriptsizemultiplier:!0,infixlinebreakstyle:!0},j.verifyDefaults={checkArity:!0,checkAttributes:!1,checkMathvariants:!0,fullErrors:!1,fixMmultiscripts:!0,fixMtables:!0};class X extends j{get isToken(){return!0}getText(){let t="";for(const e of this.childNodes)e instanceof z?t+=e.getText():"textContent"in e&&(t+=e.textContent());return t}setChildInheritedAttributes(t,e,r,n){for(const s of this.childNodes)s instanceof j&&s.setInheritedAttributes(t,e,r,n)}walkTree(t,e){t(this,e);for(const r of this.childNodes)r instanceof j&&r.walkTree(t,e);return e}}X.defaults=Object.assign(Object.assign({},j.defaults),{mathvariant:"normal",mathsize:P});class V extends j{get isSpacelike(){return this.childNodes[0].isSpacelike}get isEmbellished(){return this.childNodes[0].isEmbellished}get arity(){return-1}core(){return this.childNodes[0]}coreMO(){return this.childNodes[0].coreMO()}setTeXclass(t){return t=this.childNodes[0].setTeXclass(t),this.updateTeXclass(this.childNodes[0]),t}}V.defaults=j.defaults;class W extends j{get isEmbellished(){return this.childNodes[0].isEmbellished}core(){return this.childNodes[0]}coreMO(){return this.childNodes[0].coreMO()}setTeXclass(t){this.getPrevClass(t),this.texClass=F.ORD;const e=this.childNodes[0];let r=null;e&&(this.isEmbellished||e.isKind("mi")?(r=e.setTeXclass(t),this.updateTeXclass(this.core())):e.setTeXclass(null));for(const t of this.childNodes.slice(1))t&&t.setTeXclass(null);return r||this}}W.defaults=j.defaults;class K extends _{get isToken(){return!1}get isEmbellished(){return!1}get isSpacelike(){return!1}get linebreakContainer(){return!1}get linebreakAlign(){return""}get arity(){return 0}get isInferred(){return!1}get notParent(){return!1}get Parent(){return this.parent}get texClass(){return F.NONE}get prevClass(){return F.NONE}get prevLevel(){return 0}hasSpacingAttributes(){return!1}get attributes(){return null}core(){return this}coreMO(){return this}coreIndex(){return 0}childPosition(){return 0}setTeXclass(t){return t}texSpacing(){return""}setInheritedAttributes(t,e,r,n){}inheritAttributesFrom(t){}verifyTree(t){}mError(t,e,r=!1){return null}}class z extends K{constructor(){super(...arguments),this.text=""}get kind(){return"text"}getText(){return this.text}setText(t){return this.text=t,this}copy(){return this.factory.create(this.kind).setText(this.getText())}toString(){return this.text}}class Y extends K{constructor(){super(...arguments),this.xml=null,this.adaptor=null}get kind(){return"XML"}getXML(){return this.xml}setXML(t,e=null){return this.xml=t,this.adaptor=e,this}getSerializedXML(){return this.adaptor.serializeXML(this.xml)}copy(){return this.factory.create(this.kind).setXML(this.adaptor.clone(this.xml))}toString(){return"XML data"}}function $(t,e,r=F.BIN,n=null){return[t,e,r,n]}const J={ORD:$(0,0,F.ORD),ORD11:$(1,1,F.ORD),ORD21:$(2,1,F.ORD),ORD02:$(0,2,F.ORD),ORD55:$(5,5,F.ORD),NONE:$(0,0,F.NONE),OP:$(1,2,F.OP,{largeop:!0,movablelimits:!0,symmetric:!0}),OPFIXED:$(1,2,F.OP,{largeop:!0,movablelimits:!0}),INTEGRAL:$(0,1,F.OP,{largeop:!0,symmetric:!0}),INTEGRAL2:$(1,2,F.OP,{largeop:!0,symmetric:!0}),BIN3:$(3,3,F.BIN),BIN4:$(4,4,F.BIN),BIN01:$(0,1,F.BIN),BIN5:$(5,5,F.BIN),TALLBIN:$(4,4,F.BIN,{stretchy:!0}),BINOP:$(4,4,F.BIN,{largeop:!0,movablelimits:!0}),REL:$(5,5,F.REL),REL1:$(1,1,F.REL,{stretchy:!0}),REL4:$(4,4,F.REL),RELSTRETCH:$(5,5,F.REL,{stretchy:!0}),RELACCENT:$(5,5,F.REL,{accent:!0}),WIDEREL:$(5,5,F.REL,{accent:!0,stretchy:!0}),OPEN:$(0,0,F.OPEN,{fence:!0,stretchy:!0,symmetric:!0}),CLOSE:$(0,0,F.CLOSE,{fence:!0,stretchy:!0,symmetric:!0}),INNER:$(0,0,F.INNER),PUNCT:$(0,3,F.PUNCT),ACCENT:$(0,0,F.ORD,{accent:!0}),WIDEACCENT:$(0,0,F.ORD,{accent:!0,stretchy:!0})},Q=[[32,127,F.REL,"mo"],[160,191,F.ORD,"mo"],[192,591,F.ORD,"mi"],[688,879,F.ORD,"mo"],[880,6688,F.ORD,"mi"],[6832,6911,F.ORD,"mo"],[6912,7615,F.ORD,"mi"],[7616,7679,F.ORD,"mo"],[7680,8191,F.ORD,"mi"],[8192,8303,F.ORD,"mo"],[8304,8351,F.ORD,"mo"],[8448,8527,F.ORD,"mi"],[8528,8591,F.ORD,"mn"],[8592,8703,F.REL,"mo"],[8704,8959,F.BIN,"mo"],[8960,9215,F.ORD,"mo"],[9312,9471,F.ORD,"mn"],[9472,10223,F.ORD,"mo"],[10224,10239,F.REL,"mo"],[10240,10495,F.ORD,"mtext"],[10496,10623,F.REL,"mo"],[10624,10751,F.ORD,"mo"],[10752,11007,F.BIN,"mo"],[11008,11055,F.ORD,"mo"],[11056,11087,F.REL,"mo"],[11088,11263,F.ORD,"mo"],[11264,11744,F.ORD,"mi"],[11776,11903,F.ORD,"mo"],[11904,12255,F.ORD,"mi","normal"],[12272,12351,F.ORD,"mo"],[12352,42143,F.ORD,"mi","normal"],[42192,43055,F.ORD,"mi"],[43056,43071,F.ORD,"mn"],[43072,55295,F.ORD,"mi"],[63744,64255,F.ORD,"mi","normal"],[64256,65023,F.ORD,"mi"],[65024,65135,F.ORD,"mo"],[65136,65791,F.ORD,"mi"],[65792,65935,F.ORD,"mn"],[65936,74751,F.ORD,"mi","normal"],[74752,74879,F.ORD,"mn"],[74880,113823,F.ORD,"mi","normal"],[113824,119391,F.ORD,"mo"],[119648,119679,F.ORD,"mn"],[119808,120781,F.ORD,"mi"],[120782,120831,F.ORD,"mn"],[122624,129023,F.ORD,"mo"],[129024,129279,F.REL,"mo"],[129280,129535,F.ORD,"mo"],[131072,195103,F.ORD,"mi","normal"]];function Z(t){const e=tt.infix[t]||tt.prefix[t]||tt.postfix[t];if(e)return[0,0,e[2],"mo"];const r=t.codePointAt(0);for(const t of Q)if(r<=t[1]){if(r>=t[0])return t;break}return[0,0,F.REL,"mo"]}const tt={prefix:{"(":J.OPEN,"+":J.BIN01,"-":J.BIN01,"[":J.OPEN,"{":J.OPEN,"|":J.OPEN,"||":[0,0,F.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[0,0,F.ORD,{fence:!0,stretchy:!0,symmetric:!0}],"\xac":J.ORD21,"\xb1":J.BIN01,"\u2016":[0,0,F.ORD,{fence:!0,stretchy:!0}],"\u2018":[0,0,F.OPEN,{fence:!0}],"\u201c":[0,0,F.OPEN,{fence:!0}],\u2145:J.ORD21,\u2146:$(2,0,F.ORD),"\u2200":J.ORD21,"\u2202":J.ORD21,"\u2203":J.ORD21,"\u2204":J.ORD21,"\u2207":J.ORD21,"\u220f":J.OP,"\u2210":J.OP,"\u2211":J.OP,"\u2212":J.BIN01,"\u2213":J.BIN01,"\u221a":[1,1,F.ORD,{stretchy:!0}],"\u221b":J.ORD11,"\u221c":J.ORD11,"\u2220":J.ORD,"\u2221":J.ORD,"\u2222":J.ORD,"\u222b":J.INTEGRAL,"\u222c":J.INTEGRAL,"\u222d":J.INTEGRAL,"\u222e":J.INTEGRAL,"\u222f":J.INTEGRAL,"\u2230":J.INTEGRAL,"\u2231":J.INTEGRAL,"\u2232":J.INTEGRAL,"\u2233":J.INTEGRAL,"\u22c0":J.OP,"\u22c1":J.OP,"\u22c2":J.OP,"\u22c3":J.OP,"\u2308":J.OPEN,"\u230a":J.OPEN,"\u2329":J.OPEN,"\u2772":J.OPEN,"\u27e6":J.OPEN,"\u27e8":J.OPEN,"\u27ea":J.OPEN,"\u27ec":J.OPEN,"\u27ee":J.OPEN,"\u2980":[0,0,F.ORD,{fence:!0,stretchy:!0}],"\u2983":J.OPEN,"\u2985":J.OPEN,"\u2987":J.OPEN,"\u2989":J.OPEN,"\u298b":J.OPEN,"\u298d":J.OPEN,"\u298f":J.OPEN,"\u2991":J.OPEN,"\u2993":J.OPEN,"\u2995":J.OPEN,"\u2997":J.OPEN,"\u29fc":J.OPEN,"\u2a00":J.OP,"\u2a01":J.OP,"\u2a02":J.OP,"\u2a03":J.OP,"\u2a04":J.OP,"\u2a05":J.OP,"\u2a06":J.OP,"\u2a07":J.OP,"\u2a08":J.OP,"\u2a09":J.OP,"\u2a0a":J.OP,"\u2a0b":J.INTEGRAL2,"\u2a0c":J.INTEGRAL,"\u2a0d":J.INTEGRAL2,"\u2a0e":J.INTEGRAL2,"\u2a0f":J.INTEGRAL2,"\u2a10":J.OP,"\u2a11":J.OP,"\u2a12":J.OP,"\u2a13":J.OP,"\u2a14":J.OP,"\u2a15":J.INTEGRAL2,"\u2a16":J.INTEGRAL2,"\u2a17":J.INTEGRAL2,"\u2a18":J.INTEGRAL2,"\u2a19":J.INTEGRAL2,"\u2a1a":J.INTEGRAL2,"\u2a1b":J.INTEGRAL2,"\u2a1c":J.INTEGRAL2,"\u2afc":J.OP,"\u2aff":J.OP},postfix:{"!!":$(1,0),"!":[1,0,F.CLOSE,null],'"':J.ACCENT,"&":J.ORD,")":J.CLOSE,"++":$(0,0),"--":$(0,0),"..":$(0,0),"...":J.ORD,"'":J.ACCENT,"]":J.CLOSE,"^":J.WIDEACCENT,_:J.WIDEACCENT,"`":J.ACCENT,"|":J.CLOSE,"}":J.CLOSE,"~":J.WIDEACCENT,"||":[0,0,F.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[0,0,F.ORD,{fence:!0,stretchy:!0,symmetric:!0}],"\xa8":J.ACCENT,\u00aa:J.ACCENT,"\xaf":J.WIDEACCENT,"\xb0":J.ORD,"\xb2":J.ACCENT,"\xb3":J.ACCENT,"\xb4":J.ACCENT,"\xb8":J.ACCENT,"\xb9":J.ACCENT,\u00ba:J.ACCENT,\u02c6:J.WIDEACCENT,\u02c7:J.WIDEACCENT,\u02c9:J.WIDEACCENT,\u02ca:J.ACCENT,\u02cb:J.ACCENT,\u02cd:J.WIDEACCENT,"\u02d8":J.ACCENT,"\u02d9":J.ACCENT,"\u02da":J.ACCENT,"\u02dc":J.WIDEACCENT,"\u02dd":J.ACCENT,"\u02f7":J.WIDEACCENT,"\u0302":J.WIDEACCENT,"\u0311":J.ACCENT,"\u03f6":J.REL,"\u2016":[0,0,F.ORD,{fence:!0,stretchy:!0}],"\u2019":[0,0,F.CLOSE,{fence:!0}],"\u201a":J.ACCENT,"\u201b":J.ACCENT,"\u201d":[0,0,F.CLOSE,{fence:!0}],"\u201e":J.ACCENT,"\u201f":J.ACCENT,"\u2032":J.ORD,"\u2033":J.ORD,"\u2034":J.ORD,"\u2035":J.ORD,"\u2036":J.ORD,"\u2037":J.ORD,"\u203e":J.WIDEACCENT,"\u2057":J.ORD,"\u20db":J.ACCENT,"\u20dc":J.ACCENT,"\u2309":J.CLOSE,"\u230b":J.CLOSE,"\u232a":J.CLOSE,"\u23b4":J.WIDEACCENT,"\u23b5":J.WIDEACCENT,"\u23dc":J.WIDEACCENT,"\u23dd":J.WIDEACCENT,"\u23de":J.WIDEACCENT,"\u23df":J.WIDEACCENT,"\u23e0":J.WIDEACCENT,"\u23e1":J.WIDEACCENT,"\u25a0":J.BIN3,"\u25a1":J.BIN3,"\u25aa":J.BIN3,"\u25ab":J.BIN3,"\u25ad":J.BIN3,"\u25ae":J.BIN3,"\u25af":J.BIN3,"\u25b0":J.BIN3,"\u25b1":J.BIN3,"\u25b2":J.BIN4,"\u25b4":J.BIN4,"\u25b6":J.BIN4,"\u25b7":J.BIN4,"\u25b8":J.BIN4,"\u25bc":J.BIN4,"\u25be":J.BIN4,"\u25c0":J.BIN4,"\u25c1":J.BIN4,"\u25c2":J.BIN4,"\u25c4":J.BIN4,"\u25c5":J.BIN4,"\u25c6":J.BIN4,"\u25c7":J.BIN4,"\u25c8":J.BIN4,"\u25c9":J.BIN4,"\u25cc":J.BIN4,"\u25cd":J.BIN4,"\u25ce":J.BIN4,"\u25cf":J.BIN4,"\u25d6":J.BIN4,"\u25d7":J.BIN4,"\u25e6":J.BIN4,"\u266d":J.ORD02,"\u266e":J.ORD02,"\u266f":J.ORD02,"\u2773":J.CLOSE,"\u27e7":J.CLOSE,"\u27e9":J.CLOSE,"\u27eb":J.CLOSE,"\u27ed":J.CLOSE,"\u27ef":J.CLOSE,"\u2980":[0,0,F.ORD,{fence:!0,stretchy:!0}],"\u2984":J.CLOSE,"\u2986":J.CLOSE,"\u2988":J.CLOSE,"\u298a":J.CLOSE,"\u298c":J.CLOSE,"\u298e":J.CLOSE,"\u2990":J.CLOSE,"\u2992":J.CLOSE,"\u2994":J.CLOSE,"\u2996":J.CLOSE,"\u2998":J.CLOSE,"\u29fd":J.CLOSE},infix:{"!=":J.BIN4,"#":J.ORD,$:J.ORD,"%":[3,3,F.ORD,null],"&&":J.BIN4,"":J.ORD,"*":J.BIN3,"**":$(1,1),"*=":J.BIN4,"+":J.BIN4,"+=":J.BIN4,",":[0,3,F.PUNCT,{linebreakstyle:"after",separator:!0}],"-":J.BIN4,"-=":J.BIN4,"->":J.BIN5,".":[0,3,F.PUNCT,{linebreakstyle:"after",separator:!0}],"/":J.ORD11,"//":$(1,1),"/=":J.BIN4,":":[1,2,F.REL,null],":=":J.BIN4,";":[0,3,F.PUNCT,{linebreakstyle:"after",separator:!0}],"<":J.REL,"<=":J.BIN5,"<>":$(1,1),"=":J.REL,"==":J.BIN4,">":J.REL,">=":J.BIN5,"?":[1,1,F.CLOSE,null],"@":J.ORD11,"\\":J.ORD,"^":J.ORD11,_:J.ORD11,"|":[2,2,F.ORD,{fence:!0,stretchy:!0,symmetric:!0}],"||":[2,2,F.BIN,{fence:!0,stretchy:!0,symmetric:!0}],"|||":[2,2,F.ORD,{fence:!0,stretchy:!0,symmetric:!0}],"\xb1":J.BIN4,"\xb7":J.BIN4,"\xd7":J.BIN4,"\xf7":J.BIN4,\u02b9:J.ORD,"\u0300":J.ACCENT,"\u0301":J.ACCENT,"\u0303":J.WIDEACCENT,"\u0304":J.ACCENT,"\u0306":J.ACCENT,"\u0307":J.ACCENT,"\u0308":J.ACCENT,"\u030c":J.ACCENT,"\u0332":J.WIDEACCENT,"\u0338":J.REL4,"\u2015":[0,0,F.ORD,{stretchy:!0}],"\u2017":[0,0,F.ORD,{stretchy:!0}],"\u2020":J.BIN3,"\u2021":J.BIN3,"\u2022":J.BIN4,"\u2026":J.INNER,"\u2043":J.BIN4,"\u2044":J.TALLBIN,"\u2061":J.NONE,"\u2062":J.NONE,"\u2063":[0,0,F.NONE,{linebreakstyle:"after",separator:!0}],"\u2064":J.NONE,"\u20d7":J.ACCENT,\u2111:J.ORD,\u2113:J.ORD,\u2118:J.ORD,\u211c:J.ORD,"\u2190":J.WIDEREL,"\u2191":J.RELSTRETCH,"\u2192":J.WIDEREL,"\u2193":J.RELSTRETCH,"\u2194":J.WIDEREL,"\u2195":J.RELSTRETCH,"\u2196":J.RELSTRETCH,"\u2197":J.RELSTRETCH,"\u2198":J.RELSTRETCH,"\u2199":J.RELSTRETCH,"\u219a":J.RELACCENT,"\u219b":J.RELACCENT,"\u219c":J.WIDEREL,"\u219d":J.WIDEREL,"\u219e":J.WIDEREL,"\u219f":J.WIDEREL,"\u21a0":J.WIDEREL,"\u21a1":J.RELSTRETCH,"\u21a2":J.WIDEREL,"\u21a3":J.WIDEREL,"\u21a4":J.WIDEREL,"\u21a5":J.RELSTRETCH,"\u21a6":J.WIDEREL,"\u21a7":J.RELSTRETCH,"\u21a8":J.RELSTRETCH,"\u21a9":J.WIDEREL,"\u21aa":J.WIDEREL,"\u21ab":J.WIDEREL,"\u21ac":J.WIDEREL,"\u21ad":J.WIDEREL,"\u21ae":J.RELACCENT,"\u21af":J.RELSTRETCH,"\u21b0":J.RELSTRETCH,"\u21b1":J.RELSTRETCH,"\u21b2":J.RELSTRETCH,"\u21b3":J.RELSTRETCH,"\u21b4":J.RELSTRETCH,"\u21b5":J.RELSTRETCH,"\u21b6":J.RELACCENT,"\u21b7":J.RELACCENT,"\u21b8":J.REL,"\u21b9":J.WIDEREL,"\u21ba":J.REL,"\u21bb":J.REL,"\u21bc":J.WIDEREL,"\u21bd":J.WIDEREL,"\u21be":J.RELSTRETCH,"\u21bf":J.RELSTRETCH,"\u21c0":J.WIDEREL,"\u21c1":J.WIDEREL,"\u21c2":J.RELSTRETCH,"\u21c3":J.RELSTRETCH,"\u21c4":J.WIDEREL,"\u21c5":J.RELSTRETCH,"\u21c6":J.WIDEREL,"\u21c7":J.WIDEREL,"\u21c8":J.RELSTRETCH,"\u21c9":J.WIDEREL,"\u21ca":J.RELSTRETCH,"\u21cb":J.WIDEREL,"\u21cc":J.WIDEREL,"\u21cd":J.RELACCENT,"\u21ce":J.RELACCENT,"\u21cf":J.RELACCENT,"\u21d0":J.WIDEREL,"\u21d1":J.RELSTRETCH,"\u21d2":J.WIDEREL,"\u21d3":J.RELSTRETCH,"\u21d4":J.WIDEREL,"\u21d5":J.RELSTRETCH,"\u21d6":J.RELSTRETCH,"\u21d7":J.RELSTRETCH,"\u21d8":J.RELSTRETCH,"\u21d9":J.RELSTRETCH,"\u21da":J.WIDEREL,"\u21db":J.WIDEREL,"\u21dc":J.WIDEREL,"\u21dd":J.WIDEREL,"\u21de":J.REL,"\u21df":J.REL,"\u21e0":J.WIDEREL,"\u21e1":J.RELSTRETCH,"\u21e2":J.WIDEREL,"\u21e3":J.RELSTRETCH,"\u21e4":J.WIDEREL,"\u21e5":J.WIDEREL,"\u21e6":J.WIDEREL,"\u21e7":J.RELSTRETCH,"\u21e8":J.WIDEREL,"\u21e9":J.RELSTRETCH,"\u21ea":J.RELSTRETCH,"\u21eb":J.RELSTRETCH,"\u21ec":J.RELSTRETCH,"\u21ed":J.RELSTRETCH,"\u21ee":J.RELSTRETCH,"\u21ef":J.RELSTRETCH,"\u21f0":J.WIDEREL,"\u21f1":J.REL,"\u21f2":J.REL,"\u21f3":J.RELSTRETCH,"\u21f4":J.RELACCENT,"\u21f5":J.RELSTRETCH,"\u21f6":J.WIDEREL,"\u21f7":J.RELACCENT,"\u21f8":J.RELACCENT,"\u21f9":J.RELACCENT,"\u21fa":J.RELACCENT,"\u21fb":J.RELACCENT,"\u21fc":J.RELACCENT,"\u21fd":J.WIDEREL,"\u21fe":J.WIDEREL,"\u21ff":J.WIDEREL,"\u2201":$(1,2,F.ORD),"\u2205":J.ORD,"\u2206":J.BIN3,"\u2208":J.REL,"\u2209":J.REL,"\u220a":J.REL,"\u220b":J.REL,"\u220c":J.REL,"\u220d":J.REL,"\u220e":J.BIN3,"\u2212":J.BIN4,"\u2213":J.BIN4,"\u2214":J.BIN4,"\u2215":J.TALLBIN,"\u2216":J.BIN4,"\u2217":J.BIN4,"\u2218":J.BIN4,"\u2219":J.BIN4,"\u221d":J.REL,"\u221e":J.ORD,"\u221f":J.REL,"\u2223":J.REL,"\u2224":J.REL,"\u2225":J.REL,"\u2226":J.REL,"\u2227":J.BIN4,"\u2228":J.BIN4,"\u2229":J.BIN4,"\u222a":J.BIN4,"\u2234":J.REL,"\u2235":J.REL,"\u2236":J.REL,"\u2237":J.REL,"\u2238":J.BIN4,"\u2239":J.REL,"\u223a":J.BIN4,"\u223b":J.REL,"\u223c":J.REL,"\u223d":J.REL,"\u223d\u0331":J.BIN3,"\u223e":J.REL,"\u223f":J.BIN3,"\u2240":J.BIN4,"\u2241":J.REL,"\u2242":J.REL,"\u2242\u0338":J.REL,"\u2243":J.REL,"\u2244":J.REL,"\u2245":J.REL,"\u2246":J.REL,"\u2247":J.REL,"\u2248":J.REL,"\u2249":J.REL,"\u224a":J.REL,"\u224b":J.REL,"\u224c":J.REL,"\u224d":J.REL,"\u224e":J.REL,"\u224e\u0338":J.REL,"\u224f":J.REL,"\u224f\u0338":J.REL,"\u2250":J.REL,"\u2251":J.REL,"\u2252":J.REL,"\u2253":J.REL,"\u2254":J.REL,"\u2255":J.REL,"\u2256":J.REL,"\u2257":J.REL,"\u2258":J.REL,"\u2259":J.REL,"\u225a":J.REL,"\u225b":J.REL,"\u225c":J.REL,"\u225d":J.REL,"\u225e":J.REL,"\u225f":J.REL,"\u2260":J.REL,"\u2261":J.REL,"\u2262":J.REL,"\u2263":J.REL,"\u2264":J.REL,"\u2265":J.REL,"\u2266":J.REL,"\u2266\u0338":J.REL,"\u2267":J.REL,"\u2268":J.REL,"\u2269":J.REL,"\u226a":J.REL,"\u226a\u0338":J.REL,"\u226b":J.REL,"\u226b\u0338":J.REL,"\u226c":J.REL,"\u226d":J.REL,"\u226e":J.REL,"\u226f":J.REL,"\u2270":J.REL,"\u2271":J.REL,"\u2272":J.REL,"\u2273":J.REL,"\u2274":J.REL,"\u2275":J.REL,"\u2276":J.REL,"\u2277":J.REL,"\u2278":J.REL,"\u2279":J.REL,"\u227a":J.REL,"\u227b":J.REL,"\u227c":J.REL,"\u227d":J.REL,"\u227e":J.REL,"\u227f":J.REL,"\u227f\u0338":J.REL,"\u2280":J.REL,"\u2281":J.REL,"\u2282":J.REL,"\u2282\u20d2":J.REL,"\u2283":J.REL,"\u2283\u20d2":J.REL,"\u2284":J.REL,"\u2285":J.REL,"\u2286":J.REL,"\u2287":J.REL,"\u2288":J.REL,"\u2289":J.REL,"\u228a":J.REL,"\u228b":J.REL,"\u228c":J.BIN4,"\u228d":J.BIN4,"\u228e":J.BIN4,"\u228f":J.REL,"\u228f\u0338":J.REL,"\u2290":J.REL,"\u2290\u0338":J.REL,"\u2291":J.REL,"\u2292":J.REL,"\u2293":J.BIN4,"\u2294":J.BIN4,"\u2295":J.BIN4,"\u2296":J.BIN4,"\u2297":J.BIN4,"\u2298":J.BIN4,"\u2299":J.BIN4,"\u229a":J.BIN4,"\u229b":J.BIN4,"\u229c":J.BIN4,"\u229d":J.BIN4,"\u229e":J.BIN4,"\u229f":J.BIN4,"\u22a0":J.BIN4,"\u22a1":J.BIN4,"\u22a2":J.REL,"\u22a3":J.REL,"\u22a4":J.ORD55,"\u22a5":J.REL,"\u22a6":J.REL,"\u22a7":J.REL,"\u22a8":J.REL,"\u22a9":J.REL,"\u22aa":J.REL,"\u22ab":J.REL,"\u22ac":J.REL,"\u22ad":J.REL,"\u22ae":J.REL,"\u22af":J.REL,"\u22b0":J.REL,"\u22b1":J.REL,"\u22b2":J.REL,"\u22b3":J.REL,"\u22b4":J.REL,"\u22b5":J.REL,"\u22b6":J.REL,"\u22b7":J.REL,"\u22b8":J.REL,"\u22b9":J.REL,"\u22ba":J.BIN4,"\u22bb":J.BIN4,"\u22bc":J.BIN4,"\u22bd":J.BIN4,"\u22be":J.BIN3,"\u22bf":J.BIN3,"\u22c4":J.BIN4,"\u22c5":J.BIN4,"\u22c6":J.BIN4,"\u22c7":J.BIN4,"\u22c8":J.REL,"\u22c9":J.BIN4,"\u22ca":J.BIN4,"\u22cb":J.BIN4,"\u22cc":J.BIN4,"\u22cd":J.REL,"\u22ce":J.BIN4,"\u22cf":J.BIN4,"\u22d0":J.REL,"\u22d1":J.REL,"\u22d2":J.BIN4,"\u22d3":J.BIN4,"\u22d4":J.REL,"\u22d5":J.REL,"\u22d6":J.REL,"\u22d7":J.REL,"\u22d8":J.REL,"\u22d9":J.REL,"\u22da":J.REL,"\u22db":J.REL,"\u22dc":J.REL,"\u22dd":J.REL,"\u22de":J.REL,"\u22df":J.REL,"\u22e0":J.REL,"\u22e1":J.REL,"\u22e2":J.REL,"\u22e3":J.REL,"\u22e4":J.REL,"\u22e5":J.REL,"\u22e6":J.REL,"\u22e7":J.REL,"\u22e8":J.REL,"\u22e9":J.REL,"\u22ea":J.REL,"\u22eb":J.REL,"\u22ec":J.REL,"\u22ed":J.REL,"\u22ee":J.ORD55,"\u22ef":J.INNER,"\u22f0":J.REL,"\u22f1":[5,5,F.INNER,null],"\u22f2":J.REL,"\u22f3":J.REL,"\u22f4":J.REL,"\u22f5":J.REL,"\u22f6":J.REL,"\u22f7":J.REL,"\u22f8":J.REL,"\u22f9":J.REL,"\u22fa":J.REL,"\u22fb":J.REL,"\u22fc":J.REL,"\u22fd":J.REL,"\u22fe":J.REL,"\u22ff":J.REL,"\u2305":J.BIN3,"\u2306":J.BIN3,"\u2322":J.REL4,"\u2323":J.REL4,"\u2329":J.OPEN,"\u232a":J.CLOSE,"\u23aa":J.ORD,"\u23af":[0,0,F.ORD,{stretchy:!0}],"\u23b0":J.OPEN,"\u23b1":J.CLOSE,"\u2500":J.ORD,"\u25b3":J.BIN4,"\u25b5":J.BIN4,"\u25b9":J.BIN4,"\u25bd":J.BIN4,"\u25bf":J.BIN4,"\u25c3":J.BIN4,"\u25ef":J.BIN3,"\u2660":J.ORD,"\u2661":J.ORD,"\u2662":J.ORD,"\u2663":J.ORD,"\u2758":J.REL,"\u27f0":J.RELSTRETCH,"\u27f1":J.RELSTRETCH,"\u27f5":J.WIDEREL,"\u27f6":J.WIDEREL,"\u27f7":J.WIDEREL,"\u27f8":J.WIDEREL,"\u27f9":J.WIDEREL,"\u27fa":J.WIDEREL,"\u27fb":J.WIDEREL,"\u27fc":J.WIDEREL,"\u27fd":J.WIDEREL,"\u27fe":J.WIDEREL,"\u27ff":J.WIDEREL,"\u2900":J.RELACCENT,"\u2901":J.RELACCENT,"\u2902":J.RELACCENT,"\u2903":J.RELACCENT,"\u2904":J.RELACCENT,"\u2905":J.RELACCENT,"\u2906":J.RELACCENT,"\u2907":J.RELACCENT,"\u2908":J.REL,"\u2909":J.REL,"\u290a":J.RELSTRETCH,"\u290b":J.RELSTRETCH,"\u290c":J.WIDEREL,"\u290d":J.WIDEREL,"\u290e":J.WIDEREL,"\u290f":J.WIDEREL,"\u2910":J.WIDEREL,"\u2911":J.RELACCENT,"\u2912":J.RELSTRETCH,"\u2913":J.RELSTRETCH,"\u2914":J.RELACCENT,"\u2915":J.RELACCENT,"\u2916":J.RELACCENT,"\u2917":J.RELACCENT,"\u2918":J.RELACCENT,"\u2919":J.RELACCENT,"\u291a":J.RELACCENT,"\u291b":J.RELACCENT,"\u291c":J.RELACCENT,"\u291d":J.RELACCENT,"\u291e":J.RELACCENT,"\u291f":J.RELACCENT,"\u2920":J.RELACCENT,"\u2921":J.RELSTRETCH,"\u2922":J.RELSTRETCH,"\u2923":J.REL,"\u2924":J.REL,"\u2925":J.REL,"\u2926":J.REL,"\u2927":J.REL,"\u2928":J.REL,"\u2929":J.REL,"\u292a":J.REL,"\u292b":J.REL,"\u292c":J.REL,"\u292d":J.REL,"\u292e":J.REL,"\u292f":J.REL,"\u2930":J.REL,"\u2931":J.REL,"\u2932":J.REL,"\u2933":J.RELACCENT,"\u2934":J.REL,"\u2935":J.REL,"\u2936":J.REL,"\u2937":J.REL,"\u2938":J.REL,"\u2939":J.REL,"\u293a":J.RELACCENT,"\u293b":J.RELACCENT,"\u293c":J.RELACCENT,"\u293d":J.RELACCENT,"\u293e":J.REL,"\u293f":J.REL,"\u2940":J.REL,"\u2941":J.REL,"\u2942":J.RELACCENT,"\u2943":J.RELACCENT,"\u2944":J.RELACCENT,"\u2945":J.RELACCENT,"\u2946":J.RELACCENT,"\u2947":J.RELACCENT,"\u2948":J.RELACCENT,"\u2949":J.REL,"\u294a":J.RELACCENT,"\u294b":J.RELACCENT,"\u294c":J.REL,"\u294d":J.REL,"\u294e":J.WIDEREL,"\u294f":J.RELSTRETCH,"\u2950":J.WIDEREL,"\u2951":J.RELSTRETCH,"\u2952":J.WIDEREL,"\u2953":J.WIDEREL,"\u2954":J.RELSTRETCH,"\u2955":J.RELSTRETCH,"\u2956":J.RELSTRETCH,"\u2957":J.RELSTRETCH,"\u2958":J.RELSTRETCH,"\u2959":J.RELSTRETCH,"\u295a":J.WIDEREL,"\u295b":J.WIDEREL,"\u295c":J.RELSTRETCH,"\u295d":J.RELSTRETCH,"\u295e":J.WIDEREL,"\u295f":J.WIDEREL,"\u2960":J.RELSTRETCH,"\u2961":J.RELSTRETCH,"\u2962":J.RELACCENT,"\u2963":J.REL,"\u2964":J.RELACCENT,"\u2965":J.REL,"\u2966":J.RELACCENT,"\u2967":J.RELACCENT,"\u2968":J.RELACCENT,"\u2969":J.RELACCENT,"\u296a":J.RELACCENT,"\u296b":J.RELACCENT,"\u296c":J.RELACCENT,"\u296d":J.RELACCENT,"\u296e":J.RELSTRETCH,"\u296f":J.RELSTRETCH,"\u2970":J.RELACCENT,"\u2971":J.RELACCENT,"\u2972":J.RELACCENT,"\u2973":J.RELACCENT,"\u2974":J.RELACCENT,"\u2975":J.RELACCENT,"\u2976":J.RELACCENT,"\u2977":J.RELACCENT,"\u2978":J.RELACCENT,"\u2979":J.RELACCENT,"\u297a":J.RELACCENT,"\u297b":J.RELACCENT,"\u297c":J.RELACCENT,"\u297d":J.RELACCENT,"\u297e":J.REL,"\u297f":J.REL,"\u2981":J.BIN3,"\u2982":J.BIN3,"\u2999":J.BIN3,"\u299a":J.BIN3,"\u299b":J.BIN3,"\u299c":J.BIN3,"\u299d":J.BIN3,"\u299e":J.BIN3,"\u299f":J.BIN3,"\u29a0":J.BIN3,"\u29a1":J.BIN3,"\u29a2":J.BIN3,"\u29a3":J.BIN3,"\u29a4":J.BIN3,"\u29a5":J.BIN3,"\u29a6":J.BIN3,"\u29a7":J.BIN3,"\u29a8":J.BIN3,"\u29a9":J.BIN3,"\u29aa":J.BIN3,"\u29ab":J.BIN3,"\u29ac":J.BIN3,"\u29ad":J.BIN3,"\u29ae":J.BIN3,"\u29af":J.BIN3,"\u29b0":J.BIN3,"\u29b1":J.BIN3,"\u29b2":J.BIN3,"\u29b3":J.BIN3,"\u29b4":J.BIN3,"\u29b5":J.BIN3,"\u29b6":J.BIN4,"\u29b7":J.BIN4,"\u29b8":J.BIN4,"\u29b9":J.BIN4,"\u29ba":J.BIN4,"\u29bb":J.BIN4,"\u29bc":J.BIN4,"\u29bd":J.BIN4,"\u29be":J.BIN4,"\u29bf":J.BIN4,"\u29c0":J.REL,"\u29c1":J.REL,"\u29c2":J.BIN3,"\u29c3":J.BIN3,"\u29c4":J.BIN4,"\u29c5":J.BIN4,"\u29c6":J.BIN4,"\u29c7":J.BIN4,"\u29c8":J.BIN4,"\u29c9":J.BIN3,"\u29ca":J.BIN3,"\u29cb":J.BIN3,"\u29cc":J.BIN3,"\u29cd":J.BIN3,"\u29ce":J.REL,"\u29cf":J.REL,"\u29cf\u0338":J.REL,"\u29d0":J.REL,"\u29d0\u0338":J.REL,"\u29d1":J.REL,"\u29d2":J.REL,"\u29d3":J.REL,"\u29d4":J.REL,"\u29d5":J.REL,"\u29d6":J.BIN4,"\u29d7":J.BIN4,"\u29d8":J.BIN3,"\u29d9":J.BIN3,"\u29db":J.BIN3,"\u29dc":J.BIN3,"\u29dd":J.BIN3,"\u29de":J.REL,"\u29df":J.BIN3,"\u29e0":J.BIN3,"\u29e1":J.REL,"\u29e2":J.BIN4,"\u29e3":J.REL,"\u29e4":J.REL,"\u29e5":J.REL,"\u29e6":J.REL,"\u29e7":J.BIN3,"\u29e8":J.BIN3,"\u29e9":J.BIN3,"\u29ea":J.BIN3,"\u29eb":J.BIN3,"\u29ec":J.BIN3,"\u29ed":J.BIN3,"\u29ee":J.BIN3,"\u29ef":J.BIN3,"\u29f0":J.BIN3,"\u29f1":J.BIN3,"\u29f2":J.BIN3,"\u29f3":J.BIN3,"\u29f4":J.REL,"\u29f5":J.BIN4,"\u29f6":J.BIN4,"\u29f7":J.BIN4,"\u29f8":J.BIN3,"\u29f9":J.BIN3,"\u29fa":J.BIN3,"\u29fb":J.BIN3,"\u29fe":J.BIN4,"\u29ff":J.BIN4,"\u2a1d":J.BIN3,"\u2a1e":J.BIN3,"\u2a1f":J.BIN3,"\u2a20":J.BIN3,"\u2a21":J.BIN3,"\u2a22":J.BIN4,"\u2a23":J.BIN4,"\u2a24":J.BIN4,"\u2a25":J.BIN4,"\u2a26":J.BIN4,"\u2a27":J.BIN4,"\u2a28":J.BIN4,"\u2a29":J.BIN4,"\u2a2a":J.BIN4,"\u2a2b":J.BIN4,"\u2a2c":J.BIN4,"\u2a2d":J.BIN4,"\u2a2e":J.BIN4,"\u2a2f":J.BIN4,"\u2a30":J.BIN4,"\u2a31":J.BIN4,"\u2a32":J.BIN4,"\u2a33":J.BIN4,"\u2a34":J.BIN4,"\u2a35":J.BIN4,"\u2a36":J.BIN4,"\u2a37":J.BIN4,"\u2a38":J.BIN4,"\u2a39":J.BIN4,"\u2a3a":J.BIN4,"\u2a3b":J.BIN4,"\u2a3c":J.BIN4,"\u2a3d":J.BIN4,"\u2a3e":J.BIN4,"\u2a3f":J.BIN4,"\u2a40":J.BIN4,"\u2a41":J.BIN4,"\u2a42":J.BIN4,"\u2a43":J.BIN4,"\u2a44":J.BIN4,"\u2a45":J.BIN4,"\u2a46":J.BIN4,"\u2a47":J.BIN4,"\u2a48":J.BIN4,"\u2a49":J.BIN4,"\u2a4a":J.BIN4,"\u2a4b":J.BIN4,"\u2a4c":J.BIN4,"\u2a4d":J.BIN4,"\u2a4e":J.BIN4,"\u2a4f":J.BIN4,"\u2a50":J.BIN4,"\u2a51":J.BIN4,"\u2a52":J.BIN4,"\u2a53":J.BIN4,"\u2a54":J.BIN4,"\u2a55":J.BIN4,"\u2a56":J.BIN4,"\u2a57":J.BIN4,"\u2a58":J.BIN4,"\u2a59":J.REL,"\u2a5a":J.BIN4,"\u2a5b":J.BIN4,"\u2a5c":J.BIN4,"\u2a5d":J.BIN4,"\u2a5e":J.BIN4,"\u2a5f":J.BIN4,"\u2a60":J.BIN4,"\u2a61":J.BIN4,"\u2a62":J.BIN4,"\u2a63":J.BIN4,"\u2a64":J.BIN4,"\u2a65":J.BIN4,"\u2a66":J.REL,"\u2a67":J.REL,"\u2a68":J.REL,"\u2a69":J.REL,"\u2a6a":J.REL,"\u2a6b":J.REL,"\u2a6c":J.REL,"\u2a6d":J.REL,"\u2a6e":J.REL,"\u2a6f":J.REL,"\u2a70":J.REL,"\u2a71":J.BIN4,"\u2a72":J.BIN4,"\u2a73":J.REL,"\u2a74":J.REL,"\u2a75":J.REL,"\u2a76":J.REL,"\u2a77":J.REL,"\u2a78":J.REL,"\u2a79":J.REL,"\u2a7a":J.REL,"\u2a7b":J.REL,"\u2a7c":J.REL,"\u2a7d":J.REL,"\u2a7d\u0338":J.REL,"\u2a7e":J.REL,"\u2a7e\u0338":J.REL,"\u2a7f":J.REL,"\u2a80":J.REL,"\u2a81":J.REL,"\u2a82":J.REL,"\u2a83":J.REL,"\u2a84":J.REL,"\u2a85":J.REL,"\u2a86":J.REL,"\u2a87":J.REL,"\u2a88":J.REL,"\u2a89":J.REL,"\u2a8a":J.REL,"\u2a8b":J.REL,"\u2a8c":J.REL,"\u2a8d":J.REL,"\u2a8e":J.REL,"\u2a8f":J.REL,"\u2a90":J.REL,"\u2a91":J.REL,"\u2a92":J.REL,"\u2a93":J.REL,"\u2a94":J.REL,"\u2a95":J.REL,"\u2a96":J.REL,"\u2a97":J.REL,"\u2a98":J.REL,"\u2a99":J.REL,"\u2a9a":J.REL,"\u2a9b":J.REL,"\u2a9c":J.REL,"\u2a9d":J.REL,"\u2a9e":J.REL,"\u2a9f":J.REL,"\u2aa0":J.REL,"\u2aa1":J.REL,"\u2aa1\u0338":J.REL,"\u2aa2":J.REL,"\u2aa2\u0338":J.REL,"\u2aa3":J.REL,"\u2aa4":J.REL,"\u2aa5":J.REL,"\u2aa6":J.REL,"\u2aa7":J.REL,"\u2aa8":J.REL,"\u2aa9":J.REL,"\u2aaa":J.REL,"\u2aab":J.REL,"\u2aac":J.REL,"\u2aad":J.REL,"\u2aae":J.REL,"\u2aaf":J.REL,"\u2aaf\u0338":J.REL,"\u2ab0":J.REL,"\u2ab0\u0338":J.REL,"\u2ab1":J.REL,"\u2ab2":J.REL,"\u2ab3":J.REL,"\u2ab4":J.REL,"\u2ab5":J.REL,"\u2ab6":J.REL,"\u2ab7":J.REL,"\u2ab8":J.REL,"\u2ab9":J.REL,"\u2aba":J.REL,"\u2abb":J.REL,"\u2abc":J.REL,"\u2abd":J.REL,"\u2abe":J.REL,"\u2abf":J.REL,"\u2ac0":J.REL,"\u2ac1":J.REL,"\u2ac2":J.REL,"\u2ac3":J.REL,"\u2ac4":J.REL,"\u2ac5":J.REL,"\u2ac6":J.REL,"\u2ac7":J.REL,"\u2ac8":J.REL,"\u2ac9":J.REL,"\u2aca":J.REL,"\u2acb":J.REL,"\u2acc":J.REL,"\u2acd":J.REL,"\u2ace":J.REL,"\u2acf":J.REL,"\u2ad0":J.REL,"\u2ad1":J.REL,"\u2ad2":J.REL,"\u2ad3":J.REL,"\u2ad4":J.REL,"\u2ad5":J.REL,"\u2ad6":J.REL,"\u2ad7":J.REL,"\u2ad8":J.REL,"\u2ad9":J.REL,"\u2ada":J.REL,"\u2adb":J.REL,"\u2add":J.REL,"\u2add\u0338":J.REL,"\u2ade":J.REL,"\u2adf":J.REL,"\u2ae0":J.REL,"\u2ae1":J.REL,"\u2ae2":J.REL,"\u2ae3":J.REL,"\u2ae4":J.REL,"\u2ae5":J.REL,"\u2ae6":J.REL,"\u2ae7":J.REL,"\u2ae8":J.REL,"\u2ae9":J.REL,"\u2aea":J.REL,"\u2aeb":J.REL,"\u2aec":J.REL,"\u2aed":J.REL,"\u2aee":J.REL,"\u2aef":J.REL,"\u2af0":J.REL,"\u2af1":J.REL,"\u2af2":J.REL,"\u2af3":J.REL,"\u2af4":J.BIN4,"\u2af5":J.BIN4,"\u2af6":J.BIN4,"\u2af7":J.REL,"\u2af8":J.REL,"\u2af9":J.REL,"\u2afa":J.REL,"\u2afb":J.BIN4,"\u2afd":J.BIN4,"\u2afe":J.BIN3,"\u2b45":J.RELSTRETCH,"\u2b46":J.RELSTRETCH,"\u3008":J.OPEN,"\u3009":J.CLOSE,"\ufe37":J.WIDEACCENT,"\ufe38":J.WIDEACCENT}};tt.infix["^"]=J.WIDEREL,tt.infix._=J.WIDEREL,tt.infix["\u2adc"]=J.REL;class et extends X{constructor(){super(...arguments),this._texClass=null,this.lspace=5/18,this.rspace=5/18}get texClass(){return null===this._texClass?this.getOperatorDef(this.getText())[2]:this._texClass}set texClass(t){this._texClass=t}get kind(){return"mo"}get isEmbellished(){return!0}coreParent(){let t=null,e=this;const r=this.factory.getNodeClass("math");for(;e&&e.isEmbellished&&e.coreMO()===this&&!(e instanceof r);)t=e,e=e.parent;return t||this}coreText(t){if(!t)return"";if(t.isEmbellished)return t.coreMO().getText();for(;((t.isKind("mrow")||t.isKind("TeXAtom")||t.isKind("mstyle")||t.isKind("mphantom"))&&1===t.childNodes.length||t.isKind("munderover"))&&t.childNodes[0];)t=t.childNodes[0];return t.isToken?t.getText():""}hasSpacingAttributes(){return this.attributes.isSet("lspace")||this.attributes.isSet("rspace")}get isAccent(){let t=!1;const e=this.coreParent().parent;if(e){const r=e.isKind("mover")?e.childNodes[e.over].coreMO()?"accent":"":e.isKind("munder")?e.childNodes[e.under].coreMO()?"accentunder":"":e.isKind("munderover")?this===e.childNodes[e.over].coreMO()?"accent":this===e.childNodes[e.under].coreMO()?"accentunder":"":"";if(r){t=void 0!==e.attributes.getExplicit(r)?t:this.attributes.get("accent")}}return t}setTeXclass(t){const{form:e,fence:r}=this.attributes.getList("form","fence");return void 0===this.getProperty("texClass")&&this.hasSpacingAttributes()?null:(r&&this.texClass===F.REL&&("prefix"===e&&(this.texClass=F.OPEN),"postfix"===e&&(this.texClass=F.CLOSE)),this.adjustTeXclass(t))}adjustTeXclass(t){const e=this.texClass;let r=this.prevClass;if(e===F.NONE)return t;if(t?(!t.getProperty("autoOP")||e!==F.BIN&&e!==F.REL||(r=t.texClass=F.ORD),r=this.prevClass=t.texClass||F.ORD,this.prevLevel=this.attributes.getInherited("scriptlevel")):r=this.prevClass=F.NONE,e!==F.BIN||r!==F.NONE&&r!==F.BIN&&r!==F.OP&&r!==F.REL&&r!==F.OPEN&&r!==F.PUNCT)if(r!==F.BIN||e!==F.REL&&e!==F.CLOSE&&e!==F.PUNCT){if(e===F.BIN){let t=null,e=this.parent;for(;e&&e.parent&&e.isEmbellished&&(1===e.childNodes.length||!e.isKind("mrow")&&e.core()===t);)t=e,e=e.parent;t=t||this,e.childNodes[e.childNodes.length-1]===t&&(this.texClass=F.ORD)}}else t.texClass=this.prevClass=F.ORD;else this.texClass=F.ORD;return this}setInheritedAttributes(t={},e=!1,r=0,n=!1){super.setInheritedAttributes(t,e,r,n);const s=this.getText();this.checkOperatorTable(s),this.checkPseudoScripts(s),this.checkPrimes(s),this.checkMathAccent(s)}getOperatorDef(t){const[e,r,n]=this.handleExplicitForm(this.getForms());this.attributes.setInherited("form",e);const s=this.constructor,i=s.OPTABLE,o=i[e][t]||i[r][t]||i[n][t];if(o)return o;this.setProperty("noDictDef",!0);const a=this.attributes.get("movablelimits");if((!!t.match(s.opPattern)||a)&&void 0===this.getProperty("texClass"))return $(1,2,F.OP);const c=Z(t),[l,h]=s.MMLSPACING[c[2]];return $(l,h,c[2])}checkOperatorTable(t){const e=this.getOperatorDef(t);void 0===this.getProperty("texClass")&&(this.texClass=e[2]);for(const t of Object.keys(e[3]||{}))this.attributes.setInherited(t,e[3][t]);this.lspace=((e[0]||-1)+1)/18,this.rspace=((e[1]||-1)+1)/18}getForms(){let t=null,e=this.parent,r=this.Parent;for(;r&&r.isEmbellished;)t=e,e=r.parent,r=r.Parent;if(t=t||this,e&&e.isKind("mrow")&&1!==e.nonSpaceLength()){if(e.firstNonSpace()===t)return["prefix","infix","postfix"];if(e.lastNonSpace()===t)return["postfix","infix","prefix"]}return["infix","prefix","postfix"]}handleExplicitForm(t){if(this.attributes.isSet("form")){const e=this.attributes.get("form");t=[e].concat(t.filter((t=>t!==e)))}return t}checkPseudoScripts(t){const e=this.constructor.pseudoScripts;if(!t.match(e))return;const r=this.coreParent().Parent,n=!r||!(r.isKind("msubsup")&&!r.isKind("msub"));this.setProperty("pseudoscript",n),n&&(this.attributes.setInherited("lspace",0),this.attributes.setInherited("rspace",0))}checkPrimes(t){const e=this.constructor.primes;if(!t.match(e))return;const r=this.constructor.remapPrimes,n=(s=(i=t,Array.from(i).map((t=>t.codePointAt(0)))).map((t=>r[t])),String.fromCodePoint(...s));var s,i;this.setProperty("primes",n)}checkMathAccent(t){const e=this.Parent;if(void 0!==this.getProperty("mathaccent")||!e||!e.isKind("munderover"))return;const[r,n,s]=e.childNodes;if(r.isEmbellished&&r.coreMO()===this)return;const i=!(!n||!n.isEmbellished||n.coreMO()!==this),o=!(!s||!s.isEmbellished||n.coreMO()!==this);(i||o)&&(this.isMathAccent(t)?this.setProperty("mathaccent",!0):this.isMathAccentWithWidth(t)&&this.setProperty("mathaccent",!1))}isMathAccent(t=this.getText()){const e=this.constructor.mathaccents;return!!t.match(e)}isMathAccentWithWidth(t=this.getText()){const e=this.constructor.mathaccentsWithWidth;return!!t.match(e)}}et.defaults=Object.assign(Object.assign({},X.defaults),{form:"infix",fence:!1,separator:!1,lspace:"thickmathspace",rspace:"thickmathspace",stretchy:!1,symmetric:!1,maxsize:"infinity",minsize:"0em",largeop:!1,movablelimits:!1,accent:!1,linebreak:"auto",lineleading:"100%",linebreakstyle:"before",indentalign:"auto",indentshift:"0",indenttarget:"",indentalignfirst:"indentalign",indentshiftfirst:"indentshift",indentalignlast:"indentalign",indentshiftlast:"indentshift"}),et.MMLSPACING=[[0,0],[1,2],[3,3],[4,4],[0,0],[0,0],[0,3]],et.OPTABLE=tt,et.pseudoScripts=new RegExp(["^[\"'*`","\xaa","\xb0","\xb2-\xb4","\xb9","\xba","\u2018-\u201f","\u2032-\u2037\u2057","\u2070\u2071","\u2074-\u207f","\u2080-\u208e","]+$"].join("")),et.primes=new RegExp(["^[\"'","\u2018-\u201f","]+$"].join("")),et.opPattern=/^[a-zA-Z]{2,}$/,et.remapPrimes={34:8243,39:8242,8216:8245,8217:8242,8218:8242,8219:8245,8220:8246,8221:8243,8222:8243,8223:8246},et.mathaccents=new RegExp(["^[","\xb4\u0301\u02ca","`\u0300\u02cb","\xa8\u0308","~\u0303\u02dc","\xaf\u0304\u02c9","\u02d8\u0306","\u02c7\u030c","^\u0302\u02c6","\u20d0\u20d1","\u20d6\u20d7\u20e1","\u02d9\u0307","\u02da\u030a","\u20db","\u20dc","]$"].join("")),et.mathaccentsWithWidth=new RegExp(["^[","\u2190\u2192\u2194","\u23dc\u23dd","\u23de\u23df","]$"].join(""));const rt={attrs:new Set(["autoOP","fnOP","movesupsub","subsupOK","texprimestyle","useHeight","variantForm","withDelims","mathaccent","open","close"]),createEntity:t=>String.fromCodePoint(parseInt(t,16)),getChildren:t=>t.childNodes,getText:t=>t.getText(),appendChildren(t,e){for(const r of e)t.appendChild(r)},setAttribute(t,e,r){t.attributes.set(e,r)},setProperty(t,e,r){t.setProperty(e,r)},setProperties(t,e){for(const r of Object.keys(e)){const n=e[r];"texClass"===r?(t.texClass=n,t.setProperty(r,n)):"movablelimits"===r?(t.setProperty("movablelimits",n),(t.isKind("mo")||t.isKind("mstyle"))&&t.attributes.set("movablelimits",n)):"inferred"===r||(rt.attrs.has(r)?t.setProperty(r,n):t.attributes.set(r,n))}},getProperty:(t,e)=>t.getProperty(e),getAttribute:(t,e)=>t.attributes.get(e),removeAttribute(t,e){t.attributes.unset(e)},removeProperties(t,...e){t.removeProperty(...e)},getChildAt:(t,e)=>t.childNodes[e],setChild(t,e,r){t.childNodes[e]=r,r&&(r.parent=t)},copyChildren(t,e){const r=t.childNodes;for(let t=0;t<r.length;t++)this.setChild(e,t,r[t])},copyAttributes(t,e){e.attributes=t.attributes,this.setProperties(e,t.getAllProperties())},isType:(t,e)=>t.isKind(e),isEmbellished:t=>t.isEmbellished,getTexClass:t=>t.texClass,getCoreMO:t=>t.coreMO(),isNode:t=>t instanceof j||t instanceof K,isInferred:t=>t.isInferred,getForm(t){if(!t.isKind("mo"))return null;const e=t,r=e.getForms();for(const t of r){const r=this.getOp(e,t);if(r)return r}return null},getOp:(t,e="infix")=>et.OPTABLE[e][t.getText()]||null,getMoAttribute(t,e){var r,n;if(!t.attributes.isSet(e))for(const s of["infix","postfix","prefix"]){const i=null===(n=null===(r=this.getOp(t,s))||void 0===r?void 0:r[3])||void 0===n?void 0:n[e];if(void 0!==i)return i}return t.attributes.get(e)}},nt=rt;function st(t,e,r){const n=e.attributes,s=r.attributes;t.forEach((t=>{const e=s.getExplicit(t);null!=e&&n.set(t,e)}))}function it(t,e){const r=(t,e)=>t.getExplicitNames().filter((r=>r!==e&&("stretchy"!==r||t.getExplicit("stretchy"))&&"data-latex"!==r&&"data-latex-item"!==r)),n=t.attributes,s=e.attributes,i=r(n,"lspace"),o=r(s,"rspace");if(i.length!==o.length)return!1;for(const t of i)if(n.getExplicit(t)!==s.getExplicit(t))return!1;return!0}function ot(t,e,r){const n=[];for(const s of t.getList("m"+e+r)){const i=s.childNodes;if(i[s[e]]&&i[s[r]])continue;const o=s.parent,a=i[s[e]]?t.nodeFactory.create("node","m"+e,[i[s.base],i[s[e]]]):t.nodeFactory.create("node","m"+r,[i[s.base],i[s[r]]]);nt.copyAttributes(s,a),o.replaceChild(a,s),n.push(s)}t.removeFromList("m"+e+r,n)}function at(t,e,r){const n=[];for(const s of t.getList(e)){if(s.attributes.get("displaystyle"))continue;const e=s.childNodes[s.base],i=e.coreMO();if(e.getProperty("movablelimits")&&!i.attributes.hasExplicit("movablelimits")){const e=t.nodeFactory.create("node",r,s.childNodes);nt.copyAttributes(s,e),s.parent.replaceChild(e,s),n.push(s)}}t.removeFromList(e,n)}const ct={cleanStretchy(t){const e=t.data;for(const t of e.getList("fixStretchy"))if(nt.getProperty(t,"fixStretchy")){const r=nt.getForm(t);r&&r[3]&&r[3].stretchy&&nt.setAttribute(t,"stretchy",!1);const n=t.parent;if(!(nt.getTexClass(t)||r&&r[2])){const r=e.nodeFactory.create("node","TeXAtom",[t]);n.replaceChild(r,t),r.inheritAttributesFrom(t)}nt.removeProperties(t,"fixStretchy")}},cleanAttributes(t){t.data.root.walkTree(((t,e)=>{const r=t.attributes,n=new Set((r.get("mjx-keep-attrs")||"").split(/ /));r.unset("mjx-keep-attrs");for(const e of r.getExplicitNames())n.has(e)||r.get(e)!==t.attributes.getInherited(e)||r.unset(e)}),{})},combineRelations(t){const e=[];for(const r of t.data.getList("mo")){if(r.getProperty("relationsCombined")||!r.parent||r.parent&&!nt.isType(r.parent,"mrow")||nt.getTexClass(r)!==F.REL)continue;let t;const n=r.parent.childNodes,s=n.indexOf(r)+1,i=nt.getProperty(r,"variantForm");for(;s<n.length&&(t=n[s])&&nt.isType(t,"mo")&&nt.getTexClass(t)===F.REL;){if(i!==nt.getProperty(t,"variantForm")||!it(r,t)){r.attributes.hasExplicit("rspace")||nt.setAttribute(r,"rspace","0pt"),t.attributes.hasExplicit("lspace")||nt.setAttribute(t,"lspace","0pt");break}nt.appendChildren(r,nt.getChildren(t)),st(["stretchy","rspace"],r,t);for(const e of t.getPropertyNames())r.setProperty(e,t.getProperty(e));t.attributes.get("data-latex")&&r.attributes.set("data-latex",r.attributes.get("data-latex")+t.attributes.get("data-latex")),n.splice(s,1),e.push(t),t.parent=null,t.setProperty("relationsCombined",!0),r.setProperty("texClass",F.REL)}r.attributes.setInherited("form",r.getForms()[0])}t.data.removeFromList("mo",e)},cleanSubSup(t){const e=t.data;e.error||(ot(e,"sub","sup"),ot(e,"under","over"))},moveLimits(t){const e=t.data;at(e,"munderover","msubsup"),at(e,"munder","msub"),at(e,"mover","msup")},setInherited(t){t.data.root.setInheritedAttributes({},t.math.display,0,!1)},checkScriptlevel(t){const e=t.data,r=[];for(const t of e.getList("mstyle")){if(1!==t.childNodes[0].childNodes.length)continue;const e=t.attributes;for(const t of["displaystyle","scriptlevel"])e.getExplicit(t)===e.getInherited(t)&&e.unset(t);const n=e.getExplicitNames();if(0===n.filter((t=>"data-latex"!==t.substring(0,10))).length){const s=t.childNodes[0].childNodes[0];n.forEach((t=>s.attributes.set(t,e.get(t)))),t.parent.replaceChild(s,t),r.push(t)}}e.removeFromList("mstyle",r)}},lt=ct;var ht,ut;!function(t){t.HANDLER="handler",t.FALLBACK="fallback",t.ITEMS="items",t.TAGS="tags",t.OPTIONS="options",t.NODES="nodes",t.PREPROCESSORS="preprocessors",t.POSTPROCESSORS="postprocessors",t.INIT="init",t.CONFIG="config",t.PRIORITY="priority",t.PARSER="parser"}(ht||(ht={})),function(t){t.DELIMITER="delimiter",t.MACRO="macro",t.CHARACTER="character",t.ENVIRONMENT="environment"}(ut||(ut={}));const dt=7.2;const pt={UNIT_CASES:new class{constructor(t){this.num="([-+]?([.,]\\d+|\\d+([.,]\\d*)?))",this.unit="",this.dimenEnd=/./,this.dimenRest=/./,this.map=new Map(t),this.updateDimen()}updateDimen(){this.unit=`(${Array.from(this.map.keys()).join("|")})`,this.dimenEnd=RegExp("^\\s*"+this.num+"\\s*"+this.unit+"\\s*$"),this.dimenRest=RegExp("^\\s*"+this.num+"\\s*"+this.unit+" ?")}set(t,e){return this.map.set(t,e),this.updateDimen(),this}get(t){return this.map.get(t)||this.map.get("pt")}delete(t){return!!this.map.delete(t)&&(this.updateDimen(),!0)}}([["em",1],["ex",.43],["pt",.1],["pc",1.2],["px",.1],["in",dt],["cm",dt/2.54],["mm",dt/25.4],["mu",1/18]]),matchDimen(t,e=!1){const r=t.match(e?pt.UNIT_CASES.dimenRest:pt.UNIT_CASES.dimenEnd);return r?function([t,e,r]){return"mu"!==e?[t,e,r]:[pt.em(pt.UNIT_CASES.get(e)*parseFloat(t)).slice(0,-2),"em",r]}([r[1].replace(/,/,"."),r[4],r[0].length]):[null,null,0]},dimen2em(t){const[e,r]=pt.matchDimen(t),n=parseFloat(e||"1");return pt.UNIT_CASES.get(r)*n},em:t=>Math.abs(t)<6e-4?"0em":t.toFixed(3).replace(/\.?0+$/,"")+"em",trimSpaces(t){if("string"!=typeof t)return t;let e=t.trim();return e.match(/\\$/)&&t.match(/ $/)&&(e+=" "),e}};class mt{constructor(t,e,r){this._factory=t,this._env=e,this.global={},this.stack=[],this.global={isInner:r},this.stack=[this._factory.create("start",this.global)],e&&(this.stack[0].env=e),this.env=this.stack[0].env}set env(t){this._env=t}get env(){return this._env}Push(...t){for(const e of t){if(!e)continue;const t=nt.isNode(e)?this._factory.create("mml",e):e;t.global=this.global;const[r,n]=this.stack.length?this.Top().checkItem(t):[null,!0];n&&(r?(this.Pop(),this.Push(...r)):(t.isKind("null")||this.stack.push(t),t.env?(t.copyEnv&&Object.assign(t.env,this.env),this.env=t.env):t.env=this.env))}}Pop(){const t=this.stack.pop();return t.isOpen||delete t.env,this.env=this.stack.length?this.Top().env:{},t}Top(t=1){return this.stack.length<t?null:this.stack[this.stack.length-t]}Prev(t){const e=this.Top();return t?e.First:e.Pop()}get height(){return this.stack.length}toString(){return"stack[\n  "+this.stack.join("\n  ")+"\n]"}}class ft{static processString(t,e){const r=t.split(ft.pattern);for(let t=1,n=r.length;t<n;t+=2){let n=r[t].charAt(0);if(n>="0"&&n<="9")r[t]=e[parseInt(r[t],10)-1],"number"==typeof r[t]&&(r[t]=r[t].toString());else if("{"===n)if(n=r[t].substring(1),n>="0"&&n<="9")r[t]=e[parseInt(r[t].substring(1,r[t].length-1),10)-1],"number"==typeof r[t]&&(r[t]=r[t].toString());else{r[t].match(/^\{([a-z]+):%(\d+)\|(.*)\}$/)&&(r[t]="%"+r[t])}}return r.join("")}constructor(t,e,...r){this.id=t,this.message=ft.processString(e,r)}}ft.pattern=/%(\d+|\{\d+\}|\{[a-z]+:%\d+(?:\|(?:%\{\d+\}|%.|[^}])*)+\}|.)/g;const gt=ft;class Et{constructor(t){this._nodes=t,this.startStr="",this.startI=0,this.stopI=0}get nodes(){return this._nodes}Push(...t){this._nodes.push(...t)}Pop(){return this._nodes.pop()}get First(){return this._nodes[this.Size()-1]}set First(t){this._nodes[this.Size()-1]=t}get Last(){return this._nodes[0]}set Last(t){this._nodes[0]=t}Peek(t){return null==t&&(t=1),this._nodes.slice(this.Size()-t)}Size(){return this._nodes.length}Clear(){this._nodes=[]}toMml(t=!0,e){return 1!==this._nodes.length||e?this.create("node",t?"inferredMrow":"mrow",this._nodes,{}):this.First}create(t,...e){return this.factory.configuration.nodeFactory.create(t,...e)}}class Nt extends Et{constructor(t,...e){super(e),this.factory=t,this.global={},this._properties={},this.isOpen&&(this._env={})}get kind(){return"base"}get env(){return this._env}set env(t){this._env=t}get copyEnv(){return!0}getProperty(t){return this._properties[t]}setProperty(t,e){return this._properties[t]=e,this}get isOpen(){return!1}get isClose(){return!1}get isFinal(){return!1}isKind(t){return t===this.kind}checkItem(t){if(t.isKind("over")&&this.isOpen&&(t.setProperty("num",this.toMml(!1)),this.Clear()),t.isKind("cell")&&this.isOpen){if(t.getProperty("linebreak"))return Nt.fail;throw new gt("Misplaced","Misplaced %1",t.getName())}if(t.isClose&&this.getErrors(t.kind)){const[e,r]=this.getErrors(t.kind);throw new gt(e,r,t.getName())}return t.isFinal?(this.Push(t.First),Nt.fail):Nt.success}clearEnv(){for(const t of Object.keys(this.env))delete this.env[t]}setProperties(t){return Object.assign(this._properties,t),this}getName(){return this.getProperty("name")}toString(){return this.kind+"["+this.nodes.join("; ")+"]"}getErrors(t){return this.constructor.errors[t]||Nt.errors[t]}}Nt.fail=[null,!1],Nt.success=[null,!0],Nt.errors={end:["MissingBeginExtraEnd","Missing \\begin{%1} or extra \\end{%1}"],close:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],right:["MissingLeftExtraRight","Missing \\left or extra \\right"],middle:["ExtraMiddle","Extra \\middle"]};const bt={NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"inital",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALLIGRAPHIC:"-tex-calligraphic",BOLDCALLIGRAPHIC:"-tex-bold-calligraphic",OLDSTYLE:"-tex-oldstyle",BOLDOLDSTYLE:"-tex-bold-oldstyle",MATHITALIC:"-tex-mathit"},Tt={PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},yt={AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},It={BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},At={TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},Rt={LATEX:"data-latex",LATEXITEM:"data-latex-item"};class Lt{constructor(t,e,r){this._string=t,this.configuration=r,this.macroCount=0,this.i=0,this.currentCS="",this.saveI=0;const n=Object.hasOwn(e,"isInner"),s=e.isInner;let i;if(delete e.isInner,e){i={};for(const t of Object.keys(e))i[t]=e[t]}this.configuration.pushParser(this),this.stack=new mt(this.itemFactory,i,!n||s),this.Parse(),this.Push(this.itemFactory.create("stop")),this.updateResult(this.string,this.i),this.stack.env=i}get options(){return this.configuration.options}get itemFactory(){return this.configuration.itemFactory}get tags(){return this.configuration.tags}set string(t){this._string=t}get string(){return this._string}parse(t,e){const r=this.saveI;this.saveI=this.i;const n=this.configuration.handlers.get(t).parse(e);return this.updateResult(e[1],r),this.saveI=r,n}lookup(t,e){return this.configuration.handlers.get(t).lookup(e)}contains(t,e){return this.configuration.handlers.get(t).contains(e)}toString(){let t="";for(const e of Array.from(this.configuration.handlers.keys()))t+=e+": "+this.configuration.handlers.get(e)+"\n";return t}Parse(){let t;for(;this.i<this.string.length;)t=this.getCodePoint(),this.i+=t.length,this.parse(ut.CHARACTER,[this,t])}Push(t){t instanceof Nt&&(t.startI=this.saveI,t.stopI=this.i,t.startStr=this.string),t instanceof j&&t.isInferred?this.PushAll(t.childNodes):this.stack.Push(t)}PushAll(t){for(const e of t)this.stack.Push(e)}mml(){if(!this.stack.Top().isKind("mml"))return null;const t=this.stack.Top().First;return this.configuration.popParser(),t.attributes.set(Rt.LATEX,this.string),t}convertDelimiter(t){var e;const r=this.lookup(ut.DELIMITER,t);return null!==(e=null==r?void 0:r.char)&&void 0!==e?e:null}getCodePoint(){const t=this.string.codePointAt(this.i);return void 0===t?"":String.fromCodePoint(t)}nextIsSpace(){return!!this.string.charAt(this.i).match(/\s/)}GetNext(){for(;this.nextIsSpace();)this.i++;return this.getCodePoint()}GetCS(){const t=this.string.slice(this.i).match(/^(([a-z]+) ?|[\uD800-\uDBFF].|.)/i);return t?(this.i+=t[0].length,t[2]||t[1]):(this.i++," ")}GetArgument(t,e=!1){switch(this.GetNext()){case"":if(!e)throw new gt("MissingArgFor","Missing argument for %1",this.currentCS);return null;case"}":if(!e)throw new gt("ExtraCloseMissingOpen","Extra close brace or missing open brace");return null;case"\\":return this.i++,"\\"+this.GetCS();case"{":{const t=++this.i;let e=1;for(;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"\\":this.i++;break;case"{":e++;break;case"}":if(0===--e)return this.string.slice(t,this.i-1)}throw new gt("MissingCloseBrace","Missing close brace")}}const r=this.getCodePoint();return this.i+=r.length,r}GetBrackets(t,e,r=!1){if("["!==this.GetNext())return e;const n=++this.i;let s=0,i=0;for(;this.i<this.string.length;)switch(this.string.charAt(this.i++)){case"{":s++;break;case"\\":this.i++;break;case"}":if(s--<=0)throw new gt("ExtraCloseLooking","Extra close brace while looking for %1","']'");break;case"[":0===s&&i++;break;case"]":if(0===s){if(!r||0===i)return this.string.slice(n,this.i-1);i--}}throw new gt("MissingCloseBracket","Could not find closing ']' for argument to %1",this.currentCS)}GetDelimiter(t,e=!1){let r=this.GetNext();if(this.i+=r.length,this.i<=this.string.length&&("\\"===r?r+=this.GetCS():"{"===r&&e&&(this.i--,r=this.GetArgument(t).trim()),this.contains(ut.DELIMITER,r)))return this.convertDelimiter(r);throw new gt("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)}GetDimen(t){if("{"===this.GetNext()){const e=this.GetArgument(t),[r,n]=pt.matchDimen(e);if(r)return r+n}else{const t=this.string.slice(this.i),[e,r,n]=pt.matchDimen(t,!0);if(e)return this.i+=n,e+r}throw new gt("MissingDimOrUnits","Missing dimension or its units for %1",this.currentCS)}GetUpTo(t,e){for(;this.nextIsSpace();)this.i++;const r=this.i;let n=0;for(;this.i<this.string.length;){const t=this.i;let s=this.GetNext();switch(this.i+=s.length,s){case"\\":s+=this.GetCS();break;case"{":n++;break;case"}":if(0===n)throw new gt("ExtraCloseLooking","Extra close brace while looking for %1",e);n--}if(0===n&&s===e)return this.string.slice(r,t)}throw new gt("TokenNotFoundForCommand","Could not find %1 for %2",e,this.currentCS)}ParseArg(t){return new Lt(this.GetArgument(t),this.stack.env,this.configuration).mml()}ParseUpTo(t,e){return new Lt(this.GetUpTo(t,e),this.stack.env,this.configuration).mml()}GetDelimiterArg(t){const e=pt.trimSpaces(this.GetArgument(t));if(""===e)return null;if(this.contains(ut.DELIMITER,e))return e;throw new gt("MissingOrUnrecognizedDelim","Missing or unrecognized delimiter for %1",this.currentCS)}GetStar(){const t="*"===this.GetNext();return t&&this.i++,t}create(t,...e){const r=this.configuration.nodeFactory.create(t,...e);return r.isToken&&r.attributes.hasExplicit("mathvariant")&&"-"===r.attributes.get("mathvariant").charAt(0)&&r.setProperty("ignore-variant",!0),r}updateResult(t,e){const r=this.stack.Prev(!0);if(!r)return;const n=r.attributes.get(Rt.LATEXITEM);if(void 0!==n)return void r.attributes.set(Rt.LATEX,n);let s=(e=e<this.saveI?this.saveI:e)!==this.i?this.string.slice(e,this.i):t;if(s=s.trim(),s)if("\\"===t&&(s="\\"+s),"^"!==r.attributes.get(Rt.LATEX)||"^"===s||"\\^"===s)if("_"!==r.attributes.get(Rt.LATEX)||"_"===s||"\\_"===s)"}"!==s?r.attributes.set(Rt.LATEX,s):this.composeBraces(r);else if(r.childNodes[1]&&("}"===s?this.composeBraces(r.childNodes[1]):r.childNodes[1].attributes.set(Rt.LATEX,s)),r.childNodes[2]){const t=r.childNodes[2].attributes.get(Rt.LATEX);this.composeLatex(r,`^${t}_`,0,1)}else this.composeLatex(r,"_",0,1);else if(r.childNodes[2]&&("}"===s?this.composeBraces(r.childNodes[2]):r.childNodes[2].attributes.set(Rt.LATEX,s)),r.childNodes[1]){const t=r.childNodes[1].attributes.get(Rt.LATEX);this.composeLatex(r,`_${t}^`,0,2)}else this.composeLatex(r,"^",0,2)}composeLatex(t,e,r,n){if(!t.childNodes[r]||!t.childNodes[n])return;const s=(t.childNodes[r].attributes.get(Rt.LATEX)||"")+e+t.childNodes[n].attributes.get(Rt.LATEX);t.attributes.set(Rt.LATEX,s)}composeBraces(t){const e=this.composeBracedContent(t);t.attributes.set(Rt.LATEX,`{${e}}`)}composeBracedContent(t){var e,r;const n=(null===(e=t.childNodes[0])||void 0===e?void 0:e.childNodes)||[];let s="";for(const t of n){const e=(null===(r=null==t?void 0:t.attributes)||void 0===r?void 0:r.get(Rt.LATEX))||"";e&&(s+=s&&s.match(/[a-zA-Z]$/)&&e.match(/^[a-zA-Z]/)?" "+e:e)}return s}}class Ct{constructor(t=null){this.defaultKind="unknown",this.nodeMap=new Map,this.node={},null===t&&(t=this.constructor.defaultNodes);for(const e of Object.keys(t))this.setNodeClass(e,t[e])}create(t,...e){return(this.node[t]||this.node[this.defaultKind])(...e)}setNodeClass(t,e){this.nodeMap.set(t,e);const r=this.nodeMap.get(t);this.node[t]=(...t)=>new r(this,...t)}getNodeClass(t){return this.nodeMap.get(t)}deleteNodeClass(t){this.nodeMap.delete(t),delete this.node[t]}nodeIsKind(t,e){return t instanceof this.getNodeClass(e)}getKinds(){return Array.from(this.nodeMap.keys())}}Ct.defaultNodes={};class Ot extends Nt{}class vt extends Ct{constructor(){super(...arguments),this.defaultKind="dummy",this.configuration=null}}vt.DefaultStackItems={[Ot.prototype.kind]:Ot};const St=vt;class wt{constructor(){this.mmlFactory=null,this.factory={node:wt.createNode,token:wt.createToken,text:wt.createText,error:wt.createError}}static createNode(t,e,r=[],n={},s){const i=t.mmlFactory.create(e);return i.setChildren(r),s&&i.appendChild(s),nt.setProperties(i,n),i}static createToken(t,e,r={},n=""){const s=t.create("text",n);return t.create("node",e,[],r,s)}static createText(t,e){return null==e?null:t.mmlFactory.create("text").setText(e)}static createError(t,e){const r=t.create("text",e),n=t.create("node","mtext",[],{},r);return t.create("node","merror",[n],{"data-mjx-error":e})}setMmlFactory(t){this.mmlFactory=t}set(t,e){this.factory[t]=e}setCreators(t){for(const e in t)this.set(e,t[e])}create(t,...e){const r=(this.factory[t]||this.factory.node)(this,e[0],...e.slice(1));return"node"===t&&this.configuration.addNode(e[0],r),r}get(t){return this.factory[t]}}const Mt=!0,xt={ApplyFunction:"\u2061",Backslash:"\u2216",Because:"\u2235",Breve:"\u02d8",Cap:"\u22d2",CenterDot:"\xb7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",Congruent:"\u2261",ContourIntegral:"\u222e",Coproduct:"\u2210",Cross:"\u2a2f",Cup:"\u22d3",CupCap:"\u224d",Dagger:"\u2021",Del:"\u2207",Delta:"\u0394",Diamond:"\u22c4",DifferentialD:"\u2146",DotEqual:"\u2250",DoubleDot:"\xa8",DoubleRightTee:"\u22a8",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownLeftVector:"\u21bd",DownRightVector:"\u21c1",DownTee:"\u22a4",Downarrow:"\u21d3",Element:"\u2208",EqualTilde:"\u2242",Equilibrium:"\u21cc",Exists:"\u2203",ExponentialE:"\u2147",FilledVerySmallSquare:"\u25aa",ForAll:"\u2200",Gamma:"\u0393",Gg:"\u22d9",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Hacek:"\u02c7",Hat:"^",HumpDownHump:"\u224e",HumpEqual:"\u224f",Im:"\u2111",ImaginaryI:"\u2148",Integral:"\u222b",Intersection:"\u22c2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Lambda:"\u039b",Larr:"\u219e",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",LeftArrowRightArrow:"\u21c6",LeftCeiling:"\u2308",LeftDownVector:"\u21c3",LeftFloor:"\u230a",LeftRightArrow:"\u2194",LeftTee:"\u22a3",LeftTriangle:"\u22b2",LeftTriangleEqual:"\u22b4",LeftUpVector:"\u21bf",LeftVector:"\u21bc",Leftarrow:"\u21d0",Leftrightarrow:"\u21d4",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",Ll:"\u22d8",Lleftarrow:"\u21da",LongLeftArrow:"\u27f5",LongLeftRightArrow:"\u27f7",LongRightArrow:"\u27f6",Longleftarrow:"\u27f8",Longleftrightarrow:"\u27fa",Longrightarrow:"\u27f9",Lsh:"\u21b0",MinusPlus:"\u2213",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotLeftTriangle:"\u22ea",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotPrecedes:"\u2280",NotPrecedesSlantEqual:"\u22e0",NotRightTriangle:"\u22eb",NotRightTriangleEqual:"\u22ed",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsSlantEqual:"\u22e1",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotVerticalBar:"\u2224",Omega:"\u03a9",OverBar:"\u203e",OverBrace:"\u23de",PartialD:"\u2202",Phi:"\u03a6",Pi:"\u03a0",PlusMinus:"\xb1",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",Product:"\u220f",Proportional:"\u221d",Psi:"\u03a8",Rarr:"\u21a0",Re:"\u211c",ReverseEquilibrium:"\u21cb",RightAngleBracket:"\u27e9",RightArrow:"\u2192",RightArrowLeftArrow:"\u21c4",RightCeiling:"\u2309",RightDownVector:"\u21c2",RightFloor:"\u230b",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTriangle:"\u22b3",RightTriangleEqual:"\u22b5",RightUpVector:"\u21be",RightVector:"\u21c0",Rightarrow:"\u21d2",Rrightarrow:"\u21db",Rsh:"\u21b1",Sigma:"\u03a3",SmallCircle:"\u2218",Sqrt:"\u221a",Square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Star:"\u22c6",Subset:"\u22d0",SubsetEqual:"\u2286",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",SuchThat:"\u220b",Sum:"\u2211",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22d1",Therefore:"\u2234",Theta:"\u0398",Tilde:"\u223c",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",UnderBar:"_",UnderBrace:"\u23df",Union:"\u22c3",UnionPlus:"\u228e",UpArrow:"\u2191",UpDownArrow:"\u2195",UpTee:"\u22a5",Uparrow:"\u21d1",Updownarrow:"\u21d5",Upsilon:"\u03a5",Vdash:"\u22a9",Vee:"\u22c1",VerticalBar:"\u2223",VerticalTilde:"\u2240",Vvdash:"\u22aa",Wedge:"\u22c0",Xi:"\u039e",amp:"&",acute:"\xb4",aleph:"\u2135",alpha:"\u03b1",amalg:"\u2a3f",and:"\u2227",ang:"\u2220",angmsd:"\u2221",angsph:"\u2222",ape:"\u224a",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",beta:"\u03b2",beth:"\u2136",between:"\u226c",bigcirc:"\u25ef",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",blacklozenge:"\u29eb",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",bowtie:"\u22c8",boxdl:"\u2510",boxdr:"\u250c",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxul:"\u2518",boxur:"\u2514",bsol:"\\",bull:"\u2022",cap:"\u2229",check:"\u2713",chi:"\u03c7",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledR:"\xae",circledS:"\u24c8",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",clubs:"\u2663",colon:":",comp:"\u2201",ctdot:"\u22ef",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cup:"\u222a",curarr:"\u21b7",curlyvee:"\u22ce",curlywedge:"\u22cf",dagger:"\u2020",daleth:"\u2138",ddarr:"\u21ca",deg:"\xb0",delta:"\u03b4",digamma:"\u03dd",div:"\xf7",divideontimes:"\u22c7",dot:"\u02d9",doteqdot:"\u2251",dotplus:"\u2214",dotsquare:"\u22a1",dtdot:"\u22f1",ecir:"\u2256",efDot:"\u2252",egs:"\u2a96",ell:"\u2113",els:"\u2a95",empty:"\u2205",epsi:"\u03b5",epsiv:"\u03f5",erDot:"\u2253",eta:"\u03b7",eth:"\xf0",flat:"\u266d",fork:"\u22d4",frown:"\u2322",gEl:"\u2a8c",gamma:"\u03b3",gap:"\u2a86",gimel:"\u2137",gnE:"\u2269",gnap:"\u2a8a",gne:"\u2a88",gnsim:"\u22e7",gt:">",gtdot:"\u22d7",harrw:"\u21ad",hbar:"\u210f",hellip:"\u2026",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",imath:"\u0131",infin:"\u221e",intcal:"\u22ba",iota:"\u03b9",jmath:"\u0237",kappa:"\u03ba",kappav:"\u03f0",lEg:"\u2a8b",lambda:"\u03bb",lap:"\u2a85",larrlp:"\u21ab",larrtl:"\u21a2",lbrace:"{",lbrack:"[",le:"\u2264",leftleftarrows:"\u21c7",leftthreetimes:"\u22cb",lessdot:"\u22d6",lmoust:"\u23b0",lnE:"\u2268",lnap:"\u2a89",lne:"\u2a87",lnsim:"\u22e6",longmapsto:"\u27fc",looparrowright:"\u21ac",lowast:"\u2217",loz:"\u25ca",lt:"<",ltimes:"\u22c9",ltri:"\u25c3",macr:"\xaf",malt:"\u2720",mho:"\u2127",mu:"\u03bc",multimap:"\u22b8",nLeftarrow:"\u21cd",nLeftrightarrow:"\u21ce",nRightarrow:"\u21cf",nVDash:"\u22af",nVdash:"\u22ae",natur:"\u266e",nearr:"\u2197",nharr:"\u21ae",nlarr:"\u219a",not:"\xac",nrarr:"\u219b",nu:"\u03bd",nvDash:"\u22ad",nvdash:"\u22ac",nwarr:"\u2196",omega:"\u03c9",omicron:"\u03bf",or:"\u2228",osol:"\u2298",period:".",phi:"\u03c6",phiv:"\u03d5",pi:"\u03c0",piv:"\u03d6",prap:"\u2ab7",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",prime:"\u2032",psi:"\u03c8",quot:'"',rarrtl:"\u21a3",rbrace:"}",rbrack:"]",rho:"\u03c1",rhov:"\u03f1",rightrightarrows:"\u21c9",rightthreetimes:"\u22cc",ring:"\u02da",rmoust:"\u23b1",rtimes:"\u22ca",rtri:"\u25b9",scap:"\u2ab8",scnE:"\u2ab6",scnap:"\u2aba",scnsim:"\u22e9",sdot:"\u22c5",searr:"\u2198",sect:"\xa7",sharp:"\u266f",sigma:"\u03c3",sigmav:"\u03c2",simne:"\u2246",smile:"\u2323",spades:"\u2660",sub:"\u2282",subE:"\u2ac5",subnE:"\u2acb",subne:"\u228a",supE:"\u2ac6",supnE:"\u2acc",supne:"\u228b",swarr:"\u2199",tau:"\u03c4",theta:"\u03b8",thetav:"\u03d1",tilde:"\u02dc",times:"\xd7",triangle:"\u25b5",triangleq:"\u225c",upsi:"\u03c5",upuparrows:"\u21c8",veebar:"\u22bb",vellip:"\u22ee",weierp:"\u2118",xi:"\u03be",yen:"\xa5",zeta:"\u03b6",zigrarr:"\u21dd",nbsp:"\xa0",rsquo:"\u2019",lsquo:"\u2018"},Pt={};function Dt(t,e){if("#"===e.charAt(0))return function(t){const e="x"===t.charAt(0)?parseInt(t.slice(1),16):parseInt(t);return String.fromCodePoint(e)}(e.slice(1));if(xt[e])return xt[e];if(Mt){const t=e.match(/^[a-zA-Z](fr|scr|opf)$/)?RegExp.$1:e.charAt(0).toLowerCase();Pt[t]||(Pt[t]=!0,n((r="./util/entities/"+t+".js",o.asyncLoad?new Promise(((t,e)=>{const n=o.asyncLoad(r);n instanceof Promise?n.then((e=>t(e))).catch((t=>e(t))):t(n)})):Promise.reject(`Can't load '${r}': No mathjax.asyncLoad method specified`))))}var r;return t}class kt{static oneof(...t){return new this("string",(e=>t.includes(e)),(t=>t))}constructor(t,e,r){this.name=t,this.verify=e,this.convert=r}}new kt("boolean",(t=>"true"===t||"false"===t),(t=>"true"===t)),new kt("number",(t=>!!t.match(/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(?:e[-+]?\d+)?$/)),(t=>parseFloat(t))),new kt("integer",(t=>!!t.match(/^[-+]?\d+$/)),(t=>parseInt(t))),new kt("string",(t=>!0),(t=>t)),new kt("dimen",(t=>null!==pt.matchDimen(t)[0]),(t=>t));function _t(t,e){if(0===e)return t.replace(/^\s+/,"").replace(/([^\\\s]|^)((?:\\\\)*(?:\\\s)?)?\s+$/,"$1$2");for(;e>0;)t=t.trim().slice(1,-1),e--;return t}function Ft(t,e,r=!1,n=!1){const s=t.length;let i=0,o="",a=0,c=0,l=!0;for(;a<s;){const n=t[a++];switch(n){case"\\":o+=n+(t[a++]||""),l=!1;continue;case" ":break;case"{":l&&c++,i++;break;case"}":if(!i)throw new gt("ExtraCloseMissingOpen","Extra close brace or missing open brace");i--,l=!1;break;default:if(!i&&e.includes(n))return[_t(o,r?Math.min(1,c):c),n,t.slice(a)];c>i&&(c=i),l=!1}o+=n}if(i)throw new gt("ExtraOpenMissingClose","Extra open brace or missing close brace");return n&&c?["","",_t(o,1)]:[_t(o,r?Math.min(1,c):c),"",t.slice(a)]}const Bt={cols:(...t)=>t.map((t=>pt.em(t))).join(" "),fenced(t,e,r,n,s="",i=""){const o=t.nodeFactory,a=o.create("node","mrow",[],{open:e,close:n,texClass:F.INNER});let c;if(s)c=new Lt("\\"+s+"l"+e,t.parser.stack.env,t).mml();else{const t=o.create("text",e);c=o.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:F.OPEN},t)}if(nt.appendChildren(a,[c,r]),s)c=new Lt("\\"+s+"r"+n,t.parser.stack.env,t).mml();else{const t=o.create("text",n);c=o.create("node","mo",[],{fence:!0,stretchy:!0,symmetric:!0,texClass:F.CLOSE},t)}return i&&c.attributes.set("mathcolor",i),nt.appendChildren(a,[c]),a},fixedFence(t,e,r,n){const s=t.nodeFactory.create("node","mrow",[],{open:e,close:n,texClass:F.ORD});return e&&nt.appendChildren(s,[Bt.mathPalette(t,e,"l")]),nt.isType(r,"mrow")?nt.appendChildren(s,nt.getChildren(r)):nt.appendChildren(s,[r]),n&&nt.appendChildren(s,[Bt.mathPalette(t,n,"r")]),s},mathPalette(t,e,r){"{"!==e&&"}"!==e||(e="\\"+e);const n="{\\big"+r+" "+e+"}";return new Lt("\\mathchoice"+("{\\bigg"+r+" "+e+"}")+n+n+n,{},t).mml()},fixInitialMO(t,e){for(let r=0,n=e.length;r<n;r++){const n=e[r];if(n&&!nt.isType(n,"mspace")&&(!nt.isType(n,"TeXAtom")||nt.getChildren(n)[0]&&nt.getChildren(nt.getChildren(n)[0]).length)){if(nt.isEmbellished(n)||nt.isType(n,"TeXAtom")&&nt.getTexClass(n)===F.REL){const r=t.nodeFactory.create("node","mi");e.unshift(r)}break}}},internalMath(t,e,r,n){if(e=e.replace(/ +/g," "),t.configuration.options.internalMath)return t.configuration.options.internalMath(t,e,r,n);const s=n||t.stack.env.font,i=s?{mathvariant:s}:{};let o,a,c=[],l=0,h=0,u="",d=0;if(e.match(/\\?[${}\\]|\\\(|\\(?:eq)?ref\s*\{|\\U/)){for(;l<e.length;)if(o=e.charAt(l++),"$"===o)"$"===u&&0===d?(a=t.create("node","TeXAtom",[new Lt(e.slice(h,l-1),{},t.configuration).mml()]),c.push(a),u="",h=l):""===u&&(h<l-1&&c.push(Bt.internalText(t,e.slice(h,l-1),i)),u="$",h=l);else if("{"===o&&""!==u)d++;else if("}"===o)if("}"===u&&0===d){const r=new Lt(e.slice(h,l),{},t.configuration).mml();a=t.create("node","TeXAtom",[r],i),c.push(a),u="",h=l}else""!==u&&d&&d--;else if("\\"===o)if(""===u&&e.substring(l).match(/^(eq)?ref\s*\{/)){const r=RegExp["$&"].length;h<l-1&&c.push(Bt.internalText(t,e.slice(h,l-1),i)),u="}",h=l-1,l+=r}else if(o=e.charAt(l++),"("===o&&""===u)h<l-2&&c.push(Bt.internalText(t,e.slice(h,l-2),i)),u=")",h=l;else if(")"===o&&")"===u&&0===d)a=t.create("node","TeXAtom",[new Lt(e.slice(h,l-2),{},t.configuration).mml()]),c.push(a),u="",h=l;else if(o.match(/[${}\\]/)&&""===u)l--,e=e.substring(0,l-1)+e.substring(l);else if("U"===o){const t=e.substring(l).match(/^\s*(?:([0-9A-F])|\{\s*([0-9A-F]+)\s*\})/);if(!t)throw new gt("BadRawUnicode","Argument to %1 must a hexadecimal number with 1 to 6 digits","\\U");const r=String.fromCodePoint(parseInt(t[1]||t[2],16));e=e.substring(0,l-2)+r+e.substring(l+t[0].length),l=l-2+r.length}if(""!==u)throw new gt("MathNotTerminated","Math mode is not properly terminated")}return h<e.length&&c.push(Bt.internalText(t,e.slice(h),i)),null!=r?c=[t.create("node","mstyle",c,{displaystyle:!1,scriptlevel:r})]:c.length>1&&(c=[t.create("node","mrow",c)]),c},internalText(t,e,r){e=e.replace(/\n+/g," ").replace(/^ +/,xt.nbsp).replace(/ +$/,xt.nbsp);const n=t.create("text",e);return t.create("node","mtext",[],r,n)},underOver(t,e,r,n,s){if(Bt.checkMovableLimits(e),nt.isType(e,"munderover")&&nt.isEmbellished(e)){nt.setProperties(nt.getCoreMO(e),{lspace:0,rspace:0});const r=t.create("node","mo",[],{rspace:0});e=t.create("node","mrow",[r,e])}const i=t.create("node","munderover",[e]);nt.setChild(i,"over"===n?i.over:i.under,r);let o=i;return s&&(o=t.create("node","TeXAtom",[t.create("node","mstyle",[i],{displaystyle:!0,scriptlevel:0})],{texClass:F.OP,movesupsub:!0})),nt.setProperty(o,"subsupOK",!0),o},checkMovableLimits(t){const e=nt.isType(t,"mo")?nt.getForm(t):null;(nt.getProperty(t,"movablelimits")||e&&e[3]&&e[3].movablelimits)&&nt.setProperties(t,{movablelimits:!1})},setArrayAlign:(t,e,r)=>(r||(e=pt.trimSpaces(e||"")),"t"===e?t.arraydef.align="baseline 1":"b"===e?t.arraydef.align="baseline -1":"c"===e?t.arraydef.align="axis":e&&(r?(r.string=`[${e}]`+r.string.slice(r.i),r.i=0):t.arraydef.align=e),t),substituteArgs(t,e,r){let n="",s="",i=0;for(;i<r.length;){let o=r.charAt(i++);if("\\"===o)n+=o+r.charAt(i++);else if("#"===o)if(o=r.charAt(i++),"#"===o)n+=o;else{if(!o.match(/[1-9]/)||parseInt(o,10)>e.length)throw new gt("IllegalMacroParam","Illegal macro parameter reference");s=Bt.addArgs(t,Bt.addArgs(t,s,n),e[parseInt(o,10)-1]),n=""}else n+=o}return Bt.addArgs(t,s,n)},addArgs(t,e,r){if(r.match(/^[a-z]/i)&&e.match(/(^|[^\\])(\\\\)*\\[a-z]+$/i)&&(e+=" "),e.length+r.length>t.configuration.options.maxBuffer)throw new gt("MaxBufferSize","MathJax internal buffer size exceeded; is there a recursive macro call?");return e+r},checkMaxMacros(t,e=!0){if(!(++t.macroCount<=t.configuration.options.maxMacros))throw e?new gt("MaxMacroSub1","MathJax maximum macro substitution count exceeded; is here a recursive macro call?"):new gt("MaxMacroSub2","MathJax maximum substitution count exceeded; is there a recursive latex environment?")},checkEqnEnv(t,e=!0){const r=t.stack.Top(),n=r.First;if(!(r.getProperty("nestable")&&e&&!n||r.getProperty("nestStart"))&&(!r.isKind("start")||n))throw new gt("ErroneousNestingEq","Erroneous nesting of equation structures")},copyNode(t,e){const r=t.copy(),n=e.configuration;return r.walkTree((t=>{n.addNode(t.kind,t);const e=(t.getProperty("in-lists")||"").split(/,/);for(const r of e)r&&n.addNode(r,t)})),r},mmlFilterAttribute:(t,e,r)=>r,getFontDef(t){const e=t.stack.env.font;return e?{mathvariant:e}:{}},keyvalOptions(t,e=null,r=!1,n=!1){const s=function(t,e=!1){const r={};let n,s,i,o=t,a=!0;for(;o;)[s,n,o]=Ft(o,["=",","],e,a),a=!1,"="===n?([i,n,o]=Ft(o,[","],e),i="false"===i||"true"===i?JSON.parse(i):i,r[s]=i):s&&(r[s]=!0);return r}(t,n);if(e)for(const t of Object.keys(s))if(Object.hasOwn(e,t)){if(e[t]instanceof kt){const r=e[t],n=String(s[t]);if(!r.verify(n))throw new gt("InvalidValue","Value for key '%1' is not of the expected type",t);s[t]=r.convert(n)}}else{if(r)throw new gt("InvalidOption","Invalid option: %1",t);delete s[t]}return s},isLatinOrGreekChar:t=>!!t.normalize("NFD").match(/[a-zA-Z\u0370-\u03F0]/)};class Ut{constructor(){this.columnHandler={l:t=>t.calign[t.j++]="left",c:t=>t.calign[t.j++]="center",r:t=>t.calign[t.j++]="right",p:t=>this.getColumn(t,"top"),m:t=>this.getColumn(t,"middle"),b:t=>this.getColumn(t,"bottom"),w:t=>this.getColumn(t,"top",""),W:t=>this.getColumn(t,"top",""),"|":t=>this.addRule(t,"solid"),":":t=>this.addRule(t,"dashed"),">":t=>t.cstart[t.j]=(t.cstart[t.j]||"")+this.getBraces(t),"<":t=>t.cend[t.j-1]=(t.cend[t.j-1]||"")+this.getBraces(t),"@":t=>this.addAt(t,this.getBraces(t)),"!":t=>this.addBang(t,this.getBraces(t)),"*":t=>this.repeat(t),P:t=>this.macroColumn(t,">{$}p{#1}<{$}",1),M:t=>this.macroColumn(t,">{$}m{#1}<{$}",1),B:t=>this.macroColumn(t,">{$}b{#1}<{$}",1)," ":t=>{}},this.MAXCOLUMNS=1e4}process(t,e,r){const n={parser:t,template:e,i:0,j:0,c:"",cwidth:[],calign:[],cspace:[],clines:[],cstart:r.cstart,cend:r.cend,ralign:r.ralign,cextra:r.cextra};let s=0;for(;n.i<n.template.length;){if(s++>this.MAXCOLUMNS)throw new gt("MaxColumns","Too many column specifiers (perhaps looping column definitions?)");const t=n.template.codePointAt(n.i),e=n.c=String.fromCodePoint(t);if(n.i+=e.length,!Object.hasOwn(this.columnHandler,e))throw new gt("BadPreamToken","Illegal pream-token (%1)",e);this.columnHandler[e](n)}this.setColumnAlign(n,r),this.setColumnWidths(n,r),this.setColumnSpacing(n,r),this.setColumnLines(n,r),this.setPadding(n,r)}setColumnAlign(t,e){e.arraydef.columnalign=t.calign.join(" ")}setColumnWidths(t,e){if(!t.cwidth.length)return;const r=[...t.cwidth];r.length<t.calign.length&&r.push("auto"),e.arraydef.columnwidth=r.map((t=>t||"auto")).join(" ")}setColumnSpacing(t,e){if(!t.cspace.length)return;const r=[...t.cspace];r.length<t.calign.length&&r.push("1em"),e.arraydef.columnspacing=r.slice(1).map((t=>t||"1em")).join(" ")}setColumnLines(t,e){if(!t.clines.length)return;const r=[...t.clines];r[0]&&e.frame.push(["left",r[0]]),r.length>t.calign.length?e.frame.push(["right",r.pop()]):r.length<t.calign.length&&r.push("none"),r.length>1&&(e.arraydef.columnlines=r.slice(1).map((t=>t||"none")).join(" "))}setPadding(t,e){if(!t.cextra[0]&&!t.cextra[t.calign.length-1])return;const r=t.calign.length-1,n=t.cspace,s=t.cextra[r]?n[r]:null;e.arraydef["data-array-padding"]=`${n[0]||".5em"} ${s||".5em"}`}getColumn(t,e,r="left"){t.calign[t.j]=r||this.getAlign(t),t.cwidth[t.j]=this.getDimen(t),t.ralign[t.j]=[e,t.cwidth[t.j],t.calign[t.j]],t.j++}getDimen(t){const e=this.getBraces(t);if(!pt.matchDimen(e)[0])throw new gt("MissingColumnDimOrUnits","Missing dimension or its units for %1 column declaration",t.c);return e}getAlign(t){return y(this.getBraces(t).toLowerCase(),{l:"left",c:"center",r:"right"},"")}getBraces(t){for(;" "===t.template[t.i];)t.i++;if(t.i>=t.template.length)throw new gt("MissingArgForColumn","Missing argument for %1 column declaration",t.c);if("{"!==t.template[t.i])return t.template[t.i++];const e=++t.i;let r=1;for(;t.i<t.template.length;)switch(t.template.charAt(t.i++)){case"\\":t.i++;break;case"{":r++;break;case"}":if(0===--r)return t.template.slice(e,t.i-1)}throw new gt("MissingCloseBrace","Missing close brace")}macroColumn(t,e,r){const n=[];for(;r>0&&r--;)n.push(this.getBraces(t));t.template=Bt.substituteArgs(t.parser,n,e)+t.template.slice(t.i),t.i=0}addRule(t,e){t.clines[t.j]&&this.addAt(t,"\\,"),t.clines[t.j]=e,"0"===t.cspace[t.j]&&(t.cstart[t.j]="\\hspace{.5em}")}addAt(t,e){const{cstart:r,cspace:n,j:s}=t;t.cextra[s]=!0,t.calign[s]="center",t.clines[s]&&(".5em"===n[s]?r[s-1]+="\\hspace{.25em}":n[s]||(t.cend[s-1]=(t.cend[s-1]||"")+"\\hspace{.5em}")),r[s]=e,n[s]="0",n[++t.j]="0"}addBang(t,e){const{cstart:r,cspace:n,j:s}=t;t.cextra[s]=!0,t.calign[s]="center",r[s]=("0"===n[s]&&t.clines[s]?"\\hspace{.25em}":"")+e,n[s]||(n[s]=".5em"),n[++t.j]=".5em"}repeat(t){const e=this.getBraces(t),r=this.getBraces(t),n=parseInt(e);if(String(n)!==e)throw new gt("ColArgNotNum","First argument to %1 column specifier must be a number","*");t.template=new Array(n).fill(r).join("")+t.template.substring(t.i),t.i=0}}const qt=bt;class Ht{constructor(t,e=[]){this.options={},this.columnParser=new Ut,this.packageData=new Map,this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.handlers=t.handlers,this.nodeFactory=new wt,this.nodeFactory.configuration=this,this.nodeFactory.setCreators(t.nodes),this.itemFactory=new St(t.items),this.itemFactory.configuration=this,N(this.options,...e),N(this.options,t.options),this.mathStyle=Ht.getVariant.get(this.options.mathStyle)||Ht.getVariant.get("TeX")}pushParser(t){this.parsers.unshift(t)}popParser(){this.parsers.shift()}get parser(){return this.parsers[0]}clear(){this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.tags.resetTag()}addNode(t,e){let r=this.nodeLists[t];if(r||(r=this.nodeLists[t]=[]),r.push(e),e.kind!==t){const r=nt.getProperty(e,"in-lists")||"",n=(r?r.split(/,/):[]).concat(t).join(",");nt.setProperty(e,"in-lists",n)}}getList(t){const e=this.nodeLists[t]||[],r=[];for(const t of e)this.inTree(t)&&r.push(t);return this.nodeLists[t]=r,r}removeFromList(t,e){const r=this.nodeLists[t]||[];for(const t of e){const e=r.indexOf(t);e>=0&&r.splice(e,1)}}inTree(t){for(;t&&t!==this.root;)t=t.parent;return!!t}}Ht.getVariant=new Map([["TeX",(t,e)=>e&&t.match(/^[\u0391-\u03A9\u03F4]/)?qt.NORMAL:""],["ISO",t=>qt.ITALIC],["French",t=>t.normalize("NFD").match(/^[a-z]/)?qt.ITALIC:qt.NORMAL],["upright",t=>qt.NORMAL]]);const Gt=Ht;class jt{constructor(t="???",e=""){this.tag=t,this.id=e}}class Xt{constructor(t="",e=!1,r=!1,n=null,s="",i="",o=!1,a=""){this.env=t,this.taggable=e,this.defaultTags=r,this.tag=n,this.tagId=s,this.tagFormat=i,this.noTag=o,this.labelId=a}}class Vt{constructor(){this.counter=0,this.allCounter=0,this.configuration=null,this.ids={},this.allIds={},this.labels={},this.allLabels={},this.redo=!1,this.refUpdate=!1,this.currentTag=new Xt,this.history=[],this.stack=[],this.enTag=function(t,e){const r=this.configuration.nodeFactory,n=r.create("node","mtd",[t]),s=r.create("node","mlabeledtr",[e,n]);return r.create("node","mtable",[s],{side:this.configuration.options.tagSide,minlabelspacing:this.configuration.options.tagIndent,displaystyle:!0})}}start(t,e,r){this.currentTag&&this.stack.push(this.currentTag);const n=this.label;this.currentTag=new Xt(t,e,r),this.label=n}get env(){return this.currentTag.env}end(){this.history.push(this.currentTag);const t=this.label;this.currentTag=this.stack.pop(),t&&!this.label&&(this.label=t)}tag(t,e){this.currentTag.tag=t,this.currentTag.tagFormat=e?t:this.formatTag(t),this.currentTag.noTag=!1}notag(){this.tag("",!0),this.currentTag.noTag=!0}get noTag(){return this.currentTag.noTag}set label(t){this.currentTag.labelId=t}get label(){return this.currentTag.labelId}formatUrl(t,e){return e+"#"+encodeURIComponent(t)}formatTag(t){return"("+t+")"}formatRef(t){return this.formatTag(t)}formatId(t){return"mjx-eqn:"+t.replace(/\s/g,"_")}formatNumber(t){return t.toString()}autoTag(){null==this.currentTag.tag&&(this.counter++,this.tag(this.formatNumber(this.counter),!1))}clearTag(){this.tag(null,!0),this.currentTag.tagId=""}getTag(t=!1){if(t)return this.autoTag(),this.makeTag();const e=this.currentTag;return e.taggable&&!e.noTag&&(e.defaultTags&&this.autoTag(),e.tag)?this.makeTag():null}resetTag(){this.history=[],this.redo=!1,this.refUpdate=!1,this.clearTag()}reset(t=0){this.resetTag(),this.counter=this.allCounter=t,this.allLabels={},this.allIds={},this.label=""}startEquation(t){this.history=[],this.stack=[],this.clearTag(),this.currentTag=new Xt("",void 0,void 0),this.labels={},this.ids={},this.counter=this.allCounter,this.redo=!1;const e=t.inputData.recompile;e&&(this.refUpdate=!0,this.counter=e.counter)}finishEquation(t){this.redo&&(t.inputData.recompile={state:t.state(),counter:this.allCounter}),this.refUpdate||(this.allCounter=this.counter),Object.assign(this.allIds,this.ids),Object.assign(this.allLabels,this.labels)}finalize(t,e){if(!e.display||this.currentTag.env||null==this.currentTag.tag)return t;const r=this.makeTag();return this.enTag(t,r)}makeId(){this.currentTag.tagId=this.formatId(this.configuration.options.useLabelIds&&this.label||this.currentTag.tag)}makeTag(){this.makeId(),this.label&&(this.labels[this.label]=new jt(this.currentTag.tag,this.currentTag.tagId),this.label="");const t=new Lt("\\text{"+this.currentTag.tagFormat+"}",{},this.configuration).mml();return this.configuration.nodeFactory.create("node","mtd",[t],{id:this.currentTag.tagId,rowalign:this.configuration.options.tagAlign})}}const Wt=new Map([["none",class extends Vt{autoTag(){}getTag(){return this.currentTag.tag?super.getTag():null}}],["all",class extends Vt{finalize(t,e){if(!e.display||this.history.find((function(t){return t.taggable})))return t;const r=this.getTag(!0);return this.enTag(t,r)}}]]);let Kt="none";const zt={OPTIONS:{tags:Kt,tagSide:"right",tagIndent:"0.8em",useLabelIds:!0,ignoreDuplicateLabels:!1,tagAlign:"baseline"},add(t,e){Wt.set(t,e)},addTags(t){for(const e of Object.keys(t))zt.add(e,t[e])},create(t){const e=Wt.get(t)||Wt.get(Kt);if(!e)throw Error("Unknown tags class");return new e},setDefault(t){Kt=t},getDefault:()=>zt.create(Kt)};class Yt{constructor(t,e,r){this._token=t,this._char=e,this._attributes=r}get token(){return this._token}get char(){return this._char}get attributes(){return this._attributes}}class $t{constructor(t,e,r=[]){this._token=t,this._func=e,this._args=r}get token(){return this._token}get func(){return this._func}get args(){return this._args}}function Jt(t){return void 0===t||t}class Qt{constructor(t,e){this._name=t,this._parser=e,ae.register(this)}get name(){return this._name}parserFor(t){return this.contains(t)?this.parser:null}parse([t,e]){const r=this.parserFor(e),n=this.lookup(e);return r&&n?Jt(r(t,n)):null}set parser(t){this._parser=t}get parser(){return this._parser}}class Zt extends Qt{constructor(t,e,r){super(t,e),this._regExp=r}contains(t){return this._regExp.test(t)}lookup(t){return this.contains(t)?t:null}}class te extends Qt{constructor(){super(...arguments),this.map=new Map}lookup(t){return this.map.get(t)}contains(t){return this.map.has(t)}add(t,e){this.map.set(t,e)}remove(t){this.map.delete(t)}}class ee extends te{constructor(t,e,r){super(t,e);for(const t of Object.keys(r)){const e=r[t],[n,s]="string"==typeof e?[e,null]:e,i=new Yt(t,n,s);this.add(t,i)}}}class re extends ee{parse([t,e]){return super.parse([t,"\\"+e])}}class ne extends te{constructor(t,e,r={}){super(t,null);const n=t=>"string"==typeof t?r[t]:t;for(const[t,r]of Object.entries(e)){let e,s;Array.isArray(r)?(e=n(r[0]),s=r.slice(1)):(e=n(r),s=[]);const i=new $t(t,e,s);this.add(t,i)}}parserFor(t){const e=this.lookup(t);return e?e.func:null}parse([t,e]){const r=this.lookup(e),n=this.parserFor(e);return r&&n?Jt(n(t,r.token,...r.args)):null}}class se extends ne{parse([t,e]){const r=this.lookup(e),n=this.parserFor(e);if(!r||!n)return null;const s=t.currentCS;t.currentCS="\\"+e;const i=n(t,"\\"+r.token,...r.args);return t.currentCS=s,Jt(i)}}class ie extends ne{constructor(t,e,r,n={}){super(t,r,n),this.parser=e}parse([t,e]){const r=this.lookup(e),n=this.parserFor(e);return r&&n?Jt(this.parser(t,r.token,n,r.args)):null}}const oe=new Map,ae={register(t){oe.set(t.name,t)},getMap:t=>oe.get(t)};class ce{constructor(){this._configuration=new e,this._fallback=new I}add(t,r,n=e.DEFAULTPRIORITY){for(const e of t.slice().reverse()){const t=ae.getMap(e);if(!t)return void this.warn(`Configuration '${e}' not found! Omitted.`);this._configuration.add(t,n)}r&&this._fallback.add(r,n)}remove(t,e=null){for(const e of t){const t=this.retrieve(e);t&&this._configuration.remove(t)}e&&this._fallback.remove(e)}parse(t){for(const{item:e}of this._configuration){const r=e.parse(t);if(r===ce.FALLBACK)break;if(r)return r}const[e,r]=t;Array.from(this._fallback)[0].item(e,r)}lookup(t){const e=this.applicable(t);return e?e.lookup(t):null}contains(t){const e=this.applicable(t);return!(!e||e instanceof ee&&null===e.lookup(t).char)}toString(){const t=[];for(const{item:e}of this._configuration)t.push(e.name);return t.join(", ")}applicable(t){for(const{item:e}of this._configuration)if(e.contains(t))return e;return null}retrieve(t){for(const{item:e}of this._configuration)if(e.name===t)return e;return null}warn(t){console.log("TexParser Warning: "+t)}}ce.FALLBACK=Symbol("fallback");class le{constructor(){this.map=new Map}add(t,r,n=e.DEFAULTPRIORITY){for(const e of Object.keys(t)){const s=e;let i=this.get(s);i||(i=new ce,this.set(s,i)),i.add(t[s],r[s],n)}}remove(t,e){for(const r of Object.keys(t)){const n=this.get(r);n&&n.remove(t[r],e[r])}}set(t,e){this.map.set(t,e)}get(t){return this.map.get(t)}retrieve(t){for(const e of this.map.values()){const r=e.retrieve(t);if(r)return r}return null}keys(){return this.map.keys()}}class he{static makeProcessor(t,e){return Array.isArray(t)?t:[t,e]}static _create(t,r={}){var n;const s=null!==(n=r.priority)&&void 0!==n?n:e.DEFAULTPRIORITY,i=r.init?this.makeProcessor(r.init,s):null,o=r.config?this.makeProcessor(r.config,s):null,a=(r.preprocessors||[]).map((t=>this.makeProcessor(t,s))),c=(r.postprocessors||[]).map((t=>this.makeProcessor(t,s))),l=r.parser||"tex";return new he(t,r[ht.HANDLER]||{},r[ht.FALLBACK]||{},r[ht.ITEMS]||{},r[ht.TAGS]||{},r[ht.OPTIONS]||{},r[ht.NODES]||{},a,c,i,o,s,l)}static create(t,e={}){const r=he._create(t,e);return de.set(t,r),r}static local(t={}){return he._create("",t)}constructor(t,e={},r={},n={},s={},i={},o={},a=[],c=[],l=null,h=null,u,d){this.name=t,this.handler=e,this.fallback=r,this.items=n,this.tags=s,this.options=i,this.nodes=o,this.preprocessors=a,this.postprocessors=c,this.initMethod=l,this.configMethod=h,this.priority=u,this.parser=d,this.handler=Object.assign({[ut.CHARACTER]:[],[ut.DELIMITER]:[],[ut.MACRO]:[],[ut.ENVIRONMENT]:[]},e)}get init(){return this.initMethod?this.initMethod[0]:null}get config(){return this.configMethod?this.configMethod[0]:null}}const ue=new Map,de={set(t,e){ue.set(t,e)},get:t=>ue.get(t),keys:()=>ue.keys()};class pe{constructor(t,r=["tex"]){this.initMethod=new I,this.configMethod=new I,this.configurations=new e,this.parsers=[],this.handlers=new le,this.items={},this.tags={},this.options={},this.nodes={},this.parsers=r;for(const e of t.slice().reverse())this.addPackage(e);for(const{item:t,priority:e}of this.configurations)this.append(t,e)}init(){this.initMethod.execute(this)}config(t){this.configMethod.execute(this,t);for(const e of this.configurations)this.addFilters(t,e.item)}addPackage(t){const e="string"==typeof t?t:t[0],r=this.getPackage(e);r&&this.configurations.add(r,"string"==typeof t?r.priority:t[1])}add(t,e,r={}){const n=this.getPackage(t);this.append(n),this.configurations.add(n,n.priority),this.init();const s=e.parseOptions;s.nodeFactory.setCreators(n.nodes);for(const t of Object.keys(n.items))s.itemFactory.setNodeClass(t,n.items[t]);zt.addTags(n.tags),N(s.options,n.options),b(s.options,r),this.addFilters(e,n),n.config&&n.config(this,e)}getPackage(t){const e=de.get(t);if(e&&!this.parsers.includes(e.parser))throw Error(`Package '${t}' doesn't target the proper parser`);return e||this.warn(`Package '${t}' not found.  Omitted.`),e}append(t,e){e=e||t.priority,t.initMethod&&this.initMethod.add(t.initMethod[0],t.initMethod[1]),t.configMethod&&this.configMethod.add(t.configMethod[0],t.configMethod[1]),this.handlers.add(t.handler,t.fallback,e),Object.assign(this.items,t.items),Object.assign(this.tags,t.tags),N(this.options,t.options),Object.assign(this.nodes,t.nodes)}addFilters(t,e){for(const[r,n]of e.preprocessors)t.preFilters.add(r,n);for(const[r,n]of e.postprocessors)t.postFilters.add(r,n)}warn(t){console.warn("MathJax Warning: "+t)}}const me=["top","right","bottom","left"],fe=["width","style","color"];function ge(t){const e=t.split(/((?:'[^']*'|"[^"]*"|,[\s\n]|[^\s\n])*)/g),r=[];for(;e.length>1;)e.shift(),r.push(e.shift());return r}function Ee(t){const e=ge(this.styles[t]);0===e.length&&e.push(""),1===e.length&&e.push(e[0]),2===e.length&&e.push(e[0]),3===e.length&&e.push(e[1]);for(const r of Re.connect[t].children)this.setStyle(this.childName(t,r),e.shift())}const Ne=/^(?:[\d.]+(?:[a-z]+)|thin|medium|thick|inherit|initial|unset)$/,be=/^(?:none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset|inherit|initial|unset)$/;function Te(t){const e={width:"",style:"",color:""};for(const r of ge(this.styles[t]))r.match(Ne)&&""===e.width?e.width=r:r.match(be)&&""===e.style?e.style=r:e.color=r;for(const r of Re.connect[t].children)this.setStyle(this.childName(t,r),e[r])}function ye(t){const e=[];for(const r of Re.connect[t].children){const n=this.styles[this.childName(t,r)];n&&e.push(n)}e.length?this.styles[t]=e.join(" "):delete this.styles[t]}const Ie={style:/^(?:normal|italic|oblique|inherit|initial|unset)$/,variant:new RegExp("^(?:"+["normal|none","inherit|initial|unset","common-ligatures|no-common-ligatures","discretionary-ligatures|no-discretionary-ligatures","historical-ligatures|no-historical-ligatures","contextual|no-contextual","(?:stylistic|character-variant|swash|ornaments|annotation)\\([^)]*\\)","small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","lining-nums|oldstyle-nums|proportional-nums|tabular-nums","diagonal-fractions|stacked-fractions","ordinal|slashed-zero","jis78|jis83|jis90|jis04|simplified|traditional","full-width|proportional-width","ruby"].join("|")+")$"),weight:/^(?:normal|bold|bolder|lighter|[1-9]00|inherit|initial|unset)$/,stretch:new RegExp("^(?:"+["normal","(?:(?:ultra|extra|semi)-)?(?:condensed|expanded)","inherit|initial|unset"].join("|")+")$"),size:new RegExp("^(?:"+["xx-small|x-small|small|medium|large|x-large|xx-large|larger|smaller","[\\d.]+%|[\\d.]+[a-z]+","inherit|initial|unset"].join("|")+")(?:/(?:normal|[\\d.]+(?:%|[a-z]+)?))?$")};function Ae(t,e){for(const r of Re.connect[t].children){const n=this.childName(t,r);if(Array.isArray(e[r])){const t=e[r];t.length&&(this.styles[n]=t.join(" "))}else""!==e[r]&&(this.styles[n]=e[r])}}class Re{constructor(t=""){this.parse(t)}sanitizeValue(t){const e=this.constructor.pattern;if(!t.match(e.sanitize))return t;const r=(t=t.replace(e.value,"$1")).replace(/\\./g,"").replace(/(['"]).*?\1/g,"").replace(/[^'"]/g,"");return r.length&&(t+=r.charAt(0)),t}get cssText(){var t,e;const r=[];for(const n of Object.keys(this.styles)){const s=this.parentName(n),i=n.replace(/.*-/,"");this.styles[s]&&(null===(e=null===(t=Re.connect[s])||void 0===t?void 0:t.children)||void 0===e?void 0:e.includes(i))||r.push(`${n}: ${this.styles[n]};`)}return r.join(" ")}get styleList(){return Object.assign({},this.styles)}set(t,e){var r,n;for(t=this.normalizeName(t),this.setStyle(t,String(e)),Re.connect[t]&&!Re.connect[t].combine&&(this.combineChildren(t),delete this.styles[t]);t.match(/-/);){const e=t;if(t=this.parentName(t),!Re.connect[e]&&!(null===(n=null===(r=Re.connect[t])||void 0===r?void 0:r.children)||void 0===n?void 0:n.includes(e.substring(t.length+1))))break;Re.connect[t].combine.call(this,t)}}get(t){return t=this.normalizeName(t),Object.hasOwn(this.styles,t)?this.styles[t]:""}setStyle(t,e){this.styles[t]=this.sanitizeValue(e),Re.connect[t]&&Re.connect[t].children&&Re.connect[t].split.call(this,t),""===e&&delete this.styles[t]}combineChildren(t){const e=this.parentName(t);for(const r of Re.connect[t].children){const t=this.childName(e,r);Re.connect[t].combine.call(this,t)}}parentName(t){const e=t.replace(/-[^-]*$/,"");return t===e?"":e}childName(t,e){return e.match(/-/)?e:(Re.connect[t]&&!Re.connect[t].combine&&(e+=t.replace(/.*-/,"-"),t=this.parentName(t)),t+"-"+e)}normalizeName(t){return t.replace(/[A-Z]/g,(t=>"-"+t.toLowerCase()))}parse(t=""){const e=this.constructor.pattern;this.styles={};const r=t.replace(/\n/g," ").replace(e.comment,"").split(e.style);for(;r.length>1;){const[t,e,n]=r.splice(0,3);if(t.match(/[^\s\n;]/))return;this.set(e,n)}}}Re.pattern={sanitize:/['";]/,value:/^((:?'(?:\\.|[^'])*(?:'|$)|"(?:\\.|[^"])*(?:"|$)|\n|\\.|[^'";])*?)[\s\n]*(?:;|$).*/,style:/([-a-z]+)[\s\n]*:[\s\n]*((?:'(?:\\.|[^'])*(?:'|$)|"(?:\\.|[^"])*(?:"|$)|\n|\\.|[^'";])*?)[\s\n]*(?:;|$)/g,comment:/\/\*[^]*?\*\//g},Re.connect={padding:{children:me,split:Ee,combine:function(t){const e=Re.connect[t].children,r=[];for(const n of e){const e=this.styles[t+"-"+n];if(!e)return void delete this.styles[t];r.push(e)}r[3]===r[1]&&(r.pop(),r[2]===r[0]&&(r.pop(),r[1]===r[0]&&r.pop())),this.styles[t]=r.join(" ")}},border:{children:me,split:function(t){for(const e of Re.connect[t].children)this.setStyle(this.childName(t,e),this.styles[t])},combine:function(t){const e=[...Re.connect[t].children],r=this.styles[this.childName(t,e.shift())];for(const n of e)if(this.styles[this.childName(t,n)]!==r)return void delete this.styles[t];this.styles[t]=r}},"border-top":{children:fe,split:Te,combine:ye},"border-right":{children:fe,split:Te,combine:ye},"border-bottom":{children:fe,split:Te,combine:ye},"border-left":{children:fe,split:Te,combine:ye},"border-width":{children:me,split:Ee,combine:null},"border-style":{children:me,split:Ee,combine:null},"border-color":{children:me,split:Ee,combine:null},font:{children:["style","variant","weight","stretch","line-height","size","family"],split:function(t){const e=ge(this.styles[t]),r={style:"",variant:[],weight:"",stretch:"",size:"",family:"","line-height":""};for(const t of e){r.family||(r.family=t);for(const e of Object.keys(Ie))if((Array.isArray(r[e])||""===r[e])&&t.match(Ie[e]))if(r.family===t&&(r.family=""),"size"===e){const[n,s]=t.split(/\//);r[e]=n,s&&(r["line-height"]=s)}else""===r.size&&(Array.isArray(r[e])?r[e].push(t):""===r[e]&&(r[e]=t))}Ae.call(this,t,r),delete this.styles[t]},combine:function(t){}}};class Le extends Nt{constructor(t,e){super(t),this.global=e}get kind(){return"start"}get isOpen(){return!0}checkItem(t){if(t.isKind("stop")){let t=this.toMml();return this.global.isInner||(t=this.factory.configuration.tags.finalize(t,this.env)),[[this.factory.create("mml",t)],!0]}return super.checkItem(t)}}class Ce extends Nt{get kind(){return"stop"}get isClose(){return!0}}class Oe extends Nt{get kind(){return"open"}get isOpen(){return!0}checkItem(t){if(t.isKind("close")){const e=this.toMml(),r=this.create("node","TeXAtom",[e]);return Je(r,t),[[this.factory.create("mml",r)],!0]}return super.checkItem(t)}}Oe.errors=Object.assign(Object.create(Nt.errors),{stop:["ExtraOpenMissingClose","Extra open brace or missing close brace"]});class ve extends Nt{get kind(){return"close"}get isClose(){return!0}}class Se extends Nt{get kind(){return"null"}}class we extends Nt{get kind(){return"prime"}checkItem(t){const[e,r]=this.Peek(2),n=(nt.isType(e,"msubsup")||nt.isType(e,"msup"))&&!nt.getChildAt(e,e.sup),s=(nt.isType(e,"munderover")||nt.isType(e,"mover"))&&!nt.getChildAt(e,e.over)&&!nt.getProperty(e,"subsupOK");if(!n&&!s){return[[this.create("node",e.getProperty("movesupsub")?"mover":"msup",[e,r]),t],!0]}const i=n?e.sup:e.over;return nt.setChild(e,i,r),[[e,t],!0]}}class Me extends Nt{get kind(){return"subsup"}checkItem(t){if(t.isKind("open")||t.isKind("left"))return Nt.success;const e=this.First,r=this.getProperty("position");if(t.isKind("mml")){if(this.getProperty("primes"))if(2!==r)nt.setChild(e,2,this.getProperty("primes"));else{nt.setProperty(this.getProperty("primes"),"variantForm",!0);const e=this.create("node","mrow",[this.getProperty("primes"),t.First]);t.First=e}nt.setChild(e,r,t.First),null!=this.getProperty("movesupsub")&&nt.setProperty(e,"movesupsub",this.getProperty("movesupsub"));return[[this.factory.create("mml",e)],!0]}super.checkItem(t);const n=this.getErrors(["","sub","sup"][r]);throw new gt(n[0],n[1],...n.splice(2))}}Me.errors=Object.assign(Object.create(Nt.errors),{stop:["MissingScript","Missing superscript or subscript argument"],sup:["MissingOpenForSup","Missing open brace for superscript"],sub:["MissingOpenForSub","Missing open brace for subscript"]});class xe extends Nt{constructor(t){super(t),this.setProperty("name","\\over")}get kind(){return"over"}get isClose(){return!0}checkItem(t){if(t.isKind("over"))throw new gt("AmbiguousUseOf","Ambiguous use of %1",t.getName());if(t.isClose){let e=this.create("node","mfrac",[this.getProperty("num"),this.toMml(!1)]);return null!=this.getProperty("thickness")&&nt.setAttribute(e,"linethickness",this.getProperty("thickness")),(this.getProperty("ldelim")||this.getProperty("rdelim"))&&(nt.setProperty(e,"withDelims",!0),e=Bt.fixedFence(this.factory.configuration,this.getProperty("ldelim"),e,this.getProperty("rdelim"))),e.attributes.set(Rt.LATEXITEM,this.getProperty("name")),[[this.factory.create("mml",e),t],!0]}return super.checkItem(t)}toString(){return"over["+this.getProperty("num")+" / "+this.nodes.join("; ")+"]"}}class Pe extends Nt{constructor(t,e){super(t),this.setProperty("delim",e)}get kind(){return"left"}get isOpen(){return!0}checkItem(t){if(t.isKind("right")){const e=Bt.fenced(this.factory.configuration,this.getProperty("delim"),this.toMml(),t.getProperty("delim"),"",t.getProperty("color")),r=e.childNodes[0],n=e.childNodes[e.childNodes.length-1],s=this.factory.create("mml",e);return Je(r,this,"\\left"),Je(n,t,"\\right"),s.Peek()[0].attributes.set(Rt.LATEXITEM,"\\left"+t.startStr.slice(this.startI,t.stopI)),[[s],!0]}if(t.isKind("middle")){const e={stretchy:!0};t.getProperty("color")&&(e.mathcolor=t.getProperty("color"));const r=this.create("token","mo",e,t.getProperty("delim"));return Je(r,t,"\\middle"),this.Push(this.create("node","TeXAtom",[],{texClass:F.CLOSE}),r,this.create("node","TeXAtom",[],{texClass:F.OPEN})),this.env={},[[this],!0]}return super.checkItem(t)}}Pe.errors=Object.assign(Object.create(Nt.errors),{stop:["ExtraLeftMissingRight","Extra \\left or missing \\right"]});class De extends Nt{constructor(t,e,r){super(t),this.setProperty("delim",e),r&&this.setProperty("color",r)}get kind(){return"middle"}get isClose(){return!0}}class ke extends Nt{constructor(t,e,r){super(t),this.setProperty("delim",e),r&&this.setProperty("color",r)}get kind(){return"right"}get isClose(){return!0}}class _e extends Nt{get kind(){return"break"}constructor(t,e,r){super(t),this.setProperty("linebreak",e),this.setProperty("insert",r)}checkItem(t){var e,r;const n=this.getProperty("linebreak");if(t.isKind("mml")){const s=t.First;if(s.isKind("mo")){if("after"!==((null===(r=null===(e=nt.getOp(s))||void 0===e?void 0:e[3])||void 0===r?void 0:r.linebreakstyle)||nt.getAttribute(s,"linebreakstyle")))return nt.setAttribute(s,"linebreak",n),[[t],!0];if(!this.getProperty("insert"))return[[t],!0]}}const s=this.create("token","mspace",{linebreak:n});return[[this.factory.create("mml",s),t],!0]}}class Fe extends Nt{get kind(){return"begin"}get isOpen(){return!0}checkItem(t){if(t.isKind("end")){if(t.getName()!==this.getName())throw new gt("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),t.getName());const e=this.toMml();return Je(e,t),[[this.factory.create("mml",e)],!0]}if(t.isKind("stop"))throw new gt("EnvMissingEnd","Missing \\end{%1}",this.getName());return super.checkItem(t)}}class Be extends Nt{get kind(){return"end"}get isClose(){return!0}}class Ue extends Nt{get kind(){return"style"}checkItem(t){if(!t.isClose)return super.checkItem(t);const e=this.create("node","mstyle",this.nodes,this.getProperty("styles"));return[[this.factory.create("mml",e),t],!0]}}class qe extends Nt{get kind(){return"position"}checkItem(t){if(t.isClose)throw new gt("MissingBoxFor","Missing box for %1",this.getName());if(t.isFinal){let e=t.toMml();switch(this.getProperty("move")){case"vertical":return e=this.create("node","mpadded",[e],{height:this.getProperty("dh"),depth:this.getProperty("dd"),voffset:this.getProperty("dh")}),[[this.factory.create("mml",e)],!0];case"horizontal":return[[this.factory.create("mml",this.getProperty("left")),t,this.factory.create("mml",this.getProperty("right"))],!0]}}return super.checkItem(t)}}class He extends Nt{get kind(){return"cell"}get isClose(){return!0}}class Ge extends Nt{get isFinal(){return!0}get kind(){return"mml"}}class je extends Nt{get kind(){return"fn"}checkItem(t){const e=this.First;if(e){if(t.isOpen)return Nt.success;if(!t.isKind("fn")){let r=t.First;if(!t.isKind("mml")||!r)return[[e,t],!0];if(nt.isType(r,"mstyle")&&r.childNodes.length&&nt.isType(r.childNodes[0].childNodes[0],"mspace")||nt.isType(r,"mspace"))return[[e,t],!0];nt.isEmbellished(r)&&(r=nt.getCoreMO(r));const n=nt.getForm(r);if(null!=n&&[0,0,1,1,0,1,1,0,0,0][n[2]])return[[e,t],!0]}return[[e,this.create("token","mo",{texClass:F.NONE},xt.ApplyFunction),t],!0]}return super.checkItem(t)}}class Xe extends Nt{constructor(){super(...arguments),this.remap=ae.getMap("not_remap")}get kind(){return"not"}checkItem(t){let e,r,n;if(t.isKind("open")||t.isKind("left"))return Nt.success;if(t.isKind("mml")&&(nt.isType(t.First,"mo")||nt.isType(t.First,"mi")||nt.isType(t.First,"mtext"))&&(e=t.First,r=nt.getText(e),1===r.length&&!nt.getProperty(e,"movesupsub")&&1===nt.getChildren(e).length))return this.remap.contains(r)?(n=this.create("text",this.remap.lookup(r).char),nt.setChild(e,0,n)):(n=this.create("text","\u0338"),nt.appendChildren(e,[n])),[[t],!0];n=this.create("text","\u29f8");const s=this.create("node","mtext",[],{},n),i=this.create("node","mpadded",[s],{width:0});return e=this.create("node","TeXAtom",[i],{texClass:F.REL}),[[e,t],!0]}}class Ve extends Nt{get kind(){return"nonscript"}checkItem(t){if(t.isKind("mml")&&1===t.Size()){let e=t.First;if(e.isKind("mstyle")&&e.notParent&&(e=nt.getChildren(nt.getChildren(e)[0])[0]),e.isKind("mspace")){if(e!==t.First){const e=this.create("node","mrow",[t.Pop()]);t.Push(e)}this.factory.configuration.addNode("nonscript",t.First)}}return[[t],!0]}}class We extends Nt{get kind(){return"dots"}checkItem(t){if(t.isKind("open")||t.isKind("left"))return Nt.success;let e=this.getProperty("ldots");const r=t.First;if(t.isKind("mml")&&nt.isEmbellished(r)){const t=nt.getTexClass(nt.getCoreMO(r));t!==F.BIN&&t!==F.REL||(e=this.getProperty("cdots"))}return[[e,t],!0]}}class Ke extends Nt{constructor(){super(...arguments),this.table=[],this.row=[],this.frame=[],this.hfill=[],this.arraydef={},this.cstart=[],this.cend=[],this.cextra=[],this.atEnd=!1,this.ralign=[],this.breakAlign={cell:"",row:"",table:""},this.templateSubs=0}get kind(){return"array"}get isOpen(){return!0}get copyEnv(){return!1}checkItem(t){if(t.isClose&&!t.isKind("over")){if(t.getProperty("isEntry"))return this.EndEntry(),this.clearEnv(),this.StartEntry(),Nt.fail;if(t.getProperty("isCR"))return this.EndEntry(),this.EndRow(),this.clearEnv(),this.StartEntry(),Nt.fail;this.EndTable(),this.clearEnv();const e=this.factory.create("mml",this.createMml());if(this.getProperty("requireClose")){if(t.isKind("close"))return[[e],!0];throw new gt("MissingCloseBrace","Missing close brace")}return[[e,t],!0]}return super.checkItem(t)}createMml(){const t=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;let e=this.create("node","mtable",this.table,this.arraydef);return t&&e.setProperty("smallmatrix",!0),this.breakAlign.table&&nt.setAttribute(e,"data-break-align",this.breakAlign.table),this.getProperty("arrayPadding")&&(nt.setAttribute(e,"data-frame-styles",""),nt.setAttribute(e,"framespacing",this.getProperty("arrayPadding"))),e=this.handleFrame(e),void 0!==t&&(e=this.create("node","mstyle",[e],{scriptlevel:t})),(this.getProperty("open")||this.getProperty("close"))&&(e=Bt.fenced(this.factory.configuration,this.getProperty("open"),e,this.getProperty("close"))),e}handleFrame(t){if(!this.frame.length)return t;const e=new Map(this.frame),r=this.frame.reduce(((t,[,e])=>e===t?e:""),this.frame[0][1]);if(r){if(4===this.frame.length)return nt.setAttribute(t,"frame",r),nt.removeAttribute(t,"data-frame-styles"),t;if("solid"===r)return nt.setAttribute(t,"data-frame-styles",""),t=this.create("node","menclose",[t],{notation:Array.from(e.keys()).join(" "),"data-padding":0})}const n=me.map((t=>e.get(t)||"none")).join(" ");return nt.setAttribute(t,"data-frame-styles",n),t}StartEntry(){const t=this.row.length;let e=this.cstart[t],r=this.cend[t];const n=this.ralign[t],s=this.cextra;if(!(e||r||n||s[t]||s[t+1]))return;let[i,o,a,c]=this.getEntry();if(!s[t]||this.atEnd&&!s[t+1]||(e+="&"),"&"!==a&&(c=!!o.trim()||!!(t||a&&"\\end"!==a.substring(0,4)),s[t+1]&&!s[t]&&(r=(r||"")+"&",this.atEnd=!0)),!c&&!i)return;const l=this.parser;if(c&&(e&&(o=Bt.addArgs(l,e,o)),r&&(o=Bt.addArgs(l,o,r)),n&&(o="\\text{"+o.trim()+"}"),(e||r||n)&&++this.templateSubs>l.configuration.options.maxTemplateSubtitutions))throw new gt("MaxTemplateSubs","Maximum template substitutions exceeded; is there an invalid use of \\\\ in the template?");i&&(o=Bt.addArgs(l,i,o)),l.string=Bt.addArgs(l,o,l.string),l.i=0}getEntry(){const t=this.parser,e=/^([^]*?)([&{}]|\\\\|\\(?:begin|end)\s*\{array\}|\\cr|\\)/;let r,n=0,s=0,i=t.i;const o=["","","",!1];for(;null!==(r=t.string.slice(i).match(e));)switch(i+=r[0].length,r[2]){case"\\":i++;break;case"{":n++;break;case"}":if(!n)return o;n--;break;case"\\begin{array}":n||s++;break;case"\\end{array}":if(!n&&s){s--;break}default:{if(n||s)continue;i-=r[2].length;let e=t.string.slice(t.i,i).trim();const o=e.match(/^(?:\s*\\(?:h(?:dash)?line|hfil{1,3}|rowcolor\s*\{.*?\}))+/);return o&&(e=e.slice(o[0].length)),t.string=t.string.slice(i),t.i=0,[(null==o?void 0:o[0])||"",e,r[2],!0]}}return o}EndEntry(){const t=this.create("node","mtd",this.nodes);this.hfill.length&&(0===this.hfill[0]&&nt.setAttribute(t,"columnalign","right"),this.hfill[this.hfill.length-1]===this.Size()&&nt.setAttribute(t,"columnalign",nt.getAttribute(t,"columnalign")?"center":"left"));const e=this.ralign[this.row.length];if(e){const[r,n,s]=e,i=this.create("node","mpadded",t.childNodes[0].childNodes,{width:n,"data-overflow":"auto","data-align":s,"data-vertical-align":r});i.setProperty("vbox",r),t.childNodes[0].childNodes=[],t.appendChild(i)}else this.breakAlign.cell&&nt.setAttribute(t,"data-vertical-align",this.breakAlign.cell);this.breakAlign.cell="",this.row.push(t),this.Clear(),this.hfill=[]}EndRow(){let t="mtr";this.getProperty("isNumbered")&&3===this.row.length?(this.row.unshift(this.row.pop()),t="mlabeledtr"):this.getProperty("isLabeled")&&(t="mlabeledtr",this.setProperty("isLabeled",!1));const e=this.create("node",t,this.row);this.breakAlign.row&&(nt.setAttribute(e,"data-break-align",this.breakAlign.row),this.breakAlign.row=""),Je(e,this),this.table.push(e),this.row=[],this.atEnd=!1}EndTable(){(this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()}checkLines(){if(this.arraydef.rowlines){const t=this.arraydef.rowlines.split(/ /);t.length===this.table.length?(this.frame.push(["bottom",t.pop()]),t.length?this.arraydef.rowlines=t.join(" "):delete this.arraydef.rowlines):t.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.getProperty("rowspacing")){const t=this.arraydef.rowspacing.split(/ /);for(;t.length<this.table.length;)t.push(this.getProperty("rowspacing")+"em");this.arraydef.rowspacing=t.join(" ")}}addRowSpacing(t){if(this.arraydef.rowspacing){const e=this.arraydef.rowspacing.split(/ /);if(!this.getProperty("rowspacing")){const t=pt.dimen2em(e[0]);this.setProperty("rowspacing",t)}const r=this.getProperty("rowspacing");for(;e.length<this.table.length;)e.push(pt.em(r));e[this.table.length-1]=pt.em(Math.max(0,r+pt.dimen2em(t))),this.arraydef.rowspacing=e.join(" ")}}}class ze extends Ke{constructor(t,...e){super(t),this.maxrow=0,this.factory.configuration.tags.start(e[0],e[2],e[1])}get kind(){return"eqnarray"}EndEntry(){const t=this.arraydef.columnalign.split(/ /);"right"!==(this.row.length&&t.length?t[this.row.length%t.length]:"right")&&Bt.fixInitialMO(this.factory.configuration,this.nodes),super.EndEntry()}EndRow(){this.row.length>this.maxrow&&(this.maxrow=this.row.length);const t=this.factory.configuration.tags.getTag();t&&(this.row=[t].concat(this.row),this.setProperty("isLabeled",!0)),this.factory.configuration.tags.clearTag(),super.EndRow()}EndTable(){super.EndTable(),this.factory.configuration.tags.end(),this.extendArray("columnalign",this.maxrow),this.extendArray("columnwidth",this.maxrow),this.extendArray("columnspacing",this.maxrow-1),this.extendArray("data-break-align",this.maxrow),this.addIndentshift()}extendArray(t,e){if(!this.arraydef[t])return;const r=this.arraydef[t].split(/ /),n=[...r];if(n.length>1){for(;n.length<e;)n.push(...r);this.arraydef[t]=n.slice(0,e).join(" ")}}addIndentshift(){const t=this.arraydef.columnalign.split(/ /);let e="";for(const r of t.keys()){if("left"===t[r]&&r>0){const t="center"===e?".7em":"2em";for(const e of this.table){const n=e.childNodes[e.isKind("mlabeledtr")?r+1:r];if(n){const e=this.create("node","mstyle",n.childNodes[0].childNodes,{indentshift:t});n.childNodes[0].childNodes=[],n.appendChild(e)}}}e=t[r]}}}class Ye extends Fe{get kind(){return"mstyle"}constructor(t,e,r){super(t),this.attrList=e,this.setProperty("name",r)}checkItem(t){if(t.isKind("end")&&t.getName()===this.getName()){return[[this.create("node","mstyle",[this.toMml()],this.attrList)],!0]}return super.checkItem(t)}}class $e extends Nt{constructor(t,...e){super(t),this.factory.configuration.tags.start("equation",!0,e[0])}get kind(){return"equation"}get isOpen(){return!0}checkItem(t){if(t.isKind("end")){const e=this.toMml(),r=this.factory.configuration.tags.getTag();return this.factory.configuration.tags.end(),[[r?this.factory.configuration.tags.enTag(e,r):e,t],!0]}if(t.isKind("stop"))throw new gt("EnvMissingEnd","Missing \\end{%1}",this.getName());return super.checkItem(t)}}function Je(t,e,r=""){const n=e.startStr.slice(e.startI,e.stopI);n&&(t.attributes.set(Rt.LATEXITEM,r?r+n:n),t.attributes.set(Rt.LATEX,r?r+n:n))}const Qe=1e6,Ze={veryverythinmathspace:1/18,verythinmathspace:2/18,thinmathspace:3/18,mediummathspace:4/18,thickmathspace:5/18,verythickmathspace:6/18,veryverythickmathspace:7/18,negativeveryverythinmathspace:-1/18,negativeverythinmathspace:-2/18,negativethinmathspace:-3/18,negativemediummathspace:-4/18,negativethickmathspace:-5/18,negativeverythickmathspace:-6/18,negativeveryverythickmathspace:-7/18,thin:.04,medium:.06,thick:.1,normal:1,big:2,small:1/Math.sqrt(2),infinity:Qe};function tr(t){return Math.abs(t)<.001?"0":t.toFixed(3).replace(/\.?0+$/,"")+"em"}const er=1.2/.85,rr={fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1};function nr(t,e=1/0){const r=t.replace(/\s+/g,"").split("").map((t=>{const e={t:"top",b:"bottom",m:"middle",c:"center"}[t];if(!e)throw new gt("BadBreakAlign","Invalid alignment character: %1",t);return e}));if(r.length>e)throw new gt("TooManyAligns","Too many alignment characters: %1",t);return 1===e?r[0]:r.join(" ")}function sr(t,e){const r=t.stack.env,n=r.inRoot;r.inRoot=!0;const s=new Lt(e,r,t.configuration);let i=s.mml();const o=s.stack.global;if(o.leftRoot||o.upRoot){const e={};o.leftRoot&&(e.width=o.leftRoot),o.upRoot&&(e.voffset=o.upRoot,e.height=o.upRoot),i=t.create("node","mpadded",[i],e)}return r.inRoot=n,i}const ir={Open(t,e){t.Push(t.itemFactory.create("open"))},Close(t,e){t.Push(t.itemFactory.create("close"))},Bar(t,e){t.Push(t.create("token","mo",{stretchy:!1,texClass:F.ORD},e))},Tilde(t,e){t.Push(t.create("token","mtext",{},xt.nbsp))},Space(t,e){},Superscript(t,e){let r,n;t.GetNext().match(/\d/)&&(t.string=t.string.substring(0,t.i+1)+" "+t.string.substring(t.i+1));const s=t.stack.Top();s.isKind("prime")?([n,r]=s.Peek(2),t.stack.Pop()):(n=t.stack.Prev(),n||(n=t.create("token","mi",{},"")));const i=nt.getProperty(n,"movesupsub");let o=nt.isType(n,"msubsup")?n.sup:n.over;if(nt.isType(n,"msubsup")&&!nt.isType(n,"msup")&&nt.getChildAt(n,n.sup)||nt.isType(n,"munderover")&&!nt.isType(n,"mover")&&nt.getChildAt(n,n.over)&&!nt.getProperty(n,"subsupOK"))throw new gt("DoubleExponent","Double exponent: use braces to clarify");nt.isType(n,"msubsup")&&!nt.isType(n,"msup")||(i?((!nt.isType(n,"munderover")||nt.isType(n,"mover")||nt.getChildAt(n,n.over))&&(n=t.create("node","munderover",[n],{movesupsub:!0})),o=n.over):(n=t.create("node","msubsup",[n]),o=n.sup)),t.Push(t.itemFactory.create("subsup",n).setProperties({position:o,primes:r,movesupsub:i}))},Subscript(t,e){let r,n;t.GetNext().match(/\d/)&&(t.string=t.string.substring(0,t.i+1)+" "+t.string.substring(t.i+1));const s=t.stack.Top();s.isKind("prime")?([n,r]=s.Peek(2),t.stack.Pop()):(n=t.stack.Prev(),n||(n=t.create("token","mi",{},"")));const i=nt.getProperty(n,"movesupsub");let o=nt.isType(n,"msubsup")?n.sub:n.under;if(nt.isType(n,"msubsup")&&!nt.isType(n,"msup")&&nt.getChildAt(n,n.sub)||nt.isType(n,"munderover")&&!nt.isType(n,"mover")&&nt.getChildAt(n,n.under)&&!nt.getProperty(n,"subsupOK"))throw new gt("DoubleSubscripts","Double subscripts: use braces to clarify");nt.isType(n,"msubsup")&&!nt.isType(n,"msup")||(i?((!nt.isType(n,"munderover")||nt.isType(n,"mover")||nt.getChildAt(n,n.under))&&(n=t.create("node","munderover",[n],{movesupsub:!0})),o=n.under):(n=t.create("node","msubsup",[n]),o=n.sub)),t.Push(t.itemFactory.create("subsup",n).setProperties({position:o,primes:r,movesupsub:i}))},Prime(t,e){let r=t.stack.Prev();if(r||(r=t.create("token","mi")),nt.isType(r,"msubsup")&&!nt.isType(r,"msup")&&nt.getChildAt(r,r.sup)||nt.isType(r,"munderover")&&!nt.isType(r,"mover")&&nt.getChildAt(r,r.over)&&!nt.getProperty(r,"subsupOK"))throw new gt("DoubleExponentPrime","Prime causes double exponent: use braces to clarify");let n="";t.i--;do{n+=xt.prime,t.i++,e=t.GetNext()}while("'"===e||e===xt.rsquo);n=["","\u2032","\u2033","\u2034","\u2057"][n.length]||n;const s=t.create("token","mo",{variantForm:!0},n);t.Push(t.itemFactory.create("prime",r,s))},Comment(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++},Hash(t,e){throw new gt("CantUseHash1","You can't use 'macro parameter character #' in math mode")},MathFont(t,e,r,n=""){const s=t.GetArgument(e),i=new Lt(s,Object.assign(Object.assign({multiLetterIdentifiers:t.options.identifierPattern},t.stack.env),{font:r,italicFont:n,noAutoOP:!0}),t.configuration).mml();t.Push(t.create("node","TeXAtom",[i]))},SetFont(t,e,r){t.stack.env.font=r,t.Push(t.itemFactory.create("null"))},SetStyle(t,e,r,n,s){t.stack.env.style=r,t.stack.env.level=s,t.Push(t.itemFactory.create("style").setProperty("styles",{displaystyle:n,scriptlevel:s}))},SetSize(t,e,r){t.stack.env.size=r,t.Push(t.itemFactory.create("style").setProperty("styles",{mathsize:tr(r)}))},Spacer(t,e,r){const n=t.create("node","mspace",[],{width:tr(r)}),s=t.create("node","mstyle",[n],{scriptlevel:0});t.Push(s)},DiscretionaryTimes(t,e){t.Push(t.create("token","mo",{linebreakmultchar:"\xd7"},"\u2062"))},AllowBreak(t,e){t.Push(t.create("token","mspace"))},Break(t,e){t.Push(t.create("token","mspace",{linebreak:yt.NEWLINE}))},Linebreak(t,e,r){let n=!0;const s=t.stack.Prev(!0);if(s&&s.isKind("mo")){nt.getMoAttribute(s,"linebreakstyle")!==It.BEFORE&&(s.attributes.set("linebreak",r),n=!1)}t.Push(t.itemFactory.create("break",r,n))},LeftRight(t,e){const r=e.substring(1);t.Push(t.itemFactory.create(r,t.GetDelimiter(e),t.stack.env.color))},NamedFn(t,e,r){r||(r=e.substring(1));const n=t.create("token","mi",{texClass:F.OP},r);t.Push(t.itemFactory.create("fn",n))},NamedOp(t,e,r){r||(r=e.substring(1)),r=r.replace(/&thinsp;/,"\u2006");const n=t.create("token","mo",{movablelimits:!0,movesupsub:!0,form:Tt.PREFIX,texClass:F.OP},r);t.Push(n)},Limits(t,e,r){let n=t.stack.Prev(!0);if(!n||nt.getTexClass(nt.getCoreMO(n))!==F.OP&&null==nt.getProperty(n,"movesupsub"))throw new gt("MisplacedLimits","%1 is allowed only on operators",t.currentCS);const s=t.stack.Top();let i;nt.isType(n,"munderover")&&!r?(i=t.create("node","msubsup"),nt.copyChildren(n,i),n=s.Last=i):nt.isType(n,"msubsup")&&r&&(i=t.create("node","munderover"),nt.copyChildren(n,i),n=s.Last=i),nt.setProperty(n,"movesupsub",!!r),nt.setProperties(nt.getCoreMO(n),{movablelimits:!1}),((nt.isType(n,"mo")?nt.getMoAttribute(n,"movableLimits"):nt.getAttribute(n,"movablelimits"))||nt.getProperty(n,"movablelimits"))&&nt.setProperties(n,{movablelimits:!1})},Over(t,e,r,n){const s=t.itemFactory.create("over").setProperty("name",t.currentCS);r||n?(s.setProperty("ldelim",r),s.setProperty("rdelim",n)):e.match(/withdelims$/)&&(s.setProperty("ldelim",t.GetDelimiter(e)),s.setProperty("rdelim",t.GetDelimiter(e))),e.match(/^\\above/)?s.setProperty("thickness",t.GetDimen(e)):(e.match(/^\\atop/)||r||n)&&s.setProperty("thickness",0),t.Push(s)},Frac(t,e){const r=t.ParseArg(e),n=t.ParseArg(e),s=t.create("node","mfrac",[r,n]);t.Push(s)},Sqrt(t,e){const r=t.GetBrackets(e);let n=t.GetArgument(e);"\\frac"===n&&(n+="{"+t.GetArgument(n)+"}{"+t.GetArgument(n)+"}");let s=new Lt(n,t.stack.env,t.configuration).mml();s=r?t.create("node","mroot",[s,sr(t,r)]):t.create("node","msqrt",[s]),t.Push(s)},Root(t,e){const r=t.GetUpTo(e,"\\of"),n=t.ParseArg(e),s=t.create("node","mroot",[n,sr(t,r)]);t.Push(s)},MoveRoot(t,e,r){if(!t.stack.env.inRoot)throw new gt("MisplacedMoveRoot","%1 can appear only within a root",t.currentCS);if(t.stack.global[r])throw new gt("MultipleMoveRoot","Multiple use of %1",t.currentCS);let n=t.GetArgument(e);if(!n.match(/-?[0-9]+/))throw new gt("IntegerArg","The argument to %1 must be an integer",t.currentCS);n=parseInt(n,10)/15+"em","-"!==n.substring(0,1)&&(n="+"+n),t.stack.global[r]=n},Accent(t,e,r,n){const s=t.ParseArg(e),i=Object.assign(Object.assign({},Bt.getFontDef(t)),{accent:!0,mathaccent:void 0===n||n}),o=nt.createEntity(r),a=t.create("token","mo",i,o);nt.setAttribute(a,"stretchy",!!n);const c=nt.isEmbellished(s)?nt.getCoreMO(s):s;(nt.isType(c,"mo")||nt.getProperty(c,"movablelimits"))&&nt.setProperties(c,{movablelimits:!1});const l=t.create("node","munderover");nt.setChild(l,0,s),nt.setChild(l,1,null),nt.setChild(l,2,a);const h=t.create("node","TeXAtom",[l]);t.Push(h)},UnderOver(t,e,r,n){const s=nt.createEntity(r),i=t.create("token","mo",{stretchy:!0,accent:!0},s);s.match(et.mathaccentsWithWidth)&&i.setProperty("mathaccent",!1);const o="o"===e.charAt(1)?"over":"under",a=t.ParseArg(e);t.Push(Bt.underOver(t,a,i,o,n))},Overset(t,e){const r=t.ParseArg(e),n=t.ParseArg(e),s=r.coreMO(),i=s.isKind("mo")&&!0===nt.getMoAttribute(s,"accent");Bt.checkMovableLimits(n);const o=t.create("node","mover",[n,r],{accent:i});t.Push(o)},Underset(t,e){const r=t.ParseArg(e),n=t.ParseArg(e),s=r.coreMO(),i=s.isKind("mo")&&!0===nt.getMoAttribute(s,"accent");Bt.checkMovableLimits(n);const o=t.create("node","munder",[n,r],{accentunder:i});t.Push(o)},Overunderset(t,e){const r=t.ParseArg(e),n=t.ParseArg(e),s=t.ParseArg(e),i=r.coreMO(),o=n.coreMO(),a=i.isKind("mo")&&!0===nt.getMoAttribute(i,"accent"),c=o.isKind("mo")&&!0===nt.getMoAttribute(o,"accent");Bt.checkMovableLimits(s);const l=t.create("node","munderover",[s,n,r],{accent:a,accentunder:c});t.Push(l)},TeXAtom(t,e,r){const n={texClass:r};let s,i;if(r===F.OP){n.movesupsub=n.movablelimits=!0;const r=t.GetArgument(e),o=r.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);if(o)n.mathvariant=bt.NORMAL,i=t.create("token","mi",n,o[1]);else{const e=new Lt(r,t.stack.env,t.configuration).mml();i=t.create("node","TeXAtom",[e],n)}s=t.itemFactory.create("fn",i)}else s=t.create("node","TeXAtom",[t.ParseArg(e)],n);t.Push(s)},VBox(t,e,r){const n=new Lt(t.GetArgument(e),t.stack.env,t.configuration),s={"data-vertical-align":r,texClass:F.ORD};n.stack.env.hsize&&(s.width=n.stack.env.hsize,s["data-overflow"]="linebreak");const i=t.create("node","mpadded",[n.mml()],s);i.setProperty("vbox",r),t.Push(i)},Hsize(t,e){"="===t.GetNext()&&t.i++,t.stack.env.hsize=t.GetDimen(e),t.Push(t.itemFactory.create("null"))},ParBox(t,e){const r=t.GetBrackets(e,"c"),n=t.GetDimen(e),s=Bt.internalMath(t,t.GetArgument(e)),i=nr(r,1),o=t.create("node","mpadded",s,{width:n,"data-overflow":"linebreak","data-vertical-align":i});o.setProperty("vbox",i),t.Push(o)},BreakAlign(t,e){const r=t.stack.Top();if(!(r instanceof Ke))throw new gt("BreakNotInArray","%1 must be used in an alignment environment",t.currentCS);switch(t.GetArgument(e).trim()){case"c":if(r.First)throw new gt("BreakFirstInEntry","%1 must be at the beginning of an alignment entry",t.currentCS+"{c}");r.breakAlign.cell=nr(t.GetArgument(e),1);break;case"r":if(r.row.length||r.First)throw new gt("BreakFirstInRow","%1 must be at the beginning of an alignment row",t.currentCS+"{r}");r.breakAlign.row=nr(t.GetArgument(e));break;case"t":if(r.table.length||r.row.length||r.First)throw new gt("BreakFirstInTable","%1 must be at the beginning of an alignment",t.currentCS+"{t}");r.breakAlign.table=nr(t.GetArgument(e));break;default:throw new gt("BreakType","First argument to %1 must be one of c, r, or t",t.currentCS)}},MmlToken(t,e){const r=t.GetArgument(e);let n=t.GetBrackets(e,"").replace(/^\s+/,"");const s=t.GetArgument(e),i={},o=[];let a;try{a=t.create("node",r)}catch(t){a=null}if(!a||!a.isToken)throw new gt("NotMathMLToken","%1 is not a token element",r);for(;""!==n;){const e=n.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i);if(!e)throw new gt("InvalidMathMLAttr","Invalid MathML attribute: %1",n);if(!a.attributes.hasDefault(e[1])&&!rr[e[1]])throw new gt("UnknownAttrForElement","%1 is not a recognized attribute for %2",e[1],r);let s=Bt.mmlFilterAttribute(t,e[1],e[2].replace(/^(['"])(.*)\1$/,"$2"));s&&("true"===s.toLowerCase()?s=!0:"false"===s.toLowerCase()&&(s=!1),i[e[1]]=s,o.push(e[1])),n=n.substring(e[0].length)}o.length&&(i["mjx-keep-attrs"]=o.join(" "));const c=t.create("text",function(t){return t.replace(/\\U(?:([0-9A-Fa-f]{4})|\{\s*([0-9A-Fa-f]{1,6})\s*\})|\\./g,((t,e,r)=>"\\\\"===t?"\\":String.fromCodePoint(parseInt(e||r,16))))}(s));a.appendChild(c),nt.setProperties(a,i),t.Push(a)},Strut(t,e){const r=t.create("node","mrow"),n=t.create("node","mpadded",[r],{height:"8.6pt",depth:"3pt",width:0});t.Push(n)},Phantom(t,e,r,n){let s=t.create("node","mphantom",[t.ParseArg(e)]);(r||n)&&(s=t.create("node","mpadded",[s]),n&&(nt.setAttribute(s,"height",0),nt.setAttribute(s,"depth",0)),r&&nt.setAttribute(s,"width",0));const i=t.create("node","TeXAtom",[s]);t.Push(i)},Smash(t,e){const r=pt.trimSpaces(t.GetBrackets(e,"")),n=t.create("node","mpadded",[t.ParseArg(e)]);switch(r){case"b":nt.setAttribute(n,"depth",0);break;case"t":nt.setAttribute(n,"height",0);break;default:nt.setAttribute(n,"height",0),nt.setAttribute(n,"depth",0)}const s=t.create("node","TeXAtom",[n]);t.Push(s)},Lap(t,e){const r=t.create("node","mpadded",[t.ParseArg(e)],{width:0});"\\llap"===e&&nt.setAttribute(r,"lspace","-1width");const n=t.create("node","TeXAtom",[r]);t.Push(n)},RaiseLower(t,e){let r=t.GetDimen(e);const n=t.itemFactory.create("position").setProperties({name:t.currentCS,move:"vertical"});"-"===r.charAt(0)&&(r=r.slice(1),e="raise"===e.substring(1)?"\\lower":"\\raise"),"\\lower"===e?(n.setProperty("dh","-"+r),n.setProperty("dd","+"+r)):(n.setProperty("dh","+"+r),n.setProperty("dd","-"+r)),t.Push(n)},MoveLeftRight(t,e){let r=t.GetDimen(e),n="-"===r.charAt(0)?r.slice(1):"-"+r;if("\\moveleft"===e){const t=r;r=n,n=t}t.Push(t.itemFactory.create("position").setProperties({name:t.currentCS,move:"horizontal",left:t.create("node","mspace",[],{width:r}),right:t.create("node","mspace",[],{width:n})}))},Hskip(t,e,r=!1){const n=t.create("node","mspace",[],{width:t.GetDimen(e)});r&&nt.setAttribute(n,"linebreak","nobreak"),t.Push(n)},Nonscript(t,e){t.Push(t.itemFactory.create("nonscript"))},Rule(t,e,r){const n={width:t.GetDimen(e),height:t.GetDimen(e),depth:t.GetDimen(e)};"blank"!==r&&(n.mathbackground=t.stack.env.color||"black");const s=t.create("node","mspace",[],n);t.Push(s)},rule(t,e){const r=t.GetBrackets(e),n=t.GetDimen(e),s=t.GetDimen(e);let i=t.create("node","mspace",[],{width:n,height:s,mathbackground:t.stack.env.color||"black"});r&&(i=t.create("node","mpadded",[i],{voffset:r}),r.match(/^-/)?(nt.setAttribute(i,"height",r),nt.setAttribute(i,"depth","+"+r.substring(1))):nt.setAttribute(i,"height","+"+r)),t.Push(i)},MakeBig(t,e,r,n){const s=String(n*=er).replace(/(\.\d\d\d).+/,"$1")+"em",i=t.GetDelimiter(e,!0),o=t.create("token","mo",{minsize:s,maxsize:s,fence:!0,stretchy:!0,symmetric:!0},i),a=t.create("node","TeXAtom",[o],{texClass:r});t.Push(a)},BuildRel(t,e){const r=t.ParseUpTo(e,"\\over"),n=t.ParseArg(e),s=t.create("node","munderover");nt.setChild(s,0,n),nt.setChild(s,1,null),nt.setChild(s,2,r);const i=t.create("node","TeXAtom",[s],{texClass:F.REL});t.Push(i)},HBox(t,e,r,n){t.PushAll(Bt.internalMath(t,t.GetArgument(e),r,n))},FBox(t,e){const r=Bt.internalMath(t,t.GetArgument(e)),n=t.create("node","menclose",r,{notation:"box"});t.Push(n)},FrameBox(t,e){const r=t.GetBrackets(e),n=t.GetBrackets(e)||"c";let s=Bt.internalMath(t,t.GetArgument(e));r&&(s=[t.create("node","mpadded",s,{width:r,"data-align":y(n,{l:"left",r:"right"},"center")})]);const i=t.create("node","TeXAtom",[t.create("node","menclose",s,{notation:"box"})],{texClass:F.ORD});t.Push(i)},MakeBox(t,e){const r=t.GetBrackets(e),n=t.GetBrackets(e,"c"),s=t.create("node","mpadded",Bt.internalMath(t,t.GetArgument(e)));r&&nt.setAttribute(s,"width",r);const i=y(n.toLowerCase(),{c:"center",r:"right"},"");i&&nt.setAttribute(s,"data-align",i),n.toLowerCase()!==n&&nt.setAttribute(s,"data-overflow","linebreak"),t.Push(s)},Not(t,e){t.Push(t.itemFactory.create("not"))},Dots(t,e){const r=nt.createEntity("2026"),n=nt.createEntity("22EF"),s=t.create("token","mo",{stretchy:!1},r),i=t.create("token","mo",{stretchy:!1},n);t.Push(t.itemFactory.create("dots").setProperties({ldots:s,cdots:i}))},Matrix(t,e,r,n,s,i,o,a,c,l){const h=t.GetNext();if(""===h)throw new gt("MissingArgFor","Missing argument for %1",t.currentCS);"{"===h?t.i++:(t.string=h+"}"+t.string.slice(t.i+1),t.i=0);const u=t.itemFactory.create("array").setProperty("requireClose",!0);!r&&s||u.setProperty("arrayPadding",".2em .125em"),u.arraydef={rowspacing:o||"4pt",columnspacing:i||"1em"},c&&u.setProperty("isCases",!0),l&&(u.setProperty("isNumbered",!0),u.arraydef.side=l),(r||n)&&(u.setProperty("open",r),u.setProperty("close",n)),"D"===a&&(u.arraydef.displaystyle=!0),null!=s&&(u.arraydef.columnalign=s),t.Push(u)},Entry(t,e){t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:e}));const r=t.stack.Top(),n=r.getProperty("casesEnv");if(!r.getProperty("isCases")&&!n)return;const s=t.string;let i=0,o=-1,a=t.i,c=s.length;const l=n?new RegExp(`^\\\\end\\s*\\{${n.replace(/\*/,"\\*")}\\}`):null;for(;a<c;){const e=s.charAt(a);if("{"===e)i++,a++;else if("}"===e)0===i?c=0:(i--,0===i&&o<0&&(o=a-t.i),a++);else{if("&"===e&&0===i)throw new gt("ExtraAlignTab","Extra alignment tab in \\cases text");if("\\"===e){const t=s.substring(a);t.match(/^((\\cr)[^a-zA-Z]|\\\\)/)||l&&t.match(l)?c=0:a+=2}else a++}}const h=s.substring(t.i,a);if(!h.match(/^\s*\\text[^a-zA-Z]/)||o!==h.replace(/\s+$/,"").length-1){const e=Bt.internalMath(t,pt.trimSpaces(h),0);t.PushAll(e),t.i=a}},Cr(t,e){t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e}))},CrLaTeX(t,e,r=!1){let n;if(!r&&("*"===t.string.charAt(t.i)&&t.i++,"["===t.string.charAt(t.i))){const r=t.GetBrackets(e,""),[s,i]=pt.matchDimen(r);if(r&&!s)throw new gt("BracketMustBeDimension","Bracket argument to %1 must be a dimension",t.currentCS);n=s+i}t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:e,linebreak:!0}));const s=t.stack.Top();let i;s instanceof Ke?n&&s.addRowSpacing(n):(i=t.create("node","mspace",[],{linebreak:yt.NEWLINE}),n&&nt.setAttribute(i,"data-lineleading",n),t.Push(i))},HLine(t,e,r){null==r&&(r="solid");const n=t.stack.Top();if(!(n instanceof Ke)||n.Size())throw new gt("Misplaced","Misplaced %1",t.currentCS);if(n.table.length){const t=n.arraydef.rowlines?n.arraydef.rowlines.split(/ /):[];for(;t.length<n.table.length;)t.push("none");t[n.table.length-1]=r,n.arraydef.rowlines=t.join(" ")}else n.frame.push(["top",r])},HFill(t,e){const r=t.stack.Top();if(!(r instanceof Ke))throw new gt("UnsupportedHFill","Unsupported use of %1",t.currentCS);r.hfill.push(r.Size())},NewColumnType(t,e){const r=t.GetArgument(e),n=t.GetBrackets(e,"0"),s=t.GetArgument(e);if(1!==r.length)throw new gt("BadColumnName","Column specifier must be exactly one character: %1",r);if(!n.match(/^\d+$/))throw new gt("PositiveIntegerArg","Argument to %1 must be a positive integer",n);const i=t.configuration.columnParser;i.columnHandler[r]=t=>i.macroColumn(t,s,parseInt(n)),t.Push(t.itemFactory.create("null"))},BeginEnd(t,e){const r=t.GetArgument(e);if(r.match(/\\/))throw new gt("InvalidEnv","Invalid environment name '%1'",r);const n=t.configuration.handlers.get(ut.ENVIRONMENT).lookup(r);if(n&&"\\end"===e){if(!n.args[0]){const e=t.itemFactory.create("end").setProperty("name",r);return void t.Push(e)}t.stack.env.closing=r}Bt.checkMaxMacros(t,!1),t.parse(ut.ENVIRONMENT,[t,r])},Array(t,e,r,n,s,i,o,a,c){s||(s=t.GetArgument("\\begin{"+e.getName()+"}"));const l=t.itemFactory.create("array");return"array"===e.getName()&&l.setProperty("arrayPadding",".5em .125em"),l.parser=t,l.arraydef={columnspacing:i||"1em",rowspacing:o||"4pt"},t.configuration.columnParser.process(t,s,l),r&&l.setProperty("open",t.convertDelimiter(r)),n&&l.setProperty("close",t.convertDelimiter(n)),"'"===(a||"").charAt(1)&&(l.arraydef["data-cramped"]=!0,a=a.charAt(0)),"D"===a?l.arraydef.displaystyle=!0:a&&(l.arraydef.displaystyle=!1),l.arraydef.scriptlevel="S"===a?1:0,c&&(l.arraydef.useHeight=!1),t.Push(e),l.StartEntry(),l},AlignedArray(t,e,r=""){const n=t.GetBrackets("\\begin{"+e.getName()+"}"),s=ir.Array(t,e,null,null,null,null,null,r);return Bt.setArrayAlign(s,n)},IndentAlign(t,e){const r=`\\begin{${e.getName()}}`,n=t.GetBrackets(r,""),s=t.GetBrackets(r,""),i=t.GetBrackets(r,"");if(n&&!pt.matchDimen(n)[0]||s&&!pt.matchDimen(s)[0]||i&&!pt.matchDimen(i)[0])throw new gt("BracketMustBeDimension","Bracket argument to %1 must be a dimension",r);const o=t.GetArgument(r);if(o&&!o.match(/^([lcr]{1,3})?$/))throw new gt("BadAlignment","Alignment must be one to three copies of l, c, or r");const a=[...o].map((t=>({l:"left",c:"center",r:"right"}[t])));1===a.length&&a.push(a[0]);const c={};for(const[t,e]of[["indentshiftfirst",n],["indentshift",s||n],["indentshiftlast",i],["indentalignfirst",a[0]],["indentalign",a[1]],["indentalignlast",a[2]]])e&&(c[t]=e);t.Push(t.itemFactory.create("mstyle",c,e.getName()))},Equation:(t,e,r,n=!0)=>(t.configuration.mathItem.display=n,t.stack.env.display=n,Bt.checkEqnEnv(t),t.Push(e),t.itemFactory.create("equation",r).setProperty("name",e.getName())),EqnArray(t,e,r,n,s,i,o){const a=e.getName(),c="gather"===a||"gather*"===a;n&&Bt.checkEqnEnv(t,!c),t.Push(e),s=(s=s.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center"),i=nr(i);const l=t.itemFactory.create("eqnarray",a,r,n,t.stack.global);return l.arraydef={displaystyle:!0,columnalign:s,columnspacing:o||"1em",rowspacing:"3pt","data-break-align":i,side:t.options.tagSide,minlabelspacing:t.options.tagIndent},c&&l.setProperty("nestable",!0),l},HandleNoTag(t,e){t.tags.notag()},HandleLabel(t,e){const r=t.GetArgument(e);if(""!==r&&!t.tags.refUpdate){if(t.tags.label)throw new gt("MultipleCommand","Multiple %1",t.currentCS);if(t.tags.label=r,(t.tags.allLabels[r]||t.tags.labels[r])&&!t.options.ignoreDuplicateLabels)throw new gt("MultipleLabel","Label '%1' multiply defined",r);t.tags.labels[r]=new jt}},HandleRef(t,e,r){const n=t.GetArgument(e);let s=t.tags.allLabels[n]||t.tags.labels[n];s||(t.tags.refUpdate||(t.tags.redo=!0),s=new jt);let i=s.tag;r&&(i=t.tags.formatRef(i));const o=t.create("node","mrow",Bt.internalMath(t,i),{href:t.tags.formatUrl(s.id,t.options.baseURL),class:"MathJax_ref"});t.Push(o)},Macro(t,e,r,n,s){if(n){const i=[];if(null!=s){const r=t.GetBrackets(e);i.push(null==r?s:r)}for(let r=i.length;r<n;r++)i.push(t.GetArgument(e));r=Bt.substituteArgs(t,i,r)}t.string=Bt.addArgs(t,r,t.string.slice(t.i)),t.i=0,Bt.checkMaxMacros(t)},MathChoice(t,e){const r=t.ParseArg(e),n=t.ParseArg(e),s=t.ParseArg(e),i=t.ParseArg(e);t.Push(t.create("node","MathChoice",[r,n,s,i]))}},or=ir,ar=bt,cr={variable(t,e){var r;const n=Bt.getFontDef(t),s=t.stack.env;if(s.multiLetterIdentifiers&&""!==s.font&&(e=(null===(r=t.string.substring(t.i-1).match(s.multiLetterIdentifiers))||void 0===r?void 0:r[0])||e,t.i+=e.length-1,n.mathvariant===ar.NORMAL&&s.noAutoOP&&e.length>1&&(n.autoOP=!1)),!n.mathvariant&&Bt.isLatinOrGreekChar(e)){const r=t.configuration.mathStyle(e);r&&(n.mathvariant=r)}const i=t.create("token","mi",n,e);t.Push(i)},digit(t,e){const r=t.configuration.options.numberPattern,n=t.string.slice(t.i-1).match(r),s=Bt.getFontDef(t);if(!n)return!1;const i=t.create("token","mn",s,n[0].replace(/[{}]/g,""));return t.i+=n[0].length-1,t.Push(i),!0},controlSequence(t,e){const r=t.GetCS();t.parse(ut.MACRO,[t,r])},lcGreek(t,e){const r={mathvariant:t.configuration.mathStyle(e.char)||ar.ITALIC},n=t.create("token","mi",r,e.char);t.Push(n)},ucGreek(t,e){const r={mathvariant:t.stack.env.font||t.configuration.mathStyle(e.char,!0)||ar.NORMAL},n=t.create("token","mi",r,e.char);t.Push(n)},mathchar0mi(t,e){const r=e.attributes||{mathvariant:ar.ITALIC},n=t.create("token","mi",r,e.char);t.Push(n)},mathchar0mo(t,e){const r=e.attributes||{};r.stretchy=!1;const n=t.create("token","mo",r,e.char);nt.setProperty(n,"fixStretchy",!0),t.configuration.addNode("fixStretchy",n),t.Push(n)},mathchar7(t,e){const r=e.attributes||{mathvariant:ar.NORMAL};t.stack.env.font&&(r.mathvariant=t.stack.env.font);const n=t.create("token","mi",r,e.char);t.Push(n)},delimiter(t,e){let r=e.attributes||{};r=Object.assign({fence:!1,stretchy:!1},r);const n=t.create("token","mo",r,e.char);t.Push(n)},environment(t,e,r,n){const s=t.itemFactory.create("begin").setProperty("name",e);t.Push(r(t,s,...n.slice(1)))}},lr=tr(Ze.thickmathspace),hr=bt;new Zt("letter",cr.variable,/[a-z]/i),new Zt("digit",cr.digit,/[0-9.,]/),new Zt("command",cr.controlSequence,/^\\/),new ne("special",{"{":or.Open,"}":or.Close,"~":or.Tilde,"^":or.Superscript,_:or.Subscript,"|":or.Bar," ":or.Space,"\t":or.Space,"\r":or.Space,"\n":or.Space,"'":or.Prime,"%":or.Comment,"&":or.Entry,"#":or.Hash,"\xa0":or.Space,"\u2019":or.Prime}),new ee("lcGreek",cr.lcGreek,{alpha:"\u03b1",beta:"\u03b2",gamma:"\u03b3",delta:"\u03b4",epsilon:"\u03f5",zeta:"\u03b6",eta:"\u03b7",theta:"\u03b8",iota:"\u03b9",kappa:"\u03ba",lambda:"\u03bb",mu:"\u03bc",nu:"\u03bd",xi:"\u03be",omicron:"\u03bf",pi:"\u03c0",rho:"\u03c1",sigma:"\u03c3",tau:"\u03c4",upsilon:"\u03c5",phi:"\u03d5",chi:"\u03c7",psi:"\u03c8",omega:"\u03c9",varepsilon:"\u03b5",vartheta:"\u03d1",varpi:"\u03d6",varrho:"\u03f1",varsigma:"\u03c2",varphi:"\u03c6"}),new ee("ucGreek",cr.ucGreek,{Gamma:"\u0393",Delta:"\u0394",Theta:"\u0398",Lambda:"\u039b",Xi:"\u039e",Pi:"\u03a0",Sigma:"\u03a3",Upsilon:"\u03a5",Phi:"\u03a6",Psi:"\u03a8",Omega:"\u03a9"}),new ee("mathchar0mi",cr.mathchar0mi,{AA:"\u212b",S:["\xa7",{mathvariant:hr.NORMAL}],aleph:["\u2135",{mathvariant:hr.NORMAL}],hbar:["\u210f",{variantForm:!0}],imath:"\u0131",jmath:"\u0237",ell:"\u2113",wp:["\u2118",{mathvariant:hr.NORMAL}],Re:["\u211c",{mathvariant:hr.NORMAL}],Im:["\u2111",{mathvariant:hr.NORMAL}],partial:["\u2202",{mathvariant:hr.ITALIC}],infty:["\u221e",{mathvariant:hr.NORMAL}],prime:["\u2032",{variantForm:!0}],emptyset:["\u2205",{mathvariant:hr.NORMAL}],nabla:["\u2207",{mathvariant:hr.NORMAL}],top:["\u22a4",{mathvariant:hr.NORMAL}],bot:["\u22a5",{mathvariant:hr.NORMAL}],angle:["\u2220",{mathvariant:hr.NORMAL}],triangle:["\u25b3",{mathvariant:hr.NORMAL}],backslash:["\\",{mathvariant:hr.NORMAL}],forall:["\u2200",{mathvariant:hr.NORMAL}],exists:["\u2203",{mathvariant:hr.NORMAL}],neg:["\xac",{mathvariant:hr.NORMAL}],lnot:["\xac",{mathvariant:hr.NORMAL}],flat:["\u266d",{mathvariant:hr.NORMAL}],natural:["\u266e",{mathvariant:hr.NORMAL}],sharp:["\u266f",{mathvariant:hr.NORMAL}],clubsuit:["\u2663",{mathvariant:hr.NORMAL}],diamondsuit:["\u2662",{mathvariant:hr.NORMAL}],heartsuit:["\u2661",{mathvariant:hr.NORMAL}],spadesuit:["\u2660",{mathvariant:hr.NORMAL}]}),new ee("mathchar0mo",cr.mathchar0mo,{surd:"\u221a",coprod:["\u2210",{movesupsub:!0}],bigvee:["\u22c1",{movesupsub:!0}],bigwedge:["\u22c0",{movesupsub:!0}],biguplus:["\u2a04",{movesupsub:!0}],bigcap:["\u22c2",{movesupsub:!0}],bigcup:["\u22c3",{movesupsub:!0}],int:"\u222b",intop:["\u222b",{movesupsub:!0,movablelimits:!0}],iint:"\u222c",iiint:"\u222d",prod:["\u220f",{movesupsub:!0}],sum:["\u2211",{movesupsub:!0}],bigotimes:["\u2a02",{movesupsub:!0}],bigoplus:["\u2a01",{movesupsub:!0}],bigodot:["\u2a00",{movesupsub:!0}],oint:"\u222e",ointop:["\u222e",{movesupsub:!0,movablelimits:!0}],oiint:"\u222f",oiiint:"\u2230",bigsqcup:["\u2a06",{movesupsub:!0}],smallint:["\u222b",{largeop:!1}],triangleleft:"\u25c3",triangleright:"\u25b9",bigtriangleup:"\u25b3",bigtriangledown:"\u25bd",wedge:"\u2227",land:"\u2227",vee:"\u2228",lor:"\u2228",cap:"\u2229",cup:"\u222a",ddagger:"\u2021",dagger:"\u2020",sqcap:"\u2293",sqcup:"\u2294",uplus:"\u228e",amalg:"\u2a3f",diamond:"\u22c4",bullet:"\u2219",wr:"\u2240",div:"\xf7",odot:["\u2299",{largeop:!1}],oslash:["\u2298",{largeop:!1}],otimes:["\u2297",{largeop:!1}],ominus:["\u2296",{largeop:!1}],oplus:["\u2295",{largeop:!1}],mp:"\u2213",pm:"\xb1",circ:"\u2218",bigcirc:"\u25ef",setminus:"\u2216",cdot:"\u22c5",ast:"\u2217",times:"\xd7",star:"\u22c6",propto:"\u221d",sqsubseteq:"\u2291",sqsupseteq:"\u2292",parallel:"\u2225",mid:"\u2223",dashv:"\u22a3",vdash:"\u22a2",leq:"\u2264",le:"\u2264",geq:"\u2265",ge:"\u2265",lt:"<",gt:">",succ:"\u227b",prec:"\u227a",approx:"\u2248",succeq:"\u2ab0",preceq:"\u2aaf",supset:"\u2283",subset:"\u2282",supseteq:"\u2287",subseteq:"\u2286",in:"\u2208",ni:"\u220b",notin:"\u2209",owns:"\u220b",gg:"\u226b",ll:"\u226a",sim:"\u223c",simeq:"\u2243",perp:"\u27c2",equiv:"\u2261",asymp:"\u224d",smile:"\u2323",frown:"\u2322",ne:"\u2260",neq:"\u2260",cong:"\u2245",doteq:"\u2250",bowtie:"\u22c8",models:"\u22a7",notChar:"\u29f8",Leftrightarrow:"\u21d4",Leftarrow:"\u21d0",Rightarrow:"\u21d2",leftrightarrow:"\u2194",leftarrow:"\u2190",gets:"\u2190",rightarrow:"\u2192",to:["\u2192",{accent:!1}],mapsto:"\u21a6",leftharpoonup:"\u21bc",leftharpoondown:"\u21bd",rightharpoonup:"\u21c0",rightharpoondown:"\u21c1",nearrow:"\u2197",searrow:"\u2198",nwarrow:"\u2196",swarrow:"\u2199",rightleftharpoons:"\u21cc",hookrightarrow:"\u21aa",hookleftarrow:"\u21a9",longleftarrow:"\u27f5",Longleftarrow:"\u27f8",longrightarrow:"\u27f6",Longrightarrow:"\u27f9",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",ldots:"\u2026",cdots:"\u22ef",vdots:"\u22ee",ddots:"\u22f1",iddots:"\u22f0",dotsc:"\u2026",dotsb:"\u22ef",dotsm:"\u22ef",dotsi:"\u22ef",dotso:"\u2026",ldotp:[".",{texClass:F.PUNCT}],cdotp:["\u22c5",{texClass:F.PUNCT}],colon:[":",{texClass:F.PUNCT}]}),new ee("mathchar7",cr.mathchar7,{_:"_","#":"#",$:"$","%":"%","&":"&",And:"&"}),new re("delimiter",cr.delimiter,{"(":"(",")":")","[":"[","]":"]","<":"\u27e8",">":"\u27e9","\\lt":"\u27e8","\\gt":"\u27e9","/":"/","|":["|",{texClass:F.ORD}],".":"","\\lmoustache":"\u23b0","\\rmoustache":"\u23b1","\\lgroup":"\u27ee","\\rgroup":"\u27ef","\\arrowvert":"\u23d0","\\Arrowvert":"\u2016","\\bracevert":"\u23aa","\\Vert":["\u2016",{texClass:F.ORD}],"\\|":["\u2016",{texClass:F.ORD}],"\\vert":["|",{texClass:F.ORD}],"\\uparrow":"\u2191","\\downarrow":"\u2193","\\updownarrow":"\u2195","\\Uparrow":"\u21d1","\\Downarrow":"\u21d3","\\Updownarrow":"\u21d5","\\backslash":"\\","\\rangle":"\u27e9","\\langle":"\u27e8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"\u2309","\\lceil":"\u2308","\\rfloor":"\u230b","\\lfloor":"\u230a","\\lbrack":"[","\\rbrack":"]"}),new se("macros",{displaystyle:[or.SetStyle,"D",!0,0],textstyle:[or.SetStyle,"T",!1,0],scriptstyle:[or.SetStyle,"S",!1,1],scriptscriptstyle:[or.SetStyle,"SS",!1,2],rm:[or.SetFont,hr.NORMAL],mit:[or.SetFont,hr.ITALIC],oldstyle:[or.SetFont,hr.OLDSTYLE],cal:[or.SetFont,hr.CALLIGRAPHIC],it:[or.SetFont,hr.MATHITALIC],bf:[or.SetFont,hr.BOLD],sf:[or.SetFont,hr.SANSSERIF],tt:[or.SetFont,hr.MONOSPACE],frak:[or.MathFont,hr.FRAKTUR],Bbb:[or.MathFont,hr.DOUBLESTRUCK],mathrm:[or.MathFont,hr.NORMAL],mathup:[or.MathFont,hr.NORMAL],mathnormal:[or.MathFont,""],mathbf:[or.MathFont,hr.BOLD],mathbfup:[or.MathFont,hr.BOLD],mathit:[or.MathFont,hr.MATHITALIC],mathbfit:[or.MathFont,hr.BOLDITALIC],mathbb:[or.MathFont,hr.DOUBLESTRUCK],mathfrak:[or.MathFont,hr.FRAKTUR],mathbffrak:[or.MathFont,hr.BOLDFRAKTUR],mathscr:[or.MathFont,hr.SCRIPT],mathbfscr:[or.MathFont,hr.BOLDSCRIPT],mathsf:[or.MathFont,hr.SANSSERIF],mathsfup:[or.MathFont,hr.SANSSERIF],mathbfsf:[or.MathFont,hr.BOLDSANSSERIF],mathbfsfup:[or.MathFont,hr.BOLDSANSSERIF],mathsfit:[or.MathFont,hr.SANSSERIFITALIC],mathbfsfit:[or.MathFont,hr.SANSSERIFBOLDITALIC],mathtt:[or.MathFont,hr.MONOSPACE],mathcal:[or.MathFont,hr.CALLIGRAPHIC],mathbfcal:[or.MathFont,hr.BOLDCALLIGRAPHIC],symrm:[or.MathFont,hr.NORMAL],symup:[or.MathFont,hr.NORMAL],symnormal:[or.MathFont,""],symbf:[or.MathFont,hr.BOLD,hr.BOLDITALIC],symbfup:[or.MathFont,hr.BOLD],symit:[or.MathFont,hr.ITALIC],symbfit:[or.MathFont,hr.BOLDITALIC],symbb:[or.MathFont,hr.DOUBLESTRUCK],symfrak:[or.MathFont,hr.FRAKTUR],symbffrak:[or.MathFont,hr.BOLDFRAKTUR],symscr:[or.MathFont,hr.SCRIPT],symbfscr:[or.MathFont,hr.BOLDSCRIPT],symsf:[or.MathFont,hr.SANSSERIF,hr.SANSSERIFITALIC],symsfup:[or.MathFont,hr.SANSSERIF],symbfsf:[or.MathFont,hr.BOLDSANSSERIF],symbfsfup:[or.MathFont,hr.BOLDSANSSERIF],symsfit:[or.MathFont,hr.SANSSERIFITALIC],symbfsfit:[or.MathFont,hr.SANSSERIFBOLDITALIC],symtt:[or.MathFont,hr.MONOSPACE],symcal:[or.MathFont,hr.CALLIGRAPHIC],symbfcal:[or.MathFont,hr.BOLDCALLIGRAPHIC],textrm:[or.HBox,null,hr.NORMAL],textup:[or.HBox,null,hr.NORMAL],textnormal:[or.HBox],textit:[or.HBox,null,hr.ITALIC],textbf:[or.HBox,null,hr.BOLD],textsf:[or.HBox,null,hr.SANSSERIF],texttt:[or.HBox,null,hr.MONOSPACE],tiny:[or.SetSize,.5],Tiny:[or.SetSize,.6],scriptsize:[or.SetSize,.7],small:[or.SetSize,.85],normalsize:[or.SetSize,1],large:[or.SetSize,1.2],Large:[or.SetSize,1.44],LARGE:[or.SetSize,1.73],huge:[or.SetSize,2.07],Huge:[or.SetSize,2.49],arcsin:or.NamedFn,arccos:or.NamedFn,arctan:or.NamedFn,arg:or.NamedFn,cos:or.NamedFn,cosh:or.NamedFn,cot:or.NamedFn,coth:or.NamedFn,csc:or.NamedFn,deg:or.NamedFn,det:or.NamedOp,dim:or.NamedFn,exp:or.NamedFn,gcd:or.NamedOp,hom:or.NamedFn,inf:or.NamedOp,ker:or.NamedFn,lg:or.NamedFn,lim:or.NamedOp,liminf:[or.NamedOp,"lim&thinsp;inf"],limsup:[or.NamedOp,"lim&thinsp;sup"],ln:or.NamedFn,log:or.NamedFn,max:or.NamedOp,min:or.NamedOp,Pr:or.NamedOp,sec:or.NamedFn,sin:or.NamedFn,sinh:or.NamedFn,sup:or.NamedOp,tan:or.NamedFn,tanh:or.NamedFn,limits:[or.Limits,!0],nolimits:[or.Limits,!1],overline:[or.UnderOver,"2015"],underline:[or.UnderOver,"2015"],overbrace:[or.UnderOver,"23DE",!0],underbrace:[or.UnderOver,"23DF",!0],overparen:[or.UnderOver,"23DC"],underparen:[or.UnderOver,"23DD"],overrightarrow:[or.UnderOver,"2192"],underrightarrow:[or.UnderOver,"2192"],overleftarrow:[or.UnderOver,"2190"],underleftarrow:[or.UnderOver,"2190"],overleftrightarrow:[or.UnderOver,"2194"],underleftrightarrow:[or.UnderOver,"2194"],overset:or.Overset,underset:or.Underset,overunderset:or.Overunderset,stackrel:[or.Macro,"\\mathrel{\\mathop{#2}\\limits^{#1}}",2],stackbin:[or.Macro,"\\mathbin{\\mathop{#2}\\limits^{#1}}",2],over:or.Over,overwithdelims:or.Over,atop:or.Over,atopwithdelims:or.Over,above:or.Over,abovewithdelims:or.Over,brace:[or.Over,"{","}"],brack:[or.Over,"[","]"],choose:[or.Over,"(",")"],frac:or.Frac,sqrt:or.Sqrt,root:or.Root,uproot:[or.MoveRoot,"upRoot"],leftroot:[or.MoveRoot,"leftRoot"],left:or.LeftRight,right:or.LeftRight,middle:or.LeftRight,llap:or.Lap,rlap:or.Lap,raise:or.RaiseLower,lower:or.RaiseLower,moveleft:or.MoveLeftRight,moveright:or.MoveLeftRight,",":[or.Spacer,Ze.thinmathspace],":":[or.Spacer,Ze.mediummathspace],">":[or.Spacer,Ze.mediummathspace],";":[or.Spacer,Ze.thickmathspace],"!":[or.Spacer,Ze.negativethinmathspace],enspace:[or.Spacer,.5],quad:[or.Spacer,1],qquad:[or.Spacer,2],thinspace:[or.Spacer,Ze.thinmathspace],negthinspace:[or.Spacer,Ze.negativethinmathspace],"*":or.DiscretionaryTimes,allowbreak:or.AllowBreak,goodbreak:[or.Linebreak,yt.GOODBREAK],badbreak:[or.Linebreak,yt.BADBREAK],nobreak:[or.Linebreak,yt.NOBREAK],break:or.Break,hskip:or.Hskip,hspace:or.Hskip,kern:[or.Hskip,!0],mskip:or.Hskip,mspace:or.Hskip,mkern:[or.Hskip,!0],rule:or.rule,Rule:[or.Rule],Space:[or.Rule,"blank"],nonscript:or.Nonscript,big:[or.MakeBig,F.ORD,.85],Big:[or.MakeBig,F.ORD,1.15],bigg:[or.MakeBig,F.ORD,1.45],Bigg:[or.MakeBig,F.ORD,1.75],bigl:[or.MakeBig,F.OPEN,.85],Bigl:[or.MakeBig,F.OPEN,1.15],biggl:[or.MakeBig,F.OPEN,1.45],Biggl:[or.MakeBig,F.OPEN,1.75],bigr:[or.MakeBig,F.CLOSE,.85],Bigr:[or.MakeBig,F.CLOSE,1.15],biggr:[or.MakeBig,F.CLOSE,1.45],Biggr:[or.MakeBig,F.CLOSE,1.75],bigm:[or.MakeBig,F.REL,.85],Bigm:[or.MakeBig,F.REL,1.15],biggm:[or.MakeBig,F.REL,1.45],Biggm:[or.MakeBig,F.REL,1.75],mathord:[or.TeXAtom,F.ORD],mathop:[or.TeXAtom,F.OP],mathopen:[or.TeXAtom,F.OPEN],mathclose:[or.TeXAtom,F.CLOSE],mathbin:[or.TeXAtom,F.BIN],mathrel:[or.TeXAtom,F.REL],mathpunct:[or.TeXAtom,F.PUNCT],mathinner:[or.TeXAtom,F.INNER],vtop:[or.VBox,"top"],vcenter:[or.VBox,"center"],vbox:[or.VBox,"bottom"],hsize:or.Hsize,parbox:or.ParBox,breakAlign:or.BreakAlign,buildrel:or.BuildRel,hbox:[or.HBox,0],text:or.HBox,mbox:[or.HBox,0],fbox:or.FBox,boxed:[or.Macro,"\\fbox{$\\displaystyle{#1}$}",1],framebox:or.FrameBox,makebox:or.MakeBox,strut:or.Strut,mathstrut:[or.Macro,"\\vphantom{(}"],phantom:or.Phantom,vphantom:[or.Phantom,1,0],hphantom:[or.Phantom,0,1],smash:or.Smash,acute:[or.Accent,"00B4"],grave:[or.Accent,"0060"],ddot:[or.Accent,"00A8"],dddot:[or.Accent,"20DB"],ddddot:[or.Accent,"20DC"],tilde:[or.Accent,"007E"],bar:[or.Accent,"00AF"],breve:[or.Accent,"02D8"],check:[or.Accent,"02C7"],hat:[or.Accent,"005E"],vec:[or.Accent,"2192",!1],dot:[or.Accent,"02D9"],widetilde:[or.Accent,"007E",!0],widehat:[or.Accent,"005E",!0],matrix:or.Matrix,array:or.Matrix,pmatrix:[or.Matrix,"(",")"],cases:[or.Matrix,"{","","left left",null,".2em",null,!0],eqalign:[or.Matrix,null,null,"right left",lr,".5em","D"],displaylines:[or.Matrix,null,null,"center",null,".5em","D"],cr:or.Cr,"\\":or.CrLaTeX,newline:[or.CrLaTeX,!0],hline:or.HLine,hdashline:[or.HLine,"dashed"],eqalignno:[or.Matrix,null,null,"right left",lr,".5em","D",null,"right"],leqalignno:[or.Matrix,null,null,"right left",lr,".5em","D",null,"left"],hfill:or.HFill,hfil:or.HFill,hfilll:or.HFill,bmod:[or.Macro,'\\mmlToken{mo}[lspace="'+lr+'" rspace="'+lr+'"]{mod}'],pmod:[or.Macro,"\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:[or.Macro,"\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:[or.Macro,"\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:[or.Macro,"\\;\\Longleftrightarrow\\;"],skew:[or.Macro,"{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],pmb:[or.Macro,"\\rlap{#1}\\kern1px{#1}",1],TeX:[or.Macro,"T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:[or.Macro,"L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"],not:or.Not,dots:or.Dots,space:or.Tilde,"\xa0":or.Tilde," ":or.Tilde,begin:or.BeginEnd,end:or.BeginEnd,label:or.HandleLabel,ref:or.HandleRef,nonumber:or.HandleNoTag,newcolumntype:or.NewColumnType,mathchoice:or.MathChoice,mmlToken:or.MmlToken}),new ie("environment",cr.environment,{displaymath:[or.Equation,null,!1],math:[or.Equation,null,!1,!1],array:[or.AlignedArray],darray:[or.AlignedArray,null,"D"],equation:[or.Equation,null,!0],eqnarray:[or.EqnArray,null,!0,!0,"rcl","bmt",Bt.cols(0,Ze.thickmathspace),".5em"],indentalign:[or.IndentAlign]}),new ee("not_remap",null,{"\u2190":"\u219a","\u2192":"\u219b","\u2194":"\u21ae","\u21d0":"\u21cd","\u21d2":"\u21cf","\u21d4":"\u21ce","\u2208":"\u2209","\u220b":"\u220c","\u2223":"\u2224","\u2225":"\u2226","\u223c":"\u2241","~":"\u2241","\u2243":"\u2244","\u2245":"\u2247","\u2248":"\u2249","\u224d":"\u226d","=":"\u2260","\u2261":"\u2262","<":"\u226e",">":"\u226f","\u2264":"\u2270","\u2265":"\u2271","\u2272":"\u2274","\u2273":"\u2275","\u2276":"\u2278","\u2277":"\u2279","\u227a":"\u2280","\u227b":"\u2281","\u2282":"\u2284","\u2283":"\u2285","\u2286":"\u2288","\u2287":"\u2289","\u22a2":"\u22ac","\u22a8":"\u22ad","\u22a9":"\u22ae","\u22ab":"\u22af","\u227c":"\u22e0","\u227d":"\u22e1","\u2291":"\u22e2","\u2292":"\u22e3","\u22b2":"\u22ea","\u22b3":"\u22eb","\u22b4":"\u22ec","\u22b5":"\u22ed","\u2203":"\u2204"});const ur=bt;new ee("remap",null,{"-":"\u2212","*":"\u2217","`":"\u2018"});he.create("base",{[ht.CONFIG]:function(t,e){const r=e.parseOptions.options;r.digits&&(r.numberPattern=r.digits),new Zt("digit",cr.digit,r.initialDigit),new Zt("letter",cr.variable,r.initialLetter);t.handlers.get(ut.CHARACTER).add(["letter","digit"],null,4)},[ht.HANDLER]:{[ut.CHARACTER]:["command","special"],[ut.DELIMITER]:["delimiter"],[ut.MACRO]:["delimiter","macros","lcGreek","ucGreek","mathchar0mi","mathchar0mo","mathchar7"],[ut.ENVIRONMENT]:["environment"]},[ht.FALLBACK]:{[ut.CHARACTER]:function(t,e){const r=t.stack.env.font,n=t.stack.env.italicFont,s=r?{mathvariant:r}:{},i=ae.getMap("remap").lookup(e),o=Z(e),a=o[3],c=t.create("token",a,s,i?i.char:e),l=Bt.isLatinOrGreekChar(e)?t.configuration.mathStyle(e,!0)||n:"",h=o[4]||(r&&l===ur.NORMAL?"":l);h&&c.attributes.set("mathvariant",h),"mo"===a&&(nt.setProperty(c,"fixStretchy",!0),t.configuration.addNode("fixStretchy",c)),t.Push(c)},[ut.MACRO]:function(t,e){throw new gt("UndefinedControlSequence","Undefined control sequence %1","\\"+e)},[ut.ENVIRONMENT]:function(t,e){throw new gt("UnknownEnv","Unknown environment '%1'",e)}},[ht.ITEMS]:{[Le.prototype.kind]:Le,[Ce.prototype.kind]:Ce,[Oe.prototype.kind]:Oe,[ve.prototype.kind]:ve,[Se.prototype.kind]:Se,[we.prototype.kind]:we,[Me.prototype.kind]:Me,[xe.prototype.kind]:xe,[Pe.prototype.kind]:Pe,[De.prototype.kind]:De,[ke.prototype.kind]:ke,[_e.prototype.kind]:_e,[Fe.prototype.kind]:Fe,[Be.prototype.kind]:Be,[Ue.prototype.kind]:Ue,[qe.prototype.kind]:qe,[He.prototype.kind]:He,[Ge.prototype.kind]:Ge,[je.prototype.kind]:je,[Xe.prototype.kind]:Xe,[Ve.prototype.kind]:Ve,[We.prototype.kind]:We,[Ke.prototype.kind]:Ke,[ze.prototype.kind]:ze,[$e.prototype.kind]:$e,[Ye.prototype.kind]:Ye},[ht.OPTIONS]:{maxMacros:1e3,digits:"",numberPattern:/^(?:[0-9]+(?:\{,\}[0-9]{3})*(?:\.[0-9]*)?|\.[0-9]+)/,initialDigit:/[0-9.,]/,identifierPattern:/^[a-zA-Z]+/,initialLetter:/[a-zA-Z]/,baseURL:i.document&&0!==i.document.getElementsByTagName("base").length?String(i.document.location).replace(/#.*$/,""):""},[ht.TAGS]:{base:class extends Vt{}},[ht.POSTPROCESSORS]:[[function({data:t}){for(const e of t.getList("nonscript"))if(e.attributes.get("scriptlevel")>0){const r=e.parent;if(r.childNodes.splice(r.childIndex(e),1),t.removeFromList(e.kind,[e]),e.isKind("mrow")){const r=e.childNodes[0];t.removeFromList("mstyle",[r]),t.removeFromList("mspace",r.childNodes[0].childNodes)}}else e.isKind("mrow")&&(e.parent.replaceChild(e.childNodes[0],e),t.removeFromList("mrow",[e]))},-4]]});class dr extends A{static configure(t){const e=new pe(t,["tex"]);return e.init(),e}static tags(t,e){zt.addTags(e.tags),zt.setDefault(t.options.tags),t.tags=zt.getDefault(),t.tags.configuration=t}constructor(t={}){const[e,r,n]=T(t,dr.OPTIONS,x.OPTIONS);super(r),this.findTeX=this.options.FindTeX||new x(n);const s=this.options.packages,i=this.configuration=dr.configure(s),o=this._parseOptions=new Gt(i,[this.options,zt.OPTIONS]);b(o.options,e),i.config(this),dr.tags(o,i),this.postFilters.addList([[lt.cleanSubSup,-7],[lt.setInherited,-6],[lt.checkScriptlevel,-5],[lt.moveLimits,-4],[lt.cleanStretchy,-3],[lt.cleanAttributes,-2],[lt.combineRelations,-1]])}setMmlFactory(t){super.setMmlFactory(t),this._parseOptions.nodeFactory.setMmlFactory(t)}get parseOptions(){return this._parseOptions}reset(t=0){this.parseOptions.tags.reset(t)}compile(t,e){let r,n;this.parseOptions.clear(),this.parseOptions.mathItem=t,this.executeFilters(this.preFilters,t,e,this.parseOptions),this.latex=t.math,this.parseOptions.tags.startEquation(t);try{n=new Lt(this.latex,{display:t.display,isInner:!1},this.parseOptions),r=n.mml()}catch(t){if(!(t instanceof gt))throw t;this.parseOptions.error=!0,r=this.options.formatError(this,t)}return r=this.parseOptions.nodeFactory.create("node","math",[r]),r.attributes.set(Rt.LATEX,this.latex),t.display&&nt.setAttribute(r,"display","block"),this.parseOptions.tags.finishEquation(t),this.parseOptions.root=r,this.executeFilters(this.postFilters,t,e,this.parseOptions),n&&n.stack.env.hsize&&(nt.setAttribute(r,"maxwidth",n.stack.env.hsize),nt.setAttribute(r,"overflow","linebreak")),this.mathNode=this.parseOptions.root,this.mathNode}findMath(t){return this.findTeX.findMath(t)}formatError(t){const e=t.message.replace(/\n.*/,"");return this.parseOptions.nodeFactory.create("error",e,t.id,this.latex)}}dr.NAME="TeX",dr.OPTIONS=Object.assign(Object.assign({},A.OPTIONS),{FindTeX:null,packages:["base"],maxBuffer:5120,maxTemplateSubtitutions:1e4,mathStyle:"TeX",formatError:(t,e)=>t.formatError(e)});const pr="http://www.w3.org/1998/Math/MathML";class mr extends R{findMath(t){const e=new Set;this.findMathNodes(t,e),this.findMathPrefixed(t,e);const r=this.adaptor.root(this.adaptor.document);return"html"===this.adaptor.kind(r)&&0===e.size&&this.findMathNS(t,e),this.processMath(e)}findMathNodes(t,e){for(const r of this.adaptor.tags(t,"math"))e.add(r)}findMathPrefixed(t,e){const r=this.adaptor.root(this.adaptor.document);for(const n of this.adaptor.allAttributes(r))if("xmlns:"===n.name.substring(0,6)&&n.value===pr){const r=n.name.substring(6);for(const n of this.adaptor.tags(t,r+":math"))e.add(n)}}findMathNS(t,e){for(const r of this.adaptor.tags(t,"math",pr))e.add(r)}processMath(t){const e=this.adaptor,r=[];for(const n of t.values()){if("mjx-assistive-mml"===e.kind(e.parent(n)))continue;const t="block"===e.getAttribute(n,"display")||"display"===e.getAttribute(n,"mode"),s={node:n,n:0,delim:""},i={node:n,n:0,delim:""};r.push({math:e.outerHTML(n),start:s,end:i,display:t})}return r}}mr.OPTIONS={};class fr{constructor(t={}){const e=this.constructor;this.options=b(N({},e.OPTIONS),t)}setMmlFactory(t){this.factory=t}compile(t){const e=this.makeNode(t);return e.verifyTree(this.options.verify),e.setInheritedAttributes({},!1,0,!1),e.walkTree(this.markMrows),e}makeNode(t){const e=this.adaptor;let r=!1;const n=e.kind(t).replace(/^.*:/,"");let s=e.getAttribute(t,"data-mjx-texclass")||"";s&&(s=this.filterAttribute("data-mjx-texclass",s)||"");let i=s&&"mrow"===n?"TeXAtom":n;for(const o of this.filterClassList(e.allClasses(t)))o.match(/^MJX-TeXAtom-/)&&"mrow"===n?(s=o.substring(12),i="TeXAtom"):"MJX-fixedlimits"===o&&(r=!0);return this.factory.getNodeClass(i)?this.createMml(i,t,s,r):this.unknownNode(i,t)}createMml(t,e,r,n){const s=this.factory.create(t);return"TeXAtom"!==t||"OP"!==r||n||(s.setProperty("movesupsub",!0),s.attributes.setInherited("movablelimits",!0)),r&&(s.texClass=F[r],s.setProperty("texClass",s.texClass)),this.addAttributes(s,e),this.checkClass(s,e),this.addChildren(s,e),s}unknownNode(t,e){return this.factory.getNodeClass("html")&&this.options.allowHtmlInTokenNodes?this.factory.create("html").setHTML(e,this.adaptor):(this.error('Unknown node type "'+t+'"'),null)}addAttributes(t,e){let r=!1;for(const n of this.adaptor.allAttributes(e)){const e=n.name,s=this.filterAttribute(e,n.value);if(null!==s&&"xmlns"!==e)if("data-mjx-"===e.substring(0,9))switch(e.substring(9)){case"alternate":t.setProperty("variantForm",!0);break;case"variant":t.attributes.set("mathvariant",s),t.setProperty("ignore-variant",!0),r=!0;break;case"smallmatrix":t.setProperty("smallmatrix",!0),t.setProperty("useHeight",!1);break;case"mathaccent":t.setProperty("mathaccent","true"===s);break;case"auto-op":t.setProperty("autoOP","true"===s);break;case"script-align":t.setProperty("scriptalign",s);break;case"vbox":t.setProperty("vbox",s);break;default:t.attributes.set(e,s)}else if("class"!==e){const n=s.toLowerCase();"true"===n||"false"===n?t.attributes.set(e,"true"===n):r&&"mathvariant"===e||t.attributes.set(e,s)}}}filterAttribute(t,e){return e}filterClassList(t){return t}addChildren(t,e){if(0===t.arity)return;const r=this.adaptor;for(const n of r.childNodes(e)){const e=r.kind(n);if("#comment"!==e)if("#text"===e)this.addText(t,n);else if(t.isKind("annotation-xml"))t.appendChild(this.factory.create("XML").setXML(n,r));else{const e=t.appendChild(this.makeNode(n));0===e.arity&&r.childNodes(n).length&&!e.isKind("html")&&(this.options.fixMisplacedChildren?this.addChildren(t,n):e.mError("There should not be children for "+e.kind+" nodes",this.options.verify,!0))}}t.isToken&&this.trimSpace(t)}addText(t,e){let r=this.adaptor.value(e);(t.isToken||t.getProperty("isChars"))&&t.arity?(t.isToken&&(r=function(t){return t.replace(/&([a-z][a-z0-9]*|#(?:[0-9]+|x[0-9a-f]+));/gi,Dt)}(r),r=this.normalizeSpace(r)),t.appendChild(this.factory.create("text").setText(r))):r.match(/\S/)&&this.error('Unexpected text node "'+r+'"')}checkClass(t,e){const r=[];for(const n of this.filterClassList(this.adaptor.allClasses(e)))"MJX-"===n.substring(0,4)?"MJX-variant"===n?t.setProperty("variantForm",!0):"MJX-TeXAtom"!==n.substring(0,11)&&t.attributes.set("mathvariant",this.fixCalligraphic(n.substring(3))):r.push(n);r.length&&t.attributes.set("class",r.join(" "))}fixCalligraphic(t){return t.replace(/caligraphic/,"calligraphic")}markMrows(t){if(t.isKind("mrow")&&!t.isInferred&&t.childNodes.length>=2){const e=t.childNodes[0],r=t.childNodes[t.childNodes.length-1];e.isKind("mo")&&e.attributes.get("fence")&&e.attributes.get("stretchy")&&r.isKind("mo")&&r.attributes.get("fence")&&r.attributes.get("stretchy")&&(e.childNodes.length&&t.setProperty("open",e.getText()),r.childNodes.length&&t.setProperty("close",r.getText()))}}normalizeSpace(t){return t.replace(/[\t\n\r]/g," ").replace(/  +/g," ")}trimSpace(t){let e=t.childNodes[0];e&&(e.isKind("text")&&e.setText(e.getText().replace(/^ +/,"")),e=t.childNodes[t.childNodes.length-1],e.isKind("text")&&e.setText(e.getText().replace(/ +$/,"")))}error(t){throw new Error(t)}}fr.OPTIONS={MmlFactory:null,allowHtmlInTokenNodes:!1,fixMisplacedChildren:!0,verify:Object.assign({},j.verifyDefaults),translateEntities:!0};class gr extends A{constructor(t={}){const[e,r,n]=T(t,mr.OPTIONS,fr.OPTIONS);super(e),this.findMathML=this.options.FindMathML||new mr(r),this.mathml=this.options.MathMLCompile||new fr(n),this.mmlFilters=new I(this.options.mmlFilters)}setAdaptor(t){super.setAdaptor(t),this.findMathML.adaptor=t,this.mathml.adaptor=t}setMmlFactory(t){super.setMmlFactory(t),this.mathml.setMmlFactory(t)}get processStrings(){return!1}compile(t,e){let r=t.start.node;if(!r||!t.end.node||this.options.forceReparse||"#text"===this.adaptor.kind(r)){let n=this.executeFilters(this.preFilters,t,e,(t.math||"<math></math>").trim());"html"===this.options.parseAs&&(n=`<html><head></head><body>${n}</body></html>`);const s=this.checkForErrors(this.adaptor.parse(n,"text/"+this.options.parseAs)),i=this.adaptor.body(s);1!==this.adaptor.childNodes(i).length&&this.error("MathML must consist of a single element"),r=this.adaptor.remove(this.adaptor.firstChild(i)),"math"!==this.adaptor.kind(r).replace(/^[a-z]+:/,"")&&this.error("MathML must be formed by a <math> element, not <"+this.adaptor.kind(r)+">")}r=this.executeFilters(this.mmlFilters,t,e,r);let n=this.mathml.compile(r);return n=this.executeFilters(this.postFilters,t,e,n),t.display="block"===n.attributes.get("display"),n}checkForErrors(t){const e=this.adaptor.tags(this.adaptor.body(t),"parsererror")[0];return e&&(""===this.adaptor.textContent(e)&&this.error("Error processing MathML"),this.options.parseError.call(this,e)),t}error(t){throw new Error(t)}findMath(t){return this.findMathML.findMath(t)}}gr.NAME="MathML",gr.OPTIONS=N({parseAs:"html",forceReparse:!1,mmlFilters:[],FindMathML:null,MathMLCompile:null,parseError:function(t){this.error(this.adaptor.textContent(t).replace(/\n.*/g,""))}},A.OPTIONS);class Er{constructor(t=null){this.canMeasureNodes=!0,this.document=t}node(t,e={},r=[],n){const s=this.create(t,n);this.setAttributes(s,e);for(const t of r)this.append(s,t);return s}setProperty(t,e,r){t[e]=r}getProperty(t,e){return t[e]}setAttributes(t,e){if(e.style&&"string"!=typeof e.style)for(const r of Object.keys(e.style))this.setStyle(t,r.replace(/-([a-z])/g,((t,e)=>e.toUpperCase())),e.style[r]);if(e.properties)for(const r of Object.keys(e.properties))t[r]=e.properties[r];for(const r of Object.keys(e))"style"===r&&"string"!=typeof e.style||"properties"===r||this.setAttribute(t,r,e[r])}replace(t,e){return this.insert(t,e),this.remove(e),e}childNode(t,e){return this.childNodes(t)[e]}allClasses(t){const e=this.getAttribute(t,"class");return e?e.replace(/  +/g," ").replace(/^ /,"").replace(/ $/,"").split(/ /):[]}cssText(t){return"style"===this.kind(t)?this.textContent(t):""}}var Nr=function(t,e,r,n){return new(r||(r=Promise))((function(s,i){function o(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?s(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(o,a)}c((n=n.apply(t,e||[])).next())}))};class br extends Er{constructor(t){super(t.document),this.canMeasureNodes=!0,this.window=t,this.parser=new t.DOMParser}parse(t,e="text/html"){return this.parser.parseFromString(t,e)}create(t,e){return e?this.document.createElementNS(e,t):this.document.createElement(t)}text(t){return this.document.createTextNode(t)}head(t=this.document){return t.head||t}body(t=this.document){return t.body||t}root(t=this.document){return t.documentElement||t}doctype(t=this.document){return t.doctype?`<!DOCTYPE ${t.doctype.name}>`:""}tags(t,e,r=null){const n=r?t.getElementsByTagNameNS(r,e):t.getElementsByTagName(e);return Array.from(n)}getElements(t,e){let r=[];for(const e of t)"string"==typeof e?r=r.concat(Array.from(this.document.querySelectorAll(e))):Array.isArray(e)||e instanceof this.window.NodeList||e instanceof this.window.HTMLCollection?r=r.concat(Array.from(e)):r.push(e);return r}getElement(t,e=this.document){return e.querySelector(t)}contains(t,e){return t.contains(e)}parent(t){return t.parentNode}append(t,e){return t.appendChild(e)}insert(t,e){return this.parent(e).insertBefore(t,e)}remove(t){return this.parent(t).removeChild(t)}replace(t,e){return this.parent(e).replaceChild(t,e)}clone(t,e=!0){return t.cloneNode(e)}split(t,e){return t.splitText(e)}next(t){return t.nextSibling}previous(t){return t.previousSibling}firstChild(t){return t.firstChild}lastChild(t){return t.lastChild}childNodes(t){return Array.from(t.childNodes)}childNode(t,e){return t.childNodes[e]}kind(t){const e=t.nodeType;return 1===e||3===e||8===e?t.nodeName.toLowerCase():""}value(t){return t.nodeValue||""}textContent(t){return t.textContent}innerHTML(t){return t.innerHTML}outerHTML(t){return t.outerHTML}serializeXML(t){return(new this.window.XMLSerializer).serializeToString(t)}setAttribute(t,e,r,n=null){return n?(e=n.replace(/.*\//,"")+":"+e.replace(/^.*:/,""),t.setAttributeNS(n,e,r)):t.setAttribute(e,r)}getAttribute(t,e){return t.getAttribute(e)}removeAttribute(t,e){return t.removeAttribute(e)}hasAttribute(t,e){return t.hasAttribute(e)}allAttributes(t){return Array.from(t.attributes).map((t=>({name:t.name,value:t.value})))}addClass(t,e){t.classList?t.classList.add(e):t.className=(t.className+" "+e).trim()}removeClass(t,e){t.classList?t.classList.remove(e):t.className=t.className.split(/ /).filter((t=>t!==e)).join(" ")}hasClass(t,e){return t.classList?t.classList.contains(e):t.className.split(/ /).includes(e)}setStyle(t,e,r){t.style[e]=r}getStyle(t,e){return t.style[e]}allStyles(t){return t.style.cssText}insertRules(t,e){for(const r of e)try{t.sheet.insertRule(r,t.sheet.cssRules.length)}catch(t){console.warn(`MathJax: can't insert css rule '${r}': ${t.message}`)}}cssText(t){return"style"!==this.kind(t)?"":Array.from(t.sheet.cssRules).map((t=>t.cssText)).join("\n")}fontSize(t){const e=this.window.getComputedStyle(t);return parseFloat(e.fontSize)}fontFamily(t){return this.window.getComputedStyle(t).fontFamily||""}nodeSize(t,e=1,r=!1){if(r&&t.getBBox){const{width:r,height:n}=t.getBBox();return[r/e,n/e]}return[t.offsetWidth/e,t.offsetHeight/e]}nodeBBox(t){const{left:e,right:r,top:n,bottom:s}=t.getBoundingClientRect();return{left:e,right:r,top:n,bottom:s}}createWorker(t,e){return Nr(this,void 0,void 0,(function*(){const{path:r,maps:n,worker:s}=e,i=`${r}/${s}`,o=`\n      self.maps = '${Tr(n)}';\n      importScripts('${Tr(i)}');\n    `,a=URL.createObjectURL(new Blob([o],{type:"text/javascript"})),c=new Worker(a);return c.onmessage=t,URL.revokeObjectURL(a),c}))}}function Tr(t){return[...t].map((t=>("\\"===t||"'"===t?t="\\"+t:(t<" "||t>"~")&&(t=`\\u{${t.codePointAt(0).toString(16)}}`),t))).join("")}class yr extends X{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mi"}setInheritedAttributes(t={},e=!1,r=0,n=!1){super.setInheritedAttributes(t,e,r,n);this.getText().match(yr.singleCharacter)&&!t.mathvariant&&this.attributes.setInherited("mathvariant","italic")}setTeXclass(t){this.getPrevClass(t);const e=this.getText();return e.length>1&&e.match(yr.operatorName)&&"normal"===this.attributes.get("mathvariant")&&void 0===this.getProperty("autoOP")&&void 0===this.getProperty("texClass")&&(this.texClass=F.OP,this.setProperty("autoOP",!0)),this}}yr.defaults=Object.assign({},X.defaults),yr.operatorName=/^[a-z][a-z0-9]*$/i,yr.singleCharacter=/^[\uD800-\uDBFF]?.[\u0300-\u036F\u1AB0-\u1ABE\u1DC0-\u1DFF\u20D0-\u20EF]*$/;class Ir extends Ct{create(t,e={},r=[]){return this.node[t](e,r)}}class Ar extends V{get kind(){return"math"}get linebreakContainer(){return!0}get linebreakAlign(){return""}setChildInheritedAttributes(t,e,r,n){"display"===this.attributes.get("mode")&&this.attributes.setInherited("display","block"),t=this.addInheritedAttributes(t,this.attributes.getAllAttributes()),e=!!this.attributes.get("displaystyle")||!this.attributes.get("displaystyle")&&"block"===this.attributes.get("display"),this.attributes.setInherited("displaystyle",e),r=this.attributes.get("scriptlevel")||this.constructor.defaults.scriptlevel,super.setChildInheritedAttributes(t,e,r,n)}verifyTree(t=null){super.verifyTree(t),this.parent&&this.mError("Improper nesting of math tags",t,!0)}}Ar.defaults=Object.assign(Object.assign({},V.defaults),{mathvariant:"normal",mathsize:"normal",mathcolor:"",mathbackground:"transparent",dir:"ltr",scriptlevel:0,displaystyle:!1,display:"inline",maxwidth:"",overflow:"linebreak",altimg:"","altimg-width":"","altimg-height":"","altimg-valign":"",alttext:"",cdgroup:"",scriptsizemultiplier:1/Math.sqrt(2),scriptminsize:".4em",infixlinebreakstyle:"before",lineleading:"100%",linebreakmultchar:"\u2062",indentshift:"auto",indentalign:"auto",indenttarget:"",indentalignfirst:"indentalign",indentshiftfirst:"indentshift",indentalignlast:"indentalign",indentshiftlast:"indentshift"});class Rr extends X{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mn"}}Rr.defaults=Object.assign({},X.defaults);class Lr extends X{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mtext"}get isSpacelike(){return!!this.getText().match(/^\s*$/)&&!this.attributes.hasOneOf(Lr.NONSPACELIKE)}}Lr.NONSPACELIKE=["style","mathbackground","background"],Lr.defaults=Object.assign({},X.defaults);class Cr extends X{constructor(){super(...arguments),this.texclass=F.NONE}setTeXclass(t){return t}get kind(){return"mspace"}get arity(){return 0}get isSpacelike(){return!this.attributes.hasExplicit("linebreak")&&this.canBreak}get hasNewline(){const t=this.attributes.get("linebreak");return this.canBreak&&("newline"===t||"indentingnewline"===t)}get canBreak(){return!this.attributes.hasOneOf(Cr.NONSPACELIKE)&&"-"!==String(this.attributes.get("width")).trim().charAt(0)}}Cr.NONSPACELIKE=["height","depth","style","mathbackground","background"],Cr.defaults=Object.assign(Object.assign({},X.defaults),{width:"0em",height:"0ex",depth:"0ex",linebreak:"auto",indentshift:"auto",indentalign:"auto",indenttarget:"",indentalignfirst:"indentalign",indentshiftfirst:"indentshift",indentalignlast:"indentalign",indentshiftlast:"indentshift"});class Or extends X{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"ms"}}Or.defaults=Object.assign(Object.assign({},X.defaults),{lquote:'"',rquote:'"'});class vr extends j{constructor(){super(...arguments),this._core=null}get kind(){return"mrow"}get isSpacelike(){for(const t of this.childNodes)if(!t.isSpacelike)return!1;return!0}get isEmbellished(){let t=!1,e=0;for(const r of this.childNodes){if(r)if(r.isEmbellished){if(t)return!1;t=!0,this._core=e}else if(!r.isSpacelike)return!1;e++}return t}core(){return this.isEmbellished&&null!=this._core?this.childNodes[this._core]:this}coreMO(){return this.isEmbellished&&null!=this._core?this.childNodes[this._core].coreMO():this}nonSpaceLength(){let t=0;for(const e of this.childNodes)e&&!e.isSpacelike&&t++;return t}firstNonSpace(){for(const t of this.childNodes)if(t&&!t.isSpacelike)return t;return null}lastNonSpace(){let t=this.childNodes.length;for(;--t>=0;){const e=this.childNodes[t];if(e&&!e.isSpacelike)return e}return null}setTeXclass(t){if(null!=this.getProperty("open")||null!=this.getProperty("close")){this.getPrevClass(t),t=null;for(const e of this.childNodes)t=e.setTeXclass(t);return null==this.texClass&&(this.texClass=F.INNER),this}for(const e of this.childNodes)t=e.setTeXclass(t);return this.childNodes[0]&&this.updateTeXclass(this.childNodes[0]),t}}vr.defaults=Object.assign({},j.defaults);class Sr extends vr{get kind(){return"inferredMrow"}get isInferred(){return!0}get notParent(){return!0}toString(){return"["+this.childNodes.join(",")+"]"}}Sr.defaults=vr.defaults;class wr extends W{get kind(){return"mfrac"}get arity(){return 2}get linebreakContainer(){return!0}get linebreakAlign(){return""}setTeXclass(t){this.getPrevClass(t);for(const t of this.childNodes)t.setTeXclass(null);return this}setChildInheritedAttributes(t,e,r,n){(!e||r>0)&&r++;const s=this.attributes.get("numalign"),i=this.attributes.get("denomalign"),o=this.addInheritedAttributes(Object.assign({},t),{numalign:s,indentshift:"0",indentalignfirst:s,indentshiftfirst:"0",indentalignlast:"indentalign",indentshiftlast:"indentshift"}),a=this.addInheritedAttributes(Object.assign({},t),{denalign:i,indentshift:"0",indentalignfirst:i,indentshiftfirst:"0",indentalignlast:"indentalign",indentshiftlast:"indentshift"});this.childNodes[0].setInheritedAttributes(o,!1,r,n),this.childNodes[1].setInheritedAttributes(a,!1,r,!0)}}wr.defaults=Object.assign(Object.assign({},W.defaults),{linethickness:"medium",numalign:"center",denomalign:"center",bevelled:!1});class Mr extends j{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"msqrt"}get arity(){return-1}get linebreakContainer(){return!0}setTeXclass(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this}setChildInheritedAttributes(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,!0)}}Mr.defaults=Object.assign(Object.assign({},j.defaults),{"data-vertical-align":"bottom"});class xr extends j{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mroot"}get arity(){return 2}get linebreakContainer(){return!0}setTeXclass(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this.childNodes[1].setTeXclass(null),this}setChildInheritedAttributes(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,!0),this.childNodes[1].setInheritedAttributes(t,!1,r+2,n)}}xr.defaults=Object.assign(Object.assign({},j.defaults),{"data-vertical-align":"bottom"});class Pr extends V{get kind(){return"mstyle"}get notParent(){return this.childNodes[0]&&1===this.childNodes[0].childNodes.length}setInheritedAttributes(t={},e=!1,r=0,n=!1){this.attributes.setInherited("displaystyle",e),this.attributes.setInherited("scriptlevel",r),super.setInheritedAttributes(t,e,r,n)}setChildInheritedAttributes(t,e,r,n){let s=this.attributes.getExplicit("scriptlevel");null!=s&&(s=s.toString(),s.match(/^\s*[-+]/)?r+=parseInt(s):r=parseInt(s),n=!1);const i=this.attributes.getExplicit("displaystyle");null!=i&&(e=!0===i,n=!1);const o=this.attributes.getExplicit("data-cramped");null!=o&&(n=o),t=this.addInheritedAttributes(t,this.attributes.getAllAttributes()),this.childNodes[0].setInheritedAttributes(t,e,r,n)}}Pr.defaults=Object.assign(Object.assign({},V.defaults),{scriptlevel:P,displaystyle:P,scriptsizemultiplier:1/Math.sqrt(2),scriptminsize:".4em",mathbackground:P,mathcolor:P,dir:P,infixlinebreakstyle:"before"});class Dr extends j{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"merror"}get arity(){return-1}get linebreakContainer(){return!0}}Dr.defaults=Object.assign({},j.defaults);class kr extends V{get kind(){return"mpadded"}get linebreakContainer(){return!0}setTeXclass(t){return this.getProperty("vbox")?(this.getPrevClass(t),this.texClass=F.ORD,this.childNodes[0].setTeXclass(null),this):super.setTeXclass(t)}}kr.defaults=Object.assign(Object.assign({},V.defaults),{width:"",height:"",depth:"",lspace:0,voffset:0});class _r extends V{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mphantom"}}_r.defaults=Object.assign({},V.defaults);class Fr extends j{constructor(){super(...arguments),this.texclass=F.INNER,this.separators=[],this.open=null,this.close=null}get kind(){return"mfenced"}setTeXclass(t){this.getPrevClass(t),this.open&&(t=this.open.setTeXclass(t)),this.childNodes[0]&&(t=this.childNodes[0].setTeXclass(t));for(let e=1,r=this.childNodes.length;e<r;e++)this.separators[e-1]&&(t=this.separators[e-1].setTeXclass(t)),this.childNodes[e]&&(t=this.childNodes[e].setTeXclass(t));return this.close&&(t=this.close.setTeXclass(t)),this.updateTeXclass(this.open),t}setChildInheritedAttributes(t,e,r,n){this.addFakeNodes();for(const s of[this.open,this.close].concat(this.separators))s&&s.setInheritedAttributes(t,e,r,n);super.setChildInheritedAttributes(t,e,r,n)}addFakeNodes(){let{open:t,close:e,separators:r}=this.attributes.getList("open","close","separators");if(t=t.replace(/[ \t\n\r]/g,""),e=e.replace(/[ \t\n\r]/g,""),r=r.replace(/[ \t\n\r]/g,""),t&&(this.open=this.fakeNode(t,{fence:!0,form:"prefix"},F.OPEN)),r){for(;r.length<this.childNodes.length-1;)r+=r.charAt(r.length-1);let t=0;for(const e of this.childNodes.slice(1))e&&this.separators.push(this.fakeNode(r.charAt(t++)))}e&&(this.close=this.fakeNode(e,{fence:!0,form:"postfix"},F.CLOSE))}fakeNode(t,e={},r=null){const n=this.factory.create("text").setText(t),s=this.factory.create("mo",e,[n]);return s.texClass=r,s.parent=this,s}}Fr.defaults=Object.assign(Object.assign({},j.defaults),{open:"(",close:")",separators:","});class Br extends j{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"menclose"}get arity(){return-1}get linebreakContainer(){return!0}setTeXclass(t){return t=this.childNodes[0].setTeXclass(t),this.updateTeXclass(this.childNodes[0]),t}}Br.defaults=Object.assign(Object.assign({},j.defaults),{notation:"longdiv"});class Ur extends j{get kind(){return"maction"}get arity(){return 1}get selected(){const t=this.attributes.get("selection"),e=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[e]||this.factory.create("mrow")}get isEmbellished(){return this.selected.isEmbellished}get isSpacelike(){return this.selected.isSpacelike}core(){return this.selected.core()}coreMO(){return this.selected.coreMO()}verifyAttributes(t){super.verifyAttributes(t),"toggle"!==this.attributes.get("actiontype")&&this.attributes.hasExplicit("selection")&&this.attributes.unset("selection")}setTeXclass(t){"tooltip"===this.attributes.get("actiontype")&&this.childNodes[1]&&this.childNodes[1].setTeXclass(null);const e=this.selected;return t=e.setTeXclass(t),this.updateTeXclass(e),t}nextToggleSelection(){let t=Math.max(1,parseInt(this.attributes.get("selection"))+1);t>this.childNodes.length&&(t=1),this.attributes.set("selection",t)}setChildInheritedAttributes(t,e,r,n){var s,i;"tooltip"===this.attributes.get("actiontype").toLowerCase()?(null===(s=this.childNodes[0])||void 0===s||s.setInheritedAttributes(t,e,r,n),null===(i=this.childNodes[1])||void 0===i||i.setInheritedAttributes(t,!1,1,!1)):super.setChildInheritedAttributes(t,e,r,n)}}Ur.defaults=Object.assign(Object.assign({},j.defaults),{actiontype:"toggle",selection:1});class qr extends W{get kind(){return"msubsup"}get arity(){return 3}get base(){return 0}get sub(){return 1}get sup(){return 2}setChildInheritedAttributes(t,e,r,n){const s=this.childNodes;s[0].setInheritedAttributes(t,e,r,n),s[1].setInheritedAttributes(t,!1,r+1,n||1===this.sub),s[2]&&s[2].setInheritedAttributes(t,!1,r+1,n||2===this.sub)}}qr.defaults=Object.assign(Object.assign({},W.defaults),{subscriptshift:"",superscriptshift:""});class Hr extends qr{get kind(){return"msub"}get arity(){return 2}}Hr.defaults=Object.assign({},qr.defaults);class Gr extends qr{get kind(){return"msup"}get arity(){return 2}get sup(){return 1}get sub(){return 2}}Gr.defaults=Object.assign({},qr.defaults);class jr extends W{get kind(){return"munderover"}get arity(){return 3}get base(){return 0}get under(){return 1}get over(){return 2}get linebreakContainer(){return!0}setChildInheritedAttributes(t,e,r,n){const s=this.childNodes;s[0].setInheritedAttributes(t,e,r,n||!!s[this.over]);const i=!(e||!s[0].coreMO().attributes.get("movablelimits")),o=this.constructor.ACCENTS;s[1].setInheritedAttributes(t,!1,this.getScriptlevel(o[1],i,r),n||1===this.under),this.setInheritedAccent(1,o[1],e,r,n,i),s[2]&&(s[2].setInheritedAttributes(t,!1,this.getScriptlevel(o[2],i,r),n||2===this.under),this.setInheritedAccent(2,o[2],e,r,n,i))}getScriptlevel(t,e,r){return!e&&this.attributes.get(t)||r++,r}setInheritedAccent(t,e,r,n,s,i){const o=this.childNodes[t];if(!this.attributes.hasExplicit(e)&&o.isEmbellished){const t=o.coreMO().attributes.get("accent");this.attributes.setInherited(e,t),t!==this.attributes.getDefault(e)&&o.setInheritedAttributes({},r,this.getScriptlevel(e,i,n),s)}}}jr.defaults=Object.assign(Object.assign({},W.defaults),{accent:!1,accentunder:!1,align:"center"}),jr.ACCENTS=["","accentunder","accent"];class Xr extends jr{get kind(){return"munder"}get arity(){return 2}}Xr.defaults=Object.assign({},jr.defaults);class Vr extends jr{get kind(){return"mover"}get arity(){return 2}get over(){return 1}get under(){return 2}}Vr.defaults=Object.assign({},jr.defaults),Vr.ACCENTS=["","accent","accentunder"];class Wr extends qr{get kind(){return"mmultiscripts"}get arity(){return 1}setChildInheritedAttributes(t,e,r,n){this.childNodes[0].setInheritedAttributes(t,e,r,n);let s=!1;for(let e=1,i=0;e<this.childNodes.length;e++){const o=this.childNodes[e];if(o.isKind("mprescripts")){if(!s&&(s=!0,e%2==0)){const t=this.factory.create("none");this.childNodes.splice(e,0,t),t.parent=this,e++}}else{const e=n||i%2==0;o.setInheritedAttributes(t,!1,r+1,e),i++}}this.childNodes.length%2==(s?1:0)&&(this.appendChild(this.factory.create("none")),this.childNodes[this.childNodes.length-1].setInheritedAttributes(t,!1,r+1,n))}verifyChildren(t){let e=!1;const r=t.fixMmultiscripts;for(let n=0;n<this.childNodes.length;n++){const s=this.childNodes[n];s.isKind("mprescripts")&&(e?s.mError(s.kind+" can only appear once in "+this.kind,t,!0):(e=!0,n%2!=0||r||this.mError("There must be an equal number of prescripts of each type",t)))}this.childNodes.length%2!=(e?1:0)||r||this.mError("There must be an equal number of scripts of each type",t),super.verifyChildren(t)}}Wr.defaults=Object.assign({},qr.defaults);class Kr extends j{get kind(){return"mprescripts"}get arity(){return 0}verifyTree(t){super.verifyTree(t),this.parent&&!this.parent.isKind("mmultiscripts")&&this.mError(this.kind+" must be a child of mmultiscripts",t,!0)}}Kr.defaults=Object.assign({},j.defaults);class zr extends j{get kind(){return"none"}get arity(){return 0}verifyTree(t){super.verifyTree(t),this.parent&&!this.parent.isKind("mmultiscripts")&&this.mError(this.kind+" must be a child of mmultiscripts",t,!0)}}zr.defaults=Object.assign({},j.defaults);class Yr extends j{constructor(){super(...arguments),this.properties={useHeight:!0},this.texclass=F.ORD}get kind(){return"mtable"}get linebreakContainer(){return!0}get linebreakAlign(){return""}setInheritedAttributes(t,e,r,n){for(const e of G)t[e]&&this.attributes.setInherited(e,t[e][1]),this.attributes.hasExplicit(e)&&this.attributes.unset(e);super.setInheritedAttributes(t,e,r,n)}setChildInheritedAttributes(t,e,r,n){for(const t of this.childNodes)t.isKind("mtr")||this.replaceChild(this.factory.create("mtr"),t).appendChild(t);e=!(!this.attributes.getExplicit("displaystyle")&&!this.attributes.getDefault("displaystyle")),t=this.addInheritedAttributes(t,{columnalign:this.attributes.get("columnalign"),rowalign:"center","data-break-align":this.attributes.get("data-break-align")});const s=this.attributes.getExplicit("data-cramped"),i=O(this.attributes.get("rowalign"));for(const n of this.childNodes)t.rowalign[1]=i.shift()||t.rowalign[1],n.setInheritedAttributes(t,e,r,!!s)}verifyChildren(t){let e=null;const r=this.factory;for(let n=0;n<this.childNodes.length;n++){const s=this.childNodes[n];if(s.isKind("mtr"))e=null;else{const i=s.isKind("mtd");if(e?(this.removeChild(s),n--):e=this.replaceChild(r.create("mtr"),s),e.appendChild(i?s:r.create("mtd",{},[s])),!t.fixMtables){s.parent.removeChild(s),s.parent=this,i&&e.appendChild(r.create("mtd"));const n=s.mError("Children of "+this.kind+" must be mtr or mlabeledtr",t,i);e.childNodes[e.childNodes.length-1].appendChild(n)}}}super.verifyChildren(t)}setTeXclass(t){this.getPrevClass(t);for(const t of this.childNodes)t.setTeXclass(null);return this}}Yr.defaults=Object.assign(Object.assign({},j.defaults),{align:"axis",rowalign:"baseline",columnalign:"center",groupalign:"{left}",alignmentscope:!0,columnwidth:"auto",width:"auto",rowspacing:"1ex",columnspacing:".8em",rowlines:"none",columnlines:"none",frame:"none",framespacing:"0.4em 0.5ex",equalrows:!1,equalcolumns:!1,displaystyle:!1,side:"right",minlabelspacing:"0.8em","data-break-align":"top"});class $r extends j{get kind(){return"mtr"}get linebreakContainer(){return!0}get linebreakAlign(){return""}setChildInheritedAttributes(t,e,r,n){for(const t of this.childNodes)t.isKind("mtd")||this.replaceChild(this.factory.create("mtd"),t).appendChild(t);const s=O(this.attributes.get("columnalign")),i=O(this.attributes.get("data-break-align"));1===this.arity&&(s.unshift(this.parent.attributes.get("side")),i.unshift("top")),t=this.addInheritedAttributes(t,{rowalign:this.attributes.get("rowalign"),columnalign:"center","data-break-align":"top"});for(const o of this.childNodes)t.columnalign[1]=s.shift()||t.columnalign[1],t["data-vertical-align"]=[this.kind,i.shift()||t["data-break-align"][1]],o.setInheritedAttributes(t,e,r,n)}verifyChildren(t){if(!this.parent||this.parent.isKind("mtable")){for(const e of this.childNodes)if(!e.isKind("mtd")){this.replaceChild(this.factory.create("mtd"),e).appendChild(e),t.fixMtables||e.mError("Children of "+this.kind+" must be mtd",t)}super.verifyChildren(t)}else this.mError(this.kind+" can only be a child of an mtable",t,!0)}setTeXclass(t){this.getPrevClass(t);for(const t of this.childNodes)t.setTeXclass(null);return this}}$r.defaults=Object.assign(Object.assign({},j.defaults),{rowalign:P,columnalign:P,groupalign:P,"data-break-align":"top"});class Jr extends $r{get kind(){return"mlabeledtr"}get arity(){return 1}}class Qr extends W{get kind(){return"mtd"}get arity(){return-1}get linebreakContainer(){return!0}get linebreakAlign(){return"columnalign"}verifyChildren(t){!this.parent||this.parent.isKind("mtr")?super.verifyChildren(t):this.mError(this.kind+" can only be a child of an mtr or mlabeledtr",t,!0)}setTeXclass(t){return this.getPrevClass(t),this.childNodes[0].setTeXclass(null),this}}Qr.defaults=Object.assign(Object.assign({},W.defaults),{rowspan:1,columnspan:1,rowalign:P,columnalign:P,groupalign:P,"data-vertical-align":"top"});class Zr extends V{get kind(){return"maligngroup"}get isSpacelike(){return!0}setChildInheritedAttributes(t,e,r,n){t=this.addInheritedAttributes(t,this.attributes.getAllAttributes()),super.setChildInheritedAttributes(t,e,r,n)}}Zr.defaults=Object.assign(Object.assign({},V.defaults),{groupalign:P});class tn extends j{get kind(){return"malignmark"}get arity(){return 0}get isSpacelike(){return!0}}tn.defaults=Object.assign(Object.assign({},j.defaults),{edge:"left"});class en extends X{constructor(){super(...arguments),this.texclass=F.ORD}get kind(){return"mglyph"}verifyAttributes(t){const{src:e,fontfamily:r,index:n}=this.attributes.getList("src","fontfamily","index");""!==e||""!==r&&""!==n?super.verifyAttributes(t):this.mError("mglyph must have either src or fontfamily and index attributes",t,!0)}}en.defaults=Object.assign(Object.assign({},X.defaults),{alt:"",src:"",index:"",width:"auto",height:"auto",valign:"0em"});class rn extends W{get kind(){return"semantics"}get arity(){return 1}get notParent(){return!0}}rn.defaults=Object.assign(Object.assign({},W.defaults),{definitionUrl:null,encoding:null});class nn extends j{get kind(){return"annotation-xml"}setChildInheritedAttributes(){}}nn.defaults=Object.assign(Object.assign({},j.defaults),{definitionUrl:null,encoding:null,cd:"mathmlkeys",name:"",src:null});class sn extends nn{constructor(){super(...arguments),this.properties={isChars:!0}}get kind(){return"annotation"}}sn.defaults=Object.assign({},nn.defaults);class on extends W{get kind(){return"TeXAtom"}get arity(){return-1}get notParent(){return!0}constructor(t,e,r){super(t,e,r),this.texclass=F.ORD,this.setProperty("texClass",this.texClass)}setTeXclass(t){return this.childNodes[0].setTeXclass(null),this.adjustTeXclass(t)}adjustTeXclass(t){return t}}on.defaults=Object.assign({},W.defaults),on.prototype.adjustTeXclass=et.prototype.adjustTeXclass;class an extends W{get kind(){return"MathChoice"}get arity(){return 4}get notParent(){return!0}setInheritedAttributes(t,e,r,n){const s=e?0:Math.max(0,Math.min(r,2))+1,i=this.childNodes[s]||this.factory.create("mrow");this.parent.replaceChild(i,this),i.setInheritedAttributes(t,e,r,n)}}an.defaults=Object.assign({},W.defaults);class cn extends Y{get kind(){return"html"}getHTML(){return this.getXML()}setHTML(t,e=null){try{e.getAttribute(t,"data-mjx-hdw")}catch(r){t=e.node("span",{},[t])}return this.setXML(t,e)}getSerializedHTML(){return this.adaptor.outerHTML(this.xml)}textContent(){return this.adaptor.textContent(this.xml)}toString(){const t=this.adaptor.kind(this.xml);return`HTML=<${t}>...</${t}>`}verifyTree(t){!this.parent||this.parent.isToken||this.mError("HTML can only be a child of a token element",t,!0)}}const ln={[Ar.prototype.kind]:Ar,[yr.prototype.kind]:yr,[Rr.prototype.kind]:Rr,[et.prototype.kind]:et,[Lr.prototype.kind]:Lr,[Cr.prototype.kind]:Cr,[Or.prototype.kind]:Or,[vr.prototype.kind]:vr,[Sr.prototype.kind]:Sr,[wr.prototype.kind]:wr,[Mr.prototype.kind]:Mr,[xr.prototype.kind]:xr,[Pr.prototype.kind]:Pr,[Dr.prototype.kind]:Dr,[kr.prototype.kind]:kr,[_r.prototype.kind]:_r,[Fr.prototype.kind]:Fr,[Br.prototype.kind]:Br,[Ur.prototype.kind]:Ur,[Hr.prototype.kind]:Hr,[Gr.prototype.kind]:Gr,[qr.prototype.kind]:qr,[Xr.prototype.kind]:Xr,[Vr.prototype.kind]:Vr,[jr.prototype.kind]:jr,[Wr.prototype.kind]:Wr,[Kr.prototype.kind]:Kr,[zr.prototype.kind]:zr,[Yr.prototype.kind]:Yr,[Jr.prototype.kind]:Jr,[$r.prototype.kind]:$r,[Qr.prototype.kind]:Qr,[Zr.prototype.kind]:Zr,[tn.prototype.kind]:tn,[en.prototype.kind]:en,[rn.prototype.kind]:rn,[sn.prototype.kind]:sn,[nn.prototype.kind]:nn,[on.prototype.kind]:on,[an.prototype.kind]:an,[z.prototype.kind]:z,[Y.prototype.kind]:Y,[cn.prototype.kind]:cn};class hn extends Ir{get MML(){return this.node}}hn.defaultNodes=ln;class un{static methodName(t){return"visit"+(t.charAt(0).toUpperCase()+t.substring(1)).replace(/[^a-z0-9_]/gi,"_")+"Node"}constructor(t){this.nodeHandlers=new Map;for(const e of t.getKinds()){const t=this[un.methodName(e)];t&&this.nodeHandlers.set(e,t)}}visitTree(t,...e){return this.visitNode(t,...e)}visitNode(t,...e){return(this.nodeHandlers.get(t.kind)||this.visitDefault).call(this,t,...e)}visitDefault(t,...e){if("childNodes"in t)for(const r of t.childNodes)this.visitNode(r,...e)}setNodeHandler(t,e){this.nodeHandlers.set(t,e)}removeNodeHandler(t){this.nodeHandlers.delete(t)}}class dn extends un{constructor(t=null){t||(t=new hn),super(t)}visitTextNode(t,...e){}visitXMLNode(t,...e){}visitHtmlNode(t,...e){}getKind(t){const e=t.kind;return y(e,this.constructor.rename,e)}getAttributeList(t){const e=this.constructor,r=y(t.kind,e.defaultAttributes,{}),n=Object.assign({},r,this.getDataAttributes(t),t.attributes.getAllAttributes()),s=e.variants;return Object.hasOwn(n,"mathvariant")&&(Object.hasOwn(s,n.mathvariant)?n.mathvariant=s[n.mathvariant]:t.getProperty("ignore-variant")&&delete n.mathvariant),n}getDataAttributes(t){const e={},r=t.attributes.getExplicit("mathvariant"),n=this.constructor.variants;r&&(t.getProperty("ignore-variant")||Object.hasOwn(n,r))&&this.setDataAttribute(e,"variant",r),t.getProperty("variantForm")&&this.setDataAttribute(e,"alternate","1"),t.getProperty("pseudoscript")&&this.setDataAttribute(e,"pseudoscript","true"),!1===t.getProperty("autoOP")&&this.setDataAttribute(e,"auto-op","false");const s=t.getProperty("vbox");s&&this.setDataAttribute(e,"vbox",s);const i=t.getProperty("scriptalign");i&&this.setDataAttribute(e,"script-align",i);const o=t.getProperty("mathaccent");void 0!==o&&(o&&!t.isMathAccent()||!o&&!t.isMathAccentWithWidth())&&this.setDataAttribute(e,"mathaccent",o.toString());const a=t.getProperty("texClass");if(void 0!==a){let r=!0;if(a===F.OP&&t.isKind("mi")){const e=t.getText();r=!(e.length>1&&e.match(yr.operatorName))}r&&this.setDataAttribute(e,"texclass",a<0?"NONE":B[a])}return t.getProperty("smallmatrix")&&this.setDataAttribute(e,"smallmatrix","true"),e}setDataAttribute(t,e,r){t["data-mjx-"+e]=r}}dn.rename={TeXAtom:"mrow"},dn.variants={"-tex-calligraphic":"script","-tex-bold-calligraphic":"bold-script","-tex-oldstyle":"normal","-tex-bold-oldstyle":"bold","-tex-mathit":"italic"},dn.defaultAttributes={math:{xmlns:"http://www.w3.org/1998/Math/MathML"}};class pn extends dn{visitTree(t){return this.visitNode(t,"")}visitTextNode(t,e){return this.quoteHTML(t.getText())}visitXMLNode(t,e){return e+t.getSerializedXML()}visitHtmlNode(t,e){return t.getSerializedHTML()}visitInferredMrowNode(t,e){const r=[];for(const n of t.childNodes)r.push(this.visitNode(n,e));return r.join("\n")}visitAnnotationNode(t,e){const r=this.childNodeMml(t,"","");return`${e}<annotation${this.getAttributes(t)}>${r}</annotation>`}visitDefault(t,e){const r=this.getKind(t),[n,s]=t.isToken||0===t.childNodes.length?["",""]:["\n",e],i=this.childNodeMml(t,e+"  ",n),o=i.match(/\S/)?n+i+s:"";return`${e}<${r}${this.getAttributes(t)}>${o}</${r}>`}childNodeMml(t,e,r){let n="";for(const s of t.childNodes)n+=this.visitNode(s,e)+r;return n}getAttributes(t){const e=[],r=this.getAttributeList(t);for(const t of Object.keys(r)){const n=String(r[t]);void 0!==n&&e.push(t+'="'+this.quoteHTML(n)+'"')}return e.length?" "+e.join(" "):""}quoteHTML(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/[\uD800-\uDBFF]./g,this.toEntity).replace(/[\u0080-\uD7FF\uE000-\uFFFF]/g,this.toEntity)}toEntity(t){return function(t){return`&#x${t.codePointAt(0).toString(16).toUpperCase()};`}(t)}}var mn,fn,gn;!function(t){t.DOMAIN="domain",t.STYLE="style",t.LOCALE="locale",t.TOPIC="topic",t.MODALITY="modality"}(mn||(mn={}));class En{static createProp(...t){const e=Nn.DEFAULT_ORDER,r={};for(let n=0,s=t.length,i=e.length;n<s&&n<i;n++)r[e[n]]=t[n];return new En(r)}constructor(t,e=Object.keys(t)){this.properties=t,this.order=e}getProperties(){return this.properties}getOrder(){return this.order}getAxes(){return this.order}getProperty(t){return this.properties[t]}updateProperties(t){this.properties=t}allProperties(){const t=[];return this.order.forEach((e=>t.push(this.getProperty(e).slice()))),t}toString(){const t=[];return this.order.forEach((e=>t.push(e+": "+this.getProperty(e).toString()))),t.join("\n")}}class Nn extends En{static createCstr(...t){const e=Nn.DEFAULT_ORDER,r={};for(let n=0,s=t.length,i=e.length;n<s&&n<i;n++)r[e[n]]=t[n];return new Nn(r)}static defaultCstr(){return Nn.createCstr.apply(null,Nn.DEFAULT_ORDER.map((function(t){return Nn.DEFAULT_VALUES[t]})))}static validOrder(t){const e=Nn.DEFAULT_ORDER.slice();return t.every((t=>{const r=e.indexOf(t);return-1!==r&&e.splice(r,1)}))}constructor(t,e){const r={};for(const[e,n]of Object.entries(t))r[e]=[n];super(r,e),this.components=t}getComponents(){return this.components}getValue(t){return this.components[t]}getValues(){return this.order.map((t=>this.getValue(t)))}allProperties(){const t=super.allProperties();for(let e,r,n=0;e=t[n],r=this.order[n];n++){const t=this.getValue(r);-1===e.indexOf(t)&&e.unshift(t)}return t}toString(){return this.getValues().join(".")}equal(t){const e=t.getAxes();if(this.order.length!==e.length)return!1;for(let r,n=0;r=e[n];n++){const e=this.getValue(r);if(!e||t.getValue(r)!==e)return!1}return!0}}Nn.DEFAULT_ORDER=[mn.LOCALE,mn.MODALITY,mn.DOMAIN,mn.STYLE,mn.TOPIC],Nn.BASE_LOCALE="base",Nn.DEFAULT_VALUE="default",Nn.DEFAULT_VALUES={[mn.LOCALE]:"en",[mn.DOMAIN]:Nn.DEFAULT_VALUE,[mn.STYLE]:Nn.DEFAULT_VALUE,[mn.TOPIC]:Nn.DEFAULT_VALUE,[mn.MODALITY]:"speech"};class bn{constructor(t){this.order=t}parse(t){const e=t.split("."),r={};if(e.length>this.order.length)throw new Error("Invalid dynamic constraint: "+r);let n=0;for(let t,s=0;t=this.order[s],e.length;s++,n++){const n=e.shift();r[t]=n}return new Nn(r,this.order.slice(0,n))}}class Tn{constructor(t,e=new En(t.getProperties(),t.getOrder())){this.reference=t,this.fallback=e,this.order=this.reference.getOrder()}getReference(){return this.reference}setReference(t,e){this.reference=t,this.fallback=e||new En(t.getProperties(),t.getOrder()),this.order=this.reference.getOrder()}match(t){const e=t.getAxes();return e.length===this.reference.getAxes().length&&e.every((e=>{const r=t.getValue(e);return r===this.reference.getValue(e)||-1!==this.fallback.getProperty(e).indexOf(r)}))}compare(t,e){let r=!1;for(let n,s=0;n=this.order[s];s++){const s=t.getValue(n),i=e.getValue(n);if(!r){const t=this.reference.getValue(n);if(t===s&&t!==i)return-1;if(t===i&&t!==s)return 1;if(t===s&&t===i)continue;t!==s&&t!==i&&(r=!0)}const o=this.fallback.getProperty(n),a=o.indexOf(s),c=o.indexOf(i);if(a<c)return-1;if(c<a)return 1}return 0}toString(){return this.reference.toString()+"\n"+this.fallback.toString()}}!function(t){t.SYNC="sync",t.ASYNC="async",t.HTTP="http"}(fn||(fn={})),function(t){t.PITCH="pitch",t.RATE="rate",t.VOLUME="volume",t.PAUSE="pause",t.JOIN="join",t.LAYOUT="layout"}(gn||(gn={}));gn.PITCH,gn.RATE,gn.VOLUME,gn.PAUSE,gn.JOIN;var yn,In;!function(t){t.NONE="none",t.SHALLOW="shallow",t.DEEP="deep"}(yn||(yn={})),function(t){t.NONE="none",t.LAYOUT="layout",t.COUNTING="counting",t.PUNCTUATION="punctuation",t.SSML="ssml",t.ACSS="acss",t.SABLE="sable",t.VOICEXML="voicexml"}(In||(In={}));var An=__webpack_require__(342);function Rn(t){return t.match("/$")?t:t+"/"}class Ln{static getInstance(){return Ln.instance=Ln.instance||new Ln,Ln.instance}init(t){return t&&this.startDebugFile_(t),this.isActive_=!0,this.fileHandle}output(...t){this.isActive_&&this.output_(t)}generateOutput(t){this.isActive_&&this.output_(t.apply(t,[]))}exit(t=()=>{}){this.fileHandle.then((()=>{this.isActive_&&this.stream_&&this.stream_.end("","",t)}))}constructor(){this.isActive_=!1,this.outputFunction_=console.info,this.fileHandle=Promise.resolve(),this.stream_=null}startDebugFile_(t){this.fileHandle=An.f.fs.promises.open(t,"w"),this.fileHandle=this.fileHandle.then((e=>{this.stream_=e.createWriteStream(t),this.outputFunction_=function(...t){this.stream_.write(t.join(" ")),this.stream_.write("\n")}.bind(this),this.stream_.on("error",function(t){console.info("Invalid log file. Debug information sent to console."),this.outputFunction_=console.info}.bind(this)),this.stream_.on("finish",(function(){console.info("Finalizing debug file.")}))}))}output_(t){console.info!==this.outputFunction_?this.fileHandle.then((()=>this.outputFunction_.apply(this.outputFunction_,["Speech Rule Engine Debugger:"].concat(t)))):this.outputFunction_.apply(console,["Speech Rule Engine Debugger:"].concat(t))}}var Cn=__webpack_require__(550);class On{constructor(t={}){this.delay=!1,this.domain="mathspeak",this.style=Nn.DEFAULT_VALUES[mn.STYLE],this.locale=Nn.DEFAULT_VALUES[mn.LOCALE],this.subiso="",this.modality=Nn.DEFAULT_VALUES[mn.MODALITY],this.speech=yn.NONE,this.markup=In.NONE,this.mark=!0,this.automark=!1,this.character=!0,this.cleanpause=!0,this.cayleyshort=!0,this.linebreaks=!1,this.rate="100",this.walker="Table",this.structure=!1,this.aria=!1,this.tree=!1,this.strict=!1,this.pprint=!1,this.rules="",this.prune="",this.set(t)}set(t){this.ensureDomain(t);for(const[e,r]of Object.entries(t))(On.BINARY_FEATURES.includes(e)||On.STRING_FEATURES.includes(e))&&(this[e]=r)}json(){const t={};return[].concat(On.STRING_FEATURES,On.BINARY_FEATURES).forEach((e=>t[e]=this[e])),t}ensureDomain(t){if(t.modality&&"speech"!==t.modality||!t.modality&&"speech"!==this.modality)return;if(!t.domain&&!t.locale)return;if("default"===t.domain)return void(t.domain="mathspeak");const e=t.locale||this.locale,r=t.domain||this.domain;-1===vn.indexOf(e)||"mathspeak"===r?"en"!==e?"mathspeak"!==r&&"clearspeak"!==r&&(t.domain="mathspeak"):-1===Sn.indexOf(r)&&(t.domain="mathspeak"):t.domain="mathspeak"}}On.BINARY_FEATURES=["automark","mark","character","cleanpause","strict","structure","aria","pprint","cayleyshort","linebreaks","tree"],On.STRING_FEATURES=["markup","style","domain","speech","walker","locale","delay","modality","rate","rules","subiso","prune"];const vn=["ca","da","es"],Sn=["chromevox","clearspeak","mathspeak","emacspeak","html"];class wn extends Error{constructor(t=""){super(),this.message=t,this.name="SRE Error"}}class Mn{set defaultLocale(t){this._defaultLocale=Cn.u.ensureLocale(t,this._defaultLocale)}get defaultLocale(){return this._defaultLocale}static getInstance(){return Mn.instance=Mn.instance||new Mn,Mn.instance}static defaultEvaluator(t,e){return t}static evaluateNode(t){return Mn.nodeEvaluator(t)}getRate(){const t=parseInt(this.options.rate,10);return isNaN(t)?100:t}setDynamicCstr(t){if(this.defaultLocale&&(Nn.DEFAULT_VALUES[mn.LOCALE]=this.defaultLocale),t){const e=Object.keys(t);for(let r=0;r<e.length;r++){const n=e[r];if(-1!==Nn.DEFAULT_ORDER.indexOf(n)){const e=t[n];this.options[n]=e}}}const e=[this.options.locale,this.options.modality,this.options.domain,this.options.style].join("."),r=En.createProp([Nn.DEFAULT_VALUES[mn.LOCALE]],[Nn.DEFAULT_VALUES[mn.MODALITY]],[Nn.DEFAULT_VALUES[mn.DOMAIN]],[Nn.DEFAULT_VALUES[mn.STYLE]]),n=this.comparators[this.options.domain],s=this.parsers[this.options.domain];this.parser=s||this.defaultParser,this.dynamicCstr=this.parser.parse(e),this.dynamicCstr.updateProperties(r.getProperties()),this.comparator=n?n():new Tn(this.dynamicCstr)}constructor(){this.options=new On,this.config=!1,this.customLoader=null,this.parsers={},this.comparator=null,this.mode=fn.SYNC,this.init=!0,this.comparators={},this._defaultLocale=Nn.DEFAULT_VALUES[mn.LOCALE],this.options.locale=this.defaultLocale,this.evaluator=Mn.defaultEvaluator,this.defaultParser=new bn(Nn.DEFAULT_ORDER),this.parser=this.defaultParser,this.dynamicCstr=Nn.defaultCstr()}configurate(t){this.mode!==fn.HTTP||An.f.webworker||this.config||(!function(t){const e=document.documentElement.querySelectorAll('script[type="text/x-sre-config"]');for(let r=0,n=e.length;r<n;r++){let n;try{n=e[r].innerHTML;const s=JSON.parse(n);for(const[e,r]of Object.entries(s))t[e]=r}catch(t){Ln.getInstance().output("Illegal configuration ",n)}}}(t),this.config=!0),function(t){if("undefined"!=typeof SREfeature)for(const[e,r]of Object.entries(SREfeature))t[e]=r}(t)}setCustomLoader(t){t&&(this.customLoader=t)}setup(t){void 0!==t.mode&&(this.mode=t.mode),this.configurate(t),this.options.set(t),t.json&&(An.f.jsonPath=Rn(t.json)),this.setCustomLoader(t.custom)}json(){return Object.assign({mode:this.mode},this.options.json())}reset(){this.options=new On}}Mn.nodeEvaluator=function(t){return[]};class xn{static get(t=Mn.getInstance().options.locale){return xn.promises[t]||Promise.resolve("")}static getall(){return Promise.all(Object.values(xn.promises))}}xn.loaded={},xn.promises={};const Pn=An.f.xpath,Dn={xhtml:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",mml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function kn(t){return Dn[t]||null}class _n{constructor(){this.lookupNamespaceURI=kn}}function Fn(t,e,r){return Pn.evaluate(t,e,new _n,r,null)}function Bn(t){const e=[];for(let r=0,n=t.length;r<n;r++)e.push(t[r]);return e}function Un(t){const e=new An.f.xmldom.DOMParser,r=function(t){return(t=t.replace(/&nbsp;/g,"\xa0")).replace(/>[ \f\n\r\t\v\u200b]+</g,"><").trim()}(t),n=!!r.match(/&(?!lt|gt|amp|quot|apos)\w+;/g);if(!r)throw new Error("Empty input!");try{const t=e.parseFromString(r,n?"text/html":"text/xml");return Mn.getInstance().mode===fn.HTTP?(Pn.currentDocument=t,n?t.body.childNodes[0]:t.documentElement):t.documentElement}catch(t){throw new wn("Illegal input: "+t.message)}}var qn;function Hn(t,e){t.parentNode&&(t.parentNode.insertBefore(e,t),t.parentNode.removeChild(t))}function Gn(t){return An.f.document.createElement(t)}function jn(t,e){if(!e)return[!1,""];const r=t.match(/^<([^> ]+).*>/),n=e.match(/^<\/([^>]+)>(.*)/);return r&&n&&r[1]===n[1]?[!0,n[2]]:[!1,""]}function Xn(t){return t.tagName.toUpperCase()}function Vn(t){return(new An.f.xmldom.XMLSerializer).serializeToString(t)}!function(t){t[t.ELEMENT_NODE=1]="ELEMENT_NODE",t[t.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",t[t.TEXT_NODE=3]="TEXT_NODE",t[t.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",t[t.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",t[t.ENTITY_NODE=6]="ENTITY_NODE",t[t.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",t[t.COMMENT_NODE=8]="COMMENT_NODE",t[t.DOCUMENT_NODE=9]="DOCUMENT_NODE",t[t.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",t[t.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE",t[t.NOTATION_NODE=12]="NOTATION_NODE"}(qn||(qn={}));const Wn=new Map,Kn=new Map;var zn,Yn,$n;function Jn(t){const e=t.toString(16).toUpperCase();return e.length>3?e:("000"+e).slice(-4)}function Qn([t,e],r){const n=parseInt(t,16),s=parseInt(e,16),i=[];for(let t=n;t<=s;t++){let e=Jn(t);!1!==r[e]&&(e=r[e]||e,i.push(e))}return i}function Zn(t,e={}){return Qn(t,e).map((t=>String.fromCodePoint(parseInt(t,16))))}!function(t){t.BOLD="bold",t.BOLDFRAKTUR="bold-fraktur",t.BOLDITALIC="bold-italic",t.BOLDSCRIPT="bold-script",t.DOUBLESTRUCK="double-struck",t.DOUBLESTRUCKITALIC="double-struck-italic",t.FULLWIDTH="fullwidth",t.FRAKTUR="fraktur",t.ITALIC="italic",t.MONOSPACE="monospace",t.NORMAL="normal",t.SCRIPT="script",t.SANSSERIF="sans-serif",t.SANSSERIFITALIC="sans-serif-italic",t.SANSSERIFBOLD="sans-serif-bold",t.SANSSERIFBOLDITALIC="sans-serif-bold-italic"}(zn||(zn={})),function(t){t.SUPER="super",t.SUB="sub",t.CIRCLED="circled",t.PARENTHESIZED="parenthesized",t.PERIOD="period",t.NEGATIVECIRCLED="negative-circled",t.DOUBLECIRCLED="double-circled",t.CIRCLEDSANSSERIF="circled-sans-serif",t.NEGATIVECIRCLEDSANSSERIF="negative-circled-sans-serif",t.COMMA="comma",t.SQUARED="squared",t.NEGATIVESQUARED="negative-squared"}(Yn||(Yn={})),function(t){t.LATINCAP="latinCap",t.LATINSMALL="latinSmall",t.GREEKCAP="greekCap",t.GREEKSMALL="greekSmall",t.DIGIT="digit"}($n||($n={}));const ts=[{interval:["1D400","1D419"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLD},{interval:["1D41A","1D433"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLD},{interval:["1D56C","1D585"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLDFRAKTUR},{interval:["1D586","1D59F"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLDFRAKTUR},{interval:["1D468","1D481"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLDITALIC},{interval:["1D482","1D49B"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLDITALIC},{interval:["1D4D0","1D4E9"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLDSCRIPT},{interval:["1D4EA","1D503"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLDSCRIPT},{interval:["1D538","1D551"],base:$n.LATINCAP,subst:{"1D53A":"2102","1D53F":"210D","1D545":"2115","1D547":"2119","1D548":"211A","1D549":"211D","1D551":"2124"},capital:!0,category:"Lu",font:zn.DOUBLESTRUCK},{interval:["1D552","1D56B"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.DOUBLESTRUCK},{interval:["1D504","1D51D"],base:$n.LATINCAP,subst:{"1D506":"212D","1D50B":"210C","1D50C":"2111","1D515":"211C","1D51D":"2128"},capital:!0,category:"Lu",font:zn.FRAKTUR},{interval:["1D51E","1D537"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.FRAKTUR},{interval:["FF21","FF3A"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.FULLWIDTH},{interval:["FF41","FF5A"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.FULLWIDTH},{interval:["1D434","1D44D"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.ITALIC},{interval:["1D44E","1D467"],base:$n.LATINSMALL,subst:{"1D455":"210E"},capital:!1,category:"Ll",font:zn.ITALIC},{interval:["1D670","1D689"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.MONOSPACE},{interval:["1D68A","1D6A3"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.MONOSPACE},{interval:["0041","005A"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.NORMAL},{interval:["0061","007A"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.NORMAL},{interval:["1D49C","1D4B5"],base:$n.LATINCAP,subst:{"1D49D":"212C","1D4A0":"2130","1D4A1":"2131","1D4A3":"210B","1D4A4":"2110","1D4A7":"2112","1D4A8":"2133","1D4AD":"211B"},capital:!0,category:"Lu",font:zn.SCRIPT},{interval:["1D4B6","1D4CF"],base:$n.LATINSMALL,subst:{"1D4BA":"212F","1D4BC":"210A","1D4C4":"2134"},capital:!1,category:"Ll",font:zn.SCRIPT},{interval:["1D5A0","1D5B9"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIF},{interval:["1D5BA","1D5D3"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIF},{interval:["1D608","1D621"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIFITALIC},{interval:["1D622","1D63B"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIFITALIC},{interval:["1D5D4","1D5ED"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIFBOLD},{interval:["1D5EE","1D607"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIFBOLD},{interval:["1D63C","1D655"],base:$n.LATINCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIFBOLDITALIC},{interval:["1D656","1D66F"],base:$n.LATINSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIFBOLDITALIC},{interval:["0391","03A9"],base:$n.GREEKCAP,subst:{"03A2":"03F4"},capital:!0,category:"Lu",font:zn.NORMAL},{interval:["03B0","03D0"],base:$n.GREEKSMALL,subst:{"03B0":"2207","03CA":"2202","03CB":"03F5","03CC":"03D1","03CD":"03F0","03CE":"03D5","03CF":"03F1","03D0":"03D6"},capital:!1,category:"Ll",font:zn.NORMAL},{interval:["1D6A8","1D6C0"],base:$n.GREEKCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLD},{interval:["1D6C1","1D6E1"],base:$n.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLD},{interval:["1D6E2","1D6FA"],base:$n.GREEKCAP,subst:{},capital:!0,category:"Lu",font:zn.ITALIC},{interval:["1D6FB","1D71B"],base:$n.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:zn.ITALIC},{interval:["1D71C","1D734"],base:$n.GREEKCAP,subst:{},capital:!0,category:"Lu",font:zn.BOLDITALIC},{interval:["1D735","1D755"],base:$n.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:zn.BOLDITALIC},{interval:["1D756","1D76E"],base:$n.GREEKCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIFBOLD},{interval:["1D76F","1D78F"],base:$n.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIFBOLD},{interval:["1D790","1D7A8"],base:$n.GREEKCAP,subst:{},capital:!0,category:"Lu",font:zn.SANSSERIFBOLDITALIC},{interval:["1D7A9","1D7C9"],base:$n.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:zn.SANSSERIFBOLDITALIC},{interval:["0030","0039"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.NORMAL},{interval:["2070","2079"],base:$n.DIGIT,subst:{2071:"00B9",2072:"00B2",2073:"00B3"},offset:0,category:"No",font:Yn.SUPER},{interval:["2080","2089"],base:$n.DIGIT,subst:{},offset:0,category:"No",font:Yn.SUB},{interval:["245F","2473"],base:$n.DIGIT,subst:{"245F":"24EA"},offset:0,category:"No",font:Yn.CIRCLED},{interval:["3251","325F"],base:$n.DIGIT,subst:{},offset:21,category:"No",font:Yn.CIRCLED},{interval:["32B1","32BF"],base:$n.DIGIT,subst:{},offset:36,category:"No",font:Yn.CIRCLED},{interval:["2474","2487"],base:$n.DIGIT,subst:{},offset:1,category:"No",font:Yn.PARENTHESIZED},{interval:["2487","249B"],base:$n.DIGIT,subst:{2487:"1F100"},offset:0,category:"No",font:Yn.PERIOD},{interval:["2775","277F"],base:$n.DIGIT,subst:{2775:"24FF"},offset:0,category:"No",font:Yn.NEGATIVECIRCLED},{interval:["24EB","24F4"],base:$n.DIGIT,subst:{},offset:11,category:"No",font:Yn.NEGATIVECIRCLED},{interval:["24F5","24FE"],base:$n.DIGIT,subst:{},offset:1,category:"No",font:Yn.DOUBLECIRCLED},{interval:["277F","2789"],base:$n.DIGIT,subst:{"277F":"1F10B"},offset:0,category:"No",font:Yn.CIRCLEDSANSSERIF},{interval:["2789","2793"],base:$n.DIGIT,subst:{2789:"1F10C"},offset:0,category:"No",font:Yn.NEGATIVECIRCLEDSANSSERIF},{interval:["FF10","FF19"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.FULLWIDTH},{interval:["1D7CE","1D7D7"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.BOLD},{interval:["1D7D8","1D7E1"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.DOUBLESTRUCK},{interval:["1D7E2","1D7EB"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.SANSSERIF},{interval:["1D7EC","1D7F5"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.SANSSERIFBOLD},{interval:["1D7F6","1D7FF"],base:$n.DIGIT,subst:{},offset:0,category:"Nd",font:zn.MONOSPACE},{interval:["1F101","1F10A"],base:$n.DIGIT,subst:{},offset:0,category:"No",font:Yn.COMMA},{interval:["24B6","24CF"],base:$n.LATINCAP,subst:{},capital:!0,category:"So",font:Yn.CIRCLED},{interval:["24D0","24E9"],base:$n.LATINSMALL,subst:{},capital:!1,category:"So",font:Yn.CIRCLED},{interval:["1F110","1F129"],base:$n.LATINCAP,subst:{},capital:!0,category:"So",font:Yn.PARENTHESIZED},{interval:["249C","24B5"],base:$n.LATINSMALL,subst:{},capital:!1,category:"So",font:Yn.PARENTHESIZED},{interval:["1F130","1F149"],base:$n.LATINCAP,subst:{},capital:!0,category:"So",font:Yn.SQUARED},{interval:["1F170","1F189"],base:$n.LATINCAP,subst:{},capital:!0,category:"So",font:Yn.NEGATIVESQUARED},{interval:["1F150","1F169"],base:$n.LATINCAP,subst:{},capital:!0,category:"So",font:Yn.NEGATIVECIRCLED}],es=new Map;function rs(t,e){return t+e.split("-").map((t=>t[0].toUpperCase()+t.slice(1))).join("")}for(const t of ts){const e=rs(t.base,t.font),r=Zn(t.interval,t.subst);let n=es.get(e);n?n.unicode=n.unicode.concat(r):(n=t,n.unicode=r,es.set(e,n))}var ns;!function(t){t.PUNCTUATION="punctuation",t.FENCE="fence",t.NUMBER="number",t.IDENTIFIER="identifier",t.TEXT="text",t.OPERATOR="operator",t.RELATION="relation",t.LARGEOP="largeop",t.FUNCTION="function",t.ACCENT="accent",t.FENCED="fenced",t.FRACTION="fraction",t.PUNCTUATED="punctuated",t.RELSEQ="relseq",t.MULTIREL="multirel",t.INFIXOP="infixop",t.PREFIXOP="prefixop",t.POSTFIXOP="postfixop",t.APPL="appl",t.INTEGRAL="integral",t.BIGOP="bigop",t.SQRT="sqrt",t.ROOT="root",t.LIMUPPER="limupper",t.LIMLOWER="limlower",t.LIMBOTH="limboth",t.SUBSCRIPT="subscript",t.SUPERSCRIPT="superscript",t.UNDERSCORE="underscore",t.OVERSCORE="overscore",t.TENSOR="tensor",t.TABLE="table",t.MULTILINE="multiline",t.MATRIX="matrix",t.VECTOR="vector",t.CASES="cases",t.ROW="row",t.LINE="line",t.CELL="cell",t.ENCLOSE="enclose",t.INFERENCE="inference",t.RULELABEL="rulelabel",t.CONCLUSION="conclusion",t.PREMISES="premises",t.UNKNOWN="unknown",t.EMPTY="empty"}(ns||(ns={}));const ss=Object.assign({},ns);var is;!function(t){t.COMMA="comma",t.SEMICOLON="semicolon",t.ELLIPSIS="ellipsis",t.FULLSTOP="fullstop",t.QUESTION="question",t.EXCLAMATION="exclamation",t.QUOTES="quotes",t.DASH="dash",t.TILDE="tilde",t.PRIME="prime",t.DEGREE="degree",t.VBAR="vbar",t.COLON="colon",t.OPENFENCE="openfence",t.CLOSEFENCE="closefence",t.APPLICATION="application",t.DUMMY="dummy",t.UNIT="unit",t.LABEL="label",t.OPEN="open",t.CLOSE="close",t.TOP="top",t.BOTTOM="bottom",t.NEUTRAL="neutral",t.METRIC="metric",t.LATINLETTER="latinletter",t.GREEKLETTER="greekletter",t.OTHERLETTER="otherletter",t.NUMBERSET="numbersetletter",t.INTEGER="integer",t.FLOAT="float",t.OTHERNUMBER="othernumber",t.INFTY="infty",t.MIXED="mixed",t.MULTIACCENT="multiaccent",t.OVERACCENT="overaccent",t.UNDERACCENT="underaccent",t.UNDEROVER="underover",t.SUBSUP="subsup",t.LEFTSUB="leftsub",t.LEFTSUPER="leftsuper",t.RIGHTSUB="rightsub",t.RIGHTSUPER="rightsuper",t.LEFTRIGHT="leftright",t.ABOVEBELOW="abovebelow",t.SETEMPTY="set empty",t.SETEXT="set extended",t.SETSINGLE="set singleton",t.SETCOLLECT="set collection",t.STRING="string",t.SPACE="space",t.ANNOTATION="annotation",t.TEXT="text",t.SEQUENCE="sequence",t.ENDPUNCT="endpunct",t.STARTPUNCT="startpunct",t.NEGATIVE="negative",t.POSITIVE="positive",t.NEGATION="negation",t.MULTIOP="multiop",t.PREFIXOP="prefix operator",t.POSTFIXOP="postfix operator",t.LIMFUNC="limit function",t.INFIXFUNC="infix function",t.PREFIXFUNC="prefix function",t.POSTFIXFUNC="postfix function",t.SIMPLEFUNC="simple function",t.COMPFUNC="composed function",t.SUM="sum",t.INTEGRAL="integral",t.GEOMETRY="geometry",t.BOX="box",t.BLOCK="block",t.ADDITION="addition",t.MULTIPLICATION="multiplication",t.SUBTRACTION="subtraction",t.IMPLICIT="implicit",t.DIVISION="division",t.VULGAR="vulgar",t.EQUALITY="equality",t.INEQUALITY="inequality",t.ARROW="arrow",t.ELEMENT="element",t.NONELEMENT="nonelement",t.REELEMENT="reelement",t.RENONELEMENT="renonelement",t.SET="set",t.DETERMINANT="determinant",t.ROWVECTOR="rowvector",t.BINOMIAL="binomial",t.SQUAREMATRIX="squarematrix",t.CYCLE="cycle",t.MULTILINE="multiline",t.MATRIX="matrix",t.VECTOR="vector",t.CASES="cases",t.TABLE="table",t.CAYLEY="cayley",t.PROOF="proof",t.LEFT="left",t.RIGHT="right",t.UP="up",t.DOWN="down",t.FINAL="final",t.SINGLE="single",t.HYP="hyp",t.AXIOM="axiom",t.LOGIC="logic",t.UNKNOWN="unknown",t.MGLYPH="mglyph"}(is||(is={}));const os=Object.assign({},is);var as;!function(t){t.CALIGRAPHIC="caligraphic",t.CALIGRAPHICBOLD="caligraphic-bold",t.OLDSTYLE="oldstyle",t.OLDSTYLEBOLD="oldstyle-bold",t.UNKNOWN="unknown"}(as||(as={}));const cs=Object.assign(Object.assign(Object.assign({},zn),as),Yn);var ls;!function(t){t.ALLLETTERS="allLetters",t.D="d",t.BAR="bar",t.TILDE="tilde"}(ls||(ls={}));const hs=Object.assign(Object.assign({},$n),ls);var us=function(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(n=Object.getOwnPropertySymbols(t);s<n.length;s++)e.indexOf(n[s])<0&&Object.prototype.propertyIsEnumerable.call(t,n[s])&&(r[n[s]]=t[n[s]])}return r};const ds={functionApplication:String.fromCodePoint(8289),invisibleTimes:String.fromCodePoint(8290),invisibleComma:String.fromCodePoint(8291),invisiblePlus:String.fromCodePoint(8292)};class ps extends Map{get(t){return super.get(t)||{role:os.UNKNOWN,type:ss.UNKNOWN,font:cs.UNKNOWN}}}class ms extends Map{set(t,e,r=""){return super.set(this.secKey(e,t),r||e),this}has(t,e){return super.has(this.secKey(e,t))}get(t,e){return super.get(this.secKey(e,t))}secKey(t,e){return e?`${t} ${e}`:`${t}`}}const fs={Meaning:new ps,Secondary:new ms,FencesHoriz:new Map,FencesVert:new Map,LatexCommands:new Map};function gs(t,e){for(const r of t)fs.Meaning.set(r,{role:e.role||os.UNKNOWN,type:e.type||ss.UNKNOWN,font:e.font||cs.UNKNOWN}),e.secondary&&fs.Secondary.set(r,e.secondary)}function Es(t,e,r=1){const n={},s=function(t){let e=[];for(const r of t)Array.isArray(r)?e=e.concat(Qn(r,{}).map((t=>parseInt(t,16)))):e.push(parseInt(r,16));return e}(e);for(const e of s)n[e]||(t.set(String.fromCodePoint(e),String.fromCodePoint(e+r)),n[e]=!0,n[e+r]=!0)}const Ns=["cos","cot","csc","sec","sin","tan","arccos","arccot","arccsc","arcsec","arcsin","arctan"].concat(["cosh","coth","csch","sech","sinh","tanh","arcosh","arcoth","arcsch","arsech","arsinh","artanh"],["deg","det","dim","hom","ker","Tr"],["log","ln","lg","exp","gcd","lcm","arg","im","re","Pr"]);function bs(t,e,r,n,s,i=[],o={},a={}){const c=es.get(rs(t,n));c&&(c.unicode.forEach((t=>{fs.Meaning.set(t,{type:e,role:r,font:s}),i.forEach((e=>fs.Secondary.set(t,e)))})),function(t,e){for(const[r,n]of Object.entries(e)){const e=t[r];void 0!==e&&fs.Meaning.set(e,n)}}(c.unicode,o),function(t,e){for(const[r,n]of Object.entries(e)){const e=t[r];void 0!==e&&fs.Secondary.set(e,n)}}(c.unicode,a))}[{set:["23","26","40","5c","a1","a7","b6","bf","2017",["2022","2025"],"2027","203b","203c",["2041","2043"],["2047","2049"],["204b","204d"],"2050","2055","2056",["2058","205e"],"2234","2235","fe45","fe46","fe5f","fe60","fe68","fe6b","ff03","ff06","ff0f","ff20","ff3c"],type:ss.PUNCTUATION,role:os.UNKNOWN},{set:["22","ab","bb","2dd",["2018","201f"],"2039","203a",["301d","301f"],"fe10","ff02","ff07"],type:ss.PUNCTUATION,role:os.QUOTES},{set:["3b","204f","2a1f","2a3e","fe14","fe54","ff1b"],type:ss.PUNCTUATION,role:os.SEMICOLON},{set:["3f","203d","fe16","fe56","ff1f"],type:ss.PUNCTUATION,role:os.QUESTION},{set:["21","fe15","fe57","ff01"],type:ss.PUNCTUATION,role:os.EXCLAMATION},{set:["5e","60","a8","aa","b4","ba","2c7",["2d8","2da"],"2040","207a","207d","207e","ff3e","ff40"],type:ss.PUNCTUATION,role:os.OVERACCENT},{set:["b8","2db","2038","203f","2054","208a","208d","208e"],type:ss.PUNCTUATION,role:os.UNDERACCENT},{set:["3a","2982","fe13","fe30","fe55","ff1a"],type:ss.PUNCTUATION,role:os.COLON},{set:["2c","2063","fe50","ff0c"],type:ss.PUNCTUATION,role:os.COMMA},{set:["2026",["22ee","22f1"],"fe19"],type:ss.PUNCTUATION,role:os.ELLIPSIS},{set:["2e","fe52","ff0e"],type:ss.PUNCTUATION,role:os.FULLSTOP},{set:["2d","207b","208b","2212","2796","fe63","ff0d"],type:ss.OPERATOR,role:os.DASH,secondary:hs.BAR},{set:["5f","af",["2010","2015"],"203e","208b",["fe49","fe4f"],"fe58","ff3f","ffe3"],type:ss.PUNCTUATION,role:os.DASH,secondary:hs.BAR},{set:["7e","2dc","2f7","303","330","334","2053","223c","223d","301c","ff5e"],type:ss.OPERATOR,role:os.TILDE,secondary:hs.TILDE},{set:["27","2b9","2ba",["2032","2037"],"2057"],type:ss.PUNCTUATION,role:os.PRIME},{set:["b0"],type:ss.PUNCTUATION,role:os.DEGREE},{set:["2b","b1","2064","2213","2214","2228","222a",["228c","228e"],"2294","2295","229d","229e","22bb","22bd","22c4","22ce","22d3","2304","271b","271c","2795","27cf","29fa","29fb","29fe",["2a22","2a28"],"2a2d","2a2e","2a39","2a42","2a45","2a46","2a48","2a4a","2a4c","2a4f","2a50","2a52","2a54","2a56","2a57","2a59","2a5b","2a5d",["2a61","2a63"],"2adc","2add","fe62","ff0b"],type:ss.OPERATOR,role:os.ADDITION},{set:["2a","b7","d7","2020","2021","204e","2051","2062",["2217","2219"],"2227","2229","2240","2293","2297",["2299","229b"],"22a0","22a1","22b9","22bc",["22c5","22cc"],"22cf","22d2","22d4","2303","2305","2306","25cb","2715","2716","27ce","27d1",["29d1","29d7"],"29e2","2a1d",["2a2f","2a37"],["2a3b","2a3d"],"2a40","2a43","2a44","2a47","2a49","2a4b","2a4d","2a4e","2a51","2a53","2a55","2a58","2a5a","2a5c",["2a5e","2a60"],"2ada","2adb","fe61","ff0a"],type:ss.OPERATOR,role:os.MULTIPLICATION},{set:["2d","af","2010","2011","2052","207b","208b","2212","2216","2238","2242","2296","229f","2796","29ff",["2a29","2a2c"],"2a3a","2a41","fe63","ff0d"],type:ss.OPERATOR,role:os.SUBTRACTION},{set:["2f","f7","2044","2215","2298","2797","27cc","29bc",["29f5","29f9"],"2a38"],type:ss.OPERATOR,role:os.DIVISION},{set:["25","2030","2031","ff05","fe6a"],type:ss.OPERATOR,role:os.POSTFIXOP},{set:["ac","2200","2201","2203","2204","2206",["221a","221c"],"2310","ffe2"],type:ss.OPERATOR,role:os.PREFIXOP},{set:["2320","2321","23aa","23ae","23af","23b2","23b3","23b6","23b7"],type:ss.OPERATOR,role:os.PREFIXOP},{set:["1d7ca","1d7cb"],type:ss.OPERATOR,role:os.PREFIXOP,font:cs.BOLD},{set:["3d","7e","207c","208c","221d","2237",["223a","223f"],"2243","2245","2248",["224a","224e"],["2251","225f"],"2261","2263","229c","22cd","22d5","29e4","29e6","2a66","2a67",["2a6a","2a6c"],["2a6c","2a78"],"fe66","ff1d"],type:ss.RELATION,role:os.EQUALITY},{set:["3c","3e","2241","2242","2244","2246","2247","2249","224f","2250","2260","2262",["2264","2281"],"22b0","22b1",["22d6","22e1"],["22e6","22e9"],["2976","2978"],"29c0","29c1","29e1","29e3","29e5",["2a79","2abc"],["2af7","2afa"],"fe64","fe65","ff1c","ff1e"],type:ss.RELATION,role:os.INEQUALITY},{set:[["2282","228b"],["228f","2292"],["22b2","22b8"],"22d0","22d1",["22e2","22e5"],["22ea","22ed"],"27c3","27c4",["27c7","27c9"],["27d5","27d7"],"27dc",["2979","297b"],"29df",["2abd","2ad8"]],type:ss.RELATION,role:os.SET},{set:["2236",["27e0","27e5"],"292b","292c",["29b5","29bb"],"29be","29bf",["29c2","29d0"]],type:ss.RELATION,role:os.UNKNOWN},{set:["2205",["29b0","29b4"]],type:ss.IDENTIFIER,role:os.SETEMPTY},{set:["1ab2","221e",["29dc","29de"]],type:ss.IDENTIFIER,role:os.INFTY},{set:["22a2","22a3",["22a6","22af"],"27da","27db","27dd","27de","2ade",["2ae2","2ae6"],"2aec","2aed"],type:ss.RELATION,role:os.LOGIC},{set:["22a4","22a5","22ba","27d8","27d9","27df","2adf","2ae0",["2ae7","2aeb"],"2af1"],type:ss.IDENTIFIER,role:os.LOGIC},{set:[["2190","21ff"],"2301","2324","238b","2794",["2798","27af"],["27b1","27be"],["27f0","27ff"],["2900","292a"],["292d","2975"],["297c","297f"],["2b00","2b11"],["2b30","2b4c"],["ffe9","ffec"]],type:ss.RELATION,role:os.ARROW},{set:["2208","220a",["22f2","22f9"],"22ff","27d2","2ad9"],type:ss.OPERATOR,role:os.ELEMENT},{set:["2209"],type:ss.OPERATOR,role:os.NONELEMENT},{set:["220b","220d",["22fa","22fe"]],type:ss.OPERATOR,role:os.REELEMENT},{set:["220c"],type:ss.OPERATOR,role:os.RENONELEMENT},{set:[["220f","2211"],["22c0","22c3"],["2a00","2a0b"],"2a3f","2afc","2aff"],type:ss.LARGEOP,role:os.SUM},{set:["2140"],type:ss.LARGEOP,role:os.SUM,font:cs.DOUBLESTRUCK},{set:[["222b","2233"],["2a0c","2a17"],["2a17","2a1c"]],type:ss.LARGEOP,role:os.INTEGRAL},{set:[["2500","257F"]],type:ss.RELATION,role:os.BOX},{set:[["2580","259F"]],type:ss.IDENTIFIER,role:os.BLOCK},{set:[["25A0","25FF"],["2B12","2B2F"],["2B50","2B59"]],type:ss.RELATION,role:os.GEOMETRY},{set:["220e","2300","2302","2311","29bd","29e0",["29e8","29f3"],"2a1e","2afe","ffed","ffee"],type:ss.OPERATOR,role:os.GEOMETRY},{set:[["221f","2222"],"22be","22bf",["2312","2314"],"237c","27c0",["299b","29af"]],type:ss.OPERATOR,role:os.GEOMETRY},{set:["24",["a2","a5"],"b5","2123",["2125","2127"],"212a","212b","fe69","ff04","ffe0","ffe1","ffe5","ffe6"],type:ss.IDENTIFIER,role:os.UNKNOWN},{set:["a9","ae","210f","2114","2116","2117",["211e","2122"],"212e","2132",["2139","213b"],["2141","2144"],"214d","214e",["1f12a","1f12c"],"1f18a"],type:ss.IDENTIFIER,role:os.OTHERLETTER},{set:["2224","2226","2239","2307","27b0","27bf","27c1","27c2","27ca","27cb","27cd","27d0","27d3","27d4","2981","2999","299a","29e7","29f4","2a20","2a21","2a64","2a65","2a68","2a69","2ae1",["2aee","2af0"],"2af2","2af3","2af5","2af6","2afb","2afd"],type:ss.OPERATOR,role:os.UNKNOWN},{set:["2605","2606","26aa","26ab",["2720","274d"]],type:ss.OPERATOR,role:os.UNKNOWN},{set:[["2145","2149"]],type:ss.IDENTIFIER,role:os.LATINLETTER,font:cs.DOUBLESTRUCKITALIC,secondary:hs.ALLLETTERS},{set:[["213c","213f"]],type:ss.IDENTIFIER,role:os.GREEKLETTER,font:cs.DOUBLESTRUCK,secondary:hs.ALLLETTERS},{set:["3d0","3d7","3f6",["1d26","1d2a"],"1d5e","1d60",["1d66","1d6a"]],type:ss.IDENTIFIER,role:os.GREEKLETTER,font:cs.NORMAL,secondary:hs.ALLLETTERS},{set:[["2135","2138"]],type:ss.IDENTIFIER,role:os.OTHERLETTER,font:cs.NORMAL,secondary:hs.ALLLETTERS},{set:["131","237"],type:ss.IDENTIFIER,role:os.LATINLETTER,font:cs.NORMAL},{set:["1d6a4","1d6a5"],type:ss.IDENTIFIER,role:os.LATINLETTER,font:cs.ITALIC},{set:["2113","2118"],type:ss.IDENTIFIER,role:os.LATINLETTER,font:cs.SCRIPT},{set:[["c0","d6"],["d8","f6"],["f8","1bf"],["1c4","2af"],["1d00","1d25"],["1d6b","1d9a"],["1e00","1ef9"],["363","36f"],["1dd3","1de6"],["1d62","1d65"],"1dca","2071","207f",["2090","209c"],"2c7c"],type:ss.IDENTIFIER,role:os.LATINLETTER,font:cs.NORMAL},{set:[["00bc","00be"],["2150","215f"],"2189"],type:ss.NUMBER,role:os.FLOAT},{set:["23E8",["3248","324f"]],type:ss.NUMBER,role:os.INTEGER},{set:[["214A","214C"],"2705","2713","2714","2717","2718"],type:ss.IDENTIFIER,role:os.UNKNOWN},{set:["20","a0","ad",["2000","200f"],["2028","202f"],["205f","2060"],"206a","206b","206e","206f","feff",["fff9","fffb"]],type:ss.TEXT,role:os.SPACE},{set:["7c","a6","2223","23b8","23b9","23d0","2758",["fe31","fe34"],"ff5c","ffe4","ffe8"],type:ss.FENCE,role:os.NEUTRAL},{set:["2016","2225","2980","2af4"],type:ss.FENCE,role:os.METRIC}].forEach((t=>{var{set:e}=t,r=us(t,["set"]);return gs(function(t){let e=[];for(const r of t)Array.isArray(r)?e=e.concat(Zn(r)):e.push(String.fromCodePoint(parseInt(r,16)));return e}(e),r)})),Es(fs.FencesVert,["23b4",["23dc","23e1"],["fe35","fe44"],"fe47"]),Es(fs.FencesHoriz,["28","2045",["2308","230f"],["231c","231f"],"2329","23b0",["2768","2775"],"27c5",["27e6","27ef"],["2983","2998"],["29d8","29db"],"29fc",["2e22","2e29"],["3008","3011"],["3014","301b"],"fd3e","fe17",["fe59","fe5e"],"ff08","ff5f","ff62"]),Es(fs.FencesHoriz,["5b","7b","ff3b","ff5b"],2),Es(fs.FencesHoriz,[["239b","23a6"]],3),Es(fs.FencesHoriz,[["23a7","23a9"]],4),gs([...fs.FencesHoriz.keys()],{type:ss.FENCE,role:os.OPEN}),gs([...fs.FencesHoriz.values()],{type:ss.FENCE,role:os.CLOSE}),gs([...fs.FencesVert.keys()],{type:ss.FENCE,role:os.TOP}),gs([...fs.FencesVert.values()],{type:ss.FENCE,role:os.BOTTOM}),function(){for(const[t,e]of Object.entries(cs)){const r=!!Yn[t]?cs.UNKNOWN:e===cs.FULLWIDTH?cs.NORMAL:e;bs($n.LATINCAP,ss.IDENTIFIER,os.LATINLETTER,e,r,[hs.ALLLETTERS]),bs($n.LATINSMALL,ss.IDENTIFIER,os.LATINLETTER,e,r,[hs.ALLLETTERS],{},{3:hs.D}),bs($n.GREEKCAP,ss.IDENTIFIER,os.GREEKLETTER,e,r,[hs.ALLLETTERS]),bs($n.GREEKSMALL,ss.IDENTIFIER,os.GREEKLETTER,e,r,[hs.ALLLETTERS],{0:{type:ss.OPERATOR,role:os.PREFIXOP,font:r},26:{type:ss.OPERATOR,role:os.PREFIXOP,font:r}}),bs($n.DIGIT,ss.NUMBER,os.INTEGER,e,r)}}(),gs(["inf","lim","liminf","limsup","max","min","sup","injlim","projlim"],{type:ss.FUNCTION,role:os.LIMFUNC}),gs(Ns,{type:ss.FUNCTION,role:os.PREFIXFUNC}),gs(["mod","rem"],{type:ss.OPERATOR,role:os.PREFIXFUNC});const Ts=[];function ys(t,e){for(let r,n=0;r=Ts[n];n++){const n=r.compare(t,e);if(0!==n)return n}return 0}function Is(t){if(t.length<=1)return t;const e=t.slice();!function(t){t.sort(ys)}(e);const r=[];let n;do{n=e.pop(),r.push(n)}while(n&&e.length&&0===ys(e[e.length-1],n));return r}function As(t,e){return t.match(/^.+:.+$/)||!e?t:t+":"+e}new class{constructor(t,e=null){this.comparator=t,this.type=e,function(t){Ts.push(t)}(this)}compare(t,e){return this.type&&this.type===t.type&&this.type===e.type?this.comparator(t,e):0}}((function(t,e){return t.role===os.SIMPLEFUNC?1:e.role===os.SIMPLEFUNC?-1:0}),ss.IDENTIFIER);class Rs extends Map{set(t,e){return super.set(As(t,e.font),e),this}setNode(t){this.set(t.textContent,t.meaning())}get(t,e=null){return super.get(As(t,e))}getNode(t){return this.get(t.textContent,t.font)}}class Ls extends Map{add(t,e){const r=this.get(t);r?r.push(e):super.set(t,[e])}get(t,e=null){return super.get(As(t,e))}getNode(t){return this.get(t.textContent,t.font)}minimize(){for(const[t,e]of this)1===e.length&&this.delete(t)}isMultiValued(){for(const t of this.values())if(t.length>1)return!0;return!1}}class Cs extends Ls{add(t,e){super.add(As(t,e.font),e)}addNode(t){this.add(t.textContent,t)}toString(){const t=[];for(const[e,r]of this){const n=Array(e.length+3).join(" "),s=r.map((t=>t.toString())).join("\n"+n);t.push(e+": "+s)}return t.join("\n")}collateMeaning(){const t=new Os;for(const[e,r]of this)t.set(e,r.map((t=>t.meaning())));return t}}class Os extends Ls{add(t,e){const r=this.get(t,e.font);r&&r.find((function(t){return n=e,(r=t).type===n.type&&r.role===n.role&&r.font===n.font;var r,n}))||super.add(As(t,e.font),e)}addNode(t){this.add(t.textContent,t.meaning())}toString(){const t=[];for(const[e,r]of this){const n=Array(e.length+3).join(" "),s=r.map((t=>`{type: ${t.type}, role: ${t.role}, font: ${t.font}}`)).join("\n"+n);t.push(e+": "+s)}return t.join("\n")}reduce(){for(const[t,e]of this)1!==e.length&&this.set(t,Is(e))}default(){const t=new Rs;for(const[e,r]of this)1===r.length&&t.set(e,r[0]);return t}newDefault(){const t=this.default();this.reduce();const e=this.default();return t.size!==e.size?e:null}}var vs;!function(t){t.ANNOTATION="ANNOTATION",t.ANNOTATIONXML="ANNOTATION-XML",t.MACTION="MACTION",t.MALIGNGROUP="MALIGNGROUP",t.MALIGNMARK="MALIGNMARK",t.MATH="MATH",t.MENCLOSE="MENCLOSE",t.MERROR="MERROR",t.MFENCED="MFENCED",t.MFRAC="MFRAC",t.MGLYPH="MGLYPH",t.MI="MI",t.MLABELEDTR="MLABELEDTR",t.MMULTISCRIPTS="MMULTISCRIPTS",t.MN="MN",t.MO="MO",t.MOVER="MOVER",t.MPADDED="MPADDED",t.MPHANTOM="MPHANTOM",t.MPRESCRIPTS="MPRESCRIPTS",t.MROOT="MROOT",t.MROW="MROW",t.MS="MS",t.MSPACE="MSPACE",t.MSQRT="MSQRT",t.MSTYLE="MSTYLE",t.MSUB="MSUB",t.MSUBSUP="MSUBSUP",t.MSUP="MSUP",t.MTABLE="MTABLE",t.MTD="MTD",t.MTEXT="MTEXT",t.MTR="MTR",t.MUNDER="MUNDER",t.MUNDEROVER="MUNDEROVER",t.NONE="NONE",t.SEMANTICS="SEMANTICS"}(vs||(vs={}));const Ss=Object.values(vs),ws=[vs.MO,vs.MI,vs.MN,vs.MTEXT,vs.MS,vs.MSPACE],Ms=[vs.MERROR,vs.MPHANTOM,vs.MALIGNGROUP,vs.MALIGNMARK,vs.MPRESCRIPTS,vs.ANNOTATION,vs.ANNOTATIONXML],xs=[vs.MATH,vs.MROW,vs.MPADDED,vs.MACTION,vs.NONE,vs.MSTYLE,vs.SEMANTICS],Ps=[vs.MROOT,vs.MSQRT],Ds=["aria-label","exact-speech","alt"];function ks(t){return!!t&&Xn(t)===vs.MATH}function _s(t){return!!t&&(Ms.includes(Xn(t))||!Ss.includes(Xn(t)))}function Fs(t){return!!t&&xs.includes(Xn(t))}function Bs(t){return!!t&&Xn(t)===vs.MGLYPH&&!function(t){return!!t&&ws.includes(Xn(t))}(t.parentNode)}function Us(t){const e=[];for(let r,n=0;r=t[n];n++){if(r.nodeType!==qn.ELEMENT_NODE)continue;const t=Xn(r);Ms.includes(t)||(xs.includes(t)&&0===r.childNodes.length||e.push(r))}return e}function qs(t,e){var r;if(null===(r=e.attributes)||void 0===r?void 0:r.length){const r=e.attributes;for(let e=r.length-1;e>=0;e--){const n=r[e].name;n.match(/^ext/)&&(t.attributes[n]=r[e].value,t.nobreaking=!0),Ds.includes(n)&&(t.attributes["ext-speech"]=r[e].value,t.nobreaking=!0),n.match(/texclass$/)&&(t.attributes.texclass=r[e].value),"data-latex"===n.toLowerCase()&&(t.attributes.latex=r[e].value),"href"===n&&(t.attributes.href=r[e].value,t.nobreaking=!0)}}}function Hs(t){return t&&t.embellished&&t.childNodes.length>0?Hs(t.childNodes[0]):t}function Gs(t,e,r){r&&t.reverse();const n=[];for(let s,i=0;s=t[i];i++){if(e(s))return r?{head:t.slice(i+1).reverse(),div:s,tail:n.reverse()}:{head:n,div:s,tail:t.slice(i+1)};n.push(s)}return r?{head:[],div:null,tail:n.reverse()}:{head:n,div:null,tail:[]}}function js(t,e){let r=t;const n=[],s=[];let i=null;do{i=Gs(r,e),s.push(i.head),n.push(i.div),r=i.tail}while(i.div);return n.pop(),{rel:n,comp:s}}var Xs;!function(t){t.EMBELLISHED="embellished",t.FENCEPOINTER="fencepointer",t.FONT="font",t.ID="id",t.ANNOTATION="annotation",t.ROLE="role",t.TYPE="type",t.CHILDREN="children",t.CONTENT="content",t.TEXT="$t"}(Xs||(Xs={}));class Vs{static fromXml(t){const e=parseInt(t.getAttribute("id"),10),r=new Vs(e);return r.type=t.tagName,Vs.setAttribute(r,t,"role"),Vs.setAttribute(r,t,"font"),Vs.setAttribute(r,t,"embellished"),Vs.setAttribute(r,t,"fencepointer","fencePointer"),t.getAttribute("annotation")&&r.parseAnnotation(t.getAttribute("annotation")),qs(r,t),Vs.processChildren(r,t),r}static setAttribute(t,e,r,n){n=n||r;const s=e.getAttribute(r);s&&(t[n]=s)}static processChildren(t,e){for(const r of Bn(e.childNodes)){if(r.nodeType===qn.TEXT_NODE){t.textContent=r.textContent;continue}const e=Bn(r.childNodes).map(Vs.fromXml);e.forEach((e=>e.parent=t)),"CONTENT"===Xn(r)?t.contentNodes=e:t.childNodes=e}}constructor(t){this.id=t,this.mathml=[],this.parent=null,this.type=ss.UNKNOWN,this.role=os.UNKNOWN,this.font=cs.UNKNOWN,this.embellished=null,this.fencePointer="",this.childNodes=[],this.textContent="",this.mathmlTree=null,this.contentNodes=[],this.annotation={},this.attributes={},this.nobreaking=!1}querySelectorAll(t){let e=[];for(let r,n=0;r=this.childNodes[n];n++)e=e.concat(r.querySelectorAll(t));for(let r,n=0;r=this.contentNodes[n];n++)e=e.concat(r.querySelectorAll(t));return t(this)&&e.unshift(this),e}xml(t,e){const r=function(r,n){const s=n.map((function(r){return r.xml(t,e)})),i=t.createElementNS("",r);for(let t,e=0;t=s[e];e++)i.appendChild(t);return i},n=t.createElementNS("",this.type);return e||this.xmlAttributes(n),n.textContent=this.textContent,this.contentNodes.length>0&&n.appendChild(r(Xs.CONTENT,this.contentNodes)),this.childNodes.length>0&&n.appendChild(r(Xs.CHILDREN,this.childNodes)),n}toString(t=!1){const e=Un("<snode/>");return Vn(this.xml(e.ownerDocument,t))}allAttributes(){const t=[];return t.push([Xs.ROLE,this.role]),this.font!==cs.UNKNOWN&&t.push([Xs.FONT,this.font]),Object.keys(this.annotation).length&&t.push([Xs.ANNOTATION,this.annotationXml()]),this.embellished&&t.push([Xs.EMBELLISHED,this.embellished]),this.fencePointer&&t.push([Xs.FENCEPOINTER,this.fencePointer]),t.push([Xs.ID,this.id.toString()]),t}annotationXml(){const t=[];for(const[e,r]of Object.entries(this.annotation))r.forEach((r=>t.push(e+":"+r)));return t.join(";")}attributesXml(){const t=[];for(const[e,r]of Object.entries(this.attributes))t.push(e+":"+Vs.escapeValue(r));return t.join(";")}toJson(){const t={};t[Xs.TYPE]=this.type;const e=this.allAttributes();for(let r,n=0;r=e[n];n++)t[r[0]]=r[1].toString();return this.textContent&&(t[Xs.TEXT]=this.textContent),this.childNodes.length&&(t[Xs.CHILDREN]=this.childNodes.map((function(t){return t.toJson()}))),this.contentNodes.length&&(t[Xs.CONTENT]=this.contentNodes.map((function(t){return t.toJson()}))),t}updateContent(t,e){const r=e?t.replace(/^[ \f\n\r\t\v\u200b]*/,"").replace(/[ \f\n\r\t\v\u200b]*$/,""):t.trim();if(t=t&&!r?t:r,this.textContent===t)return;const n=fs.Meaning.get(t.replace(/\s/g," "));this.textContent=t,this.role=n.role,this.type=n.type,this.font=n.font}addMathmlNodes(t){for(let e,r=0;e=t[r];r++)-1===this.mathml.indexOf(e)&&this.mathml.push(e)}appendChild(t){this.childNodes.push(t),this.addMathmlNodes(t.mathml),t.parent=this}replaceChild(t,e){const r=this.childNodes.indexOf(t);if(-1===r)return;t.parent=null,e.parent=this,this.childNodes[r]=e;const n=t.mathml.filter((function(t){return-1===e.mathml.indexOf(t)})),s=e.mathml.filter((function(e){return-1===t.mathml.indexOf(e)}));this.removeMathmlNodes(n),this.addMathmlNodes(s)}appendContentNode(t){t&&(this.contentNodes.push(t),this.addMathmlNodes(t.mathml),t.parent=this)}removeContentNode(t){if(t){const e=this.contentNodes.indexOf(t);-1!==e&&this.contentNodes.slice(e,1)}}equals(t){if(!t)return!1;if(this.type!==t.type||this.role!==t.role||this.textContent!==t.textContent||this.childNodes.length!==t.childNodes.length||this.contentNodes.length!==t.contentNodes.length)return!1;for(let e,r,n=0;e=this.childNodes[n],r=t.childNodes[n];n++)if(!e.equals(r))return!1;for(let e,r,n=0;e=this.contentNodes[n],r=t.contentNodes[n];n++)if(!e.equals(r))return!1;return!0}displayTree(){console.info(this.displayTree_(0))}addAnnotation(t,e){e&&this.addAnnotation_(t,e)}getAnnotation(t){const e=this.annotation[t];return e||[]}hasAnnotation(t,e){const r=this.annotation[t];return!!r&&-1!==r.indexOf(e)}parseAnnotation(t){const e=t.split(";");for(let t=0,r=e.length;t<r;t++){const r=e[t].split(":");this.addAnnotation(r[0],r[1])}}meaning(){return{type:this.type,role:this.role,font:this.font}}xmlAttributes(t){const e=this.allAttributes();for(let r,n=0;r=e[n];n++)t.setAttribute(r[0],r[1]);this.addExternalAttributes(t)}addExternalAttributes(t){for(const[e,r]of Object.entries(this.attributes))t.setAttribute(e,r)}static escapeValue(t){return t.replace(/;/g,"\\0003B")}parseAttributes(t){if(!t)return;const e=t.split(";");for(let t=0,r=e.length;t<r;t++){const[r,...n]=e[t].split(":");r&&(this.attributes[r]=n.join("").replace(/\\0003B/g,";"))}}removeMathmlNodes(t){const e=this.mathml;for(let r,n=0;r=t[n];n++){const t=e.indexOf(r);-1!==t&&e.splice(t,1)}this.mathml=e}displayTree_(t){t++;const e=Array(t).join("  ");let r="";r+="\n"+e+this.toString(),r+="\n"+e+"MathmlTree:",r+="\n"+e+this.mathmlTreeString(),r+="\n"+e+"MathML:";for(let t,n=0;t=this.mathml[n];n++)r+="\n"+e+t.toString();return r+="\n"+e+"Begin Content",this.contentNodes.forEach((function(e){r+=e.displayTree_(t)})),r+="\n"+e+"End Content",r+="\n"+e+"Begin Children",this.childNodes.forEach((function(e){r+=e.displayTree_(t)})),r+="\n"+e+"End Children",r}mathmlTreeString(){return this.mathmlTree?this.mathmlTree.toString():"EMPTY"}addAnnotation_(t,e){const r=this.annotation[t];r&&!r.includes(e)?r.push(e):this.annotation[t]=[e]}}class Ws{constructor(){this.leafMap=new Cs,this.defaultMap=new Rs,this.idCounter_=-1}makeNode(t){return this.createNode_(t)}makeUnprocessed(t){const e=this.createNode_();return e.mathml=[t],e.mathmlTree=t,e}makeEmptyNode(){const t=this.createNode_();return t.type=ss.EMPTY,t}makeContentNode(t){const e=this.createNode_();return e.updateContent(t),e}makeMultipleContentNodes(t,e){const r=[];for(let n=0;n<t;n++)r.push(this.makeContentNode(e));return r}makeLeafNode(t,e){if(!t)return this.makeEmptyNode();const r=this.makeContentNode(t);r.font=e||r.font;const n=this.defaultMap.getNode(r);return n&&(r.type=n.type,r.role=n.role,r.font=n.font),this.leafMap.addNode(r),r}makeBranchNode(t,e,r,n){const s=this.createNode_();return n&&s.updateContent(n),s.type=t,s.childNodes=e,s.contentNodes=r,e.concat(r).forEach((function(t){t.parent=s,s.addMathmlNodes(t.mathml)})),s}createNode_(t){return void 0!==t?this.idCounter_=Math.max(this.idCounter_,t):t=++this.idCounter_,new Vs(t)}}class Ks{constructor(t){this.type=t,this.factory_=new Ws}getFactory(){return this.factory_}setFactory(t){this.factory_=t}getType(){return this.type}parseList(t){const e=[];for(let r,n=0;r=t[n];n++)e.push(this.parse(r));return e}}function zs(t,e){return t.type===e}function Ys(t,e){return t.embellished===e}function $s(t,e){return t.role===e}function Js(t){return zs(t,ss.FENCE)||zs(t,ss.PUNCTUATION)||zs(t,ss.OPERATOR)||zs(t,ss.RELATION)}function Qs(t){return si(t)&&!$s(t,os.DIVISION)||zs(t,ss.APPL)||ri(t)}function Zs(t){return si(t)||ri(t)}function ti(t,e){return!!e&&zs(e,ss.IDENTIFIER)&&fs.Secondary.has(t.textContent,hs.D)}function ei(t){if(zs(t,ss.IDENTIFIER)){const e=t.textContent[0];return e&&t.textContent[1]&&fs.Secondary.has(e,hs.D)}return!1}function ri(t){return ii(t)||oi(t)}function ni(t){return t.embellished?t.embellished:(e=t.type)===ss.OPERATOR||e===ss.RELATION||e===ss.FENCE||e===ss.PUNCTUATION?t.type:null;var e}function si(t){return zs(t,ss.OPERATOR)||Ys(t,ss.OPERATOR)}function ii(t){return zs(t,ss.RELATION)||Ys(t,ss.RELATION)}function oi(t){return zs(t,ss.PUNCTUATION)||Ys(t,ss.PUNCTUATION)}function ai(t){return zs(t,ss.FENCE)||Ys(t,ss.FENCE)}function ci(t){return!(!t||!ai(t))&&(!t.embellished||li(t))}function li(t){return!t.embellished||!function(t){return zs(t,ss.TENSOR)&&(!zs(t.childNodes[1],ss.EMPTY)||!zs(t.childNodes[2],ss.EMPTY))&&(!zs(t.childNodes[3],ss.EMPTY)||!zs(t.childNodes[4],ss.EMPTY))}(t)&&((!$s(t,os.CLOSE)||!zs(t,ss.TENSOR))&&((!$s(t,os.OPEN)||!zs(t,ss.SUBSCRIPT)&&!zs(t,ss.SUPERSCRIPT))&&li(t.childNodes[0])))}function hi(t){return!!t&&(zs(t,ss.TABLE)||zs(t,ss.MULTILINE))}function ui(t){return!!t&&di(t)&&hi(t.childNodes[0])}function di(t){return!!t&&zs(t,ss.FENCED)&&($s(t,os.LEFTRIGHT)||Ci(t))&&1===t.childNodes.length}function pi(t,e){return e.length>0&&$s(e[e.length-1],os.OPENFENCE)}function mi(t){return t.childNodes.every((function(t){return t.childNodes.length<=1}))}function fi(t){return zs(t,ss.LARGEOP)||zs(t,ss.LIMBOTH)||zs(t,ss.LIMLOWER)||zs(t,ss.LIMUPPER)||zs(t,ss.FUNCTION)&&$s(t,os.LIMFUNC)||(zs(t,ss.OVERSCORE)||zs(t,ss.UNDERSCORE))&&fi(t.childNodes[0])}function gi(t,e,r){return 1===e.length&&(t[r].type===ss.PUNCTUATION||t[r].embellished===ss.PUNCTUATION)&&t[r]===e[0]}function Ei(t){return zs(t,ss.IDENTIFIER)&&$s(t,os.SIMPLEFUNC)}const Ni=[ss.PUNCTUATION,ss.PUNCTUATED,ss.RELSEQ,ss.MULTIREL,ss.TABLE,ss.MULTILINE,ss.CASES,ss.INFERENCE],bi=[ss.LIMUPPER,ss.LIMLOWER,ss.LIMBOTH,ss.SUBSCRIPT,ss.SUPERSCRIPT,ss.UNDERSCORE,ss.OVERSCORE,ss.TENSOR];function Ti(t){const e=t.type;return-1===Ni.indexOf(e)&&(e!==ss.INFIXOP||t.role===os.IMPLICIT)&&(e===ss.FENCED?t.role!==os.LEFTRIGHT||Ti(t.childNodes[0]):-1===bi.indexOf(e)||Ti(t.childNodes[0]))}function yi(t){return function(t){return t.type===ss.NUMBER&&(t.role===os.INTEGER||t.role===os.FLOAT)}(t)||t.role===os.VULGAR||t.role===os.MIXED}function Ii(t){const e=t.childNodes;return t.role===os.UNIT&&(!e.length||e[0].role===os.UNIT)}function Ai(t){const e=t.childNodes;return t.type===ss.INFIXOP&&(t.role===os.MULTIPLICATION||t.role===os.IMPLICIT)&&e.length&&(Ii(e[0])||yi(e[0]))&&t.childNodes.slice(1).every(Ii)}function Ri(t){return t.type===ss.INFIXOP&&(t.role===os.IMPLICIT||t.role===os.UNIT&&!!t.contentNodes.length&&t.contentNodes[0].textContent===ds.invisibleTimes)}function Li(t){return t.type===ss.INFIXOP&&t.role===os.IMPLICIT}function Ci(t){return t.role===os.NEUTRAL||t.role===os.METRIC}function Oi(t,e){return Ci(t)&&Ci(e)&&Hs(t).textContent===Hs(e).textContent}function vi(t){return!!Ci(t)&&(!t.embellished||t.type!==ss.SUPERSCRIPT&&t.type!==ss.SUBSCRIPT&&(t.type!==ss.TENSOR||t.childNodes[3].type===ss.EMPTY&&t.childNodes[4].type===ss.EMPTY))}function Si(t){return!!Ci(t)&&(!t.embellished||(t.type!==ss.TENSOR||t.childNodes[1].type===ss.EMPTY&&t.childNodes[2].type===ss.EMPTY))}const wi={factory:null,options:new On,updateFactory:function(t){wi.factory=t},heuristics:new Map,flags:{combine_juxtaposition:!0,convert_juxtaposition:!0,multioperator:!0},blacklist:{},add:function(t){const e=t.name;wi.heuristics.set(e,t),wi.flags[e]||(wi.flags[e]=!1)},run:function(t,e,r){const n=wi.heuristics.get(t);return n&&!wi.blacklist[t]&&(wi.flags[t]||n.applicable(e))?n.apply(e):r?r(e):e}};class Mi{static getInstance(){return Mi.instance=Mi.instance||new Mi,Mi.instance}static tableToMultiline(t){if(!mi(t))return wi.run("rewrite_subcases",t,Mi.classifyTable);t.type=ss.MULTILINE;for(let e,r=0;e=t.childNodes[r];r++)Mi.rowToLine_(e,os.MULTILINE);var e;return 1!==t.childNodes.length||zs(e=t.childNodes[0],ss.LINE)&&e.contentNodes.length&&$s(e.contentNodes[0],os.LABEL)||!di(t.childNodes[0].childNodes[0])||Mi.tableToMatrixOrVector_(Mi.rewriteFencedLine_(t)),Mi.binomialForm_(t),Mi.classifyMultiline(t),t}static number(t){t.type!==ss.UNKNOWN&&t.type!==ss.IDENTIFIER||(t.type=ss.NUMBER),Mi.meaningFromContent(t,Mi.numberRole_),Mi.exprFont_(t)}static classifyMultiline(t){let e=0;const r=t.childNodes.length;let n;for(;e<r&&(!(n=t.childNodes[e])||!n.childNodes.length);)e++;if(e>=r)return;const s=n.childNodes[0].role;s!==os.UNKNOWN&&t.childNodes.every((function(t){const e=t.childNodes[0];return!e||e.role===s&&(zs(e,ss.RELATION)||zs(e,ss.RELSEQ))}))&&(t.role=s)}static classifyTable(t){const e=Mi.computeColumns_(t);return Mi.classifyByColumns_(t,e,os.EQUALITY)||Mi.classifyByColumns_(t,e,os.INEQUALITY,[os.EQUALITY])||Mi.classifyByColumns_(t,e,os.ARROW)||Mi.detectCaleyTable(t),t}static detectCaleyTable(t){if(!t.mathmlTree)return!1;const e=t.mathmlTree,r=e.getAttribute("columnlines"),n=e.getAttribute("rowlines");return!(!r||!n)&&(!(!Mi.cayleySpacing(r)||!Mi.cayleySpacing(n))&&(t.role=os.CAYLEY,!0))}static cayleySpacing(t){const e=t.split(" ");return("solid"===e[0]||"dashed"===e[0])&&e.slice(1).every((t=>"none"===t))}static proof(t,e,r){const n=Mi.separateSemantics(e);return Mi.getInstance().proof(t,n,r)}static findSemantics(t,e,r){const n=null==r?null:r,s=Mi.getSemantics(t);return!!s&&(!!s[e]&&(null==n||s[e]===n))}static getSemantics(t){const e=t.getAttribute("semantics");return e?Mi.separateSemantics(e):null}static removePrefix(t){const[,...e]=t.split("_");return e.join("_")}static separateSemantics(t){const e={};return t.split(";").forEach((function(t){const[r,n]=t.split(":");e[Mi.removePrefix(r)]=n})),e}static matchSpaces_(t,e){for(let r,n=0;r=e[n];n++){const e=t[n].mathmlTree,s=t[n+1].mathmlTree;if(!e||!s)continue;const i=e.nextSibling;if(!i||i===s)continue;const o=Mi.getSpacer_(i);o&&(r.mathml.push(o),r.mathmlTree=o,r.role=os.SPACE)}}static getSpacer_(t){if(Xn(t)===vs.MSPACE)return t;for(;Fs(t)&&1===t.childNodes.length;)if(Xn(t=t.childNodes[0])===vs.MSPACE)return t;return null}static fenceToPunct_(t){const e=Mi.FENCE_TO_PUNCT_[t.role];if(e){for(;t.embellished;)t.embellished=ss.PUNCTUATION,$s(t,os.SUBSUP)||$s(t,os.UNDEROVER)||(t.role=e),t=t.childNodes[0];t.type=ss.PUNCTUATION,t.role=e}}static classifyFunction_(t,e){if(t.type===ss.APPL||t.type===ss.BIGOP||t.type===ss.INTEGRAL)return"";if(e[0]&&e[0].textContent===ds.functionApplication){Mi.getInstance().funcAppls[t.id]=e.shift();let r=os.SIMPLEFUNC;return wi.run("simple2prefix",t),t.role!==os.PREFIXFUNC&&t.role!==os.LIMFUNC||(r=t.role),Mi.propagateFunctionRole_(t,r),"prefix"}const r=Mi.CLASSIFY_FUNCTION_[t.role];return r||((n=t).type===ss.IDENTIFIER||n.role===os.LATINLETTER||n.role===os.GREEKLETTER||n.role===os.OTHERLETTER?"simple":"");var n}static propagateFunctionRole_(t,e){if(t){if(t.type===ss.INFIXOP)return;$s(t,os.SUBSUP)||$s(t,os.UNDEROVER)||(t.role=e),Mi.propagateFunctionRole_(t.childNodes[0],e)}}static getFunctionOp_(t,e){if(e(t))return t;for(let r,n=0;r=t.childNodes[n];n++){const t=Mi.getFunctionOp_(r,e);if(t)return t}return null}static tableToMatrixOrVector_(t){const e=t.childNodes[0];zs(e,ss.MULTILINE)?Mi.tableToVector_(t):Mi.tableToMatrix_(t),t.contentNodes.forEach(e.appendContentNode.bind(e));for(let t,r=0;t=e.childNodes[r];r++)Mi.assignRoleToRow_(t,Mi.getComponentRoles_(e));return e.parent=null,e}static tableToVector_(t){const e=t.childNodes[0];e.type=ss.VECTOR,1!==e.childNodes.length?Mi.binomialForm_(e):Mi.tableToSquare_(t)}static binomialForm_(t){$s(t,os.UNKNOWN)&&(2===t.childNodes.length&&(t.role=os.BINOMIAL,t.childNodes[0].role=os.BINOMIAL,t.childNodes[1].role=os.BINOMIAL))}static tableToMatrix_(t){const e=t.childNodes[0];e.type=ss.MATRIX,e.childNodes&&e.childNodes.length>0&&e.childNodes[0].childNodes&&e.childNodes.length===e.childNodes[0].childNodes.length?Mi.tableToSquare_(t):e.childNodes&&1===e.childNodes.length&&(e.role=os.ROWVECTOR)}static tableToSquare_(t){const e=t.childNodes[0];$s(e,os.UNKNOWN)&&(Ci(t)?e.role=os.DETERMINANT:e.role=os.SQUAREMATRIX)}static getComponentRoles_(t){const e=t.role;return e&&e!==os.UNKNOWN?e:t.type.toLowerCase()||os.UNKNOWN}static tableToCases_(t,e){for(let e,r=0;e=t.childNodes[r];r++)Mi.assignRoleToRow_(e,os.CASES);return t.type=ss.CASES,t.appendContentNode(e),mi(t)&&Mi.binomialForm_(t),t}static rewriteFencedLine_(t){const e=t.childNodes[0],r=t.childNodes[0].childNodes[0],n=t.childNodes[0].childNodes[0].childNodes[0];return r.parent=t.parent,t.parent=r,n.parent=e,r.childNodes=[t],e.childNodes=[n],r}static rowToLine_(t,e){const r=e||os.UNKNOWN;zs(t,ss.ROW)&&(t.type=ss.LINE,t.role=r,1===t.childNodes.length&&zs(t.childNodes[0],ss.CELL)&&(t.childNodes=t.childNodes[0].childNodes,t.childNodes.forEach((function(e){e.parent=t}))))}static assignRoleToRow_(t,e){zs(t,ss.LINE)?t.role=e:zs(t,ss.ROW)&&(t.role=e,t.childNodes.forEach((function(t){zs(t,ss.CELL)&&(t.role=e)})))}static nextSeparatorFunction_(t){let e;if(t){if(t.match(/^\s+$/))return null;e=t.replace(/\s/g,"").split("").filter((function(t){return t}))}else e=[","];return function(){return e.length>1?e.shift():e[0]}}static meaningFromContent(t,e){const r=[...t.textContent].filter((t=>t.match(/[^\s]/))),n=r.map((t=>fs.Meaning.get(t)));e(t,r,n)}static numberRole_(t,e,r){if(t.role===os.UNKNOWN)return r.every((function(t){return t.type===ss.NUMBER&&t.role===os.INTEGER||t.type===ss.PUNCTUATION&&t.role===os.COMMA}))?(t.role=os.INTEGER,void("0"===e[0]&&t.addAnnotation("general","basenumber"))):void(r.every((function(t){return t.type===ss.NUMBER&&t.role===os.INTEGER||t.type===ss.PUNCTUATION}))?t.role=os.FLOAT:t.role=os.OTHERNUMBER)}static exprFont_(t){t.font===cs.UNKNOWN&&Mi.compSemantics(t,"font",cs)}static compSemantics(t,e,r){const n=[...t.textContent].map((t=>fs.Meaning.get(t))).reduce((function(t,n){return t&&n[e]&&n[e]!==r.UNKNOWN&&n[e]!==t?t===r.UNKNOWN?n[e]:null:t}),r.UNKNOWN);n&&(t[e]=n)}static purgeFences_(t){const e=t.rel,r=t.comp,n=[],s=[];for(;e.length>0;){const t=e.shift();let i=r.shift();ci(t)?(n.push(t),s.push(i)):(Mi.fenceToPunct_(t),i.push(t),i=i.concat(r.shift()),r.unshift(i))}return s.push(r.shift()),{rel:n,comp:s}}static rewriteFencedNode_(t){const e=t.contentNodes[0],r=t.contentNodes[1];let n=Mi.rewriteFence_(t,e);return t.contentNodes[0]=n.fence,n=Mi.rewriteFence_(n.node,r),t.contentNodes[1]=n.fence,t.contentNodes[0].parent=t,t.contentNodes[1].parent=t,n.node.parent=null,n.node}static rewriteFence_(t,e){if(!e.embellished)return{node:t,fence:e};const r=e.childNodes[0],n=Mi.rewriteFence_(t,r);return zs(e,ss.SUPERSCRIPT)||zs(e,ss.SUBSCRIPT)||zs(e,ss.TENSOR)?($s(e,os.SUBSUP)||(e.role=t.role),r!==n.node&&(e.replaceChild(r,n.node),r.parent=t),Mi.propagateFencePointer_(e,r),{node:e,fence:n.fence}):(e.replaceChild(r,n.fence),e.mathmlTree&&-1===e.mathml.indexOf(e.mathmlTree)&&e.mathml.push(e.mathmlTree),{node:n.node,fence:e})}static propagateFencePointer_(t,e){t.fencePointer=e.fencePointer||e.id.toString(),t.embellished=null}static classifyByColumns_(t,e,r,n=[]){const s=[r].concat(n);return!!(3===e.length&&Mi.testColumns_(e,1,(t=>Mi.isPureRelation_(t,s)))||2===e.length&&(Mi.testColumns_(e,1,(t=>Mi.isEndRelation_(t,s)||Mi.isPureRelation_(t,s)))||Mi.testColumns_(e,0,(t=>Mi.isEndRelation_(t,s,!0)||Mi.isPureRelation_(t,s)))))&&(t.role=r,!0)}static isEndRelation_(t,e,r){const n=r?t.childNodes.length-1:0;return zs(t,ss.RELSEQ)&&e.some((e=>$s(t,e)))&&zs(t.childNodes[n],ss.EMPTY)}static isPureRelation_(t,e){return zs(t,ss.RELATION)&&e.some((e=>$s(t,e)))}static computeColumns_(t){const e=[];for(let r,n=0;r=t.childNodes[n];n++)for(let t,n=0;t=r.childNodes[n];n++){e[n]?e[n].push(t):e[n]=[t]}return e}static testColumns_(t,e,r){const n=t[e];return!!n&&(n.some((function(t){return t.childNodes.length&&r(t.childNodes[0])}))&&n.every((function(t){return!t.childNodes.length||r(t.childNodes[0])})))}setNodeFactory(t){Mi.getInstance().factory_=t,wi.updateFactory(Mi.getInstance().factory_)}getNodeFactory(){return Mi.getInstance().factory_}identifierNode(t,e,r){if("MathML-Unit"===r)t.type=ss.IDENTIFIER,t.role=os.UNIT;else if(!e&&1===t.textContent.length&&(t.role===os.INTEGER||t.role===os.LATINLETTER||t.role===os.GREEKLETTER)&&t.font===cs.NORMAL)return t.font=cs.ITALIC,wi.run("simpleNamedFunction",t);return t.type===ss.UNKNOWN&&(t.type=ss.IDENTIFIER),Mi.exprFont_(t),wi.run("simpleNamedFunction",t)}implicitNode(t){if(t=Mi.getInstance().getMixedNumbers_(t),1===(t=Mi.getInstance().combineUnits_(t)).length)return t[0];const e=Mi.getInstance().implicitNode_(t);return wi.run("combine_juxtaposition",e)}text(t,e){return Mi.exprFont_(t),t.type=ss.TEXT,e===vs.ANNOTATIONXML?(t.role=os.ANNOTATION,t):e===vs.MS?(t.role=os.STRING,t):e===vs.MSPACE||t.textContent.match(/^\s*$/)?(t.role=os.SPACE,t):/\s/.exec(t.textContent)?(t.role=os.TEXT,t):(t.role=os.UNKNOWN,t)}row(t){return 0===(t=t.filter((function(t){return!zs(t,ss.EMPTY)}))).length?Mi.getInstance().factory_.makeEmptyNode():(t=Mi.getInstance().getFencesInRow_(t),t=Mi.getInstance().tablesInRow(t),t=Mi.getInstance().getPunctuationInRow_(t),t=Mi.getInstance().getTextInRow_(t),t=Mi.getInstance().getFunctionsInRow_(t),Mi.getInstance().relationsInRow_(t))}limitNode(t,e){if(!e.length)return Mi.getInstance().factory_.makeEmptyNode();let r,n=e[0],s=ss.UNKNOWN;if(!e[1])return n;if(wi.run("op_with_limits",e),fi(n)){r=Mi.MML_TO_LIMIT_[t];const i=r.length;if(s=r.type,e=e.slice(0,r.length+1),1===i&&Js(e[1])||2===i&&Js(e[1])&&Js(e[2]))return r=Mi.MML_TO_BOUNDS_[t],Mi.getInstance().accentNode_(n,e,r.type,r.length,r.accent);if(2===i){if(Js(e[1]))return n=Mi.getInstance().accentNode_(n,[n,e[1]],{MSUBSUP:ss.SUBSCRIPT,MUNDEROVER:ss.UNDERSCORE}[t],1,!0),e[2]?Mi.getInstance().makeLimitNode_(n,[n,e[2]],null,ss.LIMUPPER):n;if(e[2]&&Js(e[2]))return n=Mi.getInstance().accentNode_(n,[n,e[2]],{MSUBSUP:ss.SUPERSCRIPT,MUNDEROVER:ss.OVERSCORE}[t],1,!0),Mi.getInstance().makeLimitNode_(n,[n,e[1]],null,ss.LIMLOWER);e[i]||(s=ss.LIMLOWER)}return Mi.getInstance().makeLimitNode_(n,e,null,s)}return r=Mi.MML_TO_BOUNDS_[t],Mi.getInstance().accentNode_(n,e,r.type,r.length,r.accent)}tablesInRow(t){let e=js(t,ui),r=[];for(let t,n=0;t=e.rel[n];n++)r=r.concat(e.comp.shift()),r.push(Mi.tableToMatrixOrVector_(t));r=r.concat(e.comp.shift()),e=js(r,hi),r=[];for(let t,n=0;t=e.rel[n];n++){const n=e.comp.shift();pi(0,n)&&Mi.tableToCases_(t,n.pop()),r=r.concat(n),r.push(t)}return r.concat(e.comp.shift())}mfenced(t,e,r,n){if(r&&n.length>0){const t=Mi.nextSeparatorFunction_(r),e=[n.shift()];n.forEach((r=>{e.push(Mi.getInstance().factory_.makeContentNode(t())),e.push(r)})),n=e}return t&&e?Mi.getInstance().horizontalFencedNode_(Mi.getInstance().factory_.makeContentNode(t),Mi.getInstance().factory_.makeContentNode(e),n):(t&&n.unshift(Mi.getInstance().factory_.makeContentNode(t)),e&&n.push(Mi.getInstance().factory_.makeContentNode(e)),Mi.getInstance().row(n))}fractionLikeNode(t,e,r,n){let s;if(!n&&function(t){if(!t)return!1;if(["negativeveryverythinmathspace","negativeverythinmathspace","negativethinmathspace","negativemediummathspace","negativethickmathspace","negativeverythickmathspace","negativeveryverythickmathspace"].includes(t))return!0;const e=t.match(/[0-9.]+/);return!!e&&0===parseFloat(e[0])}(r)){const r=Mi.getInstance().factory_.makeBranchNode(ss.LINE,[t],[]),n=Mi.getInstance().factory_.makeBranchNode(ss.LINE,[e],[]);return s=Mi.getInstance().factory_.makeBranchNode(ss.MULTILINE,[r,n],[]),Mi.binomialForm_(s),Mi.classifyMultiline(s),s}return s=Mi.getInstance().fractionNode_(t,e),n&&s.addAnnotation("general","bevelled"),s}tensor(t,e,r,n,s){const i=Mi.getInstance().factory_.makeBranchNode(ss.TENSOR,[t,Mi.getInstance().scriptNode_(e,os.LEFTSUB),Mi.getInstance().scriptNode_(r,os.LEFTSUPER),Mi.getInstance().scriptNode_(n,os.RIGHTSUB),Mi.getInstance().scriptNode_(s,os.RIGHTSUPER)],[]);return i.role=t.role,i.embellished=ni(t),i}pseudoTensor(t,e,r){const n=t=>!zs(t,ss.EMPTY),s=e.filter(n).length,i=r.filter(n).length;if(!s&&!i)return t;const o=s?i?vs.MSUBSUP:vs.MSUB:vs.MSUP,a=[t];return s&&a.push(Mi.getInstance().scriptNode_(e,os.RIGHTSUB,!0)),i&&a.push(Mi.getInstance().scriptNode_(r,os.RIGHTSUPER,!0)),Mi.getInstance().limitNode(o,a)}font(t){const e=Mi.MATHJAX_FONTS[t];return e||t}proof(t,e,r){if(e.inference||e.axiom||console.log("Noise"),e.axiom){const e=Mi.getInstance().cleanInference(t.childNodes),n=e.length?Mi.getInstance().factory_.makeBranchNode(ss.INFERENCE,r(e),[]):Mi.getInstance().factory_.makeEmptyNode();return n.role=os.AXIOM,n.mathmlTree=t,n}const n=Mi.getInstance().inference(t,e,r);return e.proof&&(n.role=os.PROOF,n.childNodes[0].role=os.FINAL),n}inference(t,e,r){if(e.inferenceRule){const e=Mi.getInstance().getFormulas(t,[],r);return Mi.getInstance().factory_.makeBranchNode(ss.INFERENCE,[e.conclusion,e.premises],[])}const n=e.labelledRule,s=Bn(t.childNodes),i=[];"left"!==n&&"both"!==n||i.push(Mi.getInstance().getLabel(t,s,r,os.LEFT)),"right"!==n&&"both"!==n||i.push(Mi.getInstance().getLabel(t,s,r,os.RIGHT));const o=Mi.getInstance().getFormulas(t,s,r),a=Mi.getInstance().factory_.makeBranchNode(ss.INFERENCE,[o.conclusion,o.premises],i);return a.mathmlTree=t,a}getLabel(t,e,r,n){const s=Mi.getInstance().findNestedRow(e,"prooflabel",n),i=Mi.getInstance().factory_.makeBranchNode(ss.RULELABEL,r(Bn(s.childNodes)),[]);return i.role=n,i.mathmlTree=s,i}getFormulas(t,e,r){const n=e.length?Mi.getInstance().findNestedRow(e,"inferenceRule"):t,s="up"===Mi.getSemantics(n).inferenceRule,i=s?n.childNodes[1]:n.childNodes[0],o=s?n.childNodes[0]:n.childNodes[1],a=i.childNodes[0].childNodes[0],c=Bn(a.childNodes[0].childNodes),l=[];let h=1;for(const t of c)h%2&&l.push(t.childNodes[0]),h++;const u=r(l),d=r(Bn(o.childNodes[0].childNodes))[0],p=Mi.getInstance().factory_.makeBranchNode(ss.PREMISES,u,[]);p.mathmlTree=a;const m=Mi.getInstance().factory_.makeBranchNode(ss.CONCLUSION,[d],[]);return m.mathmlTree=o.childNodes[0].childNodes[0],{conclusion:m,premises:p}}findNestedRow(t,e,r){return Mi.getInstance().findNestedRow_(t,e,0,r)}cleanInference(t){return Bn(t).filter((function(t){return"MSPACE"!==Xn(t)}))}operatorNode(t){return t.type===ss.UNKNOWN&&(t.type=ss.OPERATOR),wi.run("multioperator",t)}constructor(){this.funcAppls={},this.splitRoles=new Map([[os.SUBTRACTION,os.NEGATIVE],[os.ADDITION,os.POSITIVE]]),this.splitOps=["\u2212","-","\u2010","\u2011","+"],this.factory_=new Ws,wi.updateFactory(this.factory_)}implicitNode_(t){const e=Mi.getInstance().factory_.makeMultipleContentNodes(t.length-1,ds.invisibleTimes);Mi.matchSpaces_(t,e);const r=Mi.getInstance().infixNode_(t,e[0]);return r.role=os.IMPLICIT,e.forEach((function(t){t.parent=r})),r.contentNodes=e,r}infixNode_(t,e){const r=Mi.getInstance().factory_.makeBranchNode(ss.INFIXOP,t,[e],Hs(e).textContent);return r.role=e.role,wi.run("propagateSimpleFunction",r)}explicitMixed_(t){const e=js(t,(function(t){return t.textContent===ds.invisiblePlus}));if(!e.rel.length)return t;let r=[];for(let t,n=0;t=e.rel[n];n++){const s=e.comp[n],i=e.comp[n+1],o=s.length-1;if(s[o]&&i[0]&&zs(s[o],ss.NUMBER)&&!$s(s[o],os.MIXED)&&zs(i[0],ss.FRACTION)){const t=Mi.getInstance().factory_.makeBranchNode(ss.NUMBER,[s[o],i[0]],[]);t.role=os.MIXED,r=r.concat(s.slice(0,o)),r.push(t),i.shift()}else r=r.concat(s),r.push(t)}return r.concat(e.comp[e.comp.length-1])}concatNode_(t,e,r){if(0===e.length)return t;const n=e.map((function(t){return Hs(t).textContent})).join(" "),s=Mi.getInstance().factory_.makeBranchNode(r,[t],e,n);return e.length>1&&(s.role=os.MULTIOP),s}prefixNode_(t,e){const r=this.splitSingles(e);let n=t;for(;r.length>0;){const t=r.pop();n=Mi.getInstance().concatNode_(n,t,ss.PREFIXOP),1===t.length&&-1!==this.splitOps.indexOf(t[0].textContent)&&(n.role=this.splitRoles.get(t[0].role))}return n}splitSingles(t){let e=0;const r=[];let n=0;for(;n<t.length;){const s=t[n];!this.splitRoles.has(s.role)||t[n-1]&&t[n-1].role===s.role||t[n+1]&&t[n+1].role===s.role||-1===this.splitOps.indexOf(s.textContent)||(r.push(t.slice(e,n)),r.push(t.slice(n,n+1)),e=n+1),n++}return e<n&&r.push(t.slice(e,n)),r}postfixNode_(t,e){return e.length?Mi.getInstance().concatNode_(t,e,ss.POSTFIXOP):t}combineUnits_(t){const e=js(t,(function(t){return!$s(t,os.UNIT)}));if(t.length===e.rel.length)return e.rel;const r=[];let n,s;do{const t=e.comp.shift();n=e.rel.shift();let i=null;s=r.pop(),s&&(t.length&&yi(s)?t.unshift(s):r.push(s)),1===t.length&&(i=t.pop()),t.length>1&&(i=Mi.getInstance().implicitNode_(t),i.role=os.UNIT),i&&r.push(i),n&&r.push(n)}while(n);return r}getMixedNumbers_(t){const e=js(t,(function(t){return zs(t,ss.FRACTION)&&$s(t,os.VULGAR)}));if(!e.rel.length)return t;let r=[];for(let t,n=0;t=e.rel[n];n++){const s=e.comp[n],i=s.length-1;if(s[i]&&zs(s[i],ss.NUMBER)&&($s(s[i],os.INTEGER)||$s(s[i],os.FLOAT))){const e=Mi.getInstance().factory_.makeBranchNode(ss.NUMBER,[s[i],t],[]);e.role=os.MIXED,r=r.concat(s.slice(0,i)),r.push(e)}else r=r.concat(s),r.push(t)}return r.concat(e.comp[e.comp.length-1])}getTextInRow_(t){if(0===t.length)return t;if(1===t.length)return t[0].type===ss.TEXT&&t[0].role===os.UNKNOWN&&(t[0].role=os.ANNOTATION),t;const{rel:e,comp:r}=js(t,(t=>zs(t,ss.TEXT)));if(0===e.length)return t;const n=[];let s=r.shift();for(;e.length>0;){let t=e.shift(),i=r.shift();const o=[];for(;!i.length&&e.length&&t.role!==os.SPACE&&e[0].role!==os.SPACE;)o.push(t),t=e.shift(),i=r.shift();if(o.length){s.length&&n.push(Mi.getInstance().row(s)),o.push(t);const e=Mi.getInstance().dummyNode_(o);n.push(e),s=i;continue}if(t.role!==os.UNKNOWN){s.length&&n.push(Mi.getInstance().row(s)),n.push(t),s=i;continue}const a=fs.Meaning.get(t.textContent);a.type!==ss.PUNCTUATION?a.type===ss.UNKNOWN?(Mi.meaningFromContent(t,((t,e,r)=>{if(t.role===os.UNKNOWN){if(Mi.numberRole_(t,e,r),t.role===os.OTHERNUMBER)return r.some((t=>t.type!==ss.NUMBER&&t.type!==ss.IDENTIFIER))?(t.type=ss.TEXT,void(t.role=os.ANNOTATION)):void(t.role=os.UNKNOWN);t.type=ss.NUMBER}})),t.type!==ss.TEXT||t.role===os.UNKNOWN?(t.role===os.UNKNOWN&&(e.length||i.length?i.length&&i[0].type===ss.FENCED?(t.type=ss.FUNCTION,t.role=os.PREFIXFUNC):t.role=os.TEXT:(t.type=ss.IDENTIFIER,t.role=os.UNIT)),s.push(t),s=s.concat(i)):(s.length&&n.push(Mi.getInstance().row(s)),n.push(t),s=i)):(t.type=a.type,t.role=a.role,t.font=a.font,t.addAnnotation("general","text"),s.push(t),s=s.concat(i)):(t.role=a.role,t.font=a.font,s.length&&n.push(Mi.getInstance().row(s)),n.push(t),s=i)}return s.length>0&&n.push(Mi.getInstance().row(s)),n.length>1?[Mi.getInstance().dummyNode_(n)]:n}relationsInRow_(t){const e=js(t,ii),r=e.rel[0];if(!r)return Mi.getInstance().operationsInRow_(t);if(1===t.length)return t[0];const n=e.comp.map(Mi.getInstance().operationsInRow_);let s;return e.rel.some((function(t){return!t.equals(r)}))?(s=Mi.getInstance().factory_.makeBranchNode(ss.MULTIREL,n,e.rel),e.rel.every((function(t){return t.role===r.role}))&&(s.role=r.role),s):(s=Mi.getInstance().factory_.makeBranchNode(ss.RELSEQ,n,e.rel,Hs(r).textContent),s.role=r.role,s)}operationsInRow_(t){if(0===t.length)return Mi.getInstance().factory_.makeEmptyNode();if(1===(t=Mi.getInstance().explicitMixed_(t)).length)return t[0];const e=[];for(;t.length>0&&si(t[0]);)e.push(t.shift());if(0===t.length)return Mi.getInstance().prefixNode_(e.pop(),e);if(1===t.length)return Mi.getInstance().prefixNode_(t[0],e);const r=Gs(t=wi.run("convert_juxtaposition",t),si),n=Mi.getInstance().wrapFactor(e,r);return Mi.getInstance().addFactor(n,r)}wrapPostfix(t){var e;(null===(e=t.div)||void 0===e?void 0:e.role)===os.POSTFIXOP&&(t.tail.length&&t.tail[0].type!==ss.OPERATOR?t.div.role=os.DIVISION:(t.head=[Mi.getInstance().postfixNode_(Mi.getInstance().implicitNode(t.head),[t.div])],t.div=t.tail.shift(),Mi.getInstance().wrapPostfix(t)))}wrapFactor(t,e){return Mi.getInstance().wrapPostfix(e),Mi.getInstance().prefixNode_(Mi.getInstance().implicitNode(e.head),t)}addFactor(t,e){return e.div?Mi.getInstance().operationsTree_(e.tail,t,e.div):(Ai(t)&&(t.role=os.UNIT),t)}operationsTree_(t,e,r,n=[]){if(0===t.length){if(n.unshift(r),e.type===ss.INFIXOP){const t=Mi.getInstance().postfixNode_(e.childNodes.pop(),n);return e.appendChild(t),e}return Mi.getInstance().postfixNode_(e,n)}const s=Gs(t,si);if(0===s.head.length)return n.push(s.div),Mi.getInstance().operationsTree_(s.tail,e,r,n);const i=Mi.getInstance().wrapFactor(n,s),o=Mi.getInstance().appendOperand_(e,r,i);return Mi.getInstance().addFactor(o,s)}appendOperand_(t,e,r){if(t.type!==ss.INFIXOP)return Mi.getInstance().infixNode_([t,r],e);const n=Mi.getInstance().appendDivisionOp_(t,e,r);return n||(Mi.getInstance().appendExistingOperator_(t,e,r)?t:e.role===os.MULTIPLICATION?Mi.getInstance().appendMultiplicativeOp_(t,e,r):Mi.getInstance().appendAdditiveOp_(t,e,r))}appendDivisionOp_(t,e,r){return e.role===os.DIVISION?Ri(t)?Mi.getInstance().infixNode_([t,r],e):Mi.getInstance().appendLastOperand_(t,e,r):t.role===os.DIVISION?Mi.getInstance().infixNode_([t,r],e):null}appendLastOperand_(t,e,r){let n=t,s=t.childNodes[t.childNodes.length-1];for(;s&&s.type===ss.INFIXOP&&!Ri(s);)n=s,s=n.childNodes[t.childNodes.length-1];const i=Mi.getInstance().infixNode_([n.childNodes.pop(),r],e);return n.appendChild(i),t}appendMultiplicativeOp_(t,e,r){if(Ri(t))return Mi.getInstance().infixNode_([t,r],e);let n=t,s=t.childNodes[t.childNodes.length-1];for(;s&&s.type===ss.INFIXOP&&!Ri(s);)n=s,s=n.childNodes[t.childNodes.length-1];const i=Mi.getInstance().infixNode_([n.childNodes.pop(),r],e);return n.appendChild(i),t}appendAdditiveOp_(t,e,r){return Mi.getInstance().infixNode_([t,r],e)}appendExistingOperator_(t,e,r){return!(!t||t.type!==ss.INFIXOP||Ri(t))&&(t.contentNodes[0].equals(e)?(t.appendContentNode(e),t.appendChild(r),!0):Mi.getInstance().appendExistingOperator_(t.childNodes[t.childNodes.length-1],e,r))}getFencesInRow_(t){let e=js(t,ai);e=Mi.purgeFences_(e);const r=e.comp.shift();return Mi.getInstance().fences_(e.rel,e.comp,[],[r])}fences_(t,e,r,n){if(0===t.length&&0===r.length)return n[0];const s=wi.run("bracketed_interval",[t[0],t[1],...e[0]||[]],(()=>null));if(s){t.shift(),t.shift(),e.shift();const i=n.pop()||[];return n.push([...i,s,...e.shift()]),Mi.getInstance().fences_(t,e,r,n)}const i=t=>$s(t,os.OPEN);if(0===t.length){const t=n.shift();for(;r.length>0;){if(i(r[0])){const e=r.shift();Mi.fenceToPunct_(e),t.push(e)}else{const e=Gs(r,i),s=e.head.length-1,o=Mi.getInstance().neutralFences_(e.head,n.slice(0,s));n=n.slice(s),t.push(...o),e.div&&e.tail.unshift(e.div),r=e.tail}t.push(...n.shift())}return t}const o=r[r.length-1],a=t[0].role;if(a===os.OPEN||Ci(t[0])&&(!o||!Oi(t[0],o))){r.push(t.shift());const s=e.shift();return s&&n.push(s),Mi.getInstance().fences_(t,e,r,n)}if(o&&a===os.CLOSE&&o.role===os.OPEN){const s=Mi.getInstance().horizontalFencedNode_(r.pop(),t.shift(),n.pop());return n.push(n.pop().concat([s],e.shift())),Mi.getInstance().fences_(t,e,r,n)}if(o&&Oi(t[0],o)){if(!vi(o)||!Si(t[0])){r.push(t.shift());const s=e.shift();return s&&n.push(s),Mi.getInstance().fences_(t,e,r,n)}const s=Mi.getInstance().horizontalFencedNode_(r.pop(),t.shift(),n.pop());return n.push(n.pop().concat([s],e.shift())),Mi.getInstance().fences_(t,e,r,n)}if(o&&a===os.CLOSE&&Ci(o)&&r.some(i)){const s=Gs(r,i,!0),o=n.pop(),a=n.length-s.tail.length+1,c=Mi.getInstance().neutralFences_(s.tail,n.slice(a));n=n.slice(0,a);const l=Mi.getInstance().horizontalFencedNode_(s.div,t.shift(),n.pop().concat(c,o));return n.push(n.pop().concat([l],e.shift())),Mi.getInstance().fences_(t,e,s.head,n)}const c=t.shift();return Mi.fenceToPunct_(c),n.push(n.pop().concat([c],e.shift())),Mi.getInstance().fences_(t,e,r,n)}neutralFences_(t,e){if(0===t.length)return t;if(1===t.length)return Mi.fenceToPunct_(t[0]),t;const r=t.shift();if(!vi(r)){Mi.fenceToPunct_(r);const n=e.shift();return n.unshift(r),n.concat(Mi.getInstance().neutralFences_(t,e))}const n=Gs(t,(function(t){return Oi(t,r)}));if(!n.div){Mi.fenceToPunct_(r);const n=e.shift();return n.unshift(r),n.concat(Mi.getInstance().neutralFences_(t,e))}if(!Si(n.div))return Mi.fenceToPunct_(n.div),t.unshift(r),Mi.getInstance().neutralFences_(t,e);const s=Mi.getInstance().combineFencedContent_(r,n.div,n.head,e);if(n.tail.length>0){const t=s.shift(),e=Mi.getInstance().neutralFences_(n.tail,s);return t.concat(e)}return s[0]}combineFencedContent_(t,e,r,n){if(0===r.length){const r=Mi.getInstance().horizontalFencedNode_(t,e,n.shift());return n.length>0?n[0].unshift(r):n=[[r]],n}const s=n.shift(),i=r.length-1,o=n.slice(0,i),a=(n=n.slice(i)).shift(),c=Mi.getInstance().neutralFences_(r,o);s.push(...c),s.push(...a);const l=Mi.getInstance().horizontalFencedNode_(t,e,s);return n.length>0?n[0].unshift(l):n=[[l]],n}horizontalFencedNode_(t,e,r){const n=Mi.getInstance().row(r);let s=Mi.getInstance().factory_.makeBranchNode(ss.FENCED,[n],[t,e]);return t.role===os.OPEN?(Mi.getInstance().classifyHorizontalFence_(s),s=wi.run("propagateComposedFunction",s)):s.role=t.role,s=wi.run("detect_cycle",s),Mi.rewriteFencedNode_(s)}classifyHorizontalFence_(t){t.role=os.LEFTRIGHT;const e=t.childNodes;if(!function(t){return function(t){return!!t&&-1!==["{","\ufe5b","\uff5b"].indexOf(t.textContent)}(t.contentNodes[0])&&function(t){return!!t&&-1!==["}","\ufe5c","\uff5d"].indexOf(t.textContent)}(t.contentNodes[1])}(t)||e.length>1)return;if(0===e.length||e[0].type===ss.EMPTY)return void(t.role=os.SETEMPTY);const r=e[0].type;if(1===e.length&&Ti(e[0]))return void(t.role=os.SETSINGLE);const n=e[0].role;if(r===ss.PUNCTUATED&&n===os.SEQUENCE){if(e[0].contentNodes[0].role!==os.COMMA)return 1!==e[0].contentNodes.length||e[0].contentNodes[0].role!==os.VBAR&&e[0].contentNodes[0].role!==os.COLON?void 0:(t.role=os.SETEXT,void Mi.getInstance().setExtension_(t));t.role=os.SETCOLLECT}}setExtension_(t){const e=t.childNodes[0].childNodes[0];var r;e&&e.type===ss.INFIXOP&&1===e.contentNodes.length&&(r=e.contentNodes[0],[os.ELEMENT,os.NONELEMENT,os.REELEMENT,os.RENONELEMENT].includes(r.role))&&(e.addAnnotation("set","intensional"),e.contentNodes[0].addAnnotation("set","intensional"))}getPunctuationInRow_(t){if(t.length<=1)return t;const e=t=>{const e=t.type;return"punctuation"===e||"text"===e||"operator"===e||"relation"===e},r=js(t,(function(r){if(!oi(r))return!1;if(oi(r)&&!$s(r,os.ELLIPSIS))return!0;const n=t.indexOf(r);if(0===n)return!t[1]||!e(t[1]);const s=t[n-1];if(n===t.length-1)return!e(s);const i=t[n+1];return!e(s)||!e(i)}));if(0===r.rel.length)return t;let n=[],s=r.comp.shift();s.length>0&&n.push(Mi.getInstance().row(s));let i=0;for(;r.comp.length>0;){let t=[];const e=i;do{t.push(r.rel[i++]),s=r.comp.shift()}while(r.rel[i]&&s&&0===s.length);t=wi.run("ellipses",t),r.rel.splice(e,i-e,...t),i=e+t.length,n=n.concat(t),s&&s.length>0&&n.push(Mi.getInstance().row(s))}return 1===n.length&&1===r.rel.length?n:[Mi.getInstance().punctuatedNode_(n,r.rel)]}punctuatedNode_(t,e){const r=Mi.getInstance().factory_.makeBranchNode(ss.PUNCTUATED,t,e);if(e.length===t.length){const t=e[0].role;if(t!==os.UNKNOWN&&e.every((function(e){return e.role===t})))return r.role=t,r}const n=e[0];return gi(t,e,0)?r.role=n.childNodes.length&&!n.embellished?n.role:os.STARTPUNCT:gi(t,e,t.length-1)?r.role=n.childNodes.length&&!n.embellished?n.role:os.ENDPUNCT:e.every((t=>$s(t,os.DUMMY)))?r.role=os.TEXT:e.every((t=>$s(t,os.SPACE)))?r.role=os.SPACE:r.role=os.SEQUENCE,r}dummyNode_(t){const e=Mi.getInstance().factory_.makeMultipleContentNodes(t.length-1,ds.invisibleComma);return e.forEach((function(t){t.role=os.DUMMY})),Mi.getInstance().punctuatedNode_(t,e)}accentRole_(t,e){if(!Js(t))return!1;const r=t.textContent,n=fs.Secondary.get(r,hs.BAR)||fs.Secondary.get(r,hs.TILDE)||t.role;return t.role=e===ss.UNDERSCORE?os.UNDERACCENT:os.OVERACCENT,t.addAnnotation("accent",n),!0}accentNode_(t,e,r,n,s){const i=(e=e.slice(0,n+1))[1],o=e[2];let a;if(!s&&o&&(a=Mi.getInstance().factory_.makeBranchNode(ss.SUBSCRIPT,[t,i],[]),a.role=os.SUBSUP,e=[a,o],r=ss.SUPERSCRIPT),s){const n=Mi.getInstance().accentRole_(i,r);if(o){Mi.getInstance().accentRole_(o,ss.OVERSCORE)&&!n?(a=Mi.getInstance().factory_.makeBranchNode(ss.OVERSCORE,[t,o],[]),e=[a,i],r=ss.UNDERSCORE):(a=Mi.getInstance().factory_.makeBranchNode(ss.UNDERSCORE,[t,i],[]),e=[a,o],r=ss.OVERSCORE),a.role=os.UNDEROVER}}return Mi.getInstance().makeLimitNode_(t,e,a,r)}makeLimitNode_(t,e,r,n){if(n===ss.LIMUPPER&&t.type===ss.LIMLOWER)return t.childNodes.push(e[1]),e[1].parent=t,t.type=ss.LIMBOTH,t;if(n===ss.LIMLOWER&&t.type===ss.LIMUPPER)return t.childNodes.splice(1,-1,e[1]),e[1].parent=t,t.type=ss.LIMBOTH,t;const s=Mi.getInstance().factory_.makeBranchNode(n,e,[]),i=ni(t);return r&&(r.embellished=i),s.embellished=i,s.role=t.role,s}getFunctionsInRow_(t,e){const r=e||[];if(0===t.length)return r;const n=t.shift(),s=Mi.classifyFunction_(n,t);if(!s)return r.push(n),Mi.getInstance().getFunctionsInRow_(t,r);const i=Mi.getInstance().getFunctionsInRow_(t,[]),o=Mi.getInstance().getFunctionArgs_(n,i,s);return r.concat(o)}getFunctionArgs_(t,e,r){let n,s,i;switch(r){case"integral":{const r=Mi.getInstance().getIntegralArgs_(e);if(!r.intvar&&!r.integrand.length)return r.rest.unshift(t),r.rest;const n=Mi.getInstance().row(r.integrand);return i=Mi.getInstance().integralNode_(t,n,r.intvar),wi.run("intvar_from_fraction",i),r.rest.unshift(i),r.rest}case"prefix":if(e[0]&&e[0].type===ss.FENCED){const r=e.shift();return Ci(r)||(r.role=os.LEFTRIGHT),i=Mi.getInstance().functionNode_(t,r),e.unshift(i),e}if(n=Gs(e,Qs),n.head.length)s=Mi.getInstance().row(n.head),n.div&&n.tail.unshift(n.div);else{if(!n.div||!zs(n.div,ss.APPL))return e.unshift(t),e;s=n.div}return i=Mi.getInstance().functionNode_(t,s),n.tail.unshift(i),n.tail;case"bigop":return n=Gs(e,Zs),n.head.length?(s=Mi.getInstance().row(n.head),i=Mi.getInstance().bigOpNode_(t,s),n.div&&n.tail.unshift(n.div),n.tail.unshift(i),n.tail):(e.unshift(t),e);default:{if(0===e.length)return[t];const r=e[0];return r.type===ss.FENCED&&!Ci(r)&&function(t){const e=t.childNodes;if(0===e.length)return!0;if(e.length>1)return!1;const r=e[0];if(r.type===ss.INFIXOP){if(r.role!==os.IMPLICIT)return!1;if(r.childNodes.some((t=>zs(t,ss.INFIXOP))))return!1}return!0}(r)?(r.role=os.LEFTRIGHT,Mi.propagateFunctionRole_(t,os.SIMPLEFUNC),i=Mi.getInstance().functionNode_(t,e.shift()),e.unshift(i),e):(e.unshift(t),e)}}}getIntegralArgs_(t,e=[]){if(0===t.length){const t=Gs(e,Zs);return t.div&&t.tail.unshift(t.div),{integrand:t.head,intvar:null,rest:t.tail}}wi.run("intvar_from_implicit",t);const r=t[0];if(ri(r)){const{integrand:r,rest:n}=Mi.getInstance().getIntegralArgs_(e);return{integrand:r,intvar:null,rest:n.concat(t)}}if(ei(r))return r.role=os.INTEGRAL,{integrand:e,intvar:r,rest:t.slice(1)};if(t[1]&&ti(r,t[1])){const n=Mi.getInstance().prefixNode_(t[1],[r]);return n.role=os.INTEGRAL,{integrand:e,intvar:n,rest:t.slice(2)}}return e.push(t.shift()),Mi.getInstance().getIntegralArgs_(t,e)}functionNode_(t,e){const r=Mi.getInstance().factory_.makeContentNode(ds.functionApplication),n=Mi.getInstance().funcAppls[t.id];n&&(r.mathmlTree=n.mathmlTree,r.mathml=n.mathml,r.annotation=n.annotation,r.attributes=n.attributes,delete Mi.getInstance().funcAppls[t.id]),r.type=ss.PUNCTUATION,r.role=os.APPLICATION;const s=Mi.getFunctionOp_(t,(function(t){return zs(t,ss.FUNCTION)||zs(t,ss.IDENTIFIER)&&$s(t,os.SIMPLEFUNC)}));return Mi.getInstance().functionalNode_(ss.APPL,[t,e],s,[r])}bigOpNode_(t,e){const r=Mi.getFunctionOp_(t,(t=>zs(t,ss.LARGEOP)));return Mi.getInstance().functionalNode_(ss.BIGOP,[t,e],r,[])}integralNode_(t,e,r){e=e||Mi.getInstance().factory_.makeEmptyNode(),r=r||Mi.getInstance().factory_.makeEmptyNode();const n=Mi.getFunctionOp_(t,(t=>zs(t,ss.LARGEOP)));return Mi.getInstance().functionalNode_(ss.INTEGRAL,[t,e,r],n,[])}functionalNode_(t,e,r,n){const s=e[0];let i;r&&(i=r.parent,n.push(r));const o=Mi.getInstance().factory_.makeBranchNode(t,e,n);return o.role=s.role,i&&(r.parent=i),o}fractionNode_(t,e){const r=Mi.getInstance().factory_.makeBranchNode(ss.FRACTION,[t,e],[]);return r.role=r.childNodes.every((function(t){return zs(t,ss.NUMBER)&&$s(t,os.INTEGER)}))?os.VULGAR:r.childNodes.every(Ii)?os.UNIT:os.DIVISION,wi.run("propagateSimpleFunction",r)}scriptNode_(t,e,r){let n;switch(t.length){case 0:n=Mi.getInstance().factory_.makeEmptyNode();break;case 1:if(n=t[0],r)return n;break;default:n=Mi.getInstance().dummyNode_(t)}return n.role=e,n}findNestedRow_(t,e,r,n){if(r>3)return null;for(let s,i=0;s=t[i];i++){const t=Xn(s);if(t!==vs.MSPACE){if(t===vs.MROW)return Mi.getInstance().findNestedRow_(Bn(s.childNodes),e,r+1,n);if(Mi.findSemantics(s,e,n))return s}}return null}}Mi.FENCE_TO_PUNCT_={[os.METRIC]:os.METRIC,[os.NEUTRAL]:os.VBAR,[os.OPEN]:os.OPENFENCE,[os.CLOSE]:os.CLOSEFENCE},Mi.MML_TO_LIMIT_={[vs.MSUB]:{type:ss.LIMLOWER,length:1},[vs.MUNDER]:{type:ss.LIMLOWER,length:1},[vs.MSUP]:{type:ss.LIMUPPER,length:1},[vs.MOVER]:{type:ss.LIMUPPER,length:1},[vs.MSUBSUP]:{type:ss.LIMBOTH,length:2},[vs.MUNDEROVER]:{type:ss.LIMBOTH,length:2}},Mi.MML_TO_BOUNDS_={[vs.MSUB]:{type:ss.SUBSCRIPT,length:1,accent:!1},[vs.MSUP]:{type:ss.SUPERSCRIPT,length:1,accent:!1},[vs.MSUBSUP]:{type:ss.SUBSCRIPT,length:2,accent:!1},[vs.MUNDER]:{type:ss.UNDERSCORE,length:1,accent:!0},[vs.MOVER]:{type:ss.OVERSCORE,length:1,accent:!0},[vs.MUNDEROVER]:{type:ss.UNDERSCORE,length:2,accent:!0}},Mi.CLASSIFY_FUNCTION_={[os.INTEGRAL]:"integral",[os.SUM]:"bigop",[os.PREFIXFUNC]:"prefix",[os.LIMFUNC]:"prefix",[os.SIMPLEFUNC]:"prefix",[os.COMPFUNC]:"prefix"},Mi.MATHJAX_FONTS={"-tex-caligraphic":cs.CALIGRAPHIC,"-tex-caligraphic-bold":cs.CALIGRAPHICBOLD,"-tex-calligraphic":cs.CALIGRAPHIC,"-tex-calligraphic-bold":cs.CALIGRAPHICBOLD,"-tex-oldstyle":cs.OLDSTYLE,"-tex-oldstyle-bold":cs.OLDSTYLEBOLD,"-tex-mathit":cs.ITALIC};class xi extends Ks{static getAttribute_(t,e,r){if(!t.hasAttribute(e))return r;const n=t.getAttribute(e);return n.match(/^\s*$/)?null:n}constructor(t){super("MathML"),this.options=t,wi.options=t,this.parseMap_=new Map([[vs.SEMANTICS,this.semantics_.bind(this)],[vs.MATH,this.rows_.bind(this)],[vs.MROW,this.rows_.bind(this)],[vs.MPADDED,this.rows_.bind(this)],[vs.MSTYLE,this.rows_.bind(this)],[vs.MFRAC,this.fraction_.bind(this)],[vs.MSUB,this.limits_.bind(this)],[vs.MSUP,this.limits_.bind(this)],[vs.MSUBSUP,this.limits_.bind(this)],[vs.MOVER,this.limits_.bind(this)],[vs.MUNDER,this.limits_.bind(this)],[vs.MUNDEROVER,this.limits_.bind(this)],[vs.MROOT,this.root_.bind(this)],[vs.MSQRT,this.sqrt_.bind(this)],[vs.MTABLE,this.table_.bind(this)],[vs.MLABELEDTR,this.tableLabeledRow_.bind(this)],[vs.MTR,this.tableRow_.bind(this)],[vs.MTD,this.tableCell_.bind(this)],[vs.MS,this.text_.bind(this)],[vs.MTEXT,this.text_.bind(this)],[vs.MSPACE,this.space_.bind(this)],[vs.ANNOTATIONXML,this.text_.bind(this)],[vs.MI,this.identifier_.bind(this)],[vs.MN,this.number_.bind(this)],[vs.MO,this.operator_.bind(this)],[vs.MFENCED,this.fenced_.bind(this)],[vs.MENCLOSE,this.enclosed_.bind(this)],[vs.MMULTISCRIPTS,this.multiscripts_.bind(this)],[vs.ANNOTATION,this.empty_.bind(this)],[vs.NONE,this.empty_.bind(this)],[vs.MACTION,this.action_.bind(this)]]);const e={type:ss.IDENTIFIER,role:os.NUMBERSET,font:cs.DOUBLESTRUCK};["C","H","N","P","Q","R","Z","\u2102","\u210d","\u2115","\u2119","\u211a","\u211d","\u2124"].forEach((t=>this.getFactory().defaultMap.set(t,e)).bind(this))}parse(t){Mi.getInstance().setNodeFactory(this.getFactory());const e=Bn(t.childNodes),r=Xn(t),n=this.parseMap_.get(r),s=(n||this.dummy_.bind(this))(t,e);return qs(s,t),-1!==[vs.MATH,vs.MROW,vs.MPADDED,vs.MSTYLE,vs.SEMANTICS,vs.MACTION].indexOf(r)||(s.mathml.unshift(t),s.mathmlTree=t),s}semantics_(t,e){return e.length?this.parse(e[0]):this.getFactory().makeEmptyNode()}rows_(t,e){const r=t.getAttribute("semantics");if(r&&r.match("bspr_"))return Mi.proof(t,r,this.parseList.bind(this));let n;if(1===(e=Us(e)).length)n=this.parse(e[0]),n.type!==ss.EMPTY||n.mathmlTree||(n.mathmlTree=t);else{const r=wi.run("function_from_identifiers",t);n=r&&r!==t?r:Mi.getInstance().row(this.parseList(e))}return n.mathml.unshift(t),n}fraction_(t,e){if(!e.length)return this.getFactory().makeEmptyNode();const r=this.parse(e[0]),n=e[1]?this.parse(e[1]):this.getFactory().makeEmptyNode();return Mi.getInstance().fractionLikeNode(r,n,t.getAttribute("linethickness"),"true"===t.getAttribute("bevelled"))}limits_(t,e){return Mi.getInstance().limitNode(Xn(t),this.parseList(e))}root_(t,e){return e[1]?this.getFactory().makeBranchNode(ss.ROOT,[this.parse(e[1]),this.parse(e[0])],[]):this.sqrt_(t,e)}sqrt_(t,e){const r=this.parseList(Us(e));return this.getFactory().makeBranchNode(ss.SQRT,[Mi.getInstance().row(r)],[])}table_(t,e){const r=t.getAttribute("semantics");if(r&&r.match("bspr_"))return Mi.proof(t,r,this.parseList.bind(this));const n=this.getFactory().makeBranchNode(ss.TABLE,this.parseList(e),[]);return n.mathmlTree=t,Mi.tableToMultiline(n)}tableRow_(t,e){const r=this.getFactory().makeBranchNode(ss.ROW,this.parseList(e),[]);return r.role=os.TABLE,r}tableLabeledRow_(t,e){var r;if(!e.length)return this.tableRow_(t,e);const n=this.parse(e[0]);n.role=os.LABEL,(null===(r=n.childNodes[0])||void 0===r?void 0:r.type)===ss.TEXT&&(n.childNodes[0].role=os.LABEL);const s=this.getFactory().makeBranchNode(ss.ROW,this.parseList(e.slice(1)),[n]);return s.role=os.TABLE,s}tableCell_(t,e){const r=this.parseList(Us(e));let n;n=r.length?1===r.length&&zs(r[0],ss.EMPTY)?r:[Mi.getInstance().row(r)]:[];const s=this.getFactory().makeBranchNode(ss.CELL,n,[]);return s.role=os.TABLE,s}space_(t,e){const r=t.getAttribute("width"),n=r&&r.match(/[a-z]*$/);if(!n)return this.empty_(t,e);const s=n[0],i=parseFloat(r.slice(0,n.index)),o={cm:.4,pc:.5,em:.5,ex:1,in:.15,pt:5,mm:5}[s];if(!o||isNaN(i)||i<o)return this.empty_(t,e);const a=this.getFactory().makeUnprocessed(t);return Mi.getInstance().text(a,Xn(t))}text_(t,e){const r=this.leaf_(t,e);return t.textContent?(r.updateContent(t.textContent,!0),Mi.getInstance().text(r,Xn(t))):r}identifier_(t,e){const r=this.leaf_(t,e);return Mi.getInstance().identifierNode(r,Mi.getInstance().font(t.getAttribute("mathvariant")),t.getAttribute("class"))}number_(t,e){const r=this.leaf_(t,e);return Mi.number(r),r}operator_(t,e){const r=this.leaf_(t,e);return Mi.getInstance().operatorNode(r),r}fenced_(t,e){const r=this.parseList(Us(e)),n=xi.getAttribute_(t,"separators",","),s=xi.getAttribute_(t,"open","("),i=xi.getAttribute_(t,"close",")"),o=Mi.getInstance().mfenced(s,i,n,r);return Mi.getInstance().tablesInRow([o])[0]}enclosed_(t,e){const r=this.parseList(Us(e)),n=this.getFactory().makeBranchNode(ss.ENCLOSE,[Mi.getInstance().row(r)],[]);return n.role=t.getAttribute("notation")||os.UNKNOWN,n}multiscripts_(t,e){if(!e.length)return this.getFactory().makeEmptyNode();const r=this.parse(e.shift());if(!e.length)return r;const n=[],s=[],i=[],o=[];let a=!1,c=0;for(let t,r=0;t=e[r];r++)Xn(t)!==vs.MPRESCRIPTS?(a?1&c?n.push(t):s.push(t):1&c?i.push(t):o.push(t),c++):(a=!0,c=0);return Us(n).length||Us(s).length?Mi.getInstance().tensor(r,this.parseList(s),this.parseList(n),this.parseList(o),this.parseList(i)):Mi.getInstance().pseudoTensor(r,this.parseList(o),this.parseList(i))}empty_(t,e){return this.getFactory().makeEmptyNode()}action_(t,e){const r=e[t.hasAttribute("selection")?parseInt(t.getAttribute("selection"),10)-1:0],n=this.parse(r);return n.mathmlTree=r,n}dummy_(t,e){const r=this.getFactory().makeUnprocessed(t);return r.role=t.tagName,r.textContent=t.textContent,r}leaf_(t,e){if(1===e.length&&e[0].nodeType!==qn.TEXT_NODE){const r=this.getFactory().makeUnprocessed(t);return r.role=e[0].tagName,qs(r,e[0]),r}const r=this.getFactory().makeLeafNode(t.textContent,Mi.getInstance().font(t.getAttribute("mathvariant")));return t.hasAttribute("data-latex")&&fs.LatexCommands.set(t.getAttribute("data-latex"),t.textContent),r}}class Pi{constructor(t,e,r=t=>!1){this.name=t,this.apply=e,this.applicable=r}}class Di extends Pi{}class ki extends Pi{}function _i(t,e){const r=[];for(;t.length||e.length;)t.length&&r.push(t.shift()),e.length&&r.push(e.shift());return r}const Fi="data-semantic-";var Bi;!function(t){t.ADDED="data-semantic-added",t.ALTERNATIVE="data-semantic-alternative",t.CHILDREN="data-semantic-children",t.COLLAPSED="data-semantic-collapsed",t.CONTENT="data-semantic-content",t.EMBELLISHED="data-semantic-embellished",t.FENCEPOINTER="data-semantic-fencepointer",t.FONT="data-semantic-font",t.ID="data-semantic-id",t.ANNOTATION="data-semantic-annotation",t.ATTRIBUTES="data-semantic-attributes",t.OPERATOR="data-semantic-operator",t.OWNS="data-semantic-owns",t.PARENT="data-semantic-parent",t.POSTFIX="data-semantic-postfix",t.PREFIX="data-semantic-prefix",t.ROLE="data-semantic-role",t.SPEECH="data-semantic-speech",t.STRUCTURE="data-semantic-structure",t.SUMMARY="data-semantic-summary",t.TYPE="data-semantic-type"}(Bi||(Bi={}));const Ui=[Bi.ADDED,Bi.ALTERNATIVE,Bi.CHILDREN,Bi.COLLAPSED,Bi.CONTENT,Bi.EMBELLISHED,Bi.FENCEPOINTER,Bi.FONT,Bi.ID,Bi.ANNOTATION,Bi.ATTRIBUTES,Bi.OPERATOR,Bi.OWNS,Bi.PARENT,Bi.POSTFIX,Bi.PREFIX,Bi.ROLE,Bi.SPEECH,Bi.STRUCTURE,Bi.SUMMARY,Bi.TYPE];function qi(t){return t.map((function(t){return t.id})).join(",")}function Hi(t,e){t.setAttribute(Bi.TYPE,e.type);const r=e.allAttributes();for(let e,n=0;e=r[n];n++)t.setAttribute(Fi+e[0].toLowerCase(),e[1]);e.childNodes.length&&t.setAttribute(Bi.CHILDREN,qi(e.childNodes)),e.contentNodes.length&&t.setAttribute(Bi.CONTENT,qi(e.contentNodes)),e.parent&&t.setAttribute(Bi.PARENT,e.parent.id.toString());const n=e.attributesXml();n&&t.setAttribute(Bi.ATTRIBUTES,n),function(t,e){const r=[];e.role===os.MGLYPH&&r.push("image");e.attributes.href&&r.push("link");r.length&&t.setAttribute(Bi.POSTFIX,r.join(" "))}(t,e)}function Gi(){const t=Gn("mrow");return t.setAttribute(Bi.ADDED,"true"),t}class ji{static fromTree(t){return ji.fromNode(t.root)}static fromNode(t){return new ji(ji.fromNode_(t))}static fromString(t){return new ji(ji.fromString_(t))}static simpleCollapseStructure(t){return"number"==typeof t}static contentCollapseStructure(t){return!!t&&!ji.simpleCollapseStructure(t)&&"c"===t[0]}static interleaveIds(t,e){return _i(ji.collapsedLeafs(t),ji.collapsedLeafs(e))}static collapsedLeafs(...t){return t.reduce(((t,e)=>{return t.concat((r=e,ji.simpleCollapseStructure(r)?[r]:ji.contentCollapseStructure(r[1])?r.slice(2):r.slice(1)));var r}),[])}static fromStructure(t,e,r){return new ji(ji.tree_(t,e.root,r))}static combineContentChildren(t,e,r,n){switch(t){case ss.RELSEQ:case ss.INFIXOP:case ss.MULTIREL:return _i(n,r);case ss.PREFIXOP:return r.concat(n);case ss.POSTFIXOP:return n.concat(r);case ss.MATRIX:case ss.VECTOR:case ss.FENCED:return n.unshift(r[0]),n.push(r[1]),n;case ss.CASES:return n.unshift(r[0]),n;case ss.APPL:return[n[0],r[0],n[1]];case ss.ROOT:return[n[0],n[1]];case ss.ROW:case ss.LINE:return r.length&&n.unshift(r[0]),n;default:return n}}static makeSexp_(t){return ji.simpleCollapseStructure(t)?t.toString():ji.contentCollapseStructure(t)?"(c "+t.slice(1).map(ji.makeSexp_).join(" ")+")":"("+t.map(ji.makeSexp_).join(" ")+")"}static fromString_(t){let e=t.replace(/\(/g,"[");return e=e.replace(/\)/g,"]"),e=e.replace(/ /g,","),e=e.replace(/c/g,'"c"'),JSON.parse(e)}static fromNode_(t){if(!t)return[];const e=t.contentNodes;let r;e.length&&(r=e.map(ji.fromNode_),r.unshift("c"));const n=t.childNodes;if(!n.length)return e.length?[t.id,r]:t.id;const s=n.map(ji.fromNode_);return e.length&&s.unshift(r),s.unshift(t.id),s}static tree_(t,e,r,n=0,s=1,i=1){if(!e)return[];const o=e.id,a=[o];!function(t){if(Mn.getInstance().mode!==fn.HTTP)return;let e=t;for(;e&&!e.evaluate;)e=e.parentNode;e&&e.evaluate?Pn.currentDocument=e:t.ownerDocument&&(Pn.currentDocument=t.ownerDocument)}(t);const c=function(t,e){let r;try{r=Fn(t,e,Pn.result.ORDERED_NODE_ITERATOR_TYPE)}catch(t){return[]}const n=[];for(let t=r.iterateNext();t;t=r.iterateNext())n.push(t);return n}(`.//self::*[@${Bi.ID}=${o}]`,t)[0];if(!e.childNodes.length)return ji.addAria(c,n,s,i,r),e.id;const l=ji.combineContentChildren(e.type,e.role,e.contentNodes.map((function(t){return t})),e.childNodes.map((function(t){return t})));c&&ji.addOwns_(c,l);for(let e,s=0,i=l.length;e=l[s];s++)a.push(ji.tree_(t,e,r,n+1,s+1,i));return ji.addAria(c,n,s,i,r),a}static addAria(t,e,r,n,s){const i=s.tree?e?"treeitem":"tree":"treeitem";s.aria&&t&&(t.setAttribute("aria-level",e.toString()),t.setAttribute("aria-posinset",r.toString()),t.setAttribute("aria-setsize",n.toString()),t.setAttribute("role",i),t.hasAttribute(Bi.OWNS)&&t.setAttribute("aria-owns",t.getAttribute(Bi.OWNS)))}static addOwns_(t,e){const r=t.getAttribute(Bi.COLLAPSED),n=r?ji.realLeafs_(ji.fromString(r).array):e.map((t=>t.id));t.setAttribute(Bi.OWNS,n.join(" "))}static realLeafs_(t){if(ji.simpleCollapseStructure(t))return[t];if(ji.contentCollapseStructure(t))return[];let e=[];for(let r=1;r<t.length;r++)e=e.concat(ji.realLeafs_(t[r]));return e}constructor(t){this.parents=null,this.levelsMap=null,t=0===t?t:t||[],this.array=t}populate(){this.parents&&this.levelsMap||(this.parents={},this.levelsMap={},this.populate_(this.array,this.array,[]))}toString(){return ji.makeSexp_(this.array)}populate_(t,e,r){if(ji.simpleCollapseStructure(t))return this.levelsMap[t]=e,void(this.parents[t]=t===r[0]?r.slice(1):r);const n=ji.contentCollapseStructure(t)?t.slice(1):t,s=[n[0]].concat(r);for(let e=0,r=n.length;e<r;e++){const r=n[e];this.populate_(r,t,s)}}isRoot(t){return t===this.levelsMap[t][0]}directChildren(t){if(!this.isRoot(t))return[];return this.levelsMap[t].slice(1).map((t=>ji.simpleCollapseStructure(t)?t:ji.contentCollapseStructure(t)?t[1]:t[0]))}subtreeNodes(t){if(!this.isRoot(t))return[];const e=(t,r)=>{ji.simpleCollapseStructure(t)?r.push(t):(ji.contentCollapseStructure(t)&&(t=t.slice(1)),t.forEach((t=>e(t,r))))},r=this.levelsMap[t],n=[];return e(r.slice(1),n),n}}function Xi(t,e,r){let n=null;if(!t.length)return n;const s=r[r.length-1],i=s&&s.length,o=e&&e.length,a=Mi.getInstance();if(i&&o){if(e[0].type===ss.INFIXOP&&e[0].role===os.IMPLICIT)return n=t.pop(),s.push(a.postfixNode_(s.pop(),t)),n;n=t.shift();const r=a.prefixNode_(e.shift(),t);return e.unshift(r),n}return i?(s.push(a.postfixNode_(s.pop(),t)),n):(o&&e.unshift(a.prefixNode_(e.shift(),t)),n)}function Vi(t,e,r){if(!e.length)return t;const n=t.pop(),s=e.shift(),i=r.shift();if(s.type===ss.INFIXOP&&(s.role===os.IMPLICIT||s.role===os.UNIT)){Ln.getInstance().output("Juxta Heuristic Case 2");const o=(n?[n,s]:[s]).concat(i);return Vi(t.concat(o),e,r)}if(!n)return Ln.getInstance().output("Juxta Heuristic Case 3"),Vi([s].concat(i),e,r);const o=i.shift();if(!o){Ln.getInstance().output("Juxta Heuristic Case 9");const i=wi.factory.makeBranchNode(ss.INFIXOP,[n,e.shift()],[s],s.textContent);return i.role=os.IMPLICIT,wi.run("combine_juxtaposition",i),e.unshift(i),Vi(t,e,r)}if(si(n)||si(o))return Ln.getInstance().output("Juxta Heuristic Case 4"),Vi(t.concat([n,s,o]).concat(i),e,r);let a=null;return Li(n)&&Li(o)?(Ln.getInstance().output("Juxta Heuristic Case 5"),n.contentNodes.push(s),n.contentNodes=n.contentNodes.concat(o.contentNodes),n.childNodes.push(o),n.childNodes=n.childNodes.concat(o.childNodes),o.childNodes.forEach((t=>t.parent=n)),s.parent=n,n.addMathmlNodes(s.mathml),n.addMathmlNodes(o.mathml),a=n):Li(n)?(Ln.getInstance().output("Juxta Heuristic Case 6"),n.contentNodes.push(s),n.childNodes.push(o),o.parent=n,s.parent=n,n.addMathmlNodes(s.mathml),n.addMathmlNodes(o.mathml),a=n):Li(o)?(Ln.getInstance().output("Juxta Heuristic Case 7"),o.contentNodes.unshift(s),o.childNodes.unshift(n),n.parent=o,s.parent=o,o.addMathmlNodes(s.mathml),o.addMathmlNodes(n.mathml),a=o):(Ln.getInstance().output("Juxta Heuristic Case 8"),a=wi.factory.makeBranchNode(ss.INFIXOP,[n,o],[s],s.textContent),a.role=os.IMPLICIT),t.push(a),Vi(t.concat(i),e,r)}function Wi(t){return t.childNodes[0]&&t.childNodes[0].childNodes[0]&&Xn(t.childNodes[0])===vs.MPADDED&&Xn(t.childNodes[0].childNodes[0])===vs.MPADDED&&Xn(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1])===vs.MPHANTOM}wi.add(new Di("combine_juxtaposition",(function(t){for(let e,r=t.childNodes.length-1;e=t.childNodes[r];r--)Li(e)&&!e.nobreaking&&(t.childNodes.splice(r,1,...e.childNodes),t.contentNodes.splice(r,0,...e.contentNodes),e.childNodes.concat(e.contentNodes).forEach((function(e){e.parent=t})),t.addMathmlNodes(e.mathml));return t}))),wi.add(new Di("propagateSimpleFunction",(t=>(t.type!==ss.INFIXOP&&t.type!==ss.FRACTION||!t.childNodes.every(Ei)||(t.role=os.COMPFUNC),t)),(t=>"clearspeak"===wi.options.domain))),wi.add(new Di("simpleNamedFunction",(t=>(t.role!==os.UNIT&&-1!==["f","g","h","F","G","H"].indexOf(t.textContent)&&(t.role=os.SIMPLEFUNC),t)),(t=>"clearspeak"===wi.options.domain))),wi.add(new Di("propagateComposedFunction",(t=>(t.type===ss.FENCED&&t.childNodes[0].role===os.COMPFUNC&&(t.role=os.COMPFUNC),t)),(t=>"clearspeak"===wi.options.domain))),wi.add(new Di("multioperator",(t=>{t.role!==os.UNKNOWN||t.textContent.length<=1||(Mi.compSemantics(t,"role",os),Mi.compSemantics(t,"type",ss))}))),wi.add(new ki("convert_juxtaposition",(t=>{let e=js(t,(function(t){return t.textContent===ds.invisibleTimes&&t.type===ss.OPERATOR}));e=e.rel.length?function(t){const e=[],r=[];let n=t.comp.shift(),s=null,i=[];for(;t.comp.length;)if(i=[],n.length)s&&e.push(s),r.push(n),s=t.rel.shift(),n=t.comp.shift();else{for(s&&i.push(s);!n.length&&t.comp.length;)n=t.comp.shift(),i.push(t.rel.shift());s=Xi(i,n,r)}i.length||n.length?(e.push(s),r.push(n)):(i.push(s),Xi(i,n,r));return{rel:e,comp:r}}(e):e,t=e.comp[0];for(let r,n,s=1;r=e.comp[s],n=e.rel[s-1];s++)t.push(n),t=t.concat(r);return e=js(t,(function(t){return t.textContent===ds.invisibleTimes&&(t.type===ss.OPERATOR||t.type===ss.INFIXOP)})),e.rel.length?Vi(e.comp.shift(),e.rel,e.comp):t}))),wi.add(new Di("simple2prefix",(t=>(t.textContent.length>1&&!t.textContent[0].match(/[A-Z]/)&&(t.role=os.PREFIXFUNC),t)),(t=>"braille"===wi.options.modality&&t.type===ss.IDENTIFIER))),wi.add(new Di("detect_cycle",(t=>{t.type=ss.MATRIX,t.role=os.CYCLE;const e=t.childNodes[0];return e.type=ss.ROW,e.role=os.CYCLE,e.textContent="",e.contentNodes=[],t}),(t=>t.type===ss.FENCED&&t.childNodes[0].type===ss.INFIXOP&&t.childNodes[0].role===os.IMPLICIT&&t.childNodes[0].childNodes.every((function(t){return t.type===ss.NUMBER}))&&t.childNodes[0].contentNodes.every((function(t){return t.role===os.SPACE}))))),wi.add(new ki("intvar_from_implicit",(function(t){const e=t[0].childNodes;t.splice(0,1,...e)}),(t=>t[0]&&Ri(t[0])))),wi.add(new Di("intvar_from_fraction",(function(t){const e=t.childNodes[1],r=e.childNodes[0];if(ei(r))return void(r.role=os.INTEGRAL);if(!Ri(r))return;const n=r.childNodes.length,s=r.childNodes[n-2],i=r.childNodes[n-1];if(ei(i))return void(i.role=os.INTEGRAL);if(ti(s,i)){const t=Mi.getInstance().prefixNode_(i,[s]);t.role=os.INTEGRAL,2===n?e.childNodes[0]=t:(r.childNodes.pop(),r.contentNodes.pop(),r.childNodes[n-2]=t,t.parent=r)}}),(t=>{if(t.type!==ss.INTEGRAL)return!1;const[,e,r]=t.childNodes;return r.type===ss.EMPTY&&e.type===ss.FRACTION}))),wi.add(new Di("rewrite_subcases",(function(t){t.addAnnotation("Emph","top");let e=[];if(t.hasAnnotation("Emph","left")){const r=zi(t.childNodes[0].childNodes[0].childNodes[0],!0);r.forEach((t=>t.addAnnotation("Emph","left"))),e=e.concat(r);for(let e,r=0;e=t.childNodes[r];r++)e.childNodes.shift()}if(e.push(t),t.hasAnnotation("Emph","right")){const r=zi(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1].childNodes[0]);r.forEach((t=>t.addAnnotation("Emph","left"))),e=e.concat(r),t.childNodes[0].childNodes.pop()}Mi.tableToMultiline(t);const r=Mi.getInstance().row(e),n=t.annotation.Emph;return t.annotation.Emph=["table"],n.forEach((t=>r.addAnnotation("Emph",t))),r}),(t=>{let e=!0,r=!0;if(Wi(t.childNodes[0].childNodes[0].mathmlTree)){for(let r,n=1;r=t.childNodes[n];n++)if(r.childNodes[0].childNodes.length){e=!1;break}}else e=!1;e&&t.addAnnotation("Emph","left");if(Wi(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1].mathmlTree)){const e=t.childNodes[0].childNodes.length;for(let n,s=1;n=t.childNodes[s];s++)if(n.childNodes.length>=e){r=!1;break}}else r=!1;return r&&t.addAnnotation("Emph","right"),e||r})));const Ki=[ss.PUNCTUATED,ss.RELSEQ,ss.MULTIREL,ss.INFIXOP,ss.PREFIXOP,ss.POSTFIXOP];function zi(t,e){if(!t.childNodes.length)return $i(t),[t];let r=null;if(t.type===ss.PUNCTUATED&&(e?t.role===os.ENDPUNCT:t.role===os.STARTPUNCT)){const n=t.childNodes;$i(n[e?n.length-1:0])&&(t=n[e?0:n.length-1],r=n[e?n.length-1:0])}if(-1!==Ki.indexOf(t.type)){const n=t.childNodes;$i(n[e?n.length-1:0]);const s=ji.combineContentChildren(t.type,t.role,t.contentNodes,t.childNodes);return r&&(e?s.push(r):s.unshift(r)),s}return r?e?[t,r]:[r,t]:[t]}const Yi={[os.METRIC]:os.METRIC,[os.VBAR]:os.NEUTRAL,[os.OPENFENCE]:os.OPEN,[os.CLOSEFENCE]:os.CLOSE};function $i(t){if(t.type!==ss.PUNCTUATION)return!1;const e=Yi[t.role];return!!e&&(t.role=e,t.type=ss.FENCE,t.addAnnotation("Emph","fence"),!0)}function Ji(t,e,r,n=r){const s=[];for(;t&&t.role===r;)s.push(t),t=e.shift();return s.length?(t&&e.unshift(t),[1===s.length?s[0]:Qi(s,n),e]):[t,e]}function Qi(t,e){const r=wi.factory.makeBranchNode(ss.PUNCTUATION,t,[]);return r.role=e,r}wi.add(new ki("ellipses",(t=>{const e=[];let r=t.shift();for(;r;)[r,t]=Ji(r,t,os.FULLSTOP,os.ELLIPSIS),[r,t]=Ji(r,t,os.DASH),e.push(r),r=t.shift();return e}),(t=>t.length>1))),wi.add(new ki("op_with_limits",(t=>{const e=t[0];return e.type=ss.LARGEOP,e.role=os.SUM,t}),(t=>t[0].type===ss.OPERATOR&&t.slice(1).some((t=>t.type===ss.RELSEQ||t.type===ss.MULTIREL||t.type===ss.INFIXOP&&t.role===os.ELEMENT||t.type===ss.PUNCTUATED&&t.role===os.SEQUENCE))))),wi.add(new ki("bracketed_interval",(t=>{const e=t[0],r=t[1],n=t.slice(2),s=Mi.getInstance().row(n),i=wi.factory.makeBranchNode(ss.FENCED,[s],[e,r]);return i.role=os.LEFTRIGHT,i}),(t=>{const e=t[0],r=t[1],n=t.slice(2);if(!e||"]"!==e.textContent&&"["!==e.textContent||!r||"]"!==r.textContent&&"["!==r.textContent)return!1;const s=js(n,oi);return!(1!==s.rel.length||!s.comp[0].length||!s.comp[1].length)}))),wi.add(new class extends Pi{}("function_from_identifiers",(t=>{const e=Bn(t.childNodes).map((t=>t.textContent.trim())).join("");if(fs.Meaning.get(e).type===ss.UNKNOWN)return t;const r=wi.factory.makeLeafNode(e,Mi.getInstance().font(t.getAttribute("mathvariant")));return r.mathmlTree=t,r}),(t=>{const e=Bn(t.childNodes);return!(e.length<2)&&e.every((t=>Xn(t)===vs.MI&&fs.Meaning.get(t.textContent.trim()).role===os.LATINLETTER))})));class Zi{static empty(){const t=Un("<math/>"),e=new Zi(t,new On);return e.mathml=t,e}static fromNode(t,e){const r=Zi.empty();return r.root=t,e&&(r.mathml=e),r}static fromRoot(t,e){let r=t;for(;r.parent;)r=r.parent;const n=Zi.fromNode(r);return e&&(n.mathml=e),n}static fromXml(t){const e=Zi.empty();return t.childNodes[0]&&(e.root=Vs.fromXml(t.childNodes[0])),e}constructor(t,e){this.mathml=t,this.options=e,this.parser=new xi(e),this.root=this.parser.parse(t),this.collator=this.parser.getFactory().leafMap.collateMeaning();const r=this.collator.newDefault();r&&(this.parser=new xi(e),this.parser.getFactory().defaultMap=r,this.root=this.parser.parse(t)),to.visit(this.root,{}),function(t){for(const e of Wn.values())e.active&&e.annotate(t);for(const e of Kn.values())e.active&&e.visit(t,Object.assign({},e.def))}(this.root)}xml(t){const e=Un("<stree></stree>"),r=this.root.xml(e.ownerDocument,t);return e.appendChild(r),e}toString(t){return Vn(this.xml(t))}formatXml(t){return function(t){let e="",r=/(>)(<)(\/*)/g,n=0,s=(t=t.replace(r,"$1\r\n$2$3")).split("\r\n");for(r=/(\.)*(<)(\/*)/g,s=s.map((t=>t.replace(r,"$1\r\n$2$3").split("\r\n"))).reduce(((t,e)=>t.concat(e)),[]);s.length;){let t=s.shift();if(!t)continue;let r=0;if(t.match(/^<\w[^>/]*>[^>]+$/)){const e=jn(t,s[0]);e[0]?e[1]?(t+=s.shift().slice(0,-e[1].length),e[1].trim()&&s.unshift(e[1])):t+=s.shift():r=1}else if(t.match(/^<\/\w/))0!==n&&(n-=1);else if(t.match(/^<\w[^>]*[^/]>.*$/))r=1;else if(t.match(/^<\w[^>]*\/>.+$/)){const e=t.indexOf(">")+1,r=t.slice(e);r.trim()&&s.unshift(),t=t.slice(0,e)+r}else r=0;e+=new Array(n+1).join("  ")+t+"\r\n",n+=r}return e}(this.toString(t))}displayTree(){this.root.displayTree()}replaceNode(t,e){const r=t.parent;r?r.replaceChild(t,e):this.root=e}toJson(){const t={};return t.stree=this.root.toJson(),t}}const to=new class{constructor(t,e,r,n={}){this.domain=t,this.name=e,this.func=r,this.def=n,this.active=!1}visit(t,e){let r=this.func(t,e);t.addAnnotation(this.domain,r[0]);for(let e,n=0;e=t.childNodes[n];n++)r=this.visit(e,r[1]);for(let e,n=0;e=t.contentNodes[n];n++)r=this.visit(e,r[1]);return r}}("general","unit",((t,e)=>(Ai(t)&&(t.role=os.UNIT),!1)));function eo(t,e){return new Zi(t,e)}const ro=[],no=!0,so=new Map;function io(t){const e=function(t){for(let e,r=0;e=ro[r];r++)if(e.test(t))return e.constr(t);return null}(t);let r;if(e)return r=e.getMathml(),Eo(r);if(1===t.mathml.length){if(!t.childNodes.length)return r=t.mathml[0],Hi(r,t),Eo(r);const e=t.childNodes[0];if(1===t.childNodes.length&&e.type===ss.EMPTY)return r=t.mathml[0],Hi(r,t),r.appendChild(io(e)),Eo(r);t.childNodes.forEach((t=>{t.mathml.length||(t.mathml=[Lo(t)])}))}const n=t.contentNodes.map(Ao);Co(t,n);const s=t.childNodes.map(io),i=ji.combineContentChildren(t.type,t.role,n,s);if(r=t.mathmlTree,null===r)r=oo(i,t);else{const t=mo(i);r=t?yo(t):Oo(r)}return r=Ro(r),function(t,e,r){if(!e.length)return;if(1===e.length&&t===e[0])return;const n=r.role===os.IMPLICIT&&wi.flags.combine_juxtaposition?function(t,e,r){const n=[];let s=Bn(t.childNodes),i=!1;for(;s.length;){const t=s.shift();if(t.hasAttribute(Bi.TYPE)){n.push(t);continue}const r=co(t,e);0!==r.length&&(1!==r.length?(i?t.setAttribute("AuxiliaryImplicit",!0):i=!0,s=r.concat(s)):n.push(t))}const o=[],a=r.childNodes.map((function(t){return t.mathmlTree}));for(;a.length;){const t=a.pop();if(t){if(-1!==n.indexOf(t))break;-1!==e.indexOf(t)&&o.unshift(t)}}return n.concat(o)}(t,e,r):Bn(t.childNodes);if(!n.length)return void e.forEach((function(e){t.appendChild(e)}));let s=0;for(;e.length;){const r=e[0];if(n[s]===r||uo(n[s],r)){e.shift(),s++;continue}if(n[s]&&-1===e.indexOf(n[s])){s++;continue}if(ho(r,t)){e.shift();continue}const i=n[s];if(i)lo(t,i,r),e.shift();else{if(r.parentNode){t=yo(r),e.shift();continue}const n=e[1];if(n&&n.parentNode){(t=yo(n)).insertBefore(r,n),e.shift(),e.shift();continue}t.insertBefore(r,null),e.shift()}}}(r,i,t),so.has(t.id)||(so.set(t.id,!0),Hi(r,t)),Eo(r)}function oo(t,e){const r=function(t){const e=mo(t);if(!e)return{type:po.INVALID,node:null};const r=mo(t.slice().reverse());if(e===r)return{type:po.VALID,node:e};const n=fo(e),s=function(t,e){let r=0;for(;t[r]&&-1===e.indexOf(t[r]);)r++;return t.slice(0,r+1)}(n,t),i=fo(r,(function(t){return-1!==s.indexOf(t)})),o=i[0],a=s.indexOf(o);if(-1===a)return{type:po.INVALID,node:null};return{type:s.length!==n.length?po.PRUNED:go(s[a+1],i[1])?po.VALID:po.INVALID,node:o}}(t);let n=r.node;const s=r.type;if(s!==po.VALID||!Fs(n)||!n.parentNode&&e.parent)if(n=Gi(),s===po.PRUNED)n=function(t,e,r){let n=No(e);if(ks(n)){ao(n,t),Bn(n.childNodes).forEach((function(e){t.appendChild(e)}));const e=t;t=n,n=e}const s=r.indexOf(e);return r[s]=n,Hn(n,t),t.appendChild(n),r.forEach((function(e){t.appendChild(e)})),t}(n,r.node,t);else if(t[0]){const e=mo(t);if(e){const r=function(t,e){const r=Bn(t.childNodes);let n=1/0,s=-1/0;return e.forEach((function(t){const e=r.indexOf(t);-1!==e&&(n=Math.min(n,e),s=Math.max(s,e))})),r.slice(n,s+1)}(yo(e),t);Hn(e,n),r.forEach((function(t){n.appendChild(t)}))}else ao(n,t[0]),n=t[0]}return e.mathmlTree||(e.mathmlTree=n),n}function ao(t,e){for(const r of Ui)t.hasAttribute(r)&&(e.setAttribute(r,t.getAttribute(r)),t.removeAttribute(r))}function co(t,e){const r=[];let n=Bn(t.childNodes);for(;n.length;){const t=n.shift();t.nodeType===qn.ELEMENT_NODE&&(t.hasAttribute(Bi.TYPE)||-1!==e.indexOf(t)?r.push(t):n=Bn(t.childNodes).concat(n))}return r}function lo(t,e,r){let n=e,s=yo(n);for(;s&&s.firstChild===n&&!n.hasAttribute("AuxiliaryImplicit")&&s!==t;)n=s,s=yo(n);s&&(s.insertBefore(r,n),n.removeAttribute("AuxiliaryImplicit"))}function ho(t,e){if(!t)return!1;do{if((t=yo(t))===e)return!0}while(t);return!1}function uo(t,e){const r=ds.functionApplication;if(t&&e&&t.textContent&&e.textContent&&t.textContent===r&&e.textContent===r&&"true"===e.getAttribute(Bi.ADDED)){for(let r,n=0;r=t.attributes[n];n++)e.hasAttribute(r.nodeName)||e.setAttribute(r.nodeName,r.nodeValue);return Hn(t,e),!0}return!1}var po;function mo(t){let e=0,r=null;for(;!r&&e<t.length;)t[e].parentNode&&(r=t[e]),e++;return r}function fo(t,e){const r=e||(t=>!1),n=[t];for(;!r(t)&&!ks(t)&&t.parentNode;)t=yo(t),n.unshift(t);return n}function go(t,e){return!(!t||!e||t.previousSibling||e.nextSibling)}function Eo(t){for(;!ks(t)&&bo(t);)t=yo(t);return t}function No(t){const e=Bn(t.childNodes);if(!e)return t;const r=e.filter((function(t){return t.nodeType===qn.ELEMENT_NODE&&!_s(t)}));return 1===r.length&&Fs(r[0])&&!r[0].hasAttribute(Bi.TYPE)?No(r[0]):t}function bo(t){const e=yo(t);return!(!e||!Fs(e))&&Bn(e.childNodes).every((function(e){return e===t||To(e)}))}function To(t){if(t.nodeType!==qn.ELEMENT_NODE)return!0;if(!t||_s(t))return!0;const e=Bn(t.childNodes);return!(!Fs(t)&&e.length||function(t){return!!t&&Ps.includes(Xn(t))}(t)||t.hasAttribute(Bi.TYPE)||Bs(t))&&Bn(t.childNodes).every(To)}function yo(t){return t.parentNode}function Io(t,e){const r=new ji(e);t.setAttribute(Bi.COLLAPSED,r.toString())}function Ao(t){if(t.mathml.length)return io(t);const e=no?Lo(t):Gi();return t.mathml=[e],e}function Ro(t){if(Xn(t)!==vs.MFENCED)return t;const e=Gi();for(let r,n=0;r=t.attributes[n];n++)-1===["open","close","separators"].indexOf(r.name)&&e.setAttribute(r.name,r.value);return Bn(t.childNodes).forEach((function(t){e.appendChild(t)})),Hn(t,e),e}function Lo(t){const e=Gn("mo"),r=(n=t.textContent,An.f.document.createTextNode(n));var n;return e.appendChild(r),Hi(e,t),e.setAttribute(Bi.ADDED,"true"),e}function Co(t,e){const r=t.type+(t.textContent?","+t.textContent:"");e.forEach((function(t){Oo(t).setAttribute(Bi.OPERATOR,r)}))}function Oo(t){const e=Bn(t.childNodes);if(!e)return t;const r=e.filter((function(t){return!To(t)})),n=[];for(let t,e=0;t=r[e];e++)if(Fs(t)&&t.getAttribute(Bi.TYPE)!==ss.PUNCTUATION){const e=Oo(t);e&&e!==t&&n.push(e)}else n.push(t);return 1===n.length?n[0]:t}function vo(t,e){const r=!!e,n=e||[],s=t.parent,i=t.contentNodes.map((function(t){return t.id}));i.unshift("c");const o=[t.id,i];for(let e,i=0;e=t.childNodes[i];i++){const t=io(e);n.push(t);const i=Oo(t);s&&!r&&i.setAttribute(Bi.PARENT,s.id.toString()),o.push(e.id)}return o}!function(t){t.VALID="valid",t.INVALID="invalid",t.PRUNED="pruned"}(po||(po={}));class So{constructor(t){this.semantic=t}}class wo extends So{static test(t){return!t.mathmlTree&&t.type===ss.LINE&&t.role===os.BINOMIAL}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){if(!this.semantic.childNodes.length)return this.mml;const t=this.semantic.childNodes[0];if(this.mml=io(t),this.mml.hasAttribute(Bi.TYPE)){const t=Gi();Hn(this.mml,t),t.appendChild(this.mml),this.mml=t}return Hi(this.mml,this.semantic),this.mml}}class Mo extends So{static test(t){if(!t.mathmlTree||!t.childNodes.length)return!1;const e=Xn(t.mathmlTree),r=t.childNodes[0].role;return e===vs.MSUBSUP&&r===os.SUBSUP||e===vs.MUNDEROVER&&r===os.UNDEROVER}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=this.semantic.childNodes[0],e=t.childNodes[0],r=this.semantic.childNodes[1],n=t.childNodes[1],s=io(r),i=io(e),o=io(n);return Hi(this.mml,this.semantic),this.mml.setAttribute(Bi.CHILDREN,qi([e,n,r])),[i,o,s].forEach((t=>Oo(t).setAttribute(Bi.PARENT,this.mml.getAttribute(Bi.ID)))),this.mml.setAttribute(Bi.TYPE,t.role),Io(this.mml,[this.semantic.id,[t.id,e.id,n.id],r.id]),this.mml}}class xo extends So{static multiscriptIndex(t){return t.type===ss.PUNCTUATED&&t.contentNodes[0].role===os.DUMMY?vo(t):(io(t),t.id)}static createNone_(t){const e=Gn("none");return t&&Hi(e,t),e.setAttribute(Bi.ADDED,"true"),e}constructor(t){super(t),this.mml=t.mathmlTree}completeMultiscript(t,e){const r=Bn(this.mml.childNodes).slice(1);let n=0;const s=t=>{for(const e of t){const t=r[n];if(t&&e===parseInt(t.getAttribute(Bi.ID)))t.setAttribute(Bi.PARENT,this.semantic.id.toString()),n++;else if(t&&e===parseInt(Oo(t).getAttribute(Bi.ID)))Oo(t).setAttribute(Bi.PARENT,this.semantic.id.toString()),n++;else{const r=this.semantic.querySelectorAll((t=>t.id===e));this.mml.insertBefore(xo.createNone_(r[0]),t||null)}}};s(t),r[n]&&Xn(r[n])!==vs.MPRESCRIPTS?this.mml.insertBefore(r[n],Gn("mprescripts")):n++,s(e)}}class Po extends xo{static test(t){if(!t.mathmlTree)return!1;return Xn(t.mathmlTree)===vs.MMULTISCRIPTS&&(t.type===ss.SUPERSCRIPT||t.type===ss.SUBSCRIPT)}constructor(t){super(t)}getMathml(){let t,e,r;if(Hi(this.mml,this.semantic),this.semantic.childNodes[0]&&this.semantic.childNodes[0].role===os.SUBSUP){const n=this.semantic.childNodes[0];t=n.childNodes[0],e=xo.multiscriptIndex(this.semantic.childNodes[1]),r=xo.multiscriptIndex(n.childNodes[1]);const s=[this.semantic.id,[n.id,t.id,r],e];Io(this.mml,s),this.mml.setAttribute(Bi.TYPE,n.role),this.completeMultiscript(ji.interleaveIds(r,e),[])}else{t=this.semantic.childNodes[0],e=xo.multiscriptIndex(this.semantic.childNodes[1]);const r=[this.semantic.id,t.id,e];Io(this.mml,r)}const n=ji.collapsedLeafs(r||[],e);return Oo(io(t)).setAttribute(Bi.PARENT,this.semantic.id.toString()),n.unshift(t.id),this.mml.setAttribute(Bi.CHILDREN,n.join(",")),this.mml}}class Do extends xo{static test(t){return!!t.mathmlTree&&t.type===ss.TENSOR}constructor(t){super(t)}getMathml(){io(this.semantic.childNodes[0]);const t=xo.multiscriptIndex(this.semantic.childNodes[1]),e=xo.multiscriptIndex(this.semantic.childNodes[2]),r=xo.multiscriptIndex(this.semantic.childNodes[3]),n=xo.multiscriptIndex(this.semantic.childNodes[4]);Hi(this.mml,this.semantic);const s=[this.semantic.id,this.semantic.childNodes[0].id,t,e,r,n];Io(this.mml,s);const i=ji.collapsedLeafs(t,e,r,n);return i.unshift(this.semantic.childNodes[0].id),this.mml.setAttribute(Bi.CHILDREN,i.join(",")),this.completeMultiscript(ji.interleaveIds(r,n),ji.interleaveIds(t,e)),this.mml}}class ko extends So{static test(t){return!(!t.mathmlTree||!t.fencePointer||t.mathmlTree.getAttribute("data-semantic-type"))}static makeEmptyNode_(t){const e=Gi(),r=new Vs(t);return r.type=ss.EMPTY,r.mathmlTree=e,r}static fencedMap_(t,e){e[t.id]=t.mathmlTree,t.embellished&&ko.fencedMap_(t.childNodes[0],e)}constructor(t){super(t),this.fenced=null,this.fencedMml=null,this.fencedMmlNodes=[],this.ofence=null,this.ofenceMml=null,this.ofenceMap={},this.cfence=null,this.cfenceMml=null,this.cfenceMap={},this.parentCleanup=[]}getMathml(){this.getFenced_(),this.fencedMml=io(this.fenced),this.getFencesMml_(),this.fenced.type!==ss.EMPTY||this.fencedMml.parentNode||(this.fencedMml.setAttribute(Bi.ADDED,"true"),this.cfenceMml.parentNode.insertBefore(this.fencedMml,this.cfenceMml)),this.getFencedMml_();return this.rewrite_()}fencedElement(t){return t.type===ss.FENCED||t.type===ss.MATRIX||t.type===ss.VECTOR}getFenced_(){let t=this.semantic;for(;!this.fencedElement(t);)t=t.childNodes[0];this.fenced=t.childNodes[0],this.ofence=t.contentNodes[0],this.cfence=t.contentNodes[1],ko.fencedMap_(this.ofence,this.ofenceMap),ko.fencedMap_(this.cfence,this.cfenceMap)}getFencedMml_(){let t=this.ofenceMml.nextSibling;for(t=t===this.fencedMml?t:this.fencedMml;t&&t!==this.cfenceMml;)this.fencedMmlNodes.push(t),t=t.nextSibling}getFencesMml_(){let t=this.semantic;const e=Object.keys(this.ofenceMap),r=Object.keys(this.cfenceMap);for(;!(this.ofenceMml&&this.cfenceMml||t===this.fenced);)-1===e.indexOf(t.fencePointer)||this.ofenceMml||(this.ofenceMml=t.mathmlTree),-1===r.indexOf(t.fencePointer)||this.cfenceMml||(this.cfenceMml=t.mathmlTree),t=t.childNodes[0];this.ofenceMml||(this.ofenceMml=this.ofence.mathmlTree),this.cfenceMml||(this.cfenceMml=this.cfence.mathmlTree),this.ofenceMml&&(this.ofenceMml=Eo(this.ofenceMml)),this.cfenceMml&&(this.cfenceMml=Eo(this.cfenceMml))}rewrite_(){let t=this.semantic,e=null;const r=this.introduceNewLayer_();for(Hi(r,this.fenced.parent);!this.fencedElement(t);){const n=t.mathmlTree,s=this.specialCase_(t,n);if(s)t=s;else{Hi(n,t);const e=[];for(let r,n=1;r=t.childNodes[n];n++)e.push(io(r));t=t.childNodes[0]}const i=Gn("dummy"),o=n.childNodes[0];Hn(n,i),Hn(r,n),Hn(n.childNodes[0],r),Hn(i,o),e||(e=n)}return io(this.ofence),io(this.cfence),this.cleanupParents_(),e||r}specialCase_(t,e){const r=Xn(e);let n,s=null;if(r===vs.MSUBSUP?(s=t.childNodes[0],n=Mo):r===vs.MMULTISCRIPTS&&(t.type===ss.SUPERSCRIPT||t.type===ss.SUBSCRIPT?n=Po:t.type===ss.TENSOR&&(n=Do),s=n&&t.childNodes[0]&&t.childNodes[0].role===os.SUBSUP?t.childNodes[0]:t),!s)return null;const i=s.childNodes[0],o=ko.makeEmptyNode_(i.id);return s.childNodes[0]=o,e=new n(t).getMathml(),s.childNodes[0]=i,this.parentCleanup.push(e),s.childNodes[0]}introduceNewLayer_(){const t=this.fullFence(this.ofenceMml),e=this.fullFence(this.cfenceMml);let r=Gi();if(Hn(this.fencedMml,r),this.fencedMmlNodes.forEach((t=>r.appendChild(t))),r.insertBefore(t,this.fencedMml),r.appendChild(e),!r.parentNode){const t=Gi();for(;r.childNodes.length>0;)t.appendChild(r.childNodes[0]);r.appendChild(t),r=t}return r}fullFence(t){const e=this.fencedMml.parentNode;let r=t;for(;r.parentNode&&r.parentNode!==e;)r=r.parentNode;return r}cleanupParents_(){this.parentCleanup.forEach((function(t){const e=t.childNodes[1].getAttribute(Bi.PARENT);t.childNodes[0].setAttribute(Bi.PARENT,e)}))}}class _o extends So{static test(t){return!!t.mathmlTree&&t.hasAnnotation("Emph","top")}constructor(t){super(t),this.mrows=[],this.mml=t.mathmlTree}getMathml(){if(this.recurseToTable(this.semantic),this.mrows.length){const t=Gi();this.mml.parentNode.insertBefore(t,this.mml);for(const e of this.mrows)t.appendChild(e);t.appendChild(this.mml)}return this.mml}recurseToTable(t){var e,r;if(t.hasAnnotation("Emph","top")||t.hasAnnotation("Emph","fence")||!t.hasAnnotation("Emph","left")&&!t.hasAnnotation("Emph","right")){if(!t.mathmlTree||Xn(t.mathmlTree)===vs.MTABLE&&(null===(e=t.annotation.Emph)||void 0===e?void 0:e.length)&&"table"!==t.annotation.Emph[0]){const e=Gi();Hi(e,t),this.mrows.unshift(e)}else{if(Xn(t.mathmlTree)===vs.MTABLE&&(null===(r=t.annotation.Emph)||void 0===r?void 0:r.length)&&"table"===t.annotation.Emph[0])return void this.finalizeTable(t);Hi(t.mathmlTree,t)}if(t.childNodes.forEach(this.recurseToTable.bind(this)),t.textContent||"punctuated"===t.type){const e=t.contentNodes.map((t=>{const e=Ao(t);return e.hasAttribute("data-semantic-added")?this.mrows.unshift(e):this.recurseToTable(t),e}));Co(t,e)}else t.contentNodes.forEach(this.recurseToTable.bind(this))}else io(t)}finalizeTable(t){Hi(t.mathmlTree,t),t.contentNodes.forEach((t=>{io(t)})),t.childNodes.forEach((t=>{io(t)}))}}class Fo extends So{static test(t){if(!t.mathmlTree||!t.childNodes.length)return!1;const e=Xn(t.mathmlTree),r=t.type;return(r===ss.LIMUPPER||r===ss.LIMLOWER)&&(e===vs.MSUBSUP||e===vs.MUNDEROVER)||r===ss.LIMBOTH&&(e===vs.MSUB||e===vs.MUNDER||e===vs.MSUP||e===vs.MOVER)}static walkTree_(t){t&&io(t)}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=this.semantic.childNodes;return this.semantic.type!==ss.LIMBOTH&&this.mml.childNodes.length>=3&&(this.mml=oo([this.mml],this.semantic)),Hi(this.mml,this.semantic),t[0].mathmlTree||(t[0].mathmlTree=this.semantic.mathmlTree),t.forEach(Fo.walkTree_),this.mml}}class Bo extends So{static test(t){return!!t.mathmlTree&&t.type===ss.LINE}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){return this.semantic.contentNodes.length&&io(this.semantic.contentNodes[0]),this.semantic.childNodes.length&&io(this.semantic.childNodes[0]),Hi(this.mml,this.semantic),this.mml}}class Uo extends So{static test(t){return!!t.mathmlTree&&(t.type===ss.INFERENCE||t.type===ss.PREMISES)}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){return this.semantic.childNodes.length?(this.semantic.contentNodes.forEach((function(t){io(t),Hi(t.mathmlTree,t)})),this.semantic.childNodes.forEach((function(t){io(t)})),Hi(this.mml,this.semantic),this.mml.getAttribute("data-semantic-id")===this.mml.getAttribute("data-semantic-parent")&&this.mml.removeAttribute("data-semantic-parent"),this.mml):this.mml}}class qo extends So{static test(t){return t.type===ss.MATRIX||t.type===ss.VECTOR||t.type===ss.CASES}constructor(t){super(t),this.inner=[],this.mml=t.mathmlTree}getMathml(){const t=Ao(this.semantic.contentNodes[0]),e=this.semantic.contentNodes[1]?Ao(this.semantic.contentNodes[1]):null;if(this.inner=this.semantic.childNodes.map(io),this.mml)if(Xn(this.mml)===vs.MFENCED){const r=this.mml.childNodes;this.mml.insertBefore(t,r[0]||null),e&&this.mml.appendChild(e),this.mml=Ro(this.mml)}else{const r=[t,this.mml];e&&r.push(e),this.mml=oo(r,this.semantic)}else this.mml=oo([t].concat(this.inner,[e]),this.semantic);return Hi(this.mml,this.semantic),this.mml}}class Ho extends So{static test(t){return t.type===ss.PUNCTUATED&&(t.role===os.TEXT||t.contentNodes.every((t=>t.role===os.DUMMY)))}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=[],e=vo(this.semantic,t);return this.mml=oo(t,this.semantic),Hi(this.mml,this.semantic),this.mml.removeAttribute(Bi.CONTENT),Io(this.mml,e),this.mml}}function Go(t,e){const r=t.cloneNode(!0);return function(t,e,r){return so.clear(),io(e.root),r.structure&&t.setAttribute(Bi.STRUCTURE,ji.fromStructure(t,e,r).toString()),t}(r,eo(r,e),e)}ro.push({test:Fo.test,constr:t=>new Fo(t)},{test:ko.test,constr:t=>new ko(t)},{test:Mo.test,constr:t=>new Mo(t)},{test:Do.test,constr:t=>new Do(t)},{test:Po.test,constr:t=>new Po(t)},{test:Bo.test,constr:t=>new Bo(t)},{test:wo.test,constr:t=>new wo(t)},{test:Uo.test,constr:t=>new Uo(t)},{test:_o.test,constr:t=>new _o(t)},{test:qo.test,constr:t=>new qo(t)},{test:Ho.test,constr:t=>new Ho(t)});new En({AbsoluteValue:["Auto","AbsEnd","Cardinality","Determinant"],Bar:["Auto","Conjugate"],Caps:["Auto","SayCaps"],CombinationPermutation:["Auto","ChoosePermute"],Currency:["Auto","Position","Prefix"],Ellipses:["Auto","AndSoOn"],Enclosed:["Auto","EndEnclose"],Exponent:["Auto","AfterPower","Ordinal","OrdinalPower","Exponent"],Fraction:["Auto","EndFrac","FracOver","General","GeneralEndFrac","Ordinal","Over","OverEndFrac","Per"],Functions:["Auto","None","Reciprocal"],ImpliedTimes:["Auto","MoreImpliedTimes","None"],Log:["Auto","LnAsNaturalLog"],Matrix:["Auto","Combinatoric","EndMatrix","EndVector","SilentColNum","SpeakColNum","Vector"],MultiLineLabel:["Auto","Case","Constraint","Equation","Line","None","Row","Step"],MultiLineOverview:["Auto","None"],MultiLinePausesBetweenColumns:["Auto","Long","Short"],MultsymbolDot:["Auto","Dot"],MultsymbolX:["Auto","By","Cross"],Paren:["Auto","CoordPoint","Interval","Silent","Speak","SpeakNestingLevel"],Prime:["Auto","Angle","Length"],Roots:["Auto","PosNegSqRoot","PosNegSqRootEnd","RootEnd"],SetMemberSymbol:["Auto","Belongs","Element","Member","In"],Sets:["Auto","SilentBracket","woAll"],TriangleSymbol:["Auto","Delta"],Trig:["Auto","ArcTrig","TrigInverse","Reciprocal"],VerticalLine:["Auto","Divides","Given","SuchThat"]});Cn.u.LOCALES;const jo=t=>function(t,e){const r=Un(t);try{return Go(r,e)}catch(t){return console.error(t),r}}(t,Mn.getInstance().options);M("ENRICHED",w.COMPILED+10);class Xo extends pn{visitTree(t,e){this.mactionId=0;const r=super.visitTree(t);return this.mactionId&&(e.inputData.hasMaction=!0),r}visitHtmlNode(t,e){return t.getSerializedXML()}visitMactionNode(t,e){const[r,n]=0===t.childNodes.length?["",""]:["\n",e],s=this.childNodeMml(t,e+"  ",r);let i=this.getAttributes(t);if("toggle"===t.attributes.get("actiontype")){const e=++this.mactionId;t.setProperty("mactionId",e),i=` data-maction-id="${e}" selection="${t.attributes.get("selection")}"`+i.replace(/ selection="\d+"/,"").replace(/ data-maction-id="\d+"/,"")}return`${e}<maction${i}>`+(s.match(/\S/)?r+s+n:"")+"</maction>"}}function Vo(t,e){var r;return r=class extends t{constructor(...t){super(...t),e.setMmlFactory(this.mmlFactory);const r=this.constructor.ProcessBits;r.has("enriched")||r.allocate("enriched");const n=new Xo(this.mmlFactory);this.options.MathItem=function(t,e,r){return class extends t{constructor(){super(...arguments),this.toMathML=r}serializeMml(t){if("outerHTML"in t)return t.outerHTML;if("undefined"!=typeof Element&&"undefined"!=typeof window&&t instanceof Element){const e=window.document.createElement("div");return e.appendChild(t),e.innerHTML}return t.toString()}enrich(t,r=!1){if(!(this.state()>=w.ENRICHED)){if(!this.isEscaped&&(t.options.enableEnrichment||r)){const r=new t.options.MathItem("",e);try{let e;e=this.inputData.originalMml?this.adjustSelections():this.inputData.originalMml=this.toMathML(this.root,this);const n=jo(e);this.inputData.enrichedMml=r.math=this.serializeMml(n),r.math=r.math.replace(/ role="treeitem"/g,' data-speech-node="true"').replace(/ aria-(?:posinset|owns|setsize)=".*?"/g,""),r.display=this.display,r.compile(t),this.root=r.root}catch(e){t.options.enrichError(t,this,e)}}this.state(w.ENRICHED)}}toEnriched(t){return this.serializeMml(jo(t))}unEnrich(t){const r=this.inputData.originalMml;if(!r)return;const n=new t.options.MathItem("",e);n.math=r,n.display=this.display,n.compile(t),this.root=n.root}adjustSelections(){const t=this.inputData.originalMml;if(!this.inputData.hasMaction)return t;const e=[];return this.root.walkTree((t=>{t.isKind("maction")&&(e[t.attributes.get("data-maction-id")]=t)})),t.replace(/(data-maction-id="(\d+)" selection=)"\d+"/g,((t,r,n)=>`${r}"${e[n].attributes.get("selection")}"`))}}}(this.options.MathItem,e,((t,e)=>n.visitTree(t,e)))}enrich(){if(!this.processed.isSet("enriched")){if(this.options.enableEnrichment){t=this.options.sre,Mn.getInstance().setup(t);for(const t of this.math)t.enrich(this)}this.processed.set("enriched")}var t;return this}enrichError(t,e,r){console.warn("Enrichment error:",r)}state(t,e=!1){if(super.state(t,e),t<w.ENRICHED&&(this.processed.clear("enriched"),t>=w.COMPILED))for(const t of this.math)t.unEnrich(this);return this}},r.OPTIONS=Object.assign(Object.assign({},t.OPTIONS),{enableEnrichment:!0,enrichError:(t,e,r)=>t.enrichError(t,e,r),renderActions:p(Object.assign(Object.assign({},t.OPTIONS.renderActions),{enrich:[w.ENRICHED]})),sre:p({speech:"none",locale:"en",domain:"clearspeak",style:"default",braille:"nemeth",structure:!0,aria:!0})}),r}var Wo,Ko;!function(t){t[t.NONE=0]="NONE",t[t.DEPTH=1]="DEPTH",t[t.SUMMARY=2]="SUMMARY"}(Wo||(Wo={})),function(t){t.SPEECH="data-semantic-speech-none",t.SPEECH_SSML="data-semantic-speech",t.SUMMARY="data-semantic-summary-none",t.SUMMARY_SSML="data-semantic-summary",t.PREFIX="data-semantic-prefix-none",t.PREFIX_SSML="data-semantic-prefix",t.POSTFIX="data-semantic-postfix-none",t.POSTFIX_SSML="data-semantic-postfix",t.BRAILLE="data-semantic-braille"}(Ko||(Ko={}));class zo{constructor(){this.promise=Promise.resolve(),this.adaptor=null,this._options={},this._init=!1}set element(t){this._element=t}get element(){return this._element}set options(t){this._options=Object.assign({},(null==t?void 0:t.sre)||{},{enableSpeech:t.enableSpeech,enableBraille:t.enableBraille}),delete this._options.custom}get options(){return this._options}init(t,e,r){this.options=t,this._init||(this.adaptor=e,this.webworker=r,this._init=!0)}update(t){Object.assign(this.options,t)}Speech(t){const e=t.outputData.mml,r=Object.assign({},this.options,{modality:"speech"});return this.promise=this.webworker.Speech(e,r,t)}SpeechFor(t,e){const r=Object.assign({},this.options,{modality:"speech"});return this.webworker.speechFor(e,r,t)}cancel(t){var e;null===(e=this.webworker)||void 0===e||e.Cancel(t)}updateRegions(t,e,r){e.Update(this.getLabel(t)),r.Update(this.getBraille(t))}getOptions(t){var e,r,n,s;return{locale:null!==(e=this.adaptor.getAttribute(t,"data-semantic-locale"))&&void 0!==e?e:"",domain:null!==(r=this.adaptor.getAttribute(t,"data-semantic-domain"))&&void 0!==r?r:"",style:null!==(n=this.adaptor.getAttribute(t,"data-semantic-style"))&&void 0!==n?n:"",domain2style:null!==(s=this.adaptor.getAttribute(t,"data-semantic-domain2style"))&&void 0!==s?s:""}}nextRules(t){const e=this.getOptions(t.typesetRoot);return this.update(e),this.promise=this.webworker.nextRules(t.outputData.mml,Object.assign({},this.options,{modality:"speech"}),t)}nextStyle(t,e){const r=this.getOptions(e.typesetRoot);return this.update(r),this.promise=this.webworker.nextStyle(e.outputData.mml,Object.assign({},this.options,{modality:"speech"}),this.adaptor.getAttribute(t,"data-semantic-id"),e)}getLabel(t,e="",r=" "){const n=this.adaptor;return function(t,e,r,n=" "){if(!t)return"";const s=[t];return e&&s.unshift(e),r&&s.push(r),s.join(n)}(n.getAttribute(t,Ko.SPEECH_SSML),n.getAttribute(t,Ko.PREFIX_SSML),n.getAttribute(t,Ko.POSTFIX_SSML),r)||n.getAttribute(t,"aria-label")}getBraille(t){const e=this.adaptor;return e.getAttribute(t,"aria-braillelabel")||e.getAttribute(t,Ko.BRAILLE)}getLocalePreferences(t){return this.promise=this.webworker.clearspeakLocalePreferences(this.options,t)}getRelevantPreferences(t,e,r,n){const s=t.outputData.mml;return this.promise=this.webworker.clearspeakRelevantPreferences(s,e,r,n)}}var Yo=function(t,e,r,n){return new(r||(r=Promise))((function(s,i){function o(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?s(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(o,a)}c((n=n.apply(t,e||[])).next())}))};class $o{constructor(t,e,r,n){this.cmd=t,this.item=e,this.resolve=r,this.reject=n}}class Jo{constructor(t,e){this.adaptor=t,this.options=e,this.ready=!1,this.tasks=[],this.Commands={Ready(t,e){t.ready=!0,t.postNext()},Finished(t,e){const r=t.tasks.shift();e.success?r.resolve(e.result):r.reject(e.error),t.postNext()},Log(t,e){t.options.debug&&console.log("Log:",e)}}}Start(){return Yo(this,void 0,void 0,(function*(){if(this.ready)throw Error("Worker already started");this.worker=yield this.adaptor.createWorker(this.Listener.bind(this),this.options)}))}debug(t,...e){this.options.debug&&console.info(t,...e)}Listener(t){this.debug("Worker  >>>  Client:",t.data),Object.hasOwn(this.Commands,t.data.cmd)?this.Commands[t.data.cmd](this,t.data.data):this.debug("Invalid command from worker: "+t.data.cmd)}Post(t,e){const r=new Promise(((r,n)=>{this.tasks.push(new $o(t,e,r,n))}));return this.ready&&1===this.tasks.length&&this.postNext(),r}postNext(){if(this.tasks.length){const t=Object.assign({},this.tasks[0].cmd,{debug:this.options.debug});this.worker.postMessage(t)}}Cancel(t){const e=this.tasks.findIndex((e=>e.item===t));e>0&&(this.tasks[e].reject(`Task ${this.tasks[e].cmd.cmd} cancelled`),this.tasks.splice(e,1))}Setup(t){return this.Post({cmd:"setup",data:{domain:t.domain,style:t.style,locale:t.locale,modality:t.modality}})}Speech(t,e,r){return Yo(this,void 0,void 0,(function*(){this.Attach(r,e.enableSpeech,e.enableBraille,yield this.Post({cmd:"speech",data:{mml:t,options:e}},r))}))}nextRules(t,e,r){return Yo(this,void 0,void 0,(function*(){this.Attach(r,e.enableSpeech,e.enableBraille,yield this.Post({cmd:"nextRules",data:{mml:t,options:e}},r))}))}nextStyle(t,e,r,n){return Yo(this,void 0,void 0,(function*(){this.Attach(n,e.enableSpeech,e.enableBraille,yield this.Post({cmd:"nextStyle",data:{mml:t,options:e,nodeId:r}},n))}))}speechFor(t,e,r){return Yo(this,void 0,void 0,(function*(){const n=yield this.Post({cmd:"speech",data:{mml:t,options:e}},r);return JSON.parse(n)}))}Attach(t,e,r,n){const s=JSON.parse(n),i=t.typesetRoot;if(!i)return;this.setSpecialAttributes(i,s.options,"data-semantic-",["locale","domain","style","domain2style"]);const o=this.adaptor;this.setSpecialAttributes(i,s.translations,"data-semantic-");for(const[t,e]of Object.entries(s.mactions)){let r=o.getElement("#"+t,i);r&&o.childNodes(r)[0]&&(r=o.childNodes(r)[0],o.setAttribute(r,"data-semantic-type","dummy"),this.setSpecialAttributes(r,e,""))}this.setSpeechAttributes(o.childNodes(i)[0],"",s,e,r),e&&(s.label&&(o.setAttribute(i,Ko.SPEECH,s.label),o.setAttribute(i,Ko.SPEECH_SSML,s.ssml),t.outputData.speech=s.label),o.setAttribute(i,"data-speech-attached","true")),r&&(s.braillelabel&&(o.setAttribute(i,Ko.BRAILLE,s.braillelabel),t.outputData.braille=s.braillelabel),s.braille&&o.setAttribute(i,"data-braille-attached","true"))}setSpeechAttribute(t,e,r,n){var s,i;const o=this.adaptor,a=o.getAttribute(t,"data-semantic-id");if(o.removeAttribute(t,"data-speech-node"),r&&e.speech[a]["speech-none"]){o.setAttribute(t,"data-speech-node","true");for(let[r,n]of Object.entries(e.speech[a]))r=r.replace(/-ssml$/,""),n&&o.setAttribute(t,`data-semantic-${r}`,n)}if(n&&(null===(i=null===(s=e.braille)||void 0===s?void 0:s[a])||void 0===i?void 0:i["braille-none"])){o.setAttribute(t,"data-speech-node","true");const r=e.braille[a]["braille-none"];o.setAttribute(t,Ko.BRAILLE,r)}}setSpeechAttributes(t,e,r,n,s){const i=this.adaptor;if(!t||"#text"===i.kind(t)||"#comment"===i.kind(t))return e;i.hasAttribute(t,"data-semantic-id")&&(this.setSpeechAttribute(t,r,n,s),e||i.hasAttribute(t,"data-semantic-parent")||(e=i.getAttribute(t,"data-semantic-id")));for(const o of Array.from(i.childNodes(t)))e=this.setSpeechAttributes(o,e,r,n,s);return e}setSpecialAttributes(t,e,r,n){if(e){n=n||Object.keys(e);for(const s of n){const n=e[s];n&&this.adaptor.setAttribute(t,`${r}${s.toLowerCase()}`,n)}}}Detach(t){const e=t.typesetRoot;this.adaptor.removeAttribute(e,"data-speech-attached"),this.adaptor.removeAttribute(e,"data-braille-attached"),this.detachSpeech(e)}detachSpeech(t){const e=this.adaptor,r=e.childNodes(t);if(r){if("#text"!==e.kind(t))for(const r of["none","summary-none","speech","speech-none","summary","braille"])e.removeAttribute(t,`data-semantic-${r}`);for(const t of r)this.detachSpeech(t)}}Terminate(){this.debug("Terminating pending tasks");for(const t of this.tasks)t.reject(`${t.cmd.data.cmd} cancelled by WorkerHandler termination`);return this.tasks=[],this.debug("Terminating worker"),this.worker.terminate()}Stop(){return Yo(this,void 0,void 0,(function*(){if(!this.worker)throw Error("Worker has not been started");yield this.Terminate(),this.worker=null,this.ready=!1}))}clearspeakLocalePreferences(t,e){return Yo(this,void 0,void 0,(function*(){yield this.Post({cmd:"localePreferences",data:{options:t}}).then((r=>{e.set(t.locale,JSON.parse(r))}))}))}clearspeakRelevantPreferences(t,e,r,n){return Yo(this,void 0,void 0,(function*(){yield this.Post({cmd:"relevantPreferences",data:{mml:t,id:e}}).then((t=>{r.set(n,t)}))}))}}const Qo="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof globalThis?globalThis:{};void 0!==Qo.MathJax&&Qo.MathJax.constructor==={}.constructor||(Qo.MathJax={}),Qo.MathJax.version||(Qo.MathJax={version:t,_:{},config:Qo.MathJax});const Zo=Qo.MathJax,ta=Zo.config||{},ea=()=>(ta?.loader?.paths?.mathjax||ta?.__dirname||"/")+"/sre";var ra=function(t,e,r,n){return new(r||(r=Promise))((function(s,i){function o(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?s(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(o,a)}c((n=n.apply(t,e||[])).next())}))};function na(t){var e;return e=class extends t{constructor(...t){super(...t),this.webworker=null;const e=this.constructor.ProcessBits;var r;e.has("attach-speech")||e.allocate("attach-speech"),this.options.MathItem=(r=this.options.MathItem,class extends r{constructor(){super(...arguments),this.generatorPool=new zo}attachSpeech(t){if(this.outputData.speechPromise=null,this.state()>=w.ATTACHSPEECH)return;if(this.state(w.ATTACHSPEECH),this.isEscaped||!t.options.enableSpeech&&!t.options.enableBraille||!t.options.enableEnrichment)return;t.getWebworker(),this.generatorPool.init(t.options,t.adaptor,t.webworker),this.outputData.mml=this.toMathML(this.root,this);const e=this.generatorPool.Speech(this).catch((e=>t.options.speechError(t,this,e)));t.savePromise(e),this.outputData.speechPromise=e}detachSpeech(t){t.webworker.Detach(this)}speechFor(t){return ra(this,void 0,void 0,(function*(){t=this.toEnriched(t);const e=yield this.generatorPool.SpeechFor(this,t);return[e.label,e.braillelabel]}))}clear(){this.generatorPool.cancel(this)}})}getWebworker(){this.webworker||(this.webworker=new Jo(this.adaptor,this.options.worker),this.webworker.Start())}attachSpeech(){if(!this.processed.isSet("attach-speech")){const t=this.options;if(t.enableEnrichment&&(t.enableSpeech||t.enableBraille)){this.getWebworker();for(const t of this.math)t.attachSpeech(this)}this.processed.set("attach-speech")}return this}speechError(t,e,r){console.warn("Speech generation error:",r)}state(t,e=!1){if(super.state(t,e),t<w.ATTACHSPEECH&&(this.processed.clear("attach-speech"),t>=w.TYPESET))for(const t of this.math)t.detachSpeech(this);return this}done(){const t=Object.create(null,{done:{get:()=>super.done}});return ra(this,void 0,void 0,(function*(){var e;return yield null===(e=this.webworker)||void 0===e?void 0:e.Stop(),t.done.call(this)}))}},e.OPTIONS=Object.assign(Object.assign({},t.OPTIONS),{enableSpeech:!0,enableBraille:!0,speechError:(t,e,r)=>t.speechError(t,e,r),renderActions:p(Object.assign(Object.assign({},t.OPTIONS.renderActions),{attachSpeech:[w.ATTACHSPEECH]})),worker:{path:ea(),maps:ea().replace(/[cm]js\/a11y\/sre$/,"bundle/sre/mathmaps"),worker:"speech-worker.js",debug:!1},a11y:p({speech:!0,braille:!0})}),e}M("ATTACHSPEECH",w.INSERTED+10);class sa{constructor(t={}){this.adaptor=null;const e=this.constructor;this.options=b(N({},e.OPTIONS),t),this.preFilters=new I(this.options.preFilters),this.postFilters=new I(this.options.postFilters)}get name(){return this.constructor.NAME}setAdaptor(t){this.adaptor=t}initialize(){}reset(...t){}getMetrics(t){}styleSheet(t){return null}pageElements(t){return null}executeFilters(t,e,r,n){const s={math:e,document:r,data:n};return t.execute(s),s.data}}sa.NAME="generic",sa.OPTIONS={preFilters:[],postFilters:[]};const ia=Symbol();class oa{constructor(t=null){this.next=null,this.prev=null,this.data=t}}class aa{constructor(...t){this.list=new oa(ia),this.list.next=this.list.prev=this.list,this.push(...t)}isBefore(t,e){return t<e}push(...t){for(const e of t){const t=new oa(e);t.next=this.list,t.prev=this.list.prev,this.list.prev=t,t.prev.next=t}return this}pop(){const t=this.list.prev;return t.data===ia?null:(this.list.prev=t.prev,t.prev.next=this.list,t.next=t.prev=null,t.data)}unshift(...t){for(const e of t.slice(0).reverse()){const t=new oa(e);t.next=this.list.next,t.prev=this.list,this.list.next=t,t.next.prev=t}return this}shift(){const t=this.list.next;return t.data===ia?null:(this.list.next=t.next,t.next.prev=this.list,t.next=t.prev=null,t.data)}remove(...t){const e=new Map;for(const r of t)e.set(r,!0);let r=this.list.next;for(;r.data!==ia;){const t=r.next;e.has(r.data)&&(r.prev.next=r.next,r.next.prev=r.prev,r.next=r.prev=null),r=t}return this}clear(){return this.list.next.prev=this.list.prev.next=null,this.list.next=this.list.prev=this.list,this}*[Symbol.iterator](){let t=this.list.next;for(;t.data!==ia;)yield t.data,t=t.next}*reversed(){let t=this.list.prev;for(;t.data!==ia;)yield t.data,t=t.prev}insert(t,e=null){null===e&&(e=this.isBefore.bind(this));const r=new oa(t);let n=this.list.next;for(;n.data!==ia&&e(n.data,r.data);)n=n.next;return r.prev=n.prev,r.next=n,n.prev.next=n.prev=r,this}sort(t=null){null===t&&(t=this.isBefore.bind(this));const e=[];for(const t of this)e.push(new aa(t));for(this.list.next=this.list.prev=this.list;e.length>1;){const r=e.shift(),n=e.shift();r.merge(n,t),e.push(r)}return e.length&&(this.list=e[0].list),this}merge(t,e=null){null===e&&(e=this.isBefore.bind(this));let r=this.list.next,n=t.list.next;for(;r.data!==ia&&n.data!==ia;)e(n.data,r.data)?([n.prev.next,r.prev.next]=[r,n],[n.prev,r.prev]=[r.prev,n.prev],[this.list.prev.next,t.list.prev.next]=[t.list,this.list],[this.list.prev,t.list.prev]=[t.list.prev,this.list.prev],[r,n]=[n.next,r]):r=r.next;return n.data!==ia&&(this.list.prev.next=t.list.next,t.list.next.prev=this.list.prev,t.list.prev.next=this.list,this.list.prev=t.list.prev,t.list.next=t.list.prev=t.list),this}}class ca extends aa{isBefore(t,e){return t.start.i<e.start.i||t.start.i===e.start.i&&t.start.n<e.start.n}}class la{constructor(){this.bits=0}static allocate(...t){for(const e of t){if(this.has(e))throw new Error("Bit already allocated for "+e);if(this.next===la.MAXBIT)throw new Error("Maximum number of bits already allocated");this.names.set(e,this.next),this.next<<=1}}static has(t){return this.names.has(t)}set(t){this.bits|=this.getBit(t)}clear(t){this.bits&=~this.getBit(t)}isSet(t){return!!(this.bits&this.getBit(t))}reset(){this.bits=0}getBit(t){const e=this.constructor.names.get(t);if(!e)throw new Error("Unknown bit-field name: "+t);return e}}la.MAXBIT=1<<31,la.next=1,la.names=new Map;var ha=function(t,e,r,n){return new(r||(r=Promise))((function(s,i){function o(t){try{c(n.next(t))}catch(t){i(t)}}function a(t){try{c(n.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?s(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(o,a)}c((n=n.apply(t,e||[])).next())}))};class ua extends e{static create(t){const e=new this;for(const r of Object.keys(t)){const[n,s]=this.action(r,t[r]);s&&e.add(n,s)}return e}static action(t,e){let r,n,s=!0;const i=e[0];if(1===e.length||"boolean"==typeof e[1])2===e.length&&(s=e[1]),[r,n]=this.methodActions(t);else if("string"==typeof e[1])if("string"==typeof e[2]){4===e.length&&(s=e[3]);const[t,i]=e.slice(1);[r,n]=this.methodActions(t,i)}else 3===e.length&&(s=e[2]),[r,n]=this.methodActions(e[1]);else 4===e.length&&(s=e[3]),[r,n]=e.slice(1);return[{id:t,renderDoc:r,renderMath:n,convert:s},i]}static methodActions(t,e=t){return[e=>(t&&e[t](),!1),(t,r)=>(e&&t[e](r),!1)]}renderDoc(t,e=w.UNPROCESSED){for(const r of this.items)if(r.priority>=e&&r.item.renderDoc(t))return}renderMath(t,e,r=w.UNPROCESSED){for(const n of this.items)if(n.priority>=r&&n.item.renderMath(t,e))return}renderConvert(t,e,r=w.LAST){for(const n of this.items){if(n.priority>r)return;if(n.item.convert&&n.item.renderMath(t,e))return}}findID(t){for(const e of this.items)if(e.item.id===t)return e.item;return null}}const da={all:!1,processed:!1,inputJax:null,outputJax:null},pa={all:!0,processed:!0,inputJax:[],outputJax:[]};class ma extends A{compile(t){return null}}class fa extends sa{typeset(t,e=null){return null}escaped(t,e){return null}}class ga extends ca{}class Ea{constructor(t,e,r){const n=this.constructor;this.document=t,this.options=b(N({},n.OPTIONS),r),this.math=new(this.options.MathList||ga),this.renderActions=ua.create(this.options.renderActions),this._actionPromises=[],this._readyPromise=Promise.resolve(),this.processed=new Ea.ProcessBits,this.outputJax=this.options.OutputJax||new fa;let s=this.options.InputJax||[new ma];Array.isArray(s)||(s=[s]),this.inputJax=s,this.adaptor=e,this.outputJax.setAdaptor(e),this.inputJax.map((t=>t.setAdaptor(e))),this.mmlFactory=this.options.MmlFactory||new hn,this.inputJax.map((t=>t.setMmlFactory(this.mmlFactory))),this.outputJax.initialize(),this.inputJax.map((t=>t.initialize()))}get kind(){return this.constructor.KIND}addRenderAction(t,...e){const[r,n]=ua.action(t,e);this.renderActions.add(r,n)}removeRenderAction(t){const e=this.renderActions.findID(t);e&&this.renderActions.remove(e)}render(){return this.clearPromises(),this.renderActions.renderDoc(this),this}renderPromise(){return this.whenReady((()=>r((()=>ha(this,void 0,void 0,(function*(){return this.render(),yield this.actionPromises(),this.clearPromises(),this}))))))}rerender(t=w.RERENDER){return this.state(t-1),this.render(),this}rerenderPromise(t=w.RERENDER){return this.whenReady((()=>r((()=>ha(this,void 0,void 0,(function*(){return this.rerender(t),yield this.actionPromises(),this.clearPromises(),this}))))))}convert(t,e={}){let{format:r,display:n,end:s,ex:i,em:o,containerWidth:a,scale:c,family:l}=b({format:this.inputJax[0].name,display:!0,end:w.LAST,em:16,ex:8,containerWidth:null,scale:1,family:""},e);null===a&&(a=80*i);const h=this.inputJax.reduce(((t,e)=>e.name===r?e:t),null),u=new this.options.MathItem(t,h,n);return u.start.node=this.adaptor.body(this.document),u.setMetrics(o,i,a,c),l&&this.outputJax.options.mtextInheritFont&&(u.outputData.mtextFamily=l),l&&this.outputJax.options.merrorInheritFont&&(u.outputData.merrorFamily=l),this.clearPromises(),u.convert(this,s),u.typesetRoot||u.root}convertPromise(t,e={}){return this.whenReady((()=>r((()=>ha(this,void 0,void 0,(function*(){const r=this.convert(t,e);return yield this.actionPromises(),this.clearPromises(),r}))))))}whenReady(t){return this._readyPromise=this._readyPromise.catch((t=>{})).then((()=>{const e=this._readyPromise;this._readyPromise=Promise.resolve();const r=t(),n=this._readyPromise.then((()=>r));return this._readyPromise=e,n}))}actionPromises(){return Promise.all(this._actionPromises)}clearPromises(){this._actionPromises=[]}savePromise(t){this._actionPromises.push(t)}findMath(t=null){return this.processed.set("findMath"),this}compile(){if(!this.processed.isSet("compile")){const t=[];for(const e of this.math)this.compileMath(e),void 0!==e.inputData.recompile&&t.push(e);for(const e of t){const t=e.inputData.recompile;e.state(t.state),e.inputData.recompile=t,this.compileMath(e)}this.processed.set("compile")}return this}compileMath(t){try{t.compile(this)}catch(e){if(e.retry||e.restart)throw e;this.options.compileError(this,t,e),t.inputData.error=e}}compileError(t,e){t.root=this.mmlFactory.create("math",null,[this.mmlFactory.create("merror",{"data-mjx-error":e.message,title:e.message},[this.mmlFactory.create("mtext",null,[this.mmlFactory.create("text").setText("Math input error")])])]),t.display&&t.root.attributes.set("display","block"),t.inputData.error=e.message}typeset(){if(!this.processed.isSet("typeset")){for(const t of this.math)try{t.typeset(this)}catch(e){if(e.retry||e.restart)throw e;this.options.typesetError(this,t,e),t.outputData.error=e}this.processed.set("typeset")}return this}typesetError(t,e){t.typesetRoot=this.adaptor.node("mjx-container",{class:"MathJax mjx-output-error",jax:this.outputJax.name},[this.adaptor.node("span",{"data-mjx-error":e.message,title:e.message,style:{color:"red","background-color":"yellow","line-height":"normal"}},[this.adaptor.text("Math output error")])]),t.display&&this.adaptor.setAttributes(t.typesetRoot,{style:{display:"block",margin:"1em 0","text-align":"center"}}),t.outputData.error=e.message}getMetrics(){return this.processed.isSet("getMetrics")||(this.outputJax.getMetrics(this),this.processed.set("getMetrics")),this}updateDocument(){if(!this.processed.isSet("updateDocument")){for(const t of this.math.reversed())t.updateDocument(this);this.processed.set("updateDocument")}return this}removeFromDocument(t=!1){return this}state(t,e=!1){for(const r of this.math)r.state(t,e);return t<w.INSERTED&&this.processed.clear("updateDocument"),t<w.TYPESET&&(this.processed.clear("typeset"),this.processed.clear("getMetrics")),t<w.COMPILED&&this.processed.clear("compile"),t<w.FINDMATH&&this.processed.clear("findMath"),this}reset(t={processed:!0}){return(t=b(Object.assign({},da),t)).all&&Object.assign(t,pa),t.processed&&this.processed.reset(),t.inputJax&&this.inputJax.forEach((e=>e.reset(...t.inputJax))),t.outputJax&&this.outputJax.reset(...t.outputJax),this}clear(){return this.reset(),this.math.clear(),this}done(){return Promise.resolve()}concat(t){return this.math.merge(t),this}clearMathItemsWithin(t){const e=this.getMathItemsWithin(t);for(const t of e.slice(0).reverse())t.clear();return this.math.remove(...e),e}getMathItemsWithin(t){Array.isArray(t)||(t=[t]);const e=this.adaptor,r=[],n=e.getElements(t,this.document);t:for(const t of this.math)for(const s of n)if(t.start.node&&e.contains(s,t.start.node)){r.push(t);continue t}return r}}Ea.KIND="MathDocument",Ea.OPTIONS={OutputJax:null,InputJax:null,MmlFactory:null,MathList:ga,MathItem:class extends S{},compileError:(t,e,r)=>{t.compileError(e,r)},typesetError:(t,e,r)=>{t.typesetError(e,r)},renderActions:p({find:[w.FINDMATH,"findMath","",!1],compile:[w.COMPILED],metrics:[w.METRICS,"getMetrics","",!1],typeset:[w.TYPESET],update:[w.INSERTED,"updateDocument",!1]})},Ea.ProcessBits=function(...t){const e=class extends la{};return e.allocate(...t),e}("findMath","compile","getMetrics","typeset","updateDocument");class Na extends Ea{}class ba{constructor(t,e=5){this.documentClass=Na,this.adaptor=t,this.priority=e}get name(){return this.constructor.NAME}handlesDocument(t){return!1}create(t,e){return new this.documentClass(t,this.adaptor,e)}}ba.NAME="generic";class Ta{constructor(t=null){const e=this.constructor;this.options=b(N({},e.OPTIONS),t),this.init(),this.getPatterns()}init(){this.strings=[],this.string="",this.snodes=[],this.nodes=[],this.stack=[]}getPatterns(){const t=m(this.options.skipHtmlTags),e=m(this.options.ignoreHtmlClass),r=m(this.options.processHtmlClass);this.skipHtmlTags=new RegExp("^(?:"+t.join("|")+")$","i"),this.ignoreHtmlClass=new RegExp("(?:^| )(?:"+e.join("|")+")(?: |$)"),this.processHtmlClass=new RegExp("(?:^| )(?:"+r+")(?: |$)")}pushString(){this.string.match(/\S/)&&(this.strings.push(this.string),this.nodes.push(this.snodes)),this.string="",this.snodes=[]}extendString(t,e){this.snodes.push([t,e.length]),this.string+=e}handleText(t,e){return e||this.extendString(t,this.adaptor.value(t)),this.adaptor.next(t)}handleTag(t,e){if(!e){const e=this.options.includeHtmlTags[this.adaptor.kind(t)];e instanceof Function?this.extendString(t,e(t,this.adaptor)):this.extendString(t,e)}return this.adaptor.next(t)}handleContainer(t,e){this.pushString();const r=this.adaptor.getAttribute(t,"class")||"",n=this.adaptor.kind(t)||"",s=this.processHtmlClass.exec(r);let i=t;return!this.adaptor.firstChild(t)||this.adaptor.getAttribute(t,"data-MJX")||!s&&this.skipHtmlTags.exec(n)?i=this.adaptor.next(t):(this.adaptor.next(t)&&this.stack.push([this.adaptor.next(t),e]),i=this.adaptor.firstChild(t),e=(e||this.ignoreHtmlClass.exec(r))&&!s),[i,e]}handleOther(t,e){return this.pushString(),this.adaptor.next(t)}find(t){this.init();const e=this.adaptor.next(t);let r=!1;const n=this.options.includeHtmlTags;for(;t&&t!==e;){const e=this.adaptor.kind(t);"#text"===e?t=this.handleText(t,r):Object.hasOwn(n,e)?t=this.handleTag(t,r):e?[t,r]=this.handleContainer(t,r):t=this.handleOther(t,r),!t&&this.stack.length&&(this.pushString(),[t,r]=this.stack.pop())}this.pushString();const s=[this.strings,this.nodes];return this.init(),s}}Ta.OPTIONS={skipHtmlTags:["script","noscript","style","textarea","pre","code","math","select","option","mjx-container"],includeHtmlTags:p({br:"\n",wbr:"","#comment":""}),ignoreHtmlClass:"mathjax_ignore",processHtmlClass:"mathjax_process"},M("STYLES",w.INSERTED+1);class ya extends Ea{constructor(t,e,r){const[n,s]=T(r,Ta.OPTIONS);super(t,e,n),this.domStrings=this.options.DomStrings||new Ta(s),this.domStrings.adaptor=e,this.styles=[]}findPosition(t,e,r,n){const s=this.adaptor,i=1/(n[t].length||1);let o=t;for(const[a,c]of n[t]){if(e<=c&&"#text"===s.kind(a))return{i:o,node:a,n:Math.max(e,0),delim:r};e-=c,o+=i}return{node:null,n:0,delim:r}}mathItem(t,e,r){const n=t.math,s=this.findPosition(t.n,t.start.n,t.open,r),i=this.findPosition(t.n,t.end.n,t.close,r);return new this.options.MathItem(n,e,t.display,s,i)}findMath(t){if(!this.processed.isSet("findMath")){this.adaptor.document=this.document,t=b({elements:this.options.elements||[this.adaptor.body(this.document)]},t);const e=this.adaptor.getElements(t.elements,this.document);for(const t of this.inputJax){const r=t.processStrings?this.findMathFromStrings(t,e):this.findMathFromDOM(t,e);this.math.merge(r)}this.processed.set("findMath")}return this}findMathFromStrings(t,e){const r=[],n=[];for(const t of e){const[e,s]=this.domStrings.find(t);r.push(...e),n.push(...s)}const s=new this.options.MathList;for(const e of t.findMath(r))s.push(this.mathItem(e,t,n));return s}findMathFromDOM(t,e){const r=[];for(const n of e)for(const e of t.findMath(n))r.push(new this.options.MathItem(e.math,t,e.display,e.start,e.end));return new this.options.MathList(...r)}updateDocument(){return this.processed.isSet("updateDocument")||(this.addPageElements(),this.addStyleSheet(),super.updateDocument(),this.processed.set("updateDocument")),this}addPageElements(){const t=this.adaptor,e=t.body(this.document),r=this.documentPageElements();if(r){const n=t.firstChild(e);n?t.insert(r,n):t.append(e,r)}}addStyleSheet(){const t=this.documentStyleSheet(),e=this.adaptor;if(t&&!e.parent(t)){const r=e.head(this.document),n=this.findSheet(r,e.getAttribute(t,"id"));n?e.replace(t,n):e.append(r,t)}}findSheet(t,e){if(e)for(const r of this.adaptor.tags(t,"style"))if(this.adaptor.getAttribute(r,"id")===e)return r;return null}removeFromDocument(t=!1){if(this.processed.isSet("updateDocument"))for(const e of this.math)e.state()>=w.INSERTED&&e.state(w.TYPESET,t);return this.processed.clear("updateDocument"),this}documentStyleSheet(){return this.outputJax.styleSheet(this)}documentPageElements(){return this.outputJax.pageElements(this)}addStyles(t){this.styles.push(t),"insertStyles"in this.outputJax&&this.outputJax.insertStyles(t)}getStyles(){return this.styles}}ya.KIND="HTML",ya.OPTIONS=Object.assign(Object.assign({},Ea.OPTIONS),{renderActions:p(Object.assign(Object.assign({},Ea.OPTIONS.renderActions),{styles:[w.STYLES,"","updateStyleSheet",!1]})),MathList:class extends ca{},MathItem:class extends S{get adaptor(){return this.inputJax.adaptor}constructor(t,e,r=!0,n={node:null,n:0,delim:""},s={node:null,n:0,delim:""}){super(t,e,r,n,s)}updateDocument(t){if(this.state()<w.INSERTED){if(this.inputJax.processStrings){let t=this.start.node;if(t===this.end.node)this.end.n&&this.end.n<this.adaptor.value(this.end.node).length&&this.adaptor.split(this.end.node,this.end.n),this.start.n&&(t=this.adaptor.split(this.start.node,this.start.n)),this.adaptor.replace(this.typesetRoot,t);else{for(this.start.n&&(t=this.adaptor.split(t,this.start.n));t!==this.end.node;){const e=this.adaptor.next(t);this.adaptor.remove(t),t=e}this.adaptor.insert(this.typesetRoot,t),this.end.n<this.adaptor.value(t).length&&this.adaptor.split(t,this.end.n),this.adaptor.remove(t)}}else this.adaptor.replace(this.typesetRoot,this.start.node);this.start.node=this.end.node=this.typesetRoot,this.start.n=this.end.n=0,this.state(w.INSERTED)}}updateStyleSheet(t){t.addStyleSheet()}removeFromDocument(t=!1){if(super.removeFromDocument(t),this.state()>=w.TYPESET){const e=this.adaptor,r=this.start.node;let n=e.text("");if(t){const t=this.start.delim+this.math+this.end.delim;if(this.inputJax.processStrings)n=e.text(t);else{const r=e.parse(t,"text/html");n=e.firstChild(e.body(r))}}e.parent(r)&&e.replace(n,r),this.start.node=this.end.node=n,this.start.n=this.end.n=0}}},DomStrings:null});class Ia extends ba{constructor(){super(...arguments),this.documentClass=ya}handlesDocument(t){const e=this.adaptor;if("string"==typeof t)try{t=e.parse(t,"text/html")}catch(t){}return t instanceof e.window.Document||t instanceof e.window.HTMLElement||t instanceof e.window.DocumentFragment}create(t,e){const r=this.adaptor;if("string"==typeof t)t=r.parse(t,"text/html");else if(t instanceof r.window.HTMLElement||t instanceof r.window.DocumentFragment){const e=t;t=r.parse("","text/html"),r.append(r.body(t),e)}return super.create(t,e)}}class Aa extends Ke{constructor(t,...e){super(t),this.factory.configuration.tags.start("multline",!0,e[0])}get kind(){return"multline"}EndEntry(){this.table.length&&Bt.fixInitialMO(this.factory.configuration,this.nodes);const t=this.getProperty("shove"),e=this.create("node","mtd",this.nodes,t?{columnalign:t}:{});this.setProperty("shove",null),this.row.push(e),this.Clear()}EndRow(){if(1!==this.row.length)throw new gt("MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline");const t=this.create("node","mtr",this.row);this.table.push(t),this.row=[]}EndTable(){if(super.EndTable(),this.table.length){const t=this.table.length-1;let e=-1;nt.getAttribute(nt.getChildren(this.table[0])[0],"columnalign")||nt.setAttribute(nt.getChildren(this.table[0])[0],"columnalign",At.LEFT),nt.getAttribute(nt.getChildren(this.table[t])[0],"columnalign")||nt.setAttribute(nt.getChildren(this.table[t])[0],"columnalign",At.RIGHT);const r=this.factory.configuration.tags.getTag();if(r){e=this.arraydef.side===At.LEFT?0:this.table.length-1;const t=this.table[e],n=this.create("node","mlabeledtr",[r].concat(nt.getChildren(t)));nt.copyAttributes(t,n),this.table[e]=n}}this.factory.configuration.tags.end()}}class Ra extends ze{get kind(){return"flalign"}constructor(t,e,r,n,s){super(t),this.name=e,this.numbered=r,this.padded=n,this.center=s,this.factory.configuration.tags.start(e,r,r)}EndEntry(){super.EndEntry();const t=this.getProperty("xalignat");if(t&&this.row.length>t)throw new gt("XalignOverflow","Extra %1 in row of %2","&",this.name)}EndRow(){let t;const e=this.row,r=this.getProperty("xalignat");for(;e.length<r;)e.push(this.create("node","mtd"));for(this.row=[],this.padded&&this.row.push(this.create("node","mtd"));t=e.shift();)this.row.push(t),t=e.shift(),t&&this.row.push(t),(e.length||this.padded)&&this.row.push(this.create("node","mtd"));this.row.length>this.maxrow&&(this.maxrow=this.row.length),super.EndRow();const n=this.table[this.table.length-1];if(this.getProperty("zeroWidthLabel")&&n.isKind("mlabeledtr")){const t=nt.getChildren(n)[0],e=this.factory.configuration.options.tagSide,r=Object.assign({width:0},"right"===e?{lspace:"-1width"}:{}),s=this.create("node","mpadded",nt.getChildren(t),r);t.setChildren([s])}}EndTable(){if(super.EndTable(),this.center&&this.maxrow<=2){delete this.arraydef.width,delete this.global.indentalign}}}var La;!function(t){t.NEW_DELIMITER="new-Delimiter",t.NEW_COMMAND="new-Command",t.NEW_ENVIRONMENT="new-Environment"}(La||(La={}));const Ca={GetCSname(t,e){if("\\"!==t.GetNext())throw new gt("MissingCS","%1 must be followed by a control sequence",e);const r=pt.trimSpaces(t.GetArgument(e)).substring(1);return this.checkProtectedMacros(t,r),r},GetCsNameArgument(t,e){let r=pt.trimSpaces(t.GetArgument(e));if("\\"===r.charAt(0)&&(r=r.substring(1)),!r.match(/^(.|[a-z]+)$/i))throw new gt("IllegalControlSequenceName","Illegal control sequence name for %1",e);return this.checkProtectedMacros(t,r),r},GetArgCount(t,e){let r=t.GetBrackets(e);if(r&&(r=pt.trimSpaces(r),!r.match(/^[0-9]+$/)))throw new gt("IllegalParamNumber","Illegal number of parameters specified in %1",e);return r},GetTemplate(t,e,r){let n=t.GetNext();const s=[];let i=0,o=t.i;for(;t.i<t.string.length;){if(n=t.GetNext(),"#"===n){if(o!==t.i&&(s[i]=t.string.substring(o,t.i)),n=t.string.charAt(++t.i),!n.match(/^[1-9]$/))throw new gt("CantUseHash2","Illegal use of # in template for %1",r);if(parseInt(n)!==++i)throw new gt("SequentialParam","Parameters for %1 must be numbered sequentially",r);o=t.i+1}else if("{"===n)return o!==t.i&&(s[i]=t.string.substring(o,t.i)),s.length>0?[i.toString()].concat(s):i;t.i++}throw new gt("MissingReplacementString","Missing replacement string for definition of %1",e)},GetParameter(t,e,r){if(null==r)return t.GetArgument(e);let n=t.i,s=0,i=!1;for(;t.i<t.string.length;){const o=t.string.charAt(t.i);if("{"===o)i=t.i===n,t.GetArgument(e),s=t.i-n;else{if(this.MatchParam(t,r))return i&&(n++,s-=2),t.string.substring(n,n+s);if("\\"===o){t.i++,s++,i=!1;const e=t.string.substring(t.i).match(/[a-z]+|./i);e&&(t.i+=e[0].length,s=t.i-n)}else t.i++,s++,i=!1}}throw new gt("RunawayArgument","Runaway argument for %1?",e)},MatchParam:(t,e)=>t.string.substring(t.i,t.i+e.length)!==e||e.match(/\\[a-z]+$/i)&&t.string.charAt(t.i+e.length).match(/[a-z]/i)?0:(t.i+=e.length,1),checkGlobal:(t,e,r)=>t.stack.env.isGlobal?t.configuration.packageData.get("begingroup").stack.checkGlobal(e,r):r.map((e=>t.configuration.handlers.retrieve(e))),checkProtectedMacros(t,e){var r;if(null===(r=t.options.protectedMacros)||void 0===r?void 0:r.includes(e))throw new gt("ProtectedMacro","The control sequence %1 can't be redefined",`\\${e}`)},addDelimiter(t,e,r,n){const s=e.substring(1);this.checkProtectedMacros(t,s);const[i,o]=Ca.checkGlobal(t,[s,e],[La.NEW_COMMAND,La.NEW_DELIMITER]);s!==e&&i.remove(s),o.add(e,new Yt(e,r,n)),delete t.stack.env.isGlobal},addMacro(t,e,r,n,s=""){this.checkProtectedMacros(t,e);const i=Ca.checkGlobal(t,[e],[La.NEW_COMMAND])[0];this.undefineDelimiter(t,"\\"+e),i.add(e,new $t(s||e,r,n)),delete t.stack.env.isGlobal},addEnvironment(t,e,r,n){Ca.checkGlobal(t,[e],[La.NEW_ENVIRONMENT])[0].add(e,new $t(e,r,n)),delete t.stack.env.isGlobal},undefineMacro(t,e){const r=Ca.checkGlobal(t,[e],[La.NEW_COMMAND])[0];r.remove(e),t.configuration.handlers.get(ut.MACRO).applicable(e)&&(r.add(e,new $t(e,(()=>ce.FALLBACK),[])),this.undefineDelimiter(t,"\\"+e)),delete t.stack.env.isGlobal},undefineDelimiter(t,e){const r=Ca.checkGlobal(t,[e],[La.NEW_DELIMITER])[0];r.remove(e),t.configuration.handlers.get(ut.DELIMITER).applicable(e)&&r.add(e,new Yt(e,null,{})),delete t.stack.env.isGlobal}};function Oa(t){if(!t||t.isInferred&&0===t.childNodes.length)return[null,null];if(t.isKind("msubsup")&&va(t))return[t,null];const e=nt.getChildAt(t,0);return t.isInferred&&e&&va(e)?(t.childNodes.splice(0,1),[e,t]):[null,t]}function va(t){const e=t.childNodes[0];return e&&e.isKind("mi")&&""===e.getText()}const Sa={AmsEqnArray(t,e,r,n,s,i,o,a){const c=t.GetBrackets("\\begin{"+e.getName()+"}"),l=or.EqnArray(t,e,r,n,s,i,o,a);return Bt.setArrayAlign(l,c,t)},AlignAt(t,e,r,n){const s=e.getName();let i,o="",a="";const c=[];n||(i=t.GetBrackets("\\begin{"+s+"}"));const l=t.GetArgument("\\begin{"+s+"}");if(l.match(/[^0-9]/))throw new gt("PositiveIntegerArg","Argument to %1 must be a positive integer","\\begin{"+s+"}");let h=parseInt(l,10);for(;h>0;)o+="rl",a+="bt",c.push("0em 0em"),h--;const u=c.join(" ");if(n)return Sa.EqnArray(t,e,r,n,o,a,u);const d=Sa.EqnArray(t,e,r,n,o,a,u);return Bt.setArrayAlign(d,i,t)},Multline(t,e,r){Bt.checkEqnEnv(t),t.Push(e);const n=t.options.ams.multlineIndent,s=t.itemFactory.create("multline",r,t.stack);return s.arraydef={displaystyle:!0,rowspacing:".5em",columnspacing:"100%",width:t.options.ams.multlineWidth,side:t.options.tagSide,minlabelspacing:t.options.tagIndent,"data-array-padding":`${n} ${n}`,"data-width-includes-label":!0},s},XalignAt(t,e,r,n){const s=t.GetArgument("\\begin{"+e.getName()+"}");if(s.match(/[^0-9]/))throw new gt("PositiveIntegerArg","Argument to %1 must be a positive integer","\\begin{"+e.getName()+"}");const i=n?"crl":"rlc",o=n?"mbt":"btm",a=n?"fit auto auto":"auto auto fit",c=Sa.FlalignArray(t,e,r,n,!1,i,o,a,!0);return c.setProperty("xalignat",2*parseInt(s)),c},FlalignArray(t,e,r,n,s,i,o,a,c=!1){Bt.checkEqnEnv(t),t.Push(e),i=i.split("").join(" ").replace(/r/g,"right").replace(/l/g,"left").replace(/c/g,"center"),o=nr(o);const l=t.itemFactory.create("flalign",e.getName(),r,n,s,t.stack);return l.arraydef={width:"100%",displaystyle:!0,columnalign:i,columnspacing:"0em",columnwidth:a,rowspacing:"3pt","data-break-align":o,side:t.options.tagSide,minlabelspacing:c?"0":t.options.tagIndent,"data-width-includes-label":!0},l.setProperty("zeroWidthLabel",c),l},HandleDeclareOp(t,e){const r=t.GetStar()?"*":"",n=Ca.GetCsNameArgument(t,e),s=t.GetArgument(e);Ca.addMacro(t,n,Sa.Macro,[`\\operatorname${r}{${s}}`]),t.Push(t.itemFactory.create("null"))},HandleOperatorName(t,e){const r=t.GetStar(),n=pt.trimSpaces(t.GetArgument(e));let s=new Lt(n,Object.assign(Object.assign({},t.stack.env),{font:bt.NORMAL,multiLetterIdentifiers:t.options.ams.operatornamePattern,operatorLetters:!0}),t.configuration).mml();if(s.isKind("mi")||(s=t.create("node","TeXAtom",[s])),nt.setProperties(s,{movesupsub:r,movablelimits:!0,texClass:F.OP}),!r){const e=t.GetNext(),r=t.i;"\\"===e&&++t.i&&"limits"!==t.GetCS()&&(t.i=r)}t.Push(t.itemFactory.create("fn",s))},SideSet(t,e){const[r,n]=Oa(t.ParseArg(e)),[s,i]=Oa(t.ParseArg(e)),o=t.ParseArg(e);let a=o;r&&(n?r.replaceChild(t.create("node","mphantom",[t.create("node","mpadded",[Bt.copyNode(o,t)],{width:0})]),nt.getChildAt(r,0)):(a=t.create("node","mmultiscripts",[o]),s&&nt.appendChildren(a,[nt.getChildAt(s,1)||t.create("node","none"),nt.getChildAt(s,2)||t.create("node","none")]),nt.setProperty(a,"scriptalign","left"),nt.appendChildren(a,[t.create("node","mprescripts"),nt.getChildAt(r,1)||t.create("node","none"),nt.getChildAt(r,2)||t.create("node","none")]))),s&&a===o&&(s.replaceChild(o,nt.getChildAt(s,0)),a=s);const c=t.create("node","TeXAtom",[],{texClass:F.OP,movesupsub:!0,movablelimits:!0});n&&(r&&c.appendChild(r),c.appendChild(n)),c.appendChild(a),i&&c.appendChild(i),t.Push(c)},operatorLetter:(t,e)=>!!t.stack.env.operatorLetters&&cr.variable(t,e),MultiIntegral(t,e,r){let n=t.GetNext();if("\\"===n){const s=t.i;n=t.GetArgument(e),t.i=s,"\\limits"===n&&(r="\\!\\!\\mathop{\\,\\,"+r+"}")}t.string=r+" "+t.string.slice(t.i),t.i=0},xArrow(t,e,r,n,s,i=0){const o={width:"+"+pt.em((n+s)/18),lspace:pt.em(n/18)},a=t.GetBrackets(e),c=t.ParseArg(e),l=t.create("node","mspace",[],{depth:".2em"});let h=t.create("token","mo",{stretchy:!0,texClass:F.REL},String.fromCodePoint(r));i&&h.attributes.set("minsize",pt.em(i)),h=t.create("node","mstyle",[h],{scriptlevel:0});const u=t.create("node","munderover",[h]);let d=t.create("node","mpadded",[c,l],o);if(nt.setAttribute(d,"voffset","-.2em"),nt.setAttribute(d,"height","-.2em"),nt.setChild(u,u.over,d),a){const e=new Lt(a,t.stack.env,t.configuration).mml(),r=t.create("node","mspace",[],{height:".75em"});d=t.create("node","mpadded",[e,r],o),nt.setAttribute(d,"voffset",".15em"),nt.setAttribute(d,"depth","-.15em"),nt.setChild(u,u.under,d)}nt.setProperty(u,"subsupOK",!0),t.Push(u)},HandleShove(t,e,r){const n=t.stack.Top();if("multline"!==n.kind)throw new gt("CommandOnlyAllowedInEnv","%1 only allowed in %2 environment",t.currentCS,"multline");if(n.Size())throw new gt("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",t.currentCS);n.setProperty("shove",r)},CFrac(t,e){let r=pt.trimSpaces(t.GetBrackets(e,""));const n=t.GetArgument(e),s=t.GetArgument(e),i={l:At.LEFT,r:At.RIGHT,"":""},o=new Lt("\\strut\\textstyle{"+n+"}",t.stack.env,t.configuration).mml(),a=new Lt("\\strut\\textstyle{"+s+"}",t.stack.env,t.configuration).mml(),c=t.create("node","mfrac",[o,a]);if(r=i[r],null==r)throw new gt("IllegalAlign","Illegal alignment specified in %1",t.currentCS);r&&nt.setProperties(c,{numalign:r,denomalign:r}),t.Push(c)},Genfrac(t,e,r,n,s,i){null==r&&(r=t.GetDelimiterArg(e)),null==n&&(n=t.GetDelimiterArg(e)),null==s&&(s=t.GetArgument(e)),null==i&&(i=pt.trimSpaces(t.GetArgument(e)));const o=t.ParseArg(e),a=t.ParseArg(e);let c=t.create("node","mfrac",[o,a]);if(""!==s&&nt.setAttribute(c,"linethickness",s),(r||n)&&(nt.setProperty(c,"withDelims",!0),c=Bt.fixedFence(t.configuration,r,c,n)),""!==i){const e=parseInt(i,10),r=["D","T","S","SS"][e];if(null==r)throw new gt("BadMathStyleFor","Bad math style for %1",t.currentCS);c=t.create("node","mstyle",[c]),"D"===r?nt.setProperties(c,{displaystyle:!0,scriptlevel:0}):nt.setProperties(c,{displaystyle:!1,scriptlevel:e-1})}t.Push(c)},HandleTag(t,e){if(!t.tags.currentTag.taggable&&t.tags.env)throw new gt("CommandNotAllowedInEnv","%1 not allowed in %2 environment",t.currentCS,t.tags.env);if(t.tags.currentTag.tag)throw new gt("MultipleCommand","Multiple %1",t.currentCS);const r=t.GetStar(),n=pt.trimSpaces(t.GetArgument(e));t.tags.tag(n,r),t.Push(t.itemFactory.create("null"))},HandleNoTag:or.HandleNoTag,HandleRef:or.HandleRef,Macro:or.Macro,Accent:or.Accent,Tilde:or.Tilde,Array:or.Array,Spacer:or.Spacer,NamedOp:or.NamedOp,EqnArray:or.EqnArray,Equation:or.Equation};new ee("AMSmath-mathchar0mo",cr.mathchar0mo,{iiiint:["\u2a0c",{texClass:F.OP}]}),new Zt("AMSmath-operatorLetter",Sa.operatorLetter,/[-*]/i),new se("AMSmath-macros",{mathring:[Sa.Accent,"02DA"],nobreakspace:Sa.Tilde,negmedspace:[Sa.Spacer,Ze.negativemediummathspace],negthickspace:[Sa.Spacer,Ze.negativethickmathspace],idotsint:[Sa.MultiIntegral,"\\int\\cdots\\int"],dddot:[Sa.Accent,"20DB"],ddddot:[Sa.Accent,"20DC"],sideset:Sa.SideSet,boxed:[Sa.Macro,"\\fbox{$\\displaystyle{#1}$}",1],tag:Sa.HandleTag,notag:Sa.HandleNoTag,eqref:[Sa.HandleRef,!0],substack:[Sa.Macro,"\\begin{subarray}{c}#1\\end{subarray}",1],injlim:[Sa.NamedOp,"inj&thinsp;lim"],projlim:[Sa.NamedOp,"proj&thinsp;lim"],varliminf:[Sa.Macro,"\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:[Sa.Macro,"\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:[Sa.Macro,"\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:[Sa.Macro,"\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:Sa.HandleDeclareOp,operatorname:Sa.HandleOperatorName,genfrac:Sa.Genfrac,frac:[Sa.Genfrac,"","","",""],tfrac:[Sa.Genfrac,"","","","1"],dfrac:[Sa.Genfrac,"","","","0"],binom:[Sa.Genfrac,"(",")","0",""],tbinom:[Sa.Genfrac,"(",")","0","1"],dbinom:[Sa.Genfrac,"(",")","0","0"],cfrac:Sa.CFrac,shoveleft:[Sa.HandleShove,At.LEFT],shoveright:[Sa.HandleShove,At.RIGHT],xrightarrow:[Sa.xArrow,8594,5,10],xleftarrow:[Sa.xArrow,8592,10,5]}),new ie("AMSmath-environment",cr.environment,{"equation*":[Sa.Equation,null,!1],"eqnarray*":[Sa.EqnArray,null,!1,!0,"rcl","bmt",Bt.cols(0,Ze.thickmathspace),".5em"],align:[Sa.EqnArray,null,!0,!0,"rl","bt",Bt.cols(0,2)],"align*":[Sa.EqnArray,null,!1,!0,"rl","bt",Bt.cols(0,2)],multline:[Sa.Multline,null,!0],"multline*":[Sa.Multline,null,!1],split:[Sa.EqnArray,null,!1,!1,"rl","bt",Bt.cols(0)],gather:[Sa.EqnArray,null,!0,!0,"c","m"],"gather*":[Sa.EqnArray,null,!1,!0,"c","m"],alignat:[Sa.AlignAt,null,!0,!0],"alignat*":[Sa.AlignAt,null,!1,!0],alignedat:[Sa.AlignAt,null,!1,!1],aligned:[Sa.AmsEqnArray,null,null,null,"rl","bt",Bt.cols(0,2),".5em","D"],gathered:[Sa.AmsEqnArray,null,null,null,"c","m",null,".5em","D"],xalignat:[Sa.XalignAt,null,!0,!0],"xalignat*":[Sa.XalignAt,null,!1,!0],xxalignat:[Sa.XalignAt,null,!1,!1],flalign:[Sa.FlalignArray,null,!0,!1,!0,"rlc","btm","auto auto fit"],"flalign*":[Sa.FlalignArray,null,!1,!1,!0,"rlc","btm","auto auto fit"],subarray:[Sa.Array,null,null,null,null,Bt.cols(0),"0.1em","S",!0],smallmatrix:[Sa.Array,null,null,null,"c",Bt.cols(1/3),".2em","S",!0],matrix:[Sa.Array,null,null,null,"c"],pmatrix:[Sa.Array,null,"(",")","c"],bmatrix:[Sa.Array,null,"[","]","c"],Bmatrix:[Sa.Array,null,"\\{","\\}","c"],vmatrix:[Sa.Array,null,"\\vert","\\vert","c"],Vmatrix:[Sa.Array,null,"\\Vert","\\Vert","c"],cases:[Sa.Array,null,"\\{",".","ll",null,".2em","T"]}),new re("AMSmath-delimiter",cr.delimiter,{"\\lvert":["|",{texClass:F.OPEN}],"\\rvert":["|",{texClass:F.CLOSE}],"\\lVert":["\u2016",{texClass:F.OPEN}],"\\rVert":["\u2016",{texClass:F.CLOSE}]}),new ee("AMSsymbols-mathchar0mi",cr.mathchar0mi,{digamma:"\u03dd",varkappa:"\u03f0",varGamma:["\u0393",{mathvariant:bt.ITALIC}],varDelta:["\u0394",{mathvariant:bt.ITALIC}],varTheta:["\u0398",{mathvariant:bt.ITALIC}],varLambda:["\u039b",{mathvariant:bt.ITALIC}],varXi:["\u039e",{mathvariant:bt.ITALIC}],varPi:["\u03a0",{mathvariant:bt.ITALIC}],varSigma:["\u03a3",{mathvariant:bt.ITALIC}],varUpsilon:["\u03a5",{mathvariant:bt.ITALIC}],varPhi:["\u03a6",{mathvariant:bt.ITALIC}],varPsi:["\u03a8",{mathvariant:bt.ITALIC}],varOmega:["\u03a9",{mathvariant:bt.ITALIC}],beth:"\u2136",gimel:"\u2137",daleth:"\u2138",backprime:["\u2035",{variantForm:!0}],hslash:"\u210f",varnothing:["\u2205",{variantForm:!0}],blacktriangle:"\u25b4",triangledown:["\u25bd",{variantForm:!0}],blacktriangledown:"\u25be",square:"\u25fb",Box:"\u25fb",blacksquare:"\u25fc",lozenge:"\u25ca",Diamond:"\u25ca",blacklozenge:"\u29eb",circledS:["\u24c8",{mathvariant:bt.NORMAL}],bigstar:"\u2605",sphericalangle:"\u2222",measuredangle:"\u2221",nexists:"\u2204",complement:"\u2201",mho:"\u2127",eth:["\xf0",{mathvariant:bt.NORMAL}],Finv:"\u2132",diagup:"\u2571",Game:"\u2141",diagdown:"\u2572",Bbbk:["k",{mathvariant:bt.DOUBLESTRUCK}],yen:"\xa5",circledR:"\xae",checkmark:"\u2713",maltese:"\u2720"}),new ee("AMSsymbols-mathchar0mo",cr.mathchar0mo,{dotplus:"\u2214",ltimes:"\u22c9",smallsetminus:["\u2216",{variantForm:!0}],rtimes:"\u22ca",Cap:"\u22d2",doublecap:"\u22d2",leftthreetimes:"\u22cb",Cup:"\u22d3",doublecup:"\u22d3",rightthreetimes:"\u22cc",barwedge:"\u22bc",curlywedge:"\u22cf",veebar:"\u22bb",curlyvee:"\u22ce",doublebarwedge:"\u2a5e",boxminus:"\u229f",circleddash:"\u229d",boxtimes:"\u22a0",circledast:"\u229b",boxdot:"\u22a1",circledcirc:"\u229a",boxplus:"\u229e",centerdot:["\u22c5",{variantForm:!0}],divideontimes:"\u22c7",intercal:"\u22ba",leqq:"\u2266",geqq:"\u2267",leqslant:"\u2a7d",geqslant:"\u2a7e",eqslantless:"\u2a95",eqslantgtr:"\u2a96",lesssim:"\u2272",gtrsim:"\u2273",lessapprox:"\u2a85",gtrapprox:"\u2a86",approxeq:"\u224a",lessdot:"\u22d6",gtrdot:"\u22d7",lll:"\u22d8",llless:"\u22d8",ggg:"\u22d9",gggtr:"\u22d9",lessgtr:"\u2276",gtrless:"\u2277",lesseqgtr:"\u22da",gtreqless:"\u22db",lesseqqgtr:"\u2a8b",gtreqqless:"\u2a8c",doteqdot:"\u2251",Doteq:"\u2251",eqcirc:"\u2256",risingdotseq:"\u2253",circeq:"\u2257",fallingdotseq:"\u2252",triangleq:"\u225c",backsim:"\u223d",thicksim:["\u223c",{variantForm:!0}],backsimeq:"\u22cd",thickapprox:["\u2248",{variantForm:!0}],subseteqq:"\u2ac5",supseteqq:"\u2ac6",Subset:"\u22d0",Supset:"\u22d1",sqsubset:"\u228f",sqsupset:"\u2290",preccurlyeq:"\u227c",succcurlyeq:"\u227d",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",precsim:"\u227e",succsim:"\u227f",precapprox:"\u2ab7",succapprox:"\u2ab8",vartriangleleft:"\u22b2",lhd:"\u22b2",vartriangleright:"\u22b3",rhd:"\u22b3",trianglelefteq:"\u22b4",unlhd:"\u22b4",trianglerighteq:"\u22b5",unrhd:"\u22b5",vDash:"\u22a8",Vdash:"\u22a9",Vvdash:"\u22aa",smallsmile:["\u2323",{variantForm:!0}],shortmid:["\u2223",{variantForm:!0}],smallfrown:["\u2322",{variantForm:!0}],shortparallel:["\u2225",{variantForm:!0}],bumpeq:"\u224f",between:"\u226c",Bumpeq:"\u224e",pitchfork:"\u22d4",varpropto:["\u221d",{variantForm:!0}],backepsilon:"\u220d",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",therefore:"\u2234",because:"\u2235",eqsim:"\u2242",vartriangle:["\u25b3",{variantForm:!0}],Join:"\u22c8",nless:"\u226e",ngtr:"\u226f",nleq:"\u2270",ngeq:"\u2271",nleqslant:["\u2a87",{variantForm:!0}],ngeqslant:["\u2a88",{variantForm:!0}],nleqq:["\u2270",{variantForm:!0}],ngeqq:["\u2271",{variantForm:!0}],lneq:"\u2a87",gneq:"\u2a88",lneqq:"\u2268",gneqq:"\u2269",lvertneqq:["\u2268",{variantForm:!0}],gvertneqq:["\u2269",{variantForm:!0}],lnsim:"\u22e6",gnsim:"\u22e7",lnapprox:"\u2a89",gnapprox:"\u2a8a",nprec:"\u2280",nsucc:"\u2281",npreceq:["\u22e0",{variantForm:!0}],nsucceq:["\u22e1",{variantForm:!0}],precneqq:"\u2ab5",succneqq:"\u2ab6",precnsim:"\u22e8",succnsim:"\u22e9",precnapprox:"\u2ab9",succnapprox:"\u2aba",nsim:"\u2241",ncong:"\u2247",nshortmid:["\u2224",{variantForm:!0}],nshortparallel:["\u2226",{variantForm:!0}],nmid:"\u2224",nparallel:"\u2226",nvdash:"\u22ac",nvDash:"\u22ad",nVdash:"\u22ae",nVDash:"\u22af",ntriangleleft:"\u22ea",ntriangleright:"\u22eb",ntrianglelefteq:"\u22ec",ntrianglerighteq:"\u22ed",nsubseteq:"\u2288",nsupseteq:"\u2289",nsubseteqq:["\u2288",{variantForm:!0}],nsupseteqq:["\u2289",{variantForm:!0}],subsetneq:"\u228a",supsetneq:"\u228b",varsubsetneq:["\u228a",{variantForm:!0}],varsupsetneq:["\u228b",{variantForm:!0}],subsetneqq:"\u2acb",supsetneqq:"\u2acc",varsubsetneqq:["\u2acb",{variantForm:!0}],varsupsetneqq:["\u2acc",{variantForm:!0}],leftleftarrows:"\u21c7",rightrightarrows:"\u21c9",leftrightarrows:"\u21c6",rightleftarrows:"\u21c4",Lleftarrow:"\u21da",Rrightarrow:"\u21db",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",leftarrowtail:"\u21a2",rightarrowtail:"\u21a3",looparrowleft:"\u21ab",looparrowright:"\u21ac",leftrightharpoons:"\u21cb",rightleftharpoons:["\u21cc",{variantForm:!0}],curvearrowleft:"\u21b6",curvearrowright:"\u21b7",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",Lsh:"\u21b0",Rsh:"\u21b1",upuparrows:"\u21c8",downdownarrows:"\u21ca",upharpoonleft:"\u21bf",upharpoonright:"\u21be",downharpoonleft:"\u21c3",restriction:"\u21be",multimap:"\u22b8",downharpoonright:"\u21c2",leftrightsquigarrow:"\u21ad",rightsquigarrow:"\u21dd",leadsto:"\u21dd",dashrightarrow:"\u21e2",dashleftarrow:"\u21e0",nleftarrow:"\u219a",nrightarrow:"\u219b",nLeftarrow:"\u21cd",nRightarrow:"\u21cf",nleftrightarrow:"\u21ae",nLeftrightarrow:"\u21ce"}),new re("AMSsymbols-delimiter",cr.delimiter,{"\\ulcorner":"\u231c","\\urcorner":"\u231d","\\llcorner":"\u231e","\\lrcorner":"\u231f"}),new se("AMSsymbols-macros",{implies:[Sa.Macro,"\\;\\Longrightarrow\\;"],impliedby:[Sa.Macro,"\\;\\Longleftarrow\\;"]});class wa extends Nt{get kind(){return"beginEnv"}get isOpen(){return!0}checkItem(t){if(t.isKind("end")){if(t.getName()!==this.getName())throw new gt("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),t.getName());return[[this.factory.create("mml",this.toMml())],!0]}if(t.isKind("stop"))throw new gt("EnvMissingEnd","Missing \\end{%1}",this.getName());return super.checkItem(t)}}const Ma={NewCommand(t,e){const r=Ca.GetCsNameArgument(t,e),n=Ca.GetArgCount(t,e),s=t.GetBrackets(e),i=t.GetArgument(e);Ca.addMacro(t,r,Ma.Macro,[i,n,s]),t.Push(t.itemFactory.create("null"))},NewEnvironment(t,e){const r=pt.trimSpaces(t.GetArgument(e)),n=Ca.GetArgCount(t,e),s=t.GetBrackets(e),i=t.GetArgument(e),o=t.GetArgument(e);Ca.addEnvironment(t,r,Ma.BeginEnv,[!0,i,o,n,s]),t.Push(t.itemFactory.create("null"))},MacroDef(t,e){const r=Ca.GetCSname(t,e),n=Ca.GetTemplate(t,e,"\\"+r),s=t.GetArgument(e);n instanceof Array?Ca.addMacro(t,r,Ma.MacroWithTemplate,[s].concat(n)):Ca.addMacro(t,r,Ma.Macro,[s,n]),t.Push(t.itemFactory.create("null"))},Let(t,e){const r=Ca.GetCSname(t,e);let n=t.GetNext();"="===n&&(t.i++,n=t.GetNext());const s=t.configuration.handlers;if(t.Push(t.itemFactory.create("null")),"\\"===n){if(r===(e=Ca.GetCSname(t,e)))return;const n=s.get(ut.MACRO).applicable(e);if(n instanceof ne){const s=n.lookup(e);return void Ca.addMacro(t,r,s.func,s.args,s.token)}if(n instanceof ee&&!(n instanceof re)){const s=n.lookup(e),i=t=>n.parser(t,s);return void Ca.addMacro(t,r,i,[r,s.char])}const i=s.get(ut.DELIMITER).lookup("\\"+e);return i?void Ca.addDelimiter(t,"\\"+r,i.char,i.attributes):(Ca.checkProtectedMacros(t,r),Ca.undefineMacro(t,r),void Ca.undefineDelimiter(t,"\\"+r))}t.i++;const i=s.get(ut.DELIMITER).lookup(n);i?Ca.addDelimiter(t,"\\"+r,i.char,i.attributes):Ca.addMacro(t,r,Ma.Macro,[n])},MacroWithTemplate(t,e,r,n,...s){const i=parseInt(n,10);if(s.length){const n=[];if(t.GetNext(),s[0]&&!Ca.MatchParam(t,s[0]))throw new gt("MismatchUseDef","Use of %1 doesn't match its definition",e);if(i){for(let r=0;r<i;r++)n.push(Ca.GetParameter(t,e,s[r+1]));r=Bt.substituteArgs(t,n,r)}}t.string=Bt.addArgs(t,r,t.string.slice(t.i)),t.i=0,Bt.checkMaxMacros(t)},BeginEnv(t,e,r,n,s,i){const o=e.getName();if(t.stack.env.closing===o){delete t.stack.env.closing;if(t.stack.global.beginEnv&&(t.stack.global.beginEnv--,n)){const e=t.string.slice(t.i);t.string=Bt.addArgs(t,t.string.substring(0,t.i),n),t.Parse(),t.string=e,t.i=0}return t.itemFactory.create("end").setProperty("name",o)}if(s){const e=[];if(null!=i){const r=t.GetBrackets(`\\begin{${o}}`);e.push(null==r?i:r)}for(let r=e.length;r<s;r++)e.push(t.GetArgument(`\\begin{${o}}`));r=Bt.substituteArgs(t,e,r),n=Bt.substituteArgs(t,[],n)}return t.string=Bt.addArgs(t,r,t.string.slice(t.i)),t.i=0,t.stack.global.beginEnv=(t.stack.global.beginEnv||0)+1,t.itemFactory.create("beginEnv").setProperty("name",o)},Macro:or.Macro},xa=Ma;function Pa(t,e){e.parseOptions.packageData.has("newcommand")||(e.parseOptions.packageData.set("newcommand",{}),new re(La.NEW_DELIMITER,cr.delimiter,{}),new se(La.NEW_COMMAND,{}),new ie(La.NEW_ENVIRONMENT,cr.environment,{}),e.parseOptions.handlers.add({[ut.CHARACTER]:[],[ut.DELIMITER]:[La.NEW_DELIMITER],[ut.MACRO]:[La.NEW_DELIMITER,La.NEW_COMMAND],[ut.ENVIRONMENT]:[La.NEW_ENVIRONMENT]},{},-100))}new se("Newcommand-macros",{newcommand:xa.NewCommand,renewcommand:xa.NewCommand,newenvironment:xa.NewEnvironment,renewenvironment:xa.NewEnvironment,def:xa.MacroDef,let:xa.Let});he.create("newcommand",{[ht.HANDLER]:{macro:["Newcommand-macros"]},[ht.ITEMS]:{[wa.prototype.kind]:wa},[ht.OPTIONS]:{maxMacros:1e3,protectedMacros:["begingroupSandbox"]},[ht.CONFIG]:Pa});he.create("ams",{[ht.HANDLER]:{[ut.CHARACTER]:["AMSmath-operatorLetter"],[ut.DELIMITER]:["AMSsymbols-delimiter","AMSmath-delimiter"],[ut.MACRO]:["AMSsymbols-mathchar0mi","AMSsymbols-mathchar0mo","AMSsymbols-delimiter","AMSsymbols-macros","AMSmath-mathchar0mo","AMSmath-macros","AMSmath-delimiter"],[ut.ENVIRONMENT]:["AMSmath-environment"]},[ht.ITEMS]:{[Aa.prototype.kind]:Aa,[Ra.prototype.kind]:Ra},[ht.TAGS]:{ams:class extends Vt{}},[ht.OPTIONS]:{multlineWidth:"",ams:{operatornamePattern:/^[-*a-zA-Z]+/,multlineWidth:"100%",multlineIndent:"1em"}},[ht.CONFIG]:Pa});class Da extends Lt{get texParser(){return this.configuration.packageData.get("textmacros").texParser}get tags(){return this.texParser.tags}constructor(t,e,r,n){super(t,e,r),this.level=n}mml(){return this.copyLists(),this.configuration.popParser(),null!=this.level?this.create("node","mstyle",this.nodes,{displaystyle:!1,scriptlevel:this.level}):1===this.nodes.length?this.nodes[0]:this.create("node","mrow",this.nodes)}copyLists(){const t=this.texParser.configuration;for(const[e,r]of Object.entries(this.configuration.nodeLists))for(const n of r)t.addNode(e,n);this.configuration.nodeLists={}}Parse(){this.text="",this.nodes=[],this.envStack=[],super.Parse()}saveText(){if(this.text){const t=this.stack.env.mathvariant,e=Bt.internalText(this,this.text,t?{mathvariant:t}:{});this.text="",this.Push(e)}}Push(t){if(this.text&&this.saveText(),t instanceof Ce)return super.Push(t);t instanceof Ue?this.stack.env.mathcolor=this.stack.env.color:t instanceof j&&(this.addAttributes(t),this.nodes.push(t))}PushMath(t){const e=this.stack.env;for(const r of["mathsize","mathcolor"])e[r]&&!t.attributes.hasExplicit(r)&&(t.isToken||t.isKind("mstyle")||(t=this.create("node","mstyle",[t])),nt.setAttribute(t,r,e[r]));t.isInferred&&(t=this.create("node","mrow",t.childNodes)),t.isKind("TeXAtom")||(t=this.create("node","TeXAtom",[t])),this.nodes.push(t)}addAttributes(t){const e=this.stack.env;if(t.isToken)for(const r of["mathsize","mathcolor","mathvariant"])e[r]&&!t.attributes.hasExplicit(r)&&nt.setAttribute(t,r,e[r])}ParseTextArg(t,e){const r=this.GetArgument(t);return e=Object.assign(Object.assign({},this.stack.env),e),new Da(r,e,this.configuration).mml()}ParseArg(t){return new Da(this.GetArgument(t),this.stack.env,this.configuration).mml()}Error(t,e,...r){throw new gt(t,e,...r)}}const ka={Comment(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++;t.i++},Math(t,e){t.saveText();const r=t.i;let n,s,i=0;for(;s=t.GetNext();)switch(n=t.i++,s){case"\\":")"===t.GetCS()&&(s="\\(");case"$":if(0===i&&e===s){const e=t.texParser.configuration,s=new Lt(t.string.substring(r,n),t.stack.env,e).mml();return void t.PushMath(s)}break;case"{":i++;break;case"}":0===i&&t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace"),i--}t.Error("MathNotTerminated","Math mode is not properly terminated")},MathModeOnly(t,e){t.Error("MathModeOnly","'%1' allowed only in math mode",e)},Misplaced(t,e){t.Error("Misplaced","Misplaced '%1'",e)},OpenBrace(t,e){const r=t.stack.env;t.envStack.push(r),t.stack.env=Object.assign({},r)},CloseBrace(t,e){t.envStack.length?(t.saveText(),t.stack.env=t.envStack.pop()):t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace")},OpenQuote(t,e){t.string.charAt(t.i)===e?(t.text+="\u201c",t.i++):t.text+="\u2018"},CloseQuote(t,e){t.string.charAt(t.i)===e?(t.text+="\u201d",t.i++):t.text+="\u2019"},Tilde(t,e){t.text+="\xa0"},Space(t,e){t.text+=" ",t.GetNext()},SelfQuote(t,e){t.text+=e.substring(1)},Insert(t,e,r){t.text+=r},Accent(t,e,r){const n=t.ParseArg(e),s=t.create("token","mo",{},r);t.addAttributes(s),t.Push(t.create("node","mover",[n,s]))},Emph(t,e){const r="-tex-mathit"===t.stack.env.mathvariant?"normal":"-tex-mathit";t.Push(t.ParseTextArg(e,{mathvariant:r}))},TextFont(t,e,r){t.saveText(),t.Push(t.ParseTextArg(e,{mathvariant:r}))},SetFont(t,e,r){t.saveText(),t.stack.env.mathvariant=r},SetSize(t,e,r){t.saveText(),t.stack.env.mathsize=r},CheckAutoload(t,e){const r=t.configuration.packageData.get("autoload"),s=t.texParser;e=e.slice(1);const i=s.lookup(ut.MACRO,e);if(!i||r&&i._func===r.Autoload){if(s.parse(ut.MACRO,[s,e]),!i)return;n(Promise.resolve())}s.parse(ut.MACRO,[t,e])},Macro:or.Macro,Spacer:or.Spacer,Hskip:or.Hskip,rule:or.rule,Rule:or.Rule,HandleRef:or.HandleRef,UnderOver:or.UnderOver,Lap:or.Lap,Phantom:or.Phantom,Smash:or.Smash,MmlToken:or.MmlToken},_a=bt;new ne("text-special",{$:ka.Math,"%":ka.Comment,"^":ka.MathModeOnly,_:ka.MathModeOnly,"&":ka.Misplaced,"#":ka.Misplaced,"~":ka.Tilde," ":ka.Space,"\t":ka.Space,"\r":ka.Space,"\n":ka.Space,"\xa0":ka.Tilde,"{":ka.OpenBrace,"}":ka.CloseBrace,"`":ka.OpenQuote,"'":ka.CloseQuote}),new se("text-macros",{"(":ka.Math,$:ka.SelfQuote,_:ka.SelfQuote,"%":ka.SelfQuote,"{":ka.SelfQuote,"}":ka.SelfQuote," ":ka.SelfQuote,"&":ka.SelfQuote,"#":ka.SelfQuote,"\\":[ka.Macro,"$\\\\$"],"'":[ka.Accent,"\xb4"],"\u2019":[ka.Accent,"\xb4"],"`":[ka.Accent,"`"],"\u2018":[ka.Accent,"`"],"^":[ka.Accent,"^"],'"':[ka.Accent,"\xa8"],"~":[ka.Accent,"~"],"=":[ka.Accent,"\xaf"],".":[ka.Accent,"\u02d9"],u:[ka.Accent,"\u02d8"],v:[ka.Accent,"\u02c7"],emph:ka.Emph,rm:[ka.SetFont,_a.NORMAL],mit:[ka.SetFont,_a.ITALIC],oldstyle:[ka.SetFont,_a.OLDSTYLE],cal:[ka.SetFont,_a.CALLIGRAPHIC],it:[ka.SetFont,"-tex-mathit"],bf:[ka.SetFont,_a.BOLD],sf:[ka.SetFont,_a.SANSSERIF],tt:[ka.SetFont,_a.MONOSPACE],frak:[ka.TextFont,_a.FRAKTUR],Bbb:[ka.TextFont,_a.DOUBLESTRUCK],tiny:[ka.SetSize,.5],Tiny:[ka.SetSize,.6],scriptsize:[ka.SetSize,.7],small:[ka.SetSize,.85],normalsize:[ka.SetSize,1],large:[ka.SetSize,1.2],Large:[ka.SetSize,1.44],LARGE:[ka.SetSize,1.73],huge:[ka.SetSize,2.07],Huge:[ka.SetSize,2.49],textnormal:[ka.Macro,"{\\rm #1}",1],textup:[ka.Macro,"{\\rm #1}",1],textrm:[ka.Macro,"{\\rm #1}",1],textit:[ka.Macro,"{\\it #1}",1],textbf:[ka.Macro,"{\\bf #1}",1],textsf:[ka.Macro,"{\\sf #1}",1],texttt:[ka.Macro,"{\\tt #1}",1],dagger:[ka.Insert,"\u2020"],ddagger:[ka.Insert,"\u2021"],S:[ka.Insert,"\xa7"],AA:[ka.Insert,"\u212b"],ldots:[ka.Insert,"\u2026"],vdots:[ka.Insert,"\u22ee"],",":[ka.Spacer,Ze.thinmathspace],":":[ka.Spacer,Ze.mediummathspace],">":[ka.Spacer,Ze.mediummathspace],";":[ka.Spacer,Ze.thickmathspace],"!":[ka.Spacer,Ze.negativethinmathspace],enspace:[ka.Spacer,.5],quad:[ka.Spacer,1],qquad:[ka.Spacer,2],thinspace:[ka.Spacer,Ze.thinmathspace],negthinspace:[ka.Spacer,Ze.negativethinmathspace],hskip:ka.Hskip,hspace:ka.Hskip,kern:ka.Hskip,mskip:ka.Hskip,mspace:ka.Hskip,mkern:ka.Hskip,rule:ka.rule,Rule:[ka.Rule],Space:[ka.Rule,"blank"],color:ka.CheckAutoload,textcolor:ka.CheckAutoload,colorbox:ka.CheckAutoload,fcolorbox:ka.CheckAutoload,href:ka.CheckAutoload,style:ka.CheckAutoload,class:ka.CheckAutoload,data:ka.CheckAutoload,cssId:ka.CheckAutoload,unicode:ka.CheckAutoload,U:ka.CheckAutoload,char:ka.CheckAutoload,ref:[ka.HandleRef,!1],eqref:[ka.HandleRef,!0],underline:[ka.UnderOver,"2015"],llap:ka.Lap,rlap:ka.Lap,phantom:ka.Phantom,vphantom:[ka.Phantom,1,0],hphantom:[ka.Phantom,0,1],smash:ka.Smash,mmlToken:ka.MmlToken});he.create("text-base",{[ht.PARSER]:"text",[ht.PRIORITY]:1,[ht.HANDLER]:{[ut.CHARACTER]:["command","text-special"],[ut.MACRO]:["text-macros"]},[ht.FALLBACK]:{[ut.CHARACTER]:(t,e)=>{t.text+=e},[ut.MACRO]:(t,e)=>{const r=t.texParser,n=r.lookup(ut.MACRO,e);n&&n._func!==ka.Macro&&t.Error("MathMacro","%1 is only supported in math mode","\\"+e),r.parse(ut.MACRO,[t,e])}},[ht.ITEMS]:{[Le.prototype.kind]:Le,[Ce.prototype.kind]:Ce,[Ge.prototype.kind]:Ge,[Ue.prototype.kind]:Ue}});function Fa(t,e,r,n){const s=t.configuration.packageData.get("textmacros");return t instanceof Da||(s.texParser=t),s.parseOptions.clear(),[new Da(e,n?{mathvariant:n}:{},s.parseOptions,r).mml()]}he.create("textmacros",{[ht.PRIORITY]:1,[ht.CONFIG]:(t,e)=>{const r=new pe(e.parseOptions.options.textmacros.packages,["tex","text"]);r.init();const n=new Gt(r,[]);n.options=e.parseOptions.options,r.config(e),zt.addTags(r.tags),n.tags=zt.getDefault(),n.tags.configuration=n,n.packageData=e.parseOptions.packageData,n.packageData.set("textmacros",{textConf:r,parseOptions:n,jax:e,texParser:null}),n.options.internalMath=Fa},[ht.PREPROCESSORS]:[t=>{const e=t.data.packageData.get("textmacros");e.parseOptions.nodeFactory.setMmlFactory(e.jax.mmlFactory)}],[ht.OPTIONS]:{textmacros:{packages:["text-base"]}}});var Ba,Ua;Ba=function(t){const e=new Ia(t);return o.handlers.register(e),e}(new br(window)),Ua=new gr,!Ba.documentClass.prototype.enrich&&Ua&&(Ba=function(t,e){return e.setAdaptor(t.adaptor),t.documentClass=Vo(t.documentClass,e),t}(Ba,Ua)),Ba.documentClass=na(Ba.documentClass);const qa=o.document(document,{worker:{path:"https://cdn.jsdelivr.net/npm/mathjax@4/sre",maps:"https://cdn.jsdelivr.net/npm/mathjax@4/sre/mathmaps"},renderActions:{typeset:[150,null,(t,e)=>function(t,e){ja=t;const r=e.adaptor,n=Ga(t.root);t.typesetRoot=r.firstChild(r.body(r.parse(n,"text/html")))}(t,e)]},InputJax:new dr({packages:["base","ams","newcommand","textmacros"]})}),Ha=new pn,Ga=t=>Ha.visitTree(t,qa);let ja;const Xa=window.MathJax?.config||window.MathJax||{};window.MathJax={version:o.version,html:qa,async toSpeechMML(t,e){e=Object.assign({display:!0,latex:!1,speech:!0,braille:!0,entities:!0},e),Object.assign(qa.options,{enableSpeech:e.speech,enableBraille:e.braille});const r=await qa.convertPromise(t,{display:e.display}),n=qa.adaptor,s=n.getAttribute(r,"data-semantic-speech-none"),i=n.getAttribute(r,"data-semantic-braille");s&&ja.root.attributes.set("aria-label",s),i&&ja.root.attributes.set("aria-braillelabel",i),ja.root.walkTree((t=>{const r=t.attributes.getAllAttributes();for(const t of Object.keys(r))(t.startsWith("data-semantic")||!e.latex&&t.startsWith("data-latex")||"aria-level"===t)&&delete r[t]}));let o=Ga(ja.root).replace(/<math (.*?)>/,((t,e)=>`<math ${e=e.replace(/( .+?=".*?")/g,"\n $1")}>`));return e.entities||(o=o.replace(/&#x(.*?);/g,((t,e)=>String.fromCodePoint(parseInt(e,16))))),o}},Xa.startup?.ready&&Xa.startup.ready()})()})();