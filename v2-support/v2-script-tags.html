<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width">
<link rel="stylesheet" href="../styles/globals.css">
<title>MathJax v4 with v2-compatible script tags for expressions</title>
  <script class="show">
  MathJax = {
    options: {
      renderActions: {
        //
        // We replace the stander find renderAction with one that looks 
        // for the MathJax v2 script tags for TeX input.
        //
        find: [10, function (doc) {
          //
          // Loop through all the script tags for TeX input.
          //   Determine if each is a display or inline expression.
          //   Create a new MathItem for the expression.
          //   Create a text node with the expression contents.
          //   Replace the script tag by the new text node.
          //   Hook the MathItem to the text node in the DOM.
          //   Add the MathItem to the document's math list.
          //
          for (const node of document.querySelectorAll('script[type^="text/tex"]')) {
            const display = !!node.type.match(/; *mode=display/);
            const math = new doc.options.MathItem(node.textContent, doc.inputJax.tex, display);
            const text = document.createTextNode('');
            node.parentNode.replaceChild(text, node);
            math.start = {node: text, delim: '', n: 0};
            math.end = {node: text, delim: '', n: 0};
            doc.math.push(math);
          }
        }, '']
      }
    }
  };
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js"></script>
</head>
<body>

<div id="frame">

<h1>Using MathJax v2 script tags</h1>
  
<div class="description">
 
  <p>
  This file demonstrates a configuration that locates <code>&lt;script&gt;</code> tags
  like the ones used in MathJax v2 to mark the mathematics within the page.
  </p>
  </div>

  <div class="show display">
 
  <script type="text/tex">x + 1</script>

  <script type="text/tex; mode=display">x+1\over x-1</script>
 
</div>
</div>

<div class="explain inset">
<p>
This example shows how to process a complete HTML page containing MathJax
version 2 styled <code>&lt;script&gt;</code> tags (that store the math
content in the page).  In version 2, these tags are generated automatically
by the <code>tex2jax</code> or other pre-processors, but they could also be
created by other software that generates the page so as to avoid the need
for the pre-processing step, and the need for quoting HTML special
characters like less-than signs and ampersands.
</p>

<p>
MathJax version 4 does not look for such <code>&lt;script&gt;</code> tags
itself, but you can implement it yourself, as in this example.
</p>

<p>
The key lines are in the <code>renderAction</code> in the MathJax
configuration, as described in the comments there.  The
<code>renderAction</code> replaces the usual page-search action (the
<code>find</code> action at priority 10) with one that searches for
<code>&lt;script&gt;</code> tags with <code>type="math/tex"</code> (or
<code>type="math/tex; mode=display"</code>), and creates the needed
<code>MathItem</code> instances for them.  The <code>&lt;script&gt;</code>
tags are replaced by empty text nodes, and the math items refers to these
for their starting and ending locations.
</p>
</div>

<script src="../scripts/source.js"></script>
</body>
</html>
