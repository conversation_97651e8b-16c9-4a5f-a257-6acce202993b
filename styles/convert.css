@import url("globals.css");

/* Use reader friendly font */
body {
  font-family: sans-serif;
}

/* Converter Box */
.converter {
  display: inline-block;
  box-sizing: border-box;
  margin: 0 2px;
  width: calc(50% - 4px);
  min-width: 25em;
  vertical-align: top;
  background-color: var(--box-background);
  border: 2px solid var(--box-border);
  padding: .75em;
  border-radius: 6px;
}

/* Explanation Box */
#explanation {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  min-width: 25em;
  vertical-align: top;
  margin-bottom: 2em;
  line-height: 1.5;
  padding-right: .75em;
}

/* Code Display */
.speech {
  font-size: 120%;
  min-height: 8em;
  border: 1px solid var(--border-color);
  background-color: var(--text-background);
  padding: .5em .5em .5em 1em;
  overflow: auto;
  white-space: pre;
}

#errors code,
#explanation code {
  background-color: var(--text-background);
  display: inline-block;
  padding: 10px 20px;
  border: 1px solid var(--border-color);
  margin: 1px;
}
#explanation code {
  padding: 0 2px;
}

/* Flex Layout Replacing Table */
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: flex-end;
  margin: 1em 0;
}

.radio-group {
  display: flex;
  gap: 1em;
  flex-wrap: wrap;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 0.4em;
  font-weight: normal;
}

.select-group {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.select-group select {
  margin-top: .2em;
}

.convert-button {
  margin-left: auto;
}

/* Utility */
.fullwidth {
  width: 100%;
}

.preferences-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1em 1em; /* vertical and horizontal gaps */
  margin-top: 1em;
}

.preference-item {
  display: flex;
  flex-direction: column;
  flex: 1 1 140px; /* grow, shrink, base width */
  min-width: 140px;
  max-width: 350px;

  background-color: var(--text-background);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 0.6em 1em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preference-item label {
  font-weight: bold;
  margin-bottom: 0.3em;
  user-select: none;
}

.preference-item select {
  width: 100%;
  background-color: var(--select-background);
  color: var(--select-text);
  padding: 0.1em 0.4em 0.2em;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  box-sizing: border-box;
}
