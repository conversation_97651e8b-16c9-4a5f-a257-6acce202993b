/**
 * MathJax 调试工具
 * 用于诊断MathJax加载和渲染问题
 */

export class MathJaxDebugger {
  /**
   * 检查MathJax状态
   */
  static checkMathJaxStatus() {
    const status = {
      timestamp: new Date().toISOString(),
      windowMathJax: !!window.MathJax,
      mathJaxVersion: window.MathJax?.version || 'unknown',
      startup: {
        exists: !!(window.MathJax && window.MathJax.startup),
        document: !!(window.MathJax && window.MathJax.startup && window.MathJax.startup.document),
        promise: !!(window.MathJax && window.MathJax.startup && window.MathJax.startup.promise)
      },
      methods: {
        typesetPromise: !!(window.MathJax && window.MathJax.typesetPromise),
        typeset: !!(window.MathJax && window.MathJax.typeset),
        tex2chtml: !!(window.MathJax && window.MathJax.tex2chtml),
        tex2svg: !!(window.MathJax && window.MathJax.tex2svg)
      },
      config: {
        tex: !!(window.MathJax && window.MathJax.config && window.MathJax.config.tex),
        chtml: !!(window.MathJax && window.MathJax.config && window.MathJax.config.chtml),
        fontURL: window.MathJax?.config?.chtml?.fontURL || 'not set'
      },
      environment: {
        online: navigator.onLine,
        protocol: window.location.protocol,
        hostname: window.location.hostname,
        userAgent: navigator.userAgent.substring(0, 100) + '...'
      }
    }

    console.group('🔍 MathJax 状态检查')
    console.table(status.startup)
    console.table(status.methods)
    console.table(status.config)
    console.table(status.environment)
    console.groupEnd()

    return status
  }

  /**
   * 测试字体加载
   */
  static async testFontLoading() {
    const fontTests = [
      '/fonts/mathjax/mjx-ncm-n.woff',
      '/fonts/mathjax/mjx-ncm-b.woff',
      '/fonts/mathjax/mjx-ncm-i.woff',
      '/fonts/mathjax/mjx-ncm-mi.woff'
    ]

    console.group('🔤 字体加载测试')
    
    const results = []
    for (const fontUrl of fontTests) {
      try {
        const response = await fetch(fontUrl, { method: 'HEAD' })
        const result = {
          url: fontUrl,
          status: response.status,
          ok: response.ok,
          size: response.headers.get('content-length') || 'unknown'
        }
        results.push(result)
        console.log(`✅ ${fontUrl}: ${response.status} (${result.size} bytes)`)
      } catch (error) {
        const result = {
          url: fontUrl,
          status: 'error',
          ok: false,
          error: error.message
        }
        results.push(result)
        console.error(`❌ ${fontUrl}: ${error.message}`)
      }
    }
    
    console.groupEnd()
    return results
  }

  /**
   * 测试MathJax脚本加载
   */
  static async testScriptLoading() {
    const scriptTests = [
      '/js/mathjax/tex-mml-chtml-mathjax-newcm.js',
      '/js/mathjax/tex-mml-chtml.js'
    ]

    console.group('📜 脚本加载测试')
    
    const results = []
    for (const scriptUrl of scriptTests) {
      try {
        const response = await fetch(scriptUrl, { method: 'HEAD' })
        const result = {
          url: scriptUrl,
          status: response.status,
          ok: response.ok,
          size: response.headers.get('content-length') || 'unknown',
          type: response.headers.get('content-type') || 'unknown'
        }
        results.push(result)
        console.log(`✅ ${scriptUrl}: ${response.status} (${result.size} bytes)`)
      } catch (error) {
        const result = {
          url: scriptUrl,
          status: 'error',
          ok: false,
          error: error.message
        }
        results.push(result)
        console.error(`❌ ${scriptUrl}: ${error.message}`)
      }
    }
    
    console.groupEnd()
    return results
  }

  /**
   * 测试数学公式渲染
   */
  static async testMathRendering() {
    if (!window.MathJax) {
      console.error('❌ MathJax 未加载，无法测试渲染')
      return false
    }

    console.group('🧮 数学公式渲染测试')

    // 创建测试容器
    const testContainer = document.createElement('div')
    testContainer.innerHTML = `
      <p>行内公式测试: $E = mc^2$</p>
      <p>显示公式测试:</p>
      $$\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}$$
    `
    testContainer.style.position = 'absolute'
    testContainer.style.top = '-9999px'
    testContainer.style.left = '-9999px'
    document.body.appendChild(testContainer)

    try {
      if (window.MathJax.typesetPromise) {
        await window.MathJax.typesetPromise([testContainer])
        console.log('✅ typesetPromise 渲染成功')
      } else if (window.MathJax.typeset) {
        window.MathJax.typeset([testContainer])
        console.log('✅ typeset 渲染成功')
      } else {
        throw new Error('没有可用的渲染方法')
      }

      // 检查是否有MathJax生成的元素
      const mathElements = testContainer.querySelectorAll('.MathJax, mjx-container')
      console.log(`✅ 生成了 ${mathElements.length} 个数学元素`)
      
      document.body.removeChild(testContainer)
      console.groupEnd()
      return true
    } catch (error) {
      console.error('❌ 渲染测试失败:', error)
      document.body.removeChild(testContainer)
      console.groupEnd()
      return false
    }
  }

  /**
   * 完整诊断
   */
  static async fullDiagnosis() {
    console.group('🔧 MathJax 完整诊断')
    console.log('开始时间:', new Date().toISOString())

    const results = {
      status: this.checkMathJaxStatus(),
      fonts: await this.testFontLoading(),
      scripts: await this.testScriptLoading(),
      rendering: await this.testMathRendering()
    }

    console.log('诊断完成时间:', new Date().toISOString())
    console.groupEnd()

    // 生成诊断报告
    this.generateReport(results)
    
    return results
  }

  /**
   * 生成诊断报告
   */
  static generateReport(results) {
    console.group('📋 诊断报告')
    
    const fontOkCount = results.fonts.filter(f => f.ok).length
    const scriptOkCount = results.scripts.filter(s => s.ok).length
    
    console.log(`字体文件: ${fontOkCount}/${results.fonts.length} 可用`)
    console.log(`脚本文件: ${scriptOkCount}/${results.scripts.length} 可用`)
    console.log(`MathJax状态: ${results.status.windowMathJax ? '已加载' : '未加载'}`)
    console.log(`渲染测试: ${results.rendering ? '通过' : '失败'}`)
    
    if (results.rendering && fontOkCount > 0 && scriptOkCount > 0) {
      console.log('🎉 MathJax 工作正常！')
    } else {
      console.log('⚠️  MathJax 存在问题，请检查上述测试结果')
    }
    
    console.groupEnd()
  }
}

export default MathJaxDebugger
