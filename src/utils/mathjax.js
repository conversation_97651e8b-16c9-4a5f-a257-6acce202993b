/**
 * MathJax工具类
 * 处理MathJax的加载、配置和渲染
 * 支持完全离线部署，不依赖CDN
 */

import { getMathJaxConfig, MATHJAX_CONFIG } from '@/config/mathjax.config.js'

class MathJaxUtil {
  constructor() {
    this.isReady = false
    this.isLoading = false
    this.loadPromise = null
    this.fontsPreloaded = false
  }

  /**
   * 初始化MathJax
   */
  async init() {
    if (this.isReady) {
      return Promise.resolve()
    }

    if (this.isLoading) {
      return this.loadPromise
    }

    // 预加载字体
    if (!this.fontsPreloaded) {
      //preloadMathJaxFonts()
      this.fontsPreloaded = true
    }

    this.isLoading = true
    this.loadPromise = this._loadMathJax()

    try {
      await this.loadPromise

      // 等待MathJax完全就绪，增加超时保护
      if (window.MathJax && window.MathJax.startup) {
        await new Promise((resolve, reject) => {
          let attempts = 0
          const maxAttempts = 100 // 10秒超时

          const checkReady = () => {
            attempts++
            if (window.MathJax.startup.document) {
              resolve()
            } else if (attempts >= maxAttempts) {
              console.warn('MathJax启动超时，但继续初始化')
              resolve() // 不要reject，允许继续
            } else {
              setTimeout(checkReady, 100)
            }
          }

          if (window.MathJax.startup.document) {
            resolve()
          } else if (window.MathJax.startup.promise) {
            window.MathJax.startup.promise.then(resolve).catch(() => {
              console.warn('MathJax startup promise失败，尝试轮询检查')
              checkReady()
            })
          } else {
            checkReady()
          }
        })
      }

      this.isReady = true
      this.isLoading = false
      console.log('MathJax初始化完成')

    } catch (error) {
      console.error('MathJax初始化失败:', error)
      this.isLoading = false
      throw error
    }

    return this.loadPromise
  }

  /**
   * 加载MathJax
   */
  async _loadMathJax() {
    // 检查是否已经存在
    if (window.MathJax) {
      console.log('MathJax已存在')
      return Promise.resolve()
    }

    // 使用配置文件中的MathJax配置
    window.MathJax = getMathJaxConfig()

    // 尝试不同的加载方式
    return this._tryLoadMethods()
  }

  /**
   * 尝试不同的加载方法
   */
  async _tryLoadMethods() {
    // 优先使用本地资源，确保离线环境可用
    const methods = [
      () => this._loadFromLocalWithFont(),
    ]

    for (let i = 0; i < methods.length; i++) {
      try {
        console.log(`尝试加载方法 ${i + 1}/${methods.length}`)
        await methods[i]()
        return
      } catch (error) {
        console.warn(`MathJax加载方法 ${i + 1} 失败:`, error.message)
        if (i === methods.length - 1) {
          throw new Error('所有MathJax加载方法都失败了')
        }
      }
    }
  }

  /**
   * 从本地静态资源加载（带字体支持）
   */
  _loadFromLocalWithFont() {
    return new Promise((resolve, reject) => {
      console.log('尝试从本地静态资源加载MathJax（带字体支持）')

      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.async = true
      script.src = MATHJAX_CONFIG.LOCAL_PATHS.BASIC

      script.onload = () => {
        console.log('从本地加载MathJax（带字体）成功')
        resolve()
      }

      script.onerror = () => {
        reject(new Error('从本地加载MathJax（带字体）失败'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 尝试从多个路径加载
   */
  _tryLoadFromPaths(paths, resolve, reject, index = 0) {
    if (index >= paths.length) {
      reject(new Error('所有本地路径都加载失败'))
      return
    }

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = paths[index]

    script.onload = () => {
      console.log(`从路径 ${paths[index]} 加载MathJax成功`)
      resolve()
    }

    script.onerror = () => {
      console.warn(`路径 ${paths[index]} 加载失败，尝试下一个`)
      this._tryLoadFromPaths(paths, resolve, reject, index + 1)
    }

    document.head.appendChild(script)
  }

  /**
   * 渲染指定元素中的数学公式
   */
  async render(element) {
    if (!this.isReady) {
      console.log('MathJax未就绪，正在初始化...')
      await this.init()
    }

    // 详细检查MathJax状态
    console.log('MathJax状态检查:', {
      hasMathJax: !!window.MathJax,
      hasTypesetPromise: !!(window.MathJax && window.MathJax.typesetPromise),
      hasStartup: !!(window.MathJax && window.MathJax.startup),
      isReady: this.isReady
    })

    if (!window.MathJax) {
      throw new Error('MathJax对象不存在，请检查脚本是否正确加载')
    }

    // 等待MathJax完全初始化，增加超时保护
    if (window.MathJax.startup && !window.MathJax.startup.document) {
      console.log('等待MathJax启动完成...')
      await new Promise((resolve, reject) => {
        let attempts = 0
        const maxAttempts = 50 // 5秒超时

        const checkReady = () => {
          attempts++
          if (window.MathJax.startup.document) {
            console.log('MathJax启动完成')
            resolve()
          } else if (attempts >= maxAttempts) {
            console.warn('MathJax启动超时，尝试强制继续')
            resolve() // 不要reject，尝试继续
          } else {
            setTimeout(checkReady, 100)
          }
        }
        checkReady()
      })
    }

    // 确保有可用的渲染方法
    if (!window.MathJax.typesetPromise && !window.MathJax.typeset) {
      // 尝试等待一下，可能还在初始化
      await new Promise(resolve => setTimeout(resolve, 500))
    }

    if (window.MathJax.typesetPromise) {
      try {
        console.log('开始渲染MathJax公式...')
        await window.MathJax.typesetPromise([element])
        console.log('MathJax渲染完成')
      } catch (error) {
        console.error('MathJax渲染错误:', error)
        throw error
      }
    } else if (window.MathJax.typeset) {
      // 备用方法
      try {
        console.log('使用备用方法渲染MathJax公式...')
        window.MathJax.typeset([element])
        console.log('MathJax渲染完成（备用方法）')
      } catch (error) {
        console.error('MathJax渲染错误（备用方法）:', error)
        throw error
      }
    } else {
      throw new Error('MathJax渲染方法不可用，请检查MathJax版本和配置')
    }
  }

  /**
   * 检测内容是否包含数学公式
   */
  containsMath(content) {
    if (!content) return false

    const mathPatterns = [
      /\$.*?\$/,           // 行内公式 $...$
      /\$\$.*?\$\$/,       // 显示公式 $$...$$
      /\\\(.*?\\\)/,       // 行内公式 \(...\)
      /\\\[.*?\\\]/,       // 显示公式 \[...\]
      /\\begin\{.*?\}.*?\\end\{.*?\}/,  // 环境公式
      /\\[a-zA-Z]+/        // LaTeX命令
    ]

    return mathPatterns.some(pattern => pattern.test(content))
  }

  /**
   * 获取MathJax状态
   */
  getStatus() {
    return {
      isReady: this.isReady,
      isLoading: this.isLoading,
      hasMathJax: !!window.MathJax
    }
  }

  /**
   * 重置MathJax状态，强制重新初始化
   */
  reset() {
    console.log('重置MathJax状态...')
    this.isReady = false
    this.isLoading = false
    this.loadPromise = null
    this.fontsPreloaded = false

    // 清理现有的MathJax实例
    if (window.MathJax) {
      try {
        // 移除现有的script标签
        const scripts = document.querySelectorAll('script[src*="mathjax"]')
        scripts.forEach(script => script.remove())

        // 清理MathJax对象
        delete window.MathJax
        console.log('已清理现有MathJax实例')
      } catch (error) {
        console.warn('清理MathJax实例时出错:', error)
      }
    }
  }

  /**
   * 强制重新初始化
   */
  async forceReinit() {
    console.log('强制重新初始化MathJax...')
    this.reset()
    return await this.init()
  }
}

// 创建单例实例
const mathJaxUtil = new MathJaxUtil()

export default mathJaxUtil
