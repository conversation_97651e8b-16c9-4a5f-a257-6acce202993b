/**
 * MathJax 本地化配置
 * 用于无外网环境的部署
 */

export const MATHJAX_CONFIG = {
  // 本地资源路径配置
  LOCAL_PATHS: {
    // 带字体的完整版本（推荐）
    WITH_FONT: '/js/mathjax/tex-mml-chtml-mathjax-newcm.js',
    // 基础版本
    BASIC: '/js/mathjax/tex-mml-chtml.js',
    // 字体目录
    FONTS: '/fonts/mathjax'
  },

  // CDN备用路径（在线环境使用）
  CDN_PATHS: {
    MATHJAX: 'https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js',
    POLYFILL: 'https://polyfill.io/v3/polyfill.min.js?features=es6'
  },

  // MathJax 基础配置
  BASE_CONFIG: {
    tex: {
      inlineMath: [['$', '$'], ['\\(', '\\)']],
      displayMath: [['$$', '$$'], ['\\[', '\\]']],
      processEscapes: true,
      processEnvironments: true,
      // 支持更多的LaTeX包
      packages: {
        '[+]': ['ams', 'newcommand', 'configmacros', 'action', 'require']
      },
      // 自定义宏定义
      macros: {
        RR: '{\\mathbb{R}}',
        NN: '{\\mathbb{N}}',
        ZZ: '{\\mathbb{Z}}',
        QQ: '{\\mathbb{Q}}',
        CC: '{\\mathbb{C}}',
        vec: ['{\\boldsymbol{#1}}', 1],
        norm: ['{\\left\\|#1\\right\\|}', 1],
        abs: ['{\\left|#1\\right|}', 1]
      }
    },
    options: {
      skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code'],
      ignoreHtmlClass: 'tex2jax_ignore',
      processHtmlClass: 'tex2jax_process'
    },
    chtml: {
      // 本地字体路径
      fontURL: '/fonts/mathjax',
      // 字体缓存设置
      adaptiveCSS: true,
      matchFontHeight: false,
      // 字体显示优化
      displayAlign: 'center',
      displayIndent: '0'
    },
    loader: {
      // 加载超时设置
      timeout: 10000,
      // 失败重试次数
      retries: 3
    },
    startup: {
      ready: () => {
        console.log('MathJax startup ready - 本地化配置')
        window.MathJax.startup.defaultReady()
        console.log('MathJax 本地化配置加载完成')
      },
      // 页面加载完成后的回调
      pageReady: () => {
        console.log('MathJax 页面渲染准备完成')
      }
    }
  },

  // 环境检测配置
  ENVIRONMENT: {
    // 检测是否为离线环境
    /*isOffline: () => {
      return !navigator.onLine ||
             window.location.protocol === 'file:' ||
             window.location.hostname === 'localhost' ||
             window.location.hostname === '127.0.0.1'
    },*/
    isOffline: () => {
      return true
    },

    // 检测字体是否可用
    checkFontAvailability: async () => {
      try {
        const response = await fetch('/fonts/mathjax/mjx-ncm-n.woff', { method: 'HEAD' })
        return response.ok
      } catch (error) {
        console.warn('本地字体检测失败:', error)
        return false
      }
    }
  },

  // 性能优化配置
  PERFORMANCE: {
    // 预加载关键字体
    preloadFonts: [
      '/fonts/mathjax/mjx-ncm-n.woff',    // 正常字体
      '/fonts/mathjax/mjx-ncm-b.woff',    // 粗体
      '/fonts/mathjax/mjx-ncm-i.woff',    // 斜体
      '/fonts/mathjax/mjx-ncm-mi.woff'    // 数学斜体
    ],

    // 延迟加载设置
    lazyLoad: true,

    // 缓存设置
    enableCache: true
  }
}

/**
 * 获取适合当前环境的MathJax配置
 */
export function getMathJaxConfig() {
  const config = { ...MATHJAX_CONFIG.BASE_CONFIG }

  // 根据环境调整配置
  if (MATHJAX_CONFIG.ENVIRONMENT.isOffline()) {
    console.log('检测到离线环境，使用本地资源配置')
    config.chtml.fontURL = MATHJAX_CONFIG.LOCAL_PATHS.FONTS
  }

  return config
}

/**
 * 预加载字体资源
 */
export function preloadMathJaxFonts() {
  const fonts = MATHJAX_CONFIG.PERFORMANCE.preloadFonts

  fonts.forEach(fontUrl => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.type = 'font/woff'
    link.crossOrigin = 'anonymous'
    link.href = fontUrl
    document.head.appendChild(link)
  })

  console.log(`预加载了 ${fonts.length} 个MathJax字体文件`)
}

export default MATHJAX_CONFIG
