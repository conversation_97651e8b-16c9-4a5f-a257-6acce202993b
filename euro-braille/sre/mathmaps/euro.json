{"euro/characters/ANSI.min": [{"locale": "euro"}, {"modality": "braille"}, {"0": "⠬", "1": "⠡", "2": "⠣", "3": "⠩", "4": "⠹", "5": "⠱", "6": "⠫", "7": "⠻", "8": "⠳", "9": "⠪", "\\0": "⣜", "\\1": "⣁", "\\2": "⣃", "\\3": "⣉", "\\4": "⣙", "\\5": "⣑", "\\6": "⣋", "\\7": "⣛", "\\8": "⣓", "\\9": "⣊", "\\10": "⣚", "\\11": "⣅", "\\12": "⣇", "\\13": "⣍", "\\14": "⣝", "\\15": "⣕", "\\16": "⣏", "\\17": "⣟", "\\18": "⣗", "\\19": "⣎", "\\20": "⣞", "\\21": "⣥", "\\22": "⣧", "\\23": "⣺", "\\24": "⣭", "\\25": "⣽", "\\26": "⣵", "\\27": "⣷", "\\28": "⣌", "\\29": "⣾", "\\30": "⣮", "\\31": "⣸", "\\32": "", "!": "⠐", "\"": "⠈", "#": "⠼", "$": "⠨", "%": "⠿", "&": "⠯", "'": "⠠", "(": "⠦", ")": "⠴", "*": "⠔", "+": "⠖", ",": "⠂", "-": "⠤", ".": "⠄", "/": "⠲", ":": "⠒", "\\59": "⠆", "<": "⠰", "=": "⠶", ">": "⠘", "?": "⠢", "@": "⡜", "A": "⡁", "B": "⡃", "C": "⡉", "D": "⡙", "E": "⡑", "F": "⡋", "G": "⡛", "H": "⡓", "I": "⡊", "J": "⡚", "K": "⡅", "L": "⡇", "M": "⡍", "N": "⡝", "O": "⡕", "P": "⡏", "Q": "⡟", "R": "⡗", "S": "⡎", "T": "⡞", "U": "⡥", "V": "⡧", "W": "⡺", "X": "⡭", "Y": "⡽", "Z": "⡵", "[": "⡷", "\\": "⡌", "]": "⡾", "^": "⡮", "_": "⡸", "`": "⠜", "a": "⠁", "b": "⠃", "c": "⠉", "d": "⠙", "e": "⠑", "f": "⠋", "g": "⠛", "h": "⠓", "i": "⠊", "j": "⠚", "k": "⠅", "l": "⠇", "m": "⠍", "n": "⠝", "o": "⠕", "p": "⠏", "q": "⠟", "r": "⠗", "s": "⠎", "t": "⠞", "u": "⠥", "v": "⠧", "w": "⠺", "x": "⠭", "y": "⠽", "z": "⠵", "{": "⠷", "|": "⠌", "}": "⠾", "~": "⠮", "\\127": "⠸"}], "euro/characters/Braille.min": [{"locale": "euro"}, {"modality": "braille"}, {"⠀": "", "⠁": "⠁", "⠂": "⠂", "⠃": "⠃", "⠄": "⠄", "⠅": "⠅", "⠆": "⠆", "⠇": "⠇", "⠈": "⠈", "⠉": "⠉", "⠊": "⠊", "⠋": "⠋", "⠌": "⠌", "⠍": "⠍", "⠎": "⠎", "⠏": "⠏", "⠐": "⠐", "⠑": "⠑", "⠒": "⠒", "⠓": "⠓", "⠔": "⠔", "⠕": "⠕", "⠖": "⠖", "⠗": "⠗", "⠘": "⠘", "⠙": "⠙", "⠚": "⠚", "⠛": "⠛", "⠜": "⠜", "⠝": "⠝", "⠞": "⠞", "⠟": "⠟", "⠠": "⠠", "⠡": "⠡", "⠢": "⠢", "⠣": "⠣", "⠤": "⠤", "⠥": "⠥", "⠦": "⠦", "⠧": "⠧", "⠨": "⠨", "⠩": "⠩", "⠪": "⠪", "⠫": "⠫", "⠬": "⠬", "⠭": "⠭", "⠮": "⠮", "⠯": "⠯", "⠰": "⠰", "⠱": "⠱", "⠲": "⠲", "⠳": "⠳", "⠴": "⠴", "⠵": "⠵", "⠶": "⠶", "⠷": "⠷", "⠸": "⠸", "⠹": "⠹", "⠺": "⠺", "⠻": "⠻", "⠼": "⠼", "⠽": "⠽", "⠾": "⠾", "⠿": "⠿", "⡀": "⡀", "⡁": "⡁", "⡂": "⡂", "⡃": "⡃", "⡄": "⡄", "⡅": "⡅", "⡆": "⡆", "⡇": "⡇", "⡈": "⡈", "⡉": "⡉", "⡊": "⡊", "⡋": "⡋", "⡌": "⡌", "⡍": "⡍", "⡎": "⡎", "⡏": "⡏", "⡐": "⡐", "⡑": "⡑", "⡒": "⡒", "⡓": "⡓", "⡔": "⡔", "⡕": "⡕", "⡖": "⡖", "⡗": "⡗", "⡘": "⡘", "⡙": "⡙", "⡚": "⡚", "⡛": "⡛", "⡜": "⡜", "⡝": "⡝", "⡞": "⡞", "⡟": "⡟", "⡠": "⡠", "⡡": "⡡", "⡢": "⡢", "⡣": "⡣", "⡤": "⡤", "⡥": "⡥", "⡦": "⡦", "⡧": "⡧", "⡨": "⡨", "⡩": "⡩", "⡪": "⡪", "⡫": "⡫", "⡬": "⡬", "⡭": "⡭", "⡮": "⡮", "⡯": "⡯", "⡰": "⡰", "⡱": "⡱", "⡲": "⡲", "⡳": "⡳", "⡴": "⡴", "⡵": "⡵", "⡶": "⡶", "⡷": "⡷", "⡸": "⡸", "⡹": "⡹", "⡺": "⡺", "⡻": "⡻", "⡼": "⡼", "⡽": "⡽", "⡾": "⡾", "⡿": "⡿", "⢀": "⢀", "⢁": "⢁", "⢂": "⢂", "⢃": "⢃", "⢄": "⢄", "⢅": "⢅", "⢆": "⢆", "⢇": "⢇", "⢈": "⢈", "⢉": "⢉", "⢊": "⢊", "⢋": "⢋", "⢌": "⢌", "⢍": "⢍", "⢎": "⢎", "⢏": "⢏", "⢐": "⢐", "⢑": "⢑", "⢒": "⢒", "⢓": "⢓", "⢔": "⢔", "⢕": "⢕", "⢖": "⢖", "⢗": "⢗", "⢘": "⢘", "⢙": "⢙", "⢚": "⢚", "⢛": "⢛", "⢜": "⢜", "⢝": "⢝", "⢞": "⢞", "⢟": "⢟", "⢠": "⢠", "⢡": "⢡", "⢢": "⢢", "⢣": "⢣", "⢤": "⢤", "⢥": "⢥", "⢦": "⢦", "⢧": "⢧", "⢨": "⢨", "⢩": "⢩", "⢪": "⢪", "⢫": "⢫", "⢬": "⢬", "⢭": "⢭", "⢮": "⢮", "⢯": "⢯", "⢰": "⢰", "⢱": "⢱", "⢲": "⢲", "⢳": "⢳", "⢴": "⢴", "⢵": "⢵", "⢶": "⢶", "⢷": "⢷", "⢸": "⢸", "⢹": "⢹", "⢺": "⢺", "⢻": "⢻", "⢼": "⢼", "⢽": "⢽", "⢾": "⢾", "⢿": "⢿", "⣀": "⣀", "⣁": "⣁", "⣂": "⣂", "⣃": "⣃", "⣄": "⣄", "⣅": "⣅", "⣆": "⣆", "⣇": "⣇", "⣈": "⣈", "⣉": "⣉", "⣊": "⣊", "⣋": "⣋", "⣌": "⣌", "⣍": "⣍", "⣎": "⣎", "⣏": "⣏", "⣐": "⣐", "⣑": "⣑", "⣒": "⣒", "⣓": "⣓", "⣔": "⣔", "⣕": "⣕", "⣖": "⣖", "⣗": "⣗", "⣘": "⣘", "⣙": "⣙", "⣚": "⣚", "⣛": "⣛", "⣜": "⣜", "⣝": "⣝", "⣞": "⣞", "⣟": "⣟", "⣠": "⣠", "⣡": "⣡", "⣢": "⣢", "⣣": "⣣", "⣤": "⣤", "⣥": "⣥", "⣦": "⣦", "⣧": "⣧", "⣨": "⣨", "⣩": "⣩", "⣪": "⣪", "⣫": "⣫", "⣬": "⣬", "⣭": "⣭", "⣮": "⣮", "⣯": "⣯", "⣰": "⣰", "⣱": "⣱", "⣲": "⣲", "⣳": "⣳", "⣴": "⣴", "⣵": "⣵", "⣶": "⣶", "⣷": "⣷", "⣸": "⣸", "⣹": "⣹", "⣺": "⣺", "⣻": "⣻", "⣼": "⣼", "⣽": "⣽", "⣾": "⣾", "⣿": "⣿", "蜢": "⠤", "靄": "⠣⠀⠜⠀", "靅": "⠣⠭⠜⠀"}], "euro/characters/Cyrillic.min": [{"locale": "euro"}, {"modality": "braille"}, {"Ё": "⣡", "Є": "⣜", "І": "⣽", "Ї": "⣹", "А": "⣁", "Б": "⣃", "В": "⣺", "Г": "⣛", "Д": "⣙", "Е": "⣑", "Ж": "⣚", "З": "⣵", "И": "⣊", "Й": "⣯", "К": "⣅", "Л": "⣇", "М": "⣍", "Н": "⣝", "О": "⣕", "П": "⣏", "Р": "⣗", "С": "⣎", "Т": "⣞", "У": "⣥", "Ф": "⣋", "Х": "⣓", "Ц": "⣉", "Ч": "⣟", "Ш": "⣱", "Щ": "⣭", "Ъ": "⣷", "Ы": "⣮", "Ь": "⣾", "Э": "⣪", "Ю": "⣳", "Я": "⣫", "а": "⢁", "б": "⢃", "в": "⢺", "г": "⢛", "д": "⢙", "е": "⢑", "ж": "⢚", "з": "⢵", "и": "⢊", "й": "⢯", "к": "⢅", "л": "⢇", "м": "⢍", "н": "⢝", "о": "⢕", "п": "⢏", "р": "⢗", "с": "⢎", "т": "⢞", "у": "⢥", "ф": "⢋", "х": "⢓", "ц": "⢉", "ч": "⢟", "ш": "⢱", "щ": "⢭", "ъ": "⢷", "ы": "⢮", "ь": "⢾", "э": "⢪", "ю": "⢳", "я": "⢫", "ћ": "⢩", "ё": "⢡", "ђ": "⢩", "є": "⢜", "ѕ": "⢧", "і": "⢽", "ї": "⢹", "ј": "⢜", "љ": "⢣", "њ": "⢫", "ў": "⢬", "ѣ": "⢜", "ѧ": "⢁", "ѫ": "⢻", "ѹ": "⢬", "҄": "⣈", "Ґ": "⣻", "ґ": "⢻", "ң": "⢝", "ү": "⢳", "һ": "⢃", "ә": "⢪"}], "euro/characters/Fullwidth.min": [{"locale": "euro"}, {"modality": "braille"}, {"０": "⠬", "１": "⠡", "２": "⠣", "３": "⠩", "４": "⠹", "５": "⠱", "６": "⠫", "７": "⠻", "８": "⠳", "９": "⠪", "Ａ": "⡁", "Ｂ": "⡃", "Ｃ": "⡉", "Ｄ": "⡙", "Ｅ": "⡑", "Ｆ": "⡋", "Ｇ": "⡛", "Ｈ": "⡓", "Ｉ": "⡊", "Ｊ": "⡚", "Ｋ": "⡅", "Ｌ": "⡇", "Ｍ": "⡍", "Ｎ": "⡝", "Ｏ": "⡕", "Ｐ": "⡏", "Ｑ": "⡟", "Ｒ": "⡗", "Ｓ": "⡎", "Ｔ": "⡞", "Ｕ": "⡥", "Ｖ": "⡧", "Ｗ": "⡺", "Ｘ": "⡭", "Ｙ": "⡽", "Ｚ": "⡵", "ａ": "⠁", "ｂ": "⠃", "ｃ": "⠉", "ｄ": "⠙", "ｅ": "⠑", "ｆ": "⠋", "ｇ": "⠛", "ｈ": "⠓", "ｉ": "⠊", "ｊ": "⠚", "ｋ": "⠅", "ｌ": "⠇", "ｍ": "⠍", "ｎ": "⠝", "ｏ": "⠕", "ｐ": "⠏", "ｑ": "⠟", "ｒ": "⠗", "ｓ": "⠎", "ｔ": "⠞", "ｕ": "⠥", "ｖ": "⠧", "ｗ": "⠺", "ｘ": "⠭", "ｙ": "⠽", "ｚ": "⠵"}], "euro/characters/Greek.min": [{"locale": "euro"}, {"modality": "braille"}, {"ʹ": "⠼", "͵": "⣰", ";": "⠢", "΄": "⠈", "΅": "⠘", "Ά": "⣜", "·": "⡨", "Έ": "⣫", "Ή": "⣿", "Ί": "⣻", "Ό": "⣪", "Ύ": "⣳", "Ώ": "⣚", "ΐ": "⢊", "Α": "⣁", "Β": "⣃", "Γ": "⣛", "Δ": "⣙", "Ε": "⣑", "Ζ": "⣵", "Η": "⣱", "Θ": "⣹", "Ι": "⢊", "Κ": "⣅", "Λ": "⣇", "Μ": "⣍", "Ν": "⣝", "Ξ": "⣭", "Ο": "⣕", "Π": "⣏", "Ρ": "⣗", "Σ": "⣎", "Τ": "⣞", "Υ": "⣥", "Φ": "⣋", "Χ": "⣯", "Ψ": "⣽", "Ω": "⣺", "Ϊ": "⣊", "Ϋ": "⣽", "ά": "⢜", "έ": "⢫", "ή": "⢿", "ί": "⢻", "ΰ": "⢽", "α": "⢁", "β": "⢃", "γ": "⢛", "δ": "⢙", "ε": "⢑", "ζ": "⢵", "η": "⢱", "θ": "⢹", "ι": "⢊", "κ": "⢅", "λ": "⢇", "μ": "⢍", "ν": "⢝", "ξ": "⢭", "ο": "⢕", "π": "⢏", "ρ": "⢗", "ς": "⢎", "σ": "⢎", "τ": "⢞", "υ": "⢥", "φ": "⢋", "χ": "⢯", "ψ": "⠽", "ω": "⢺", "ϊ": "⢌", "ϋ": "⢾", "ό": "⢪", "ύ": "⢳", "ώ": "⢚", "ϐ": "⣃", "ϑ": "⣹", "ϒ": "⢧", "ϓ": "⠾", "ϔ": "⢥", "ϕ": "⣋", "ϖ": "⣏", "ϗ": "⠯", "Ϙ": "⣣", "ϙ": "⢣", "Ϛ": "⣧", "ϛ": "⢧", "Ϝ": "⣹", "ϝ": "⢧", "Ϟ": "⣟", "ϟ": "⢟", "Ϡ": "⣜", "ϡ": "⢜", "ϰ": "⣟", "ϱ": "⣗", "ϲ": "⣎", "ϴ": "⣹", "ϵ": "⣑", "Ϻ": "⣮", "ϻ": "⢮"}], "euro/characters/Hebrew.min": [{"locale": "euro"}, {"modality": "braille"}, {"א": "⢁", "ב": "⢃", "ג": "⠛", "ד": "⢙", "ה": "⢓", "ו": "⢺", "ז": "⢵", "ח": "⢭", "ט": "⢞", "י": "⢚", "ך": "⢅", "כ": "⢡", "ל": "⢇", "ם": "⢍", "מ": "⢍", "ן": "⢝", "נ": "⢝", "ס": "⢎", "ע": "⢫", "ף": "⢏", "פ": "⢏", "ץ": "⢮", "צ": "⢮", "ק": "⢟", "ר": "⢗", "ש": "⢩", "ת": "⢹", "ְ": "⠄", "ֱ": "⠢", "ֲ": "⠒", "ֳ": "⠜", "ִ": "⠊", "ֵ": "⠌", "ֶ": "⠑", "ַ": "⠉", "ָ": "⠣", "ֹ": "⠕", "ֻ": "⠥", "ּ": "⠐", "ֽ": "⠈", "ׁ": "⢱", "ׂ": "⢎"}], "euro/characters/Latin.min": [{"locale": "euro"}, {"modality": "braille"}, {"": "⢀", "": "⢋", "": "⢊", "": "⡒", "": "⡫", "": "⣫", "": "⢵", "": "⢟", "": "⡻", "": "⣶", " ": "⡀", "¡": "⡤", "¢": "⢐", "£": "⡨", "¤": "⣨", "¥": "⢨", "¦": "⢑", "§": "⡔", "¨": "⢈", "©": "⢯", "ª": "⢓", "«": "⣰", "¬": "⣲", "­": "⢤", "®": "⢗", "¯": "⢘", "°": "⢸", "±": "⣖", "²": "⢃", "³": "⢉", "´": "⢰", "µ": "⢍", "¶": "⢙", "·": "⡄", "¸": "⢠", "¹": "⢁", "º": "⢚", "»": "⣘", "¼": "⢥", "½": "⢧", "¾": "⢭", "¿": "⢄", "À": "⣦", "Á": "⢂", "Â": "⡡", "Ã": "⡬", "Ä": "⡰", "Å": "⡼", "Æ": "⡈", "Ç": "⡯", "È": "⣔", "É": "⡿", "Ê": "⡣", "Ë": "⢖", "Ì": "⡐", "Í": "⢒", "Î": "⡩", "Ï": "⢶", "Ð": "⡴", "Ñ": "⡲", "Ò": "⣐", "Ó": "⢲", "Ô": "⡹", "Õ": "⡢", "Ö": "⢔", "×": "⢎", "Ø": "⡪", "Ù": "⣴", "Ú": "⢢", "Û": "⡱", "Ü": "⢦", "Ý": "⢴", "Þ": "⡖", "ß": "⢼", "à": "⢷", "á": "⢡", "â": "⣡", "ã": "⣬", "ä": "⢜", "å": "⣼", "æ": "⣈", "ç": "⣯", "è": "⢮", "é": "⢿", "ê": "⣣", "ë": "⢫", "ì": "⢌", "í": "⢩", "î": "⣩", "ï": "⢻", "ð": "⢞", "ñ": "⢝", "ò": "⢬", "ó": "⢹", "ô": "⣹", "õ": "⢕", "ö": "⢪", "÷": "⣳", "ø": "⣪", "ù": "⢾", "ú": "⢱", "û": "⣱", "ü": "⢳", "ý": "⢺", "þ": "⢏", "ÿ": "⢽"}], "euro/characters/Multilingual.min": [{"locale": "euro"}, {"modality": "braille"}, {"Ā": "⣡", "ā": "⢡", "Ă": "⣷", "ă": "⢷", "Ą": "⢽", "ą": "⢡", "Ć": "⣩", "ć": "⢩", "Ĉ": "⣩", "ĉ": "⢩", "Č": "⣩", "č": "⢩", "Đ": "⣹", "đ": "⢹", "Ē": "⣱", "ē": "⢱", "Ę": "⣣", "ę": "⢣", "ě": "⠣", "Ĝ": "⣻", "ĝ": "⢻", "Ğ": "⡻", "ğ": "⠻", "Ģ": "⣻", "ģ": "⢻", "Ĥ": "⣳", "ĥ": "⢳", "ī": "⢪", "İ": "⣌", "ı": "⢌", "Ĵ": "⣺", "ĵ": "⢺", "Ķ": "⣥", "ķ": "⢥", "Ļ": "⣧", "ļ": "⢧", "Ł": "⢽", "ł": "⢱", "Ń": "⣹", "ń": "⢹", "Ņ": "⣽", "ņ": "⢽", "Ő": "⣻", "ő": "⢻", "Œ": "⡆", "œ": "⡨", "Ŗ": "⣷", "ŗ": "⢷", "ř": "⠺", "Ś": "⢽", "ś": "⢽", "Ŝ": "⣮", "ŝ": "⢮", "Ş": "⢽", "ş": "⢯", "Š": "⣠", "š": "⢱", "Ţ": "⣪", "ţ": "⢞", "Ť": "⣳", "ť": "⠳", "Ū": "⣬", "ū": "⢬", "Ŭ": "⣬", "ŭ": "⢬", "ů": "⠾", "Ű": "⣾", "ű": "⢾", "Ÿ": "⣿", "Ź": "⢽", "ź": "⢽", "Ż": "⢽", "ż": "⢯", "Ž": "⡳", "ž": "⠮", "ƒ": "⣀", "Ơ": "⡧", "ơ": "⠧", "ɜ": "⡵", "ˆ": "⣄", "ˇ": "⢽", "˘": "⢽", "˙": "⠐", "˛": "⢽", "˜": "⣒"}], "euro/characters/Special.min": [{"locale": "euro"}, {"modality": "braille"}, {" ": "", "‑": "⠤", "–": "⢤", "—": "⠤", "―": "⠤", "‗": "⠤", "‘": "⡦", "’": "⡈", " ": "⢙", "‚": "⣤", "“": "⢅", "”": "⢇", "„": "⢣", "†": "⢋", "‡": "⢛", "•": "⠔", "…": "⢆", "‰": "⢊", "‹": "⡂", "›": "⢟", "ⁿ": "⡭", "€": "⡘", "₯": "⣹", "№": "⠼", "™": "⣻", "∙": "⢈", "√": "⡩", "∞": "⣮", "∩": "⣐", "≈": "⣔", "≡": "⢶", "≤": "⢰", "≥": "⢘", "⌐": "⡹", "⌠": "⡌", "⌡": "⡱", "─": "⡠", "│": "⡆", "┌": "⡒", "┐": "⣂", "└": "⣄", "┘": "⣠", "├": "⡦", "┤": "⢵", "┬": "⢅", "┴": "⡂", "═": "⣒", "║": "⢿", "╒": "⢯", "╓": "⢕", "╔": "⡶", "╕": "⢉", "╖": "⢤", "╗": "⢛", "╘": "⢊", "╙": "⢇", "╚": "⣆", "╛": "⣬", "╜": "⣨", "╝": "⣢", "╞": "⢠", "╟": "⡒", "╠": "⢋", "╡": "⢢", "╢": "⢺", "╣": "⡷", "╤": "⡬", "╥": "⢂", "╦": "⢟", "╧": "⡡", "╨": "⡐", "╩": "⢀", "╪": "⡴", "╫": "⢗", "╬": "⣰", "▄": "⡣", "█": "⣿", "▀": "⡾", "▌": "⣦", "▐": "⣴", "░": "⣀", "▒": "⣤", "▓": "⣶", "■": "⡿", "●": "⠔"}], "euro/rules/euro.min": {"locale": "euro", "modality": "braille", "domain": "default", "rules": [["Rule", "stree", "default", "[n] ./*[1]", "self::stree"], ["Rule", "latex", "default", "[n] @latex", "self::*[@latex]"], ["Rule", "nolatex", "default", "[m] children/* (separator:\"⠀\")", "self::*", "not(@latex)"], ["Rule", "row", "default", "[m] children/* (separator:\"⠀⠯⠀\")", "self::row[not(@latex)]"], ["Precondition", "binary-operation", "default", "self::infixop"], ["<PERSON><PERSON>", "binary-operation", "self::relseq"], ["<PERSON><PERSON>", "binary-operation", "self::multirel"], ["Action", "binary-operation", "[m] children/* (sepFunc:CTFcontentIterator);"], ["Rule", "fences-open-close", "default", "[n] content/*[1]; [n] children/*[1]; [n] content/*[2]", "self::fenced"]]}}