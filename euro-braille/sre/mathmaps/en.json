{"en/messages/alphabets.min": {"kind": "alphabets", "locale": "en", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alpha", "beta", "gamma", "delta", "epsilon", "zeta", "eta", "theta", "iota", "kappa", "lamda", "mu", "nu", "xi", "omicron", "pi", "rho", "final sigma", "sigma", "tau", "upsilon", "phi", "chi", "psi", "omega", "partial differential", "epsilon", "theta", "kappa", "phi", "rho", "pi"], "greekCap": ["Alpha", "Beta", "Gamma", "Delta", "Epsilon", "Zeta", "Eta", "Theta", "Iota", "Kappa", "Lamda", "Mu", "<PERSON>u", "Xi", "Omicron", "Pi", "Rho", "Theta", "Sigma", "Tau", "Upsilon", "Phi", "<PERSON>", "Psi", "Omega"], "capPrefix": {"default": "cap", "mathspeak": "upper"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "en/messages/messages.min": {"kind": "messages", "locale": "en", "messages": {"MS": {"START": "Start", "FRAC_V": "Fraction", "FRAC_B": "<PERSON><PERSON>", "FRAC_S": "<PERSON><PERSON>", "END": "End", "FRAC_OVER": "Over", "TWICE": "Twice", "NEST_FRAC": "Nest", "ENDFRAC": "EndFrac", "SUPER": "Super", "SUB": "Sub", "SUP": "<PERSON><PERSON>", "SUPERSCRIPT": "Superscript", "SUBSCRIPT": "Subscript", "BASELINE": "Baseline", "BASE": "Base", "NESTED": "Nested", "NEST_ROOT": "Nest", "STARTROOT": "StartRoot", "ENDROOT": "EndRoot", "ROOTINDEX": "RootIndex", "ROOT": "Root", "INDEX": "Index", "UNDER": "Under", "UNDERSCRIPT": "Underscript", "OVER": "Over", "OVERSCRIPT": "Overscript", "ENDSCRIPTS": "Endscripts"}, "MSroots": {}, "font": {"bold": "bold", "bold-fraktur": "bold fraktur", "bold-italic": "bold italic", "bold-script": "bold script", "caligraphic": "calligraphic", "caligraphic-bold": "calligraphic bold", "double-struck": "double struck", "double-struck-italic": "double struck italic", "fraktur": "fraktur", "fullwidth": "fullwidth", "italic": "italic", "monospace": "monospace", "normal": "normal", "oldstyle": "oldstyle", "oldstyle-bold": "oldstyle bold", "script": "script", "sans-serif": "sans serif", "sans-serif-italic": "sans serif italic", "sans-serif-bold": "sans serif bold", "sans-serif-bold-italic": "sans serif bold italic", "unknown": "unknown"}, "embellish": {"super": "super", "sub": "sub", "circled": "circled", "parenthesized": "parenthesized", "period": ["period", "postfix<PERSON><PERSON><PERSON>"], "negative-circled": "black circled", "double-circled": "double circled", "circled-sans-serif": "circled sans serif", "negative-circled-sans-serif": "black circled sans serif", "comma": ["comma", "postfix<PERSON><PERSON><PERSON>"], "squared": "squared", "negative-squared": "black squared"}, "role": {"addition": "addition", "multiplication": "multiplication", "subtraction": "subtraction", "division": "division", "equality": "equality", "inequality": "inequality", "element": "element", "arrow": "arrow", "determinant": "determinant", "rowvector": "row vector", "binomial": "binomial", "squarematrix": "square matrix", "multiline": "multiple lines", "matrix": "matrix", "vector": "vector", "cases": "case statement", "table": "table", "unknown": "unknown"}, "enclose": {"longdiv": "long division", "actuarial": "actuarial symbol", "radical": "square root", "box": "box", "roundedbox": "rounded box", "circle": "circle", "left": "left vertical-line", "right": "right vertical-line", "top": "overbar", "bottom": "underbar", "updiagonalstrike": "crossout", "downdiagonalstrike": "crossout", "verticalstrike": "vertical strikeout", "horizontalstrike": "crossout", "madruwb": "Arabic factorial symbol", "updiagonalarrow": "diagonal arrow", "phasorangle": "phasor angle", "unknown": "long division"}, "navigate": {"COLLAPSIBLE": "collapsible", "EXPANDABLE": "expandable", "LEVEL": "Level"}, "regexp": {"TEXT": "a-zA-Z", "NUMBER": "((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+", "DECIMAL_MARK": "\\.", "DIGIT_GROUP": ",", "JOINER_SUBSUPER": " ", "JOINER_FRAC": ""}, "unitTimes": ""}}, "en/messages/numbers.min": {"kind": "numbers", "locale": "en", "messages": {"zero": "zero", "ones": ["", "one", "two", "three", "four", "five", "six", "seven", "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen", "fifteen", "sixteen", "seventeen", "eighteen", "nineteen"], "tens": ["", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"], "large": ["", "thousand", "million", "billion", "trillion", "quadrillion", "quintillion", "sextillion", "septillion", "octillion", "nonillion", "decillion"], "vulgarSep": " ", "numSep": " "}}, "en/si/prefixes.min": [{"Y": "yotta", "Z": "zetta", "E": "exa", "P": "peta", "T": "tera", "G": "giga", "M": "mega", "k": "kilo", "h": "hecto", "da": "deka", "d": "deci", "c": "centi", "m": "milli", "µ": "micro", "μ": "micro", "n": "nano", "p": "pico", "f": "femto", "a": "atto", "z": "zepto", "y": "yocto"}], "en/functions/algebra.min": [{"locale": "en"}, {"key": "deg", "mappings": {"default": {"default": "degree"}}}, {"key": "det", "mappings": {"default": {"default": "determinant"}, "mathspeak": {"default": "det"}}}, {"key": "dim", "mappings": {"default": {"default": "dimension"}}}, {"key": "hom", "mappings": {"default": {"default": "homomorphism"}, "mathspeak": {"default": "hom"}, "clearspeak": {"default": "hom"}}}, {"key": "ker", "mappings": {"default": {"default": "kernel"}}}, {"key": "Tr", "mappings": {"default": {"default": "trace"}}}], "en/functions/elementary.min": [{"locale": "en"}, {"key": "log", "mappings": {"default": {"default": "log"}}}, {"key": "ln", "mappings": {"default": {"default": "natural log"}, "mathspeak": {"default": "ln"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "natural log"}}}, {"key": "lg", "mappings": {"default": {"default": "log base 10"}}}, {"key": "exp", "mappings": {"default": {"default": "exponential"}, "mathspeak": {"default": "exp"}, "clearspeak": {"default": "exp"}}}, {"key": "gcd", "mappings": {"default": {"default": "greatest common divisor"}, "mathspeak": {"default": "gcd"}, "clearspeak": {"default": "gcd"}}}, {"key": "lcm", "mappings": {"default": {"default": "least common multiple"}, "mathspeak": {"default": "lcm"}, "clearspeak": {"default": "lcm"}}}, {"key": "arg", "mappings": {"default": {"default": "argument"}, "mathspeak": {"default": "arg"}, "clearspeak": {"default": "arg"}}}, {"key": "im", "mappings": {"default": {"default": "imaginary part"}, "mathspeak": {"default": "im"}, "clearspeak": {"default": "imaginary"}}}, {"key": "re", "mappings": {"default": {"default": "real part"}, "mathspeak": {"default": "re"}, "clearspeak": {"default": "real"}}}, {"key": "inf", "mappings": {"default": {"default": "infimum"}, "mathspeak": {"default": "inf"}, "clearspeak": {"default": "inf"}}}, {"key": "lim", "mappings": {"default": {"default": "limit"}, "mathspeak": {"default": "limit"}, "clearspeak": {"default": "lim"}}}, {"key": "liminf", "mappings": {"default": {"default": "limit inferior"}, "mathspeak": {"default": "liminf"}, "clearspeak": {"default": "liminf"}}}, {"key": "limsup", "mappings": {"default": {"default": "limit superior"}, "mathspeak": {"default": "limsup"}, "clearspeak": {"default": "limsup"}}}, {"key": "max", "mappings": {"default": {"default": "maximum"}, "mathspeak": {"default": "max"}, "clearspeak": {"default": "max"}}}, {"key": "min", "mappings": {"default": {"default": "minimum"}, "mathspeak": {"default": "min"}, "clearspeak": {"default": "min"}}}, {"key": "sup", "mappings": {"default": {"default": "supremum"}, "mathspeak": {"default": "sup"}, "clearspeak": {"default": "sup"}}}, {"key": "<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "colimit"}}}, {"key": "proj<PERSON>", "mappings": {"default": {"default": "projective limit"}}}, {"key": "mod", "mappings": {"default": {"default": "modulo"}, "mathspeak": {"default": "mod"}, "clearspeak": {"default": "mod"}}}, {"key": "Pr", "mappings": {"default": {"default": "probability"}}}], "en/functions/hyperbolic.min": [{"locale": "en"}, {"key": "cosh", "mappings": {"default": {"default": "hyperbolic cosine"}}}, {"key": "coth", "mappings": {"default": {"default": "hyperbolic cotangent"}}}, {"key": "csch", "mappings": {"default": {"default": "hyperbolic cosecant"}}}, {"key": "sech", "mappings": {"default": {"default": "hyperbolic secant"}}}, {"key": "sinh", "mappings": {"default": {"default": "hyperbolic sine"}}}, {"key": "tanh", "mappings": {"default": {"default": "hyperbolic tangent"}}}, {"key": "arcosh", "mappings": {"default": {"default": "area hyperbolic cosine"}}}, {"key": "arcoth", "mappings": {"default": {"default": "area hyperbolic cotangent"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "area hyperbolic cosecant"}}}, {"key": "arsech", "mappings": {"default": {"default": "area hyperbolic secant"}}}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "area hyperbolic sine"}}}, {"key": "artanh", "mappings": {"default": {"default": "area hyperbolic tangent"}}}], "en/functions/trigonometry.min": [{"locale": "en"}, {"key": "cos", "mappings": {"default": {"default": "cosine"}}}, {"key": "cot", "mappings": {"default": {"default": "cotangent"}}}, {"key": "csc", "mappings": {"default": {"default": "cosecant"}}}, {"key": "sec", "mappings": {"default": {"default": "secant"}}}, {"key": "sin", "mappings": {"default": {"default": "sine"}}}, {"key": "tan", "mappings": {"default": {"default": "tangent"}}}, {"key": "arccos", "mappings": {"default": {"default": "arc cosine"}, "clearspeak": {"Trig_TrigInverse": "cosine inverse"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "arc cotangent"}, "clearspeak": {"Trig_TrigInverse": "cotangent inverse"}}}, {"key": "arccsc", "mappings": {"default": {"default": "arc cosecant"}, "clearspeak": {"Trig_TrigInverse": "cosecant inverse"}}}, {"key": "arcsec", "mappings": {"default": {"default": "arc secant"}, "clearspeak": {"Trig_TrigInverse": "secant inverse"}}}, {"key": "arcsin", "mappings": {"default": {"default": "arc sine"}, "clearspeak": {"Trig_TrigInverse": "sine inverse"}}}, {"key": "arctan", "mappings": {"default": {"default": "arc tangent"}, "clearspeak": {"Trig_TrigInverse": "tangent inverse"}}}], "en/symbols/digits_rest.min": [{"locale": "en"}, {"key": "00B2", "mappings": {"default": {"default": "squared"}, "mathspeak": {"default": "squared"}, "clearspeak": {"default": "squared"}}}, {"key": "00B3", "mappings": {"default": {"default": "cubed"}, "mathspeak": {"default": "cubed"}, "clearspeak": {"default": "cubed"}}}, {"key": "00BC", "mappings": {"default": {"default": "one quarter"}}}, {"key": "00BD", "mappings": {"default": {"default": "one half"}}}, {"key": "00BE", "mappings": {"default": {"default": "three quarters"}}}, {"key": "2150", "mappings": {"default": {"default": "one seventh"}}}, {"key": "2151", "mappings": {"default": {"default": "one ninth"}}}, {"key": "2152", "mappings": {"default": {"default": "one tenth"}}}, {"key": "2153", "mappings": {"default": {"default": "one third"}}}, {"key": "2154", "mappings": {"default": {"default": "two thirds"}}}, {"key": "2155", "mappings": {"default": {"default": "one fifth"}}}, {"key": "2156", "mappings": {"default": {"default": "two fifths"}}}, {"key": "2157", "mappings": {"default": {"default": "three fifths"}}}, {"key": "2158", "mappings": {"default": {"default": "four fifths"}}}, {"key": "2159", "mappings": {"default": {"default": "one sixth"}}}, {"key": "215A", "mappings": {"default": {"default": "five sixths"}}}, {"key": "215B", "mappings": {"default": {"default": "one eighth"}}}, {"key": "215C", "mappings": {"default": {"default": "three eighths"}}}, {"key": "215D", "mappings": {"default": {"default": "five eighths"}}}, {"key": "215E", "mappings": {"default": {"default": "seven eighths"}}}, {"key": "215F", "mappings": {"default": {"default": "numerator one"}}}, {"key": "2189", "mappings": {"default": {"default": "zero thirds"}}}, {"key": "3248", "mappings": {"default": {"default": "circled ten on black square"}}}, {"key": "3249", "mappings": {"default": {"default": "circled twenty on black square"}}}, {"key": "324A", "mappings": {"default": {"default": "circled thirty on black square"}}}, {"key": "324B", "mappings": {"default": {"default": "circled forty on black square"}}}, {"key": "324C", "mappings": {"default": {"default": "circled fifty on black square"}}}, {"key": "324D", "mappings": {"default": {"default": "circled sixty on black square"}}}, {"key": "324E", "mappings": {"default": {"default": "circled seventy on black square"}}}, {"key": "324F", "mappings": {"default": {"default": "circled eighty on black square"}}}], "en/symbols/greek-rest.min": [{"locale": "en"}, {"key": "0394", "mappings": {"clearspeak": {"default": "triangle", "TriangleSymbol_Delta": "cap Delta"}}}], "en/symbols/greek-scripts.min": [{"locale": "en"}, {"key": "1D26", "mappings": {"default": {"default": "small cap Gamma"}, "mathspeak": {"default": "small upper Gamma"}}}, {"key": "1D27", "mappings": {"default": {"default": "small cap Lamda"}, "mathspeak": {"default": "small upper Lamda"}}}, {"key": "1D28", "mappings": {"default": {"default": "small cap Pi"}, "mathspeak": {"default": "small upper Pi"}}}, {"key": "1D29", "mappings": {"default": {"default": "small cap Rho"}, "mathspeak": {"default": "small upper Rho"}}}, {"key": "1D2A", "mappings": {"default": {"default": "small cap Psi"}, "mathspeak": {"default": "small upper Psi"}}}, {"key": "1D5E", "mappings": {"default": {"default": "superscript gamma"}}}, {"key": "1D60", "mappings": {"default": {"default": "superscript phi"}}}, {"key": "1D66", "mappings": {"default": {"default": "subscript beta"}}}, {"key": "1D67", "mappings": {"default": {"default": "subscript gamma"}}}, {"key": "1D68", "mappings": {"default": {"default": "subscript rho"}}}, {"key": "1D69", "mappings": {"default": {"default": "subscript phi"}}}, {"key": "1D6A", "mappings": {"default": {"default": "subscript chi"}}}], "en/symbols/greek-symbols.min": [{"locale": "en"}, {"key": "03D0", "mappings": {"default": {"default": "beta"}}}, {"key": "03D7", "mappings": {"default": {"default": "kai"}}}, {"key": "03F6", "mappings": {"default": {"default": "reversed epsilon"}}}, {"key": "1D7CA", "mappings": {"default": {"default": "bold cap Digamma"}, "mathspeak": {"default": "bold upper Digamma"}}}, {"key": "1D7CB", "mappings": {"default": {"default": "bold digamma"}}}], "en/symbols/hebrew_letters.min": [{"locale": "en"}, {"key": "2135", "mappings": {"default": {"default": "first transfinite cardinal", "alternative": "alef"}}}, {"key": "2136", "mappings": {"default": {"default": "second transfinite cardinal", "alternative": "bet"}}}, {"key": "2137", "mappings": {"default": {"default": "third transfinite cardinal", "alternative": "gimel"}}}, {"key": "2138", "mappings": {"default": {"default": "fourth transfinite cardinal", "alternative": "dalet"}}}], "en/symbols/latin-lower-double-accent.min": [{"locale": "en"}, {"key": "01D6", "mappings": {"default": {"default": "u double overdot overbar"}}}, {"key": "01D8", "mappings": {"default": {"default": "u double overdot acute"}}}, {"key": "01DA", "mappings": {"default": {"default": "u double overdot caron"}}}, {"key": "01DC", "mappings": {"default": {"default": "u double overdot grave"}}}, {"key": "01DF", "mappings": {"default": {"default": "a double overdot overbar"}}}, {"key": "01E1", "mappings": {"default": {"default": "a overdot overbar"}}}, {"key": "01ED", "mappings": {"default": {"default": "o ogonek overbar"}}}, {"key": "01FB", "mappings": {"default": {"default": "a ring above acute"}}}, {"key": "022B", "mappings": {"default": {"default": "o double overdot overbar"}}}, {"key": "022D", "mappings": {"default": {"default": "o tilde overbar"}}}, {"key": "0231", "mappings": {"default": {"default": "o overdot overbar"}}}, {"key": "1E09", "mappings": {"default": {"default": "c cedilla acute"}}}, {"key": "1E15", "mappings": {"default": {"default": "e overbar grave"}}}, {"key": "1E17", "mappings": {"default": {"default": "e overbar acute"}}}, {"key": "1E1D", "mappings": {"default": {"default": "e cedilla breve"}}}, {"key": "1E2F", "mappings": {"default": {"default": "i double overdot acute"}}}, {"key": "1E39", "mappings": {"default": {"default": "l underdot overbar"}}}, {"key": "1E4D", "mappings": {"default": {"default": "o tilde acute"}}}, {"key": "1E4F", "mappings": {"default": {"default": "o tilde double overdot"}}}, {"key": "1E51", "mappings": {"default": {"default": "o overbar grave"}}}, {"key": "1E53", "mappings": {"default": {"default": "o overbar acute"}}}, {"key": "1E5D", "mappings": {"default": {"default": "r underdot overbar"}}}, {"key": "1E65", "mappings": {"default": {"default": "s acute overdot"}}}, {"key": "1E67", "mappings": {"default": {"default": "s caron overdot"}}}, {"key": "1E69", "mappings": {"default": {"default": "s underdot overdot"}}}, {"key": "1E79", "mappings": {"default": {"default": "u tilde acute"}}}, {"key": "1E7B", "mappings": {"default": {"default": "u overbar double overdot"}}}, {"key": "1EA5", "mappings": {"default": {"default": "a hat acute"}}}, {"key": "1EA7", "mappings": {"default": {"default": "a hat grave"}}}, {"key": "1EA9", "mappings": {"default": {"default": "a hat hook above"}}}, {"key": "1EAB", "mappings": {"default": {"default": "a hat tilde"}}}, {"key": "1EAD", "mappings": {"default": {"default": "a hat underdot"}}}, {"key": "1EAF", "mappings": {"default": {"default": "a breve acute"}}}, {"key": "1EB1", "mappings": {"default": {"default": "a breve grave"}}}, {"key": "1EB3", "mappings": {"default": {"default": "a breve hook above"}}}, {"key": "1EB5", "mappings": {"default": {"default": "a breve tilde"}}}, {"key": "1EB7", "mappings": {"default": {"default": "a breve underdot"}}}, {"key": "1EBF", "mappings": {"default": {"default": "e hat acute"}}}, {"key": "1EC1", "mappings": {"default": {"default": "e hat grave"}}}, {"key": "1EC3", "mappings": {"default": {"default": "e hat hook above"}}}, {"key": "1EC5", "mappings": {"default": {"default": "e hat tilde"}}}, {"key": "1EC7", "mappings": {"default": {"default": "e hat underdot"}}}, {"key": "1ED1", "mappings": {"default": {"default": "o hat acute"}}}, {"key": "1ED3", "mappings": {"default": {"default": "o hat grave"}}}, {"key": "1ED5", "mappings": {"default": {"default": "o hat hook above"}}}, {"key": "1ED7", "mappings": {"default": {"default": "o hat tilde"}}}, {"key": "1ED9", "mappings": {"default": {"default": "o hat underdot"}}}, {"key": "1EDB", "mappings": {"default": {"default": "o acute prime"}}}, {"key": "1EDD", "mappings": {"default": {"default": "o grave prime"}}}, {"key": "1EDF", "mappings": {"default": {"default": "o hook above prime"}}}, {"key": "1EE1", "mappings": {"default": {"default": "o tilde prime"}}}, {"key": "1EE3", "mappings": {"default": {"default": "o underdot prime"}}}, {"key": "1EE9", "mappings": {"default": {"default": "u acute prime"}}}, {"key": "1EEB", "mappings": {"default": {"default": "u grave prime"}}}, {"key": "1EED", "mappings": {"default": {"default": "u hook above prime"}}}, {"key": "1EEF", "mappings": {"default": {"default": "u tilde prime"}}}, {"key": "1EF1", "mappings": {"default": {"default": "u underdot prime"}}}], "en/symbols/latin-lower-phonetic.min": [{"locale": "en"}, {"key": "00F8", "mappings": {"default": {"default": "o with stroke"}}}, {"key": "0111", "mappings": {"default": {"default": "d with stroke"}}}, {"key": "0127", "mappings": {"default": {"default": "h with stroke"}}}, {"key": "0142", "mappings": {"default": {"default": "l with stroke"}}}, {"key": "0167", "mappings": {"default": {"default": "t with stroke"}}}, {"key": "0180", "mappings": {"default": {"default": "b with stroke"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda with stroke"}}}, {"key": "01B6", "mappings": {"default": {"default": "z with stroke"}}}, {"key": "01BE", "mappings": {"default": {"default": "latin letter inverted glottal stop with stroke"}}}, {"key": "01E5", "mappings": {"default": {"default": "g with stroke"}}}, {"key": "01FF", "mappings": {"default": {"default": "o with stroke and acute"}}}, {"key": "023C", "mappings": {"default": {"default": "c with stroke"}}}, {"key": "0247", "mappings": {"default": {"default": "e with stroke"}}}, {"key": "0249", "mappings": {"default": {"default": "j with stroke"}}}, {"key": "024D", "mappings": {"default": {"default": "r with stroke"}}}, {"key": "024F", "mappings": {"default": {"default": "y with stroke"}}}, {"key": "025F", "mappings": {"default": {"default": "dotless j with stroke"}}}, {"key": "0268", "mappings": {"default": {"default": "i with stroke"}}}, {"key": "0284", "mappings": {"default": {"default": "dotless j with stroke and hook"}}}, {"key": "02A1", "mappings": {"default": {"default": "latin letter glottal stop with stroke"}}}, {"key": "02A2", "mappings": {"default": {"default": "latin letter reversed glottal stop with stroke"}}}, {"key": "1D13", "mappings": {"default": {"default": "sideways o with stroke"}}}, {"key": "1D7C", "mappings": {"default": {"default": "iota with stroke"}}}, {"key": "1D7D", "mappings": {"default": {"default": "p with stroke"}}}, {"key": "1D7F", "mappings": {"default": {"default": "upsilon with stroke"}}}, {"key": "1E9C", "mappings": {"default": {"default": "long s with diagonal stroke"}}}, {"key": "1E9D", "mappings": {"default": {"default": "long s with high stroke"}}}, {"key": "018D", "mappings": {"default": {"default": "turned delta"}}}, {"key": "1E9B", "mappings": {"default": {"default": "long s with dot above"}}}, {"key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}}, {"key": "017F", "mappings": {"default": {"default": "long s"}}}, {"key": "0183", "mappings": {"default": {"default": "b with topbar"}}}, {"key": "0185", "mappings": {"default": {"default": "tone six"}}}, {"key": "0188", "mappings": {"default": {"default": "c with hook"}}}, {"key": "018C", "mappings": {"default": {"default": "d with topbar"}}}, {"key": "0192", "mappings": {"default": {"default": "f with hook"}}}, {"key": "0195", "mappings": {"default": {"default": "hv"}}}, {"key": "0199", "mappings": {"default": {"default": "k with hook"}}}, {"key": "019A", "mappings": {"default": {"default": "l with bar"}}}, {"key": "019E", "mappings": {"default": {"default": "n with long right leg"}}}, {"key": "01A1", "mappings": {"default": {"default": "o with horn"}}}, {"key": "01A3", "mappings": {"default": {"default": "oi"}}}, {"key": "01A5", "mappings": {"default": {"default": "p with hook"}}}, {"key": "01A8", "mappings": {"default": {"default": "tone two"}}}, {"key": "01AA", "mappings": {"default": {"default": "latin letter reversed esh loop"}}}, {"key": "01AB", "mappings": {"default": {"default": "t with palatal hook"}}}, {"key": "01AD", "mappings": {"default": {"default": "t with hook"}}}, {"key": "01B0", "mappings": {"default": {"default": "u with horn"}}}, {"key": "01B4", "mappings": {"default": {"default": "y with hook"}}}, {"key": "01B9", "mappings": {"default": {"default": "ezh reversed"}}}, {"key": "01BA", "mappings": {"default": {"default": "ezh with tail"}}}, {"key": "01BD", "mappings": {"default": {"default": "tone five"}}}, {"key": "01BF", "mappings": {"default": {"default": "latin letter wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "dz with caron"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "ae with macron"}}}, {"key": "01EF", "mappings": {"default": {"default": "ezh with caron"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"key": "0293", "mappings": {"default": {"default": "ezh with curl"}}}, {"key": "02A4", "mappings": {"default": {"default": "dezh digraph"}}}, {"key": "01DD", "mappings": {"default": {"default": "turned e"}}}, {"key": "01FD", "mappings": {"default": {"default": "ae with acute"}}}, {"key": "0221", "mappings": {"default": {"default": "d with curl"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "z with hook"}}}, {"key": "0234", "mappings": {"default": {"default": "l with curl"}}}, {"key": "0235", "mappings": {"default": {"default": "n with curl"}}}, {"key": "0236", "mappings": {"default": {"default": "t with curl"}}}, {"key": "0238", "mappings": {"default": {"default": "db digraph"}}}, {"key": "0239", "mappings": {"default": {"default": "qp digraph"}}}, {"key": "023F", "mappings": {"default": {"default": "s with swash tail"}}}, {"key": "0240", "mappings": {"default": {"default": "z with swash tail"}}}, {"key": "0242", "mappings": {"default": {"default": "glottal stop"}}}, {"key": "024B", "mappings": {"default": {"default": "q with hook tail"}}}, {"key": "0250", "mappings": {"default": {"default": "turned a"}}}, {"key": "0251", "mappings": {"default": {"default": "alpha"}}}, {"key": "0252", "mappings": {"default": {"default": "turned alpha"}}}, {"key": "0253", "mappings": {"default": {"default": "b with hook"}}}, {"key": "0254", "mappings": {"default": {"default": "open o"}}}, {"key": "0255", "mappings": {"default": {"default": "c with curl"}}}, {"key": "0256", "mappings": {"default": {"default": "d with tail"}}}, {"key": "0257", "mappings": {"default": {"default": "d with hook"}}}, {"key": "0258", "mappings": {"default": {"default": "reversed e"}}}, {"key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"key": "025A", "mappings": {"default": {"default": "schwa with hook"}}}, {"key": "025B", "mappings": {"default": {"default": "open e"}}}, {"key": "025C", "mappings": {"default": {"default": "reversed open e"}}}, {"key": "025D", "mappings": {"default": {"default": "reversed open e with hook"}}}, {"key": "025E", "mappings": {"default": {"default": "closed reversed open e"}}}, {"key": "0260", "mappings": {"default": {"default": "g with hook"}}}, {"key": "0261", "mappings": {"default": {"default": "script g"}}}, {"key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"key": "0264", "mappings": {"default": {"default": "rams horn"}}}, {"key": "0265", "mappings": {"default": {"default": "turned h"}}}, {"key": "0266", "mappings": {"default": {"default": "h with hook"}}}, {"key": "0267", "mappings": {"default": {"default": "heng with hook"}}}, {"key": "0269", "mappings": {"default": {"default": "iota"}}}, {"key": "026B", "mappings": {"default": {"default": "l with middle tilde"}}}, {"key": "026C", "mappings": {"default": {"default": "l with belt"}}}, {"key": "026D", "mappings": {"default": {"default": "l with retroflex hook"}}}, {"key": "026F", "mappings": {"default": {"default": "turned m"}}}, {"key": "0270", "mappings": {"default": {"default": "turned m with long leg"}}}, {"key": "0271", "mappings": {"default": {"default": "m with hook"}}}, {"key": "0272", "mappings": {"default": {"default": "n with left hook"}}}, {"key": "0273", "mappings": {"default": {"default": "n with retroflex hook"}}}, {"key": "0275", "mappings": {"default": {"default": "barred o"}}}, {"key": "0277", "mappings": {"default": {"default": "closed omega"}}}, {"key": "0278", "mappings": {"default": {"default": "phi"}}}, {"key": "0279", "mappings": {"default": {"default": "turned r"}}}, {"key": "027A", "mappings": {"default": {"default": "turned r with long leg"}}}, {"key": "027B", "mappings": {"default": {"default": "turned r with hook"}}}, {"key": "027C", "mappings": {"default": {"default": "r with long leg"}}}, {"key": "027D", "mappings": {"default": {"default": "r with tail"}}}, {"key": "027E", "mappings": {"default": {"default": "r with fishhook"}}}, {"key": "027F", "mappings": {"default": {"default": "reversed r with fishhook"}}}, {"key": "0282", "mappings": {"default": {"default": "s with hook"}}}, {"key": "0283", "mappings": {"default": {"default": "esh"}}}, {"key": "0285", "mappings": {"default": {"default": "squat reversed esh"}}}, {"key": "0286", "mappings": {"default": {"default": "esh with curl"}}}, {"key": "0287", "mappings": {"default": {"default": "turned t"}}}, {"key": "0288", "mappings": {"default": {"default": "t with retroflex hook"}}}, {"key": "0289", "mappings": {"default": {"default": "u bar"}}}, {"key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"key": "028B", "mappings": {"default": {"default": "v with hook"}}}, {"key": "028C", "mappings": {"default": {"default": "turned v"}}}, {"key": "028D", "mappings": {"default": {"default": "turned w"}}}, {"key": "028E", "mappings": {"default": {"default": "turned y"}}}, {"key": "0290", "mappings": {"default": {"default": "z with retroflex hook"}}}, {"key": "0291", "mappings": {"default": {"default": "z with curl"}}}, {"key": "0295", "mappings": {"default": {"default": "latin letter pharyngeal voiced fricative"}}}, {"key": "0296", "mappings": {"default": {"default": "latin letter inverted glottal stop"}}}, {"key": "0297", "mappings": {"default": {"default": "latin letter stretched c"}}}, {"key": "0298", "mappings": {"default": {"default": "latin letter bilabial click"}}}, {"key": "029A", "mappings": {"default": {"default": "closed open e"}}}, {"key": "029E", "mappings": {"default": {"default": "turned k"}}}, {"key": "02A0", "mappings": {"default": {"default": "q with hook"}}}, {"key": "02A3", "mappings": {"default": {"default": "dz digraph"}}}, {"key": "02A5", "mappings": {"default": {"default": "dz digraph with curl"}}}, {"key": "02A6", "mappings": {"default": {"default": "ts digraph"}}}, {"key": "02A7", "mappings": {"default": {"default": "tesh digraph"}}}, {"key": "02A8", "mappings": {"default": {"default": "tc digraph with curl"}}}, {"key": "02A9", "mappings": {"default": {"default": "feng digraph"}}}, {"key": "02AA", "mappings": {"default": {"default": "ls digraph"}}}, {"key": "02AB", "mappings": {"default": {"default": "lz digraph"}}}, {"key": "02AC", "mappings": {"default": {"default": "latin letter bilabial percussive"}}}, {"key": "02AD", "mappings": {"default": {"default": "latin letter bidental percussive"}}}, {"key": "02AE", "mappings": {"default": {"default": "turned h with fishhook"}}}, {"key": "02AF", "mappings": {"default": {"default": "turned h with fishhook and tail"}}}, {"key": "1D02", "mappings": {"default": {"default": "turned ae"}}}, {"key": "1D08", "mappings": {"default": {"default": "turned open e"}}}, {"key": "1D09", "mappings": {"default": {"default": "turned i"}}}, {"key": "1D11", "mappings": {"default": {"default": "sideways o"}}}, {"key": "1D12", "mappings": {"default": {"default": "sideways open o"}}}, {"key": "1D14", "mappings": {"default": {"default": "turned oe"}}}, {"key": "1D16", "mappings": {"default": {"default": "top half o"}}}, {"key": "1D17", "mappings": {"default": {"default": "bottom half o"}}}, {"key": "1D1D", "mappings": {"default": {"default": "sideways u"}}}, {"key": "1D1E", "mappings": {"default": {"default": "sideways diaeresized u"}}}, {"key": "1D1F", "mappings": {"default": {"default": "sideways turned m"}}}, {"key": "1D24", "mappings": {"default": {"default": "latin letter voiced laryngeal spirant"}}}, {"key": "1D25", "mappings": {"default": {"default": "latin letter ain"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "b with middle tilde"}}}, {"key": "1D6D", "mappings": {"default": {"default": "d with middle tilde"}}}, {"key": "1D6E", "mappings": {"default": {"default": "f with middle tilde"}}}, {"key": "1D6F", "mappings": {"default": {"default": "m with middle tilde"}}}, {"key": "1D70", "mappings": {"default": {"default": "n with middle tilde"}}}, {"key": "1D71", "mappings": {"default": {"default": "p with middle tilde"}}}, {"key": "1D72", "mappings": {"default": {"default": "r with middle tilde"}}}, {"key": "1D73", "mappings": {"default": {"default": "r with fishhook and middle tilde"}}}, {"key": "1D74", "mappings": {"default": {"default": "s with middle tilde"}}}, {"key": "1D75", "mappings": {"default": {"default": "t with middle tilde"}}}, {"key": "1D76", "mappings": {"default": {"default": "z with middle tilde"}}}, {"key": "1D77", "mappings": {"default": {"default": "turned g"}}}, {"key": "1D79", "mappings": {"default": {"default": "insular g"}}}, {"key": "1D7A", "mappings": {"default": {"default": "th with strikethrough"}}}, {"key": "1D80", "mappings": {"default": {"default": "b with palatal hook"}}}, {"key": "1D81", "mappings": {"default": {"default": "d with palatal hook"}}}, {"key": "1D82", "mappings": {"default": {"default": "f with palatal hook"}}}, {"key": "1D83", "mappings": {"default": {"default": "g with palatal hook"}}}, {"key": "1D84", "mappings": {"default": {"default": "k with palatal hook"}}}, {"key": "1D85", "mappings": {"default": {"default": "l with palatal hook"}}}, {"key": "1D86", "mappings": {"default": {"default": "m with palatal hook"}}}, {"key": "1D87", "mappings": {"default": {"default": "n with palatal hook"}}}, {"key": "1D88", "mappings": {"default": {"default": "p with palatal hook"}}}, {"key": "1D89", "mappings": {"default": {"default": "r with palatal hook"}}}, {"key": "1D8A", "mappings": {"default": {"default": "s with palatal hook"}}}, {"key": "1D8B", "mappings": {"default": {"default": "esh with palatal hook"}}}, {"key": "1D8C", "mappings": {"default": {"default": "v with palatal hook"}}}, {"key": "1D8D", "mappings": {"default": {"default": "x with palatal hook"}}}, {"key": "1D8E", "mappings": {"default": {"default": "z with palatal hook"}}}, {"key": "1D8F", "mappings": {"default": {"default": "a with retroflex hook"}}}, {"key": "1D90", "mappings": {"default": {"default": "alpha with retroflex hook"}}}, {"key": "1D91", "mappings": {"default": {"default": "d with hook and tail"}}}, {"key": "1D92", "mappings": {"default": {"default": "e with retroflex hook"}}}, {"key": "1D93", "mappings": {"default": {"default": "open e with retroflex hook"}}}, {"key": "1D94", "mappings": {"default": {"default": "reversed open e with retroflex hook"}}}, {"key": "1D95", "mappings": {"default": {"default": "schwa with retroflex hook"}}}, {"key": "1D96", "mappings": {"default": {"default": "i with retroflex hook"}}}, {"key": "1D97", "mappings": {"default": {"default": "open o with retroflex hook"}}}, {"key": "1D98", "mappings": {"default": {"default": "esh with retroflex hook"}}}, {"key": "1D99", "mappings": {"default": {"default": "u with retroflex hook"}}}, {"key": "1D9A", "mappings": {"default": {"default": "ezh with retroflex hook"}}}, {"key": "0149", "mappings": {"default": {"default": "n preceded by apostrophe"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "en/symbols/latin-lower-single-accent.min": [{"locale": "en"}, {"key": "00E0", "mappings": {"default": {"default": "a grave"}, "mathspeak": {"default": "modifying above a with grave", "brief": "mod above a with grave", "sbrief": "mod above a with grave"}}}, {"key": "00E1", "mappings": {"default": {"default": "a acute"}, "mathspeak": {"default": "modifying above a with acute", "brief": "mod above a with acute", "sbrief": "mod above a with acute"}}}, {"key": "00E2", "mappings": {"default": {"default": "a hat"}, "mathspeak": {"default": "modifying above a with caret", "brief": "mod above a with caret", "sbrief": "mod above a with caret"}}}, {"key": "00E3", "mappings": {"default": {"default": "a tilde"}, "mathspeak": {"default": "a overtilde", "brief": "a overtilde", "sbrief": "a overtilde"}}}, {"key": "00E4", "mappings": {"default": {"default": "a double overdot"}, "mathspeak": {"default": "modifying above a with double dot", "brief": "mod above a with double dot", "sbrief": "mod above a with double dot"}}}, {"key": "00E5", "mappings": {"default": {"default": "a ring"}, "mathspeak": {"default": "modifying above a with ring", "brief": "mod above a with ring", "sbrief": "mod above a with ring"}}}, {"key": "00E7", "mappings": {"default": {"default": "c cedilla"}, "mathspeak": {"default": "modifying above c with cedilla", "brief": "mod above c with cedilla", "sbrief": "mod above c with cedilla"}}}, {"key": "00E8", "mappings": {"default": {"default": "e grave"}, "mathspeak": {"default": "modifying above e with grave", "brief": "mod above e with grave", "sbrief": "mod above e with grave"}}}, {"key": "00E9", "mappings": {"default": {"default": "e acute"}, "mathspeak": {"default": "modifying above e with acute", "brief": "mod above e with acute", "sbrief": "mod above e with acute"}}}, {"key": "00EA", "mappings": {"default": {"default": "e hat"}, "mathspeak": {"default": "modifying above e with caret", "brief": "mod above e with caret", "sbrief": "mod above e with caret"}}}, {"key": "00EB", "mappings": {"default": {"default": "e double overdot"}, "mathspeak": {"default": "modifying above e with double dot", "brief": "mod above e with double dot", "sbrief": "mod above e with double dot"}}}, {"key": "00EC", "mappings": {"default": {"default": "i grave"}, "mathspeak": {"default": "modifying above i with grave", "brief": "mod above i with grave", "sbrief": "mod above i with grave"}}}, {"key": "00ED", "mappings": {"default": {"default": "i acute"}, "mathspeak": {"default": "modifying above i with acute", "brief": "mod above i with acute", "sbrief": "mod above i with acute"}}}, {"key": "00EE", "mappings": {"default": {"default": "i hat"}, "mathspeak": {"default": "modifying above i with caret", "brief": "mod above i with caret", "sbrief": "mod above i with caret"}}}, {"key": "00EF", "mappings": {"default": {"default": "i double overdot"}, "mathspeak": {"default": "modifying above i with double dot", "brief": "mod above i with double dot", "sbrief": "mod above i with double dot"}}}, {"key": "00F1", "mappings": {"default": {"default": "n tilde"}, "mathspeak": {"default": "n overtilde", "brief": "n overtilde", "sbrief": "n overtilde"}}}, {"key": "00F2", "mappings": {"default": {"default": "o grave"}, "mathspeak": {"default": "modifying above o with grave", "brief": "mod above o with grave", "sbrief": "mod above o with grave"}}}, {"key": "00F3", "mappings": {"default": {"default": "o acute"}, "mathspeak": {"default": "modifying above o with acute", "brief": "mod above o with acute", "sbrief": "mod above o with acute"}}}, {"key": "00F4", "mappings": {"default": {"default": "o hat"}, "mathspeak": {"default": "modifying above o with caret", "brief": "mod above o with caret", "sbrief": "mod above o with caret"}}}, {"key": "00F5", "mappings": {"default": {"default": "o tilde"}, "mathspeak": {"default": "o overtilde", "brief": "o overtilde", "sbrief": "o overtilde"}}}, {"key": "00F6", "mappings": {"default": {"default": "o double overdot"}, "mathspeak": {"default": "modifying above o with double dot", "brief": "mod above o with double dot", "sbrief": "mod above o with double dot"}}}, {"key": "00F9", "mappings": {"default": {"default": "u grave"}, "mathspeak": {"default": "modifying above u with grave", "brief": "mod above u with grave", "sbrief": "mod above u with grave"}}}, {"key": "00FA", "mappings": {"default": {"default": "u acute"}, "mathspeak": {"default": "modifying above u with acute", "brief": "mod above u with acute", "sbrief": "mod above u with acute"}}}, {"key": "00FB", "mappings": {"default": {"default": "u hat"}, "mathspeak": {"default": "modifying above u with caret", "brief": "mod above u with caret", "sbrief": "mod above u with caret"}}}, {"key": "00FC", "mappings": {"default": {"default": "u double overdot"}, "mathspeak": {"default": "modifying above u with double dot", "brief": "mod above u with double dot", "sbrief": "mod above u with double dot"}}}, {"key": "00FD", "mappings": {"default": {"default": "y acute"}, "mathspeak": {"default": "modifying above y with acute", "brief": "mod above y with acute", "sbrief": "mod above y with acute"}}}, {"key": "00FF", "mappings": {"default": {"default": "y double overdot"}, "mathspeak": {"default": "modifying above y with double dot", "brief": "mod above y with double dot", "sbrief": "mod above y with double dot"}}}, {"key": "0101", "mappings": {"default": {"default": "a overbar"}, "mathspeak": {"default": "a overbar", "brief": "a overbar", "sbrief": "a overbar"}}}, {"key": "0103", "mappings": {"default": {"default": "a breve"}, "mathspeak": {"default": "modifying above a with breve", "brief": "mod above a with breve", "sbrief": "mod above a with breve"}}}, {"key": "0105", "mappings": {"default": {"default": "a ogonek"}, "mathspeak": {"default": "modifying above a with ogonek", "brief": "mod above a with ogonek", "sbrief": "mod above a with ogonek"}}}, {"key": "0107", "mappings": {"default": {"default": "c acute"}, "mathspeak": {"default": "modifying above c with acute", "brief": "mod above c with acute", "sbrief": "mod above c with acute"}}}, {"key": "0109", "mappings": {"default": {"default": "c hat"}, "mathspeak": {"default": "modifying above c with caret", "brief": "mod above c with caret", "sbrief": "mod above c with caret"}}}, {"key": "010B", "mappings": {"default": {"default": "c overdot"}, "mathspeak": {"default": "modifying above c with dot", "brief": "mod above c with dot", "sbrief": "mod above c with dot"}}}, {"key": "010D", "mappings": {"default": {"default": "c caron"}, "mathspeak": {"default": "modifying above c with caron", "brief": "mod above c with caron", "sbrief": "mod above c with caron"}}}, {"key": "010F", "mappings": {"default": {"default": "d caron"}, "mathspeak": {"default": "modifying above d with caron", "brief": "mod above d with caron", "sbrief": "mod above d with caron"}}}, {"key": "0113", "mappings": {"default": {"default": "e overbar"}, "mathspeak": {"default": "e overbar", "brief": "e overbar", "sbrief": "e overbar"}}}, {"key": "0115", "mappings": {"default": {"default": "e breve"}, "mathspeak": {"default": "modifying above e with breve", "brief": "mod above e with breve", "sbrief": "mod above e with breve"}}}, {"key": "0117", "mappings": {"default": {"default": "e overdot"}, "mathspeak": {"default": "modifying above e with dot", "brief": "mod above e with dot", "sbrief": "mod above e with dot"}}}, {"key": "0119", "mappings": {"default": {"default": "e ogonek"}, "mathspeak": {"default": "modifying above e with ogonek", "brief": "mod above e with ogonek", "sbrief": "mod above e with ogonek"}}}, {"key": "011B", "mappings": {"default": {"default": "e caron"}, "mathspeak": {"default": "modifying above e with caron", "brief": "mod above e with caron", "sbrief": "mod above e with caron"}}}, {"key": "011D", "mappings": {"default": {"default": "g hat"}, "mathspeak": {"default": "modifying above g with caret", "brief": "mod above g with caret", "sbrief": "mod above g with caret"}}}, {"key": "011F", "mappings": {"default": {"default": "g breve"}, "mathspeak": {"default": "modifying above g with breve", "brief": "mod above g with breve", "sbrief": "mod above g with breve"}}}, {"key": "0121", "mappings": {"default": {"default": "g overdot"}, "mathspeak": {"default": "modifying above g with dot", "brief": "mod above g with dot", "sbrief": "mod above g with dot"}}}, {"key": "0123", "mappings": {"default": {"default": "g cedilla"}, "mathspeak": {"default": "modifying above g with cedilla", "brief": "mod above g with cedilla", "sbrief": "mod above g with cedilla"}}}, {"key": "0125", "mappings": {"default": {"default": "h hat"}, "mathspeak": {"default": "modifying above h with caret", "brief": "mod above h with caret", "sbrief": "mod above h with caret"}}}, {"key": "0129", "mappings": {"default": {"default": "i tilde"}, "mathspeak": {"default": "i overtilde", "brief": "i overtilde", "sbrief": "i overtilde"}}}, {"key": "012B", "mappings": {"default": {"default": "i overbar"}, "mathspeak": {"default": "i overbar", "brief": "i overbar", "sbrief": "i overbar"}}}, {"key": "012D", "mappings": {"default": {"default": "i breve"}, "mathspeak": {"default": "modifying above i with breve", "brief": "mod above i with breve", "sbrief": "mod above i with breve"}}}, {"key": "012F", "mappings": {"default": {"default": "i ogonek"}, "mathspeak": {"default": "modifying above i with ogonek", "brief": "mod above i with ogonek", "sbrief": "mod above i with ogonek"}}}, {"key": "0131", "mappings": {"default": {"default": "dotless i"}, "mathspeak": {"default": "modifying above dotless i", "brief": "mod above dotless i", "sbrief": "mod above dotless i"}}}, {"key": "0135", "mappings": {"default": {"default": "j hat"}, "mathspeak": {"default": "modifying above j with caret", "brief": "mod above j with caret", "sbrief": "mod above j with caret"}}}, {"key": "0137", "mappings": {"default": {"default": "k cedilla"}, "mathspeak": {"default": "modifying above k with cedilla", "brief": "mod above k with cedilla", "sbrief": "mod above k with cedilla"}}}, {"key": "013A", "mappings": {"default": {"default": "l acute"}, "mathspeak": {"default": "modifying above l with acute", "brief": "mod above l with acute", "sbrief": "mod above l with acute"}}}, {"key": "013C", "mappings": {"default": {"default": "l cedilla"}, "mathspeak": {"default": "modifying above l with cedilla", "brief": "mod above l with cedilla", "sbrief": "mod above l with cedilla"}}}, {"key": "013E", "mappings": {"default": {"default": "l caron"}, "mathspeak": {"default": "modifying above l with caron", "brief": "mod above l with caron", "sbrief": "mod above l with caron"}}}, {"key": "0140", "mappings": {"default": {"default": "l middle dot"}, "mathspeak": {"default": "modifying above l with middle dot", "brief": "mod above l with middle dot", "sbrief": "mod above l with middle dot"}}}, {"key": "0144", "mappings": {"default": {"default": "n acute"}, "mathspeak": {"default": "modifying above n with acute", "brief": "mod above n with acute", "sbrief": "mod above n with acute"}}}, {"key": "0146", "mappings": {"default": {"default": "n cedilla"}, "mathspeak": {"default": "modifying above n with cedilla", "brief": "mod above n with cedilla", "sbrief": "mod above n with cedilla"}}}, {"key": "0148", "mappings": {"default": {"default": "n caron"}, "mathspeak": {"default": "modifying above n with caron", "brief": "mod above n with caron", "sbrief": "mod above n with caron"}}}, {"key": "014D", "mappings": {"default": {"default": "o overbar"}, "mathspeak": {"default": "o overbar", "brief": "o overbar", "sbrief": "o overbar"}}}, {"key": "014F", "mappings": {"default": {"default": "o breve"}, "mathspeak": {"default": "modifying above o with breve", "brief": "mod above o with breve", "sbrief": "mod above o with breve"}}}, {"key": "0151", "mappings": {"default": {"default": "o double acute"}, "mathspeak": {"default": "modifying above o with double acute", "brief": "mod above o with double acute", "sbrief": "mod above o with double acute"}}}, {"key": "0155", "mappings": {"default": {"default": "r acute"}, "mathspeak": {"default": "modifying above r with acute", "brief": "mod above r with acute", "sbrief": "mod above r with acute"}}}, {"key": "0157", "mappings": {"default": {"default": "r cedilla"}, "mathspeak": {"default": "modifying above r with cedilla", "brief": "mod above r with cedilla", "sbrief": "mod above r with cedilla"}}}, {"key": "0159", "mappings": {"default": {"default": "r caron"}, "mathspeak": {"default": "modifying above r with caron", "brief": "mod above r with caron", "sbrief": "mod above r with caron"}}}, {"key": "015B", "mappings": {"default": {"default": "s acute"}, "mathspeak": {"default": "modifying above s with acute", "brief": "mod above s with acute", "sbrief": "mod above s with acute"}}}, {"key": "015D", "mappings": {"default": {"default": "s hat"}, "mathspeak": {"default": "modifying above s with caret", "brief": "mod above s with caret", "sbrief": "mod above s with caret"}}}, {"key": "015F", "mappings": {"default": {"default": "s cedilla"}, "mathspeak": {"default": "modifying above s with cedilla", "brief": "mod above s with cedilla", "sbrief": "mod above s with cedilla"}}}, {"key": "0161", "mappings": {"default": {"default": "s caron"}, "mathspeak": {"default": "modifying above s with caron", "brief": "mod above s with caron", "sbrief": "mod above s with caron"}}}, {"key": "0163", "mappings": {"default": {"default": "t cedilla"}, "mathspeak": {"default": "modifying above t with cedilla", "brief": "mod above t with cedilla", "sbrief": "mod above t with cedilla"}}}, {"key": "0165", "mappings": {"default": {"default": "t caron"}, "mathspeak": {"default": "modifying above t with caron", "brief": "mod above t with caron", "sbrief": "mod above t with caron"}}}, {"key": "0169", "mappings": {"default": {"default": "u tilde"}, "mathspeak": {"default": "u overtilde", "brief": "u overtilde", "sbrief": "u overtilde"}}}, {"key": "016B", "mappings": {"default": {"default": "u overbar"}, "mathspeak": {"default": "u overbar", "brief": "u overbar", "sbrief": "u overbar"}}}, {"key": "016D", "mappings": {"default": {"default": "u breve"}, "mathspeak": {"default": "modifying above u with breve", "brief": "mod above u with breve", "sbrief": "mod above u with breve"}}}, {"key": "016F", "mappings": {"default": {"default": "u ring"}, "mathspeak": {"default": "modifying above u with ring", "brief": "mod above u with ring", "sbrief": "mod above u with ring"}}}, {"key": "0171", "mappings": {"default": {"default": "u double acute"}, "mathspeak": {"default": "modifying above u with double acute", "brief": "mod above u with double acute", "sbrief": "mod above u with double acute"}}}, {"key": "0173", "mappings": {"default": {"default": "u ogonek"}, "mathspeak": {"default": "modifying above u with ogonek", "brief": "mod above u with ogonek", "sbrief": "mod above u with ogonek"}}}, {"key": "0175", "mappings": {"default": {"default": "w hat"}, "mathspeak": {"default": "modifying above w with caret", "brief": "mod above w with caret", "sbrief": "mod above w with caret"}}}, {"key": "0177", "mappings": {"default": {"default": "y hat"}, "mathspeak": {"default": "modifying above y with caret", "brief": "mod above y with caret", "sbrief": "mod above y with caret"}}}, {"key": "017A", "mappings": {"default": {"default": "z acute"}, "mathspeak": {"default": "modifying above z with acute", "brief": "mod above z with acute", "sbrief": "mod above z with acute"}}}, {"key": "017C", "mappings": {"default": {"default": "z overdot"}, "mathspeak": {"default": "modifying above z with dot", "brief": "mod above z with dot", "sbrief": "mod above z with dot"}}}, {"key": "017E", "mappings": {"default": {"default": "z caron"}, "mathspeak": {"default": "modifying above z with caron", "brief": "mod above z with caron", "sbrief": "mod above z with caron"}}}, {"key": "01CE", "mappings": {"default": {"default": "a caron"}, "mathspeak": {"default": "modifying above a with caron", "brief": "mod above a with caron", "sbrief": "mod above a with caron"}}}, {"key": "01D0", "mappings": {"default": {"default": "i caron"}, "mathspeak": {"default": "modifying above i with caron", "brief": "mod above i with caron", "sbrief": "mod above i with caron"}}}, {"key": "01D2", "mappings": {"default": {"default": "o caron"}, "mathspeak": {"default": "modifying above o with caron", "brief": "mod above o with caron", "sbrief": "mod above o with caron"}}}, {"key": "01D4", "mappings": {"default": {"default": "u caron"}, "mathspeak": {"default": "modifying above u with caron", "brief": "mod above u with caron", "sbrief": "mod above u with caron"}}}, {"key": "01E7", "mappings": {"default": {"default": "g caron"}, "mathspeak": {"default": "modifying above g with caron", "brief": "mod above g with caron", "sbrief": "mod above g with caron"}}}, {"key": "01E9", "mappings": {"default": {"default": "k caron"}, "mathspeak": {"default": "modifying above k with caron", "brief": "mod above k with caron", "sbrief": "mod above k with caron"}}}, {"key": "01EB", "mappings": {"default": {"default": "o ogonek"}, "mathspeak": {"default": "modifying above o with ogonek", "brief": "mod above o with ogonek", "sbrief": "mod above o with ogonek"}}}, {"key": "01F0", "mappings": {"default": {"default": "j caron"}, "mathspeak": {"default": "modifying above j with caron", "brief": "mod above j with caron", "sbrief": "mod above j with caron"}}}, {"key": "01F5", "mappings": {"default": {"default": "g acute"}, "mathspeak": {"default": "modifying above g with acute", "brief": "mod above g with acute", "sbrief": "mod above g with acute"}}}, {"key": "01F9", "mappings": {"default": {"default": "n grave"}, "mathspeak": {"default": "modifying above n with grave", "brief": "mod above n with grave", "sbrief": "mod above n with grave"}}}, {"key": "0201", "mappings": {"default": {"default": "a double grave"}, "mathspeak": {"default": "modifying above a with double grave", "brief": "mod above a with double grave", "sbrief": "mod above a with double grave"}}}, {"key": "0203", "mappings": {"default": {"default": "a inverted breve"}, "mathspeak": {"default": "modifying above a with inverted breve", "brief": "mod above a with inverted breve", "sbrief": "mod above a with inverted breve"}}}, {"key": "0205", "mappings": {"default": {"default": "e double grave"}, "mathspeak": {"default": "modifying above e with double grave", "brief": "mod above e with double grave", "sbrief": "mod above e with double grave"}}}, {"key": "0207", "mappings": {"default": {"default": "e inverted breve"}, "mathspeak": {"default": "modifying above e with inverted breve", "brief": "mod above e with inverted breve", "sbrief": "mod above e with inverted breve"}}}, {"key": "0209", "mappings": {"default": {"default": "i double grave"}, "mathspeak": {"default": "modifying above i with double grave", "brief": "mod above i with double grave", "sbrief": "mod above i with double grave"}}}, {"key": "020B", "mappings": {"default": {"default": "i inverted breve"}, "mathspeak": {"default": "modifying above i with inverted breve", "brief": "mod above i with inverted breve", "sbrief": "mod above i with inverted breve"}}}, {"key": "020D", "mappings": {"default": {"default": "o double grave"}, "mathspeak": {"default": "modifying above o with double grave", "brief": "mod above o with double grave", "sbrief": "mod above o with double grave"}}}, {"key": "020F", "mappings": {"default": {"default": "o inverted breve"}, "mathspeak": {"default": "modifying above o with inverted breve", "brief": "mod above o with inverted breve", "sbrief": "mod above o with inverted breve"}}}, {"key": "0211", "mappings": {"default": {"default": "r double grave"}, "mathspeak": {"default": "modifying above r with double grave", "brief": "mod above r with double grave", "sbrief": "mod above r with double grave"}}}, {"key": "0213", "mappings": {"default": {"default": "r inverted breve"}, "mathspeak": {"default": "modifying above r with inverted breve", "brief": "mod above r with inverted breve", "sbrief": "mod above r with inverted breve"}}}, {"key": "0215", "mappings": {"default": {"default": "u double grave"}, "mathspeak": {"default": "modifying above u with double grave", "brief": "mod above u with double grave", "sbrief": "mod above u with double grave"}}}, {"key": "0217", "mappings": {"default": {"default": "u inverted breve"}, "mathspeak": {"default": "modifying above u with inverted breve", "brief": "mod above u with inverted breve", "sbrief": "mod above u with inverted breve"}}}, {"key": "0219", "mappings": {"default": {"default": "s comma below"}, "mathspeak": {"default": "modifying below s with comma below", "brief": "mod below s with comma below", "sbrief": "mod below s with comma below"}}}, {"key": "021B", "mappings": {"default": {"default": "t comma below"}, "mathspeak": {"default": "modifying below t with comma below", "brief": "mod below t with comma below", "sbrief": "mod below t with comma below"}}}, {"key": "021F", "mappings": {"default": {"default": "h caron"}, "mathspeak": {"default": "modifying above h with caron", "brief": "mod above h with caron", "sbrief": "mod above h with caron"}}}, {"key": "0227", "mappings": {"default": {"default": "a overdot"}, "mathspeak": {"default": "modifying above a with dot", "brief": "mod above a with dot", "sbrief": "mod above a with dot"}}}, {"key": "0229", "mappings": {"default": {"default": "e cedilla"}, "mathspeak": {"default": "modifying above e with cedilla", "brief": "mod above e with cedilla", "sbrief": "mod above e with cedilla"}}}, {"key": "022F", "mappings": {"default": {"default": "o overdot"}, "mathspeak": {"default": "modifying above o with dot", "brief": "mod above o with dot", "sbrief": "mod above o with dot"}}}, {"key": "0233", "mappings": {"default": {"default": "y overbar"}, "mathspeak": {"default": "y overbar", "brief": "y overbar", "sbrief": "y overbar"}}}, {"key": "0237", "mappings": {"default": {"default": "dotless j"}, "mathspeak": {"default": "modifying above dotless j", "brief": "mod above dotless j", "sbrief": "mod above dotless j"}}}, {"key": "1E01", "mappings": {"default": {"default": "a ring below"}, "mathspeak": {"default": "modifying below a with ring below", "brief": "mod below a with ring below", "sbrief": "mod below a with ring below"}}}, {"key": "1E03", "mappings": {"default": {"default": "b overdot"}, "mathspeak": {"default": "modifying above b with dot", "brief": "mod above b with dot", "sbrief": "mod above b with dot"}}}, {"key": "1E05", "mappings": {"default": {"default": "b underdot"}, "mathspeak": {"default": "modifying below b with dot", "brief": "mod below b with dot", "sbrief": "mod below b with dot"}}}, {"key": "1E07", "mappings": {"default": {"default": "b underbar"}, "mathspeak": {"default": "b underbar", "brief": "b underbar", "sbrief": "b underbar"}}}, {"key": "1E0B", "mappings": {"default": {"default": "d overdot"}, "mathspeak": {"default": "modifying above d with dot", "brief": "mod above d with dot", "sbrief": "mod above d with dot"}}}, {"key": "1E0D", "mappings": {"default": {"default": "d underdot"}, "mathspeak": {"default": "modifying below d with dot", "brief": "mod below d with dot", "sbrief": "mod below d with dot"}}}, {"key": "1E0F", "mappings": {"default": {"default": "d underbar"}, "mathspeak": {"default": "d underbar", "brief": "d underbar", "sbrief": "d underbar"}}}, {"key": "1E11", "mappings": {"default": {"default": "d cedilla"}, "mathspeak": {"default": "modifying above d with cedilla", "brief": "mod above d with cedilla", "sbrief": "mod above d with cedilla"}}}, {"key": "1E13", "mappings": {"default": {"default": "d underhat"}, "mathspeak": {"default": "modifying below d with caret", "brief": "mod below d with caret", "sbrief": "mod below d with caret"}}}, {"key": "1E19", "mappings": {"default": {"default": "e underhat"}, "mathspeak": {"default": "modifying below e with caret", "brief": "mod below e with caret", "sbrief": "mod below e with caret"}}}, {"key": "1E1B", "mappings": {"default": {"default": "e tilde below"}, "mathspeak": {"default": "e undertilde", "brief": "e undertilde", "sbrief": "e undertilde"}}}, {"key": "1E1F", "mappings": {"default": {"default": "f overdot"}, "mathspeak": {"default": "modifying above f with dot", "brief": "mod above f with dot", "sbrief": "mod above f with dot"}}}, {"key": "1E21", "mappings": {"default": {"default": "g overbar"}, "mathspeak": {"default": "g overbar", "brief": "g overbar", "sbrief": "g overbar"}}}, {"key": "1E23", "mappings": {"default": {"default": "h overdot"}, "mathspeak": {"default": "modifying above h with dot", "brief": "mod above h with dot", "sbrief": "mod above h with dot"}}}, {"key": "1E25", "mappings": {"default": {"default": "h underdot"}, "mathspeak": {"default": "modifying below h with dot", "brief": "mod below h with dot", "sbrief": "mod below h with dot"}}}, {"key": "1E27", "mappings": {"default": {"default": "h double overdot"}, "mathspeak": {"default": "modifying above h with double dot", "brief": "mod above h with double dot", "sbrief": "mod above h with double dot"}}}, {"key": "1E29", "mappings": {"default": {"default": "h cedilla"}, "mathspeak": {"default": "modifying above h with cedilla", "brief": "mod above h with cedilla", "sbrief": "mod above h with cedilla"}}}, {"key": "1E2B", "mappings": {"default": {"default": "h breve below"}, "mathspeak": {"default": "modifying below h with breve below", "brief": "mod below h with breve below", "sbrief": "mod below h with breve below"}}}, {"key": "1E2D", "mappings": {"default": {"default": "i tilde below"}, "mathspeak": {"default": "i undertilde", "brief": "i undertilde", "sbrief": "i undertilde"}}}, {"key": "1E31", "mappings": {"default": {"default": "k acute"}, "mathspeak": {"default": "modifying above k with acute", "brief": "mod above k with acute", "sbrief": "mod above k with acute"}}}, {"key": "1E33", "mappings": {"default": {"default": "k underdot"}, "mathspeak": {"default": "modifying below k with dot", "brief": "mod below k with dot", "sbrief": "mod below k with dot"}}}, {"key": "1E35", "mappings": {"default": {"default": "k underbar"}, "mathspeak": {"default": "k underbar", "brief": "k underbar", "sbrief": "k underbar"}}}, {"key": "1E37", "mappings": {"default": {"default": "l underdot"}, "mathspeak": {"default": "modifying below l with dot", "brief": "mod below l with dot", "sbrief": "mod below l with dot"}}}, {"key": "1E3B", "mappings": {"default": {"default": "l underbar"}, "mathspeak": {"default": "l underbar", "brief": "l underbar", "sbrief": "l underbar"}}}, {"key": "1E3D", "mappings": {"default": {"default": "l underhat"}, "mathspeak": {"default": "modifying below l with caret", "brief": "mod below l with caret", "sbrief": "mod below l with caret"}}}, {"key": "1E3F", "mappings": {"default": {"default": "m acute"}, "mathspeak": {"default": "modifying above m with acute", "brief": "mod above m with acute", "sbrief": "mod above m with acute"}}}, {"key": "1E41", "mappings": {"default": {"default": "m overdot"}, "mathspeak": {"default": "modifying above m with dot", "brief": "mod above m with dot", "sbrief": "mod above m with dot"}}}, {"key": "1E43", "mappings": {"default": {"default": "m underdot"}, "mathspeak": {"default": "modifying below m with dot", "brief": "mod below m with dot", "sbrief": "mod below m with dot"}}}, {"key": "1E45", "mappings": {"default": {"default": "n overdot"}, "mathspeak": {"default": "modifying above n with dot", "brief": "mod above n with dot", "sbrief": "mod above n with dot"}}}, {"key": "1E47", "mappings": {"default": {"default": "n underdot"}, "mathspeak": {"default": "modifying below n with dot", "brief": "mod below n with dot", "sbrief": "mod below n with dot"}}}, {"key": "1E49", "mappings": {"default": {"default": "n underbar"}, "mathspeak": {"default": "n underbar", "brief": "n underbar", "sbrief": "n underbar"}}}, {"key": "1E4B", "mappings": {"default": {"default": "n underhat"}, "mathspeak": {"default": "modifying below n with caret", "brief": "mod below n with caret", "sbrief": "mod below n with caret"}}}, {"key": "1E55", "mappings": {"default": {"default": "p acute"}, "mathspeak": {"default": "modifying above p with acute", "brief": "mod above p with acute", "sbrief": "mod above p with acute"}}}, {"key": "1E57", "mappings": {"default": {"default": "p overdot"}, "mathspeak": {"default": "modifying above p with dot", "brief": "mod above p with dot", "sbrief": "mod above p with dot"}}}, {"key": "1E59", "mappings": {"default": {"default": "r overdot"}, "mathspeak": {"default": "modifying above r with dot", "brief": "mod above r with dot", "sbrief": "mod above r with dot"}}}, {"key": "1E5B", "mappings": {"default": {"default": "r underdot"}, "mathspeak": {"default": "modifying below r with dot", "brief": "mod below r with dot", "sbrief": "mod below r with dot"}}}, {"key": "1E5F", "mappings": {"default": {"default": "r underbar"}, "mathspeak": {"default": "r underbar", "brief": "r underbar", "sbrief": "r underbar"}}}, {"key": "1E61", "mappings": {"default": {"default": "s overdot"}, "mathspeak": {"default": "modifying above s with dot", "brief": "mod above s with dot", "sbrief": "mod above s with dot"}}}, {"key": "1E63", "mappings": {"default": {"default": "s underdot"}, "mathspeak": {"default": "modifying below s with dot", "brief": "mod below s with dot", "sbrief": "mod below s with dot"}}}, {"key": "1E6B", "mappings": {"default": {"default": "t overdot"}, "mathspeak": {"default": "modifying above t with dot", "brief": "mod above t with dot", "sbrief": "mod above t with dot"}}}, {"key": "1E6D", "mappings": {"default": {"default": "t underdot"}, "mathspeak": {"default": "modifying below t with dot", "brief": "mod below t with dot", "sbrief": "mod below t with dot"}}}, {"key": "1E6F", "mappings": {"default": {"default": "t underbar"}, "mathspeak": {"default": "t underbar", "brief": "t underbar", "sbrief": "t underbar"}}}, {"key": "1E71", "mappings": {"default": {"default": "t underhat"}, "mathspeak": {"default": "modifying below t with caret", "brief": "mod below t with caret", "sbrief": "mod below t with caret"}}}, {"key": "1E73", "mappings": {"default": {"default": "u double underdot"}, "mathspeak": {"default": "modifying below u with double dot", "brief": "mod below u with double dot", "sbrief": "mod below u with double dot"}}}, {"key": "1E75", "mappings": {"default": {"default": "u tilde below"}, "mathspeak": {"default": "u undertilde", "brief": "u undertilde", "sbrief": "u undertilde"}}}, {"key": "1E77", "mappings": {"default": {"default": "u underhat"}, "mathspeak": {"default": "modifying below u with caret", "brief": "mod below u with caret", "sbrief": "mod below u with caret"}}}, {"key": "1E7D", "mappings": {"default": {"default": "v tilde"}, "mathspeak": {"default": "v overtilde", "brief": "v overtilde", "sbrief": "v overtilde"}}}, {"key": "1E7F", "mappings": {"default": {"default": "v underdot"}, "mathspeak": {"default": "modifying below v with dot", "brief": "mod below v with dot", "sbrief": "mod below v with dot"}}}, {"key": "1E81", "mappings": {"default": {"default": "w grave"}, "mathspeak": {"default": "modifying above w with grave", "brief": "mod above w with grave", "sbrief": "mod above w with grave"}}}, {"key": "1E83", "mappings": {"default": {"default": "w acute"}, "mathspeak": {"default": "modifying above w with acute", "brief": "mod above w with acute", "sbrief": "mod above w with acute"}}}, {"key": "1E85", "mappings": {"default": {"default": "w double overdot"}, "mathspeak": {"default": "modifying above w with double dot", "brief": "mod above w with double dot", "sbrief": "mod above w with double dot"}}}, {"key": "1E87", "mappings": {"default": {"default": "w overdot"}, "mathspeak": {"default": "modifying above w with dot", "brief": "mod above w with dot", "sbrief": "mod above w with dot"}}}, {"key": "1E89", "mappings": {"default": {"default": "w underdot"}, "mathspeak": {"default": "modifying below w with dot", "brief": "mod below w with dot", "sbrief": "mod below w with dot"}}}, {"key": "1E8B", "mappings": {"default": {"default": "x overdot"}, "mathspeak": {"default": "modifying above x with dot", "brief": "mod above x with dot", "sbrief": "mod above x with dot"}}}, {"key": "1E8D", "mappings": {"default": {"default": "x double overdot"}, "mathspeak": {"default": "modifying above x with double dot", "brief": "mod above x with double dot", "sbrief": "mod above x with double dot"}}}, {"key": "1E8F", "mappings": {"default": {"default": "y overdot"}, "mathspeak": {"default": "modifying above y with dot", "brief": "mod above y with dot", "sbrief": "mod above y with dot"}}}, {"key": "1E91", "mappings": {"default": {"default": "z hat"}, "mathspeak": {"default": "modifying above z with caret", "brief": "mod above z with caret", "sbrief": "mod above z with caret"}}}, {"key": "1E93", "mappings": {"default": {"default": "z underdot"}, "mathspeak": {"default": "modifying below z with dot", "brief": "mod below z with dot", "sbrief": "mod below z with dot"}}}, {"key": "1E95", "mappings": {"default": {"default": "z underbar"}, "mathspeak": {"default": "z underbar", "brief": "z underbar", "sbrief": "z underbar"}}}, {"key": "1E96", "mappings": {"default": {"default": "h underbar"}, "mathspeak": {"default": "h underbar", "brief": "h underbar", "sbrief": "h underbar"}}}, {"key": "1E97", "mappings": {"default": {"default": "t double overdot"}, "mathspeak": {"default": "modifying above t with double dot", "brief": "mod above t with double dot", "sbrief": "mod above t with double dot"}}}, {"key": "1E98", "mappings": {"default": {"default": "w ring"}, "mathspeak": {"default": "modifying above w with ring", "brief": "mod above w with ring", "sbrief": "mod above w with ring"}}}, {"key": "1E99", "mappings": {"default": {"default": "y ring"}, "mathspeak": {"default": "modifying above y with ring", "brief": "mod above y with ring", "sbrief": "mod above y with ring"}}}, {"key": "1E9A", "mappings": {"default": {"default": "a right half ring"}, "mathspeak": {"default": "modifying above a with right half ring", "brief": "mod above a with right half ring", "sbrief": "mod above a with right half ring"}}}, {"key": "1EA1", "mappings": {"default": {"default": "a underdot"}, "mathspeak": {"default": "modifying below a with dot", "brief": "mod below a with dot", "sbrief": "mod below a with dot"}}}, {"key": "1EA3", "mappings": {"default": {"default": "a hook"}, "mathspeak": {"default": "modifying above a with hook", "brief": "mod above a with hook", "sbrief": "mod above a with hook"}}}, {"key": "1EB9", "mappings": {"default": {"default": "e underdot"}, "mathspeak": {"default": "modifying below e with dot", "brief": "mod below e with dot", "sbrief": "mod below e with dot"}}}, {"key": "1EBB", "mappings": {"default": {"default": "e hook"}, "mathspeak": {"default": "modifying above e with hook", "brief": "mod above e with hook", "sbrief": "mod above e with hook"}}}, {"key": "1EBD", "mappings": {"default": {"default": "e tilde"}, "mathspeak": {"default": "e overtilde", "brief": "e overtilde", "sbrief": "e overtilde"}}}, {"key": "1EC9", "mappings": {"default": {"default": "i hook"}, "mathspeak": {"default": "modifying above i with hook", "brief": "mod above i with hook", "sbrief": "mod above i with hook"}}}, {"key": "1ECB", "mappings": {"default": {"default": "i underdot"}, "mathspeak": {"default": "modifying below i with dot", "brief": "mod below i with dot", "sbrief": "mod below i with dot"}}}, {"key": "1ECD", "mappings": {"default": {"default": "o underdot"}, "mathspeak": {"default": "modifying below o with dot", "brief": "mod below o with dot", "sbrief": "mod below o with dot"}}}, {"key": "1ECF", "mappings": {"default": {"default": "o hook"}, "mathspeak": {"default": "modifying above o with hook", "brief": "mod above o with hook", "sbrief": "mod above o with hook"}}}, {"key": "1EE5", "mappings": {"default": {"default": "u underdot"}, "mathspeak": {"default": "modifying below u with dot", "brief": "mod below u with dot", "sbrief": "mod below u with dot"}}}, {"key": "1EE7", "mappings": {"default": {"default": "u hook"}, "mathspeak": {"default": "modifying above u with hook", "brief": "mod above u with hook", "sbrief": "mod above u with hook"}}}, {"key": "1EF3", "mappings": {"default": {"default": "y grave"}, "mathspeak": {"default": "modifying above y with grave", "brief": "mod above y with grave", "sbrief": "mod above y with grave"}}}, {"key": "1EF5", "mappings": {"default": {"default": "y underdot"}, "mathspeak": {"default": "modifying below y with dot", "brief": "mod below y with dot", "sbrief": "mod below y with dot"}}}, {"key": "1EF7", "mappings": {"default": {"default": "y hook"}, "mathspeak": {"default": "modifying above y with hook", "brief": "mod above y with hook", "sbrief": "mod above y with hook"}}}, {"key": "1EF9", "mappings": {"default": {"default": "y tilde"}, "mathspeak": {"default": "y overtilde", "brief": "y overtilde", "sbrief": "y overtilde"}}}], "en/symbols/latin-rest.min": [{"locale": "en"}, {"key": "210E", "mappings": {"default": {"default": "italic h", "physics": "planck constant"}}}, {"key": "0363", "mappings": {"default": {"default": "combining a"}}}, {"key": "0364", "mappings": {"default": {"default": "combining e"}}}, {"key": "0365", "mappings": {"default": {"default": "combining i"}}}, {"key": "0366", "mappings": {"default": {"default": "combining o"}}}, {"key": "0367", "mappings": {"default": {"default": "combining u"}}}, {"key": "0368", "mappings": {"default": {"default": "combining c"}}}, {"key": "0369", "mappings": {"default": {"default": "combining d"}}}, {"key": "036A", "mappings": {"default": {"default": "combining h"}}}, {"key": "036B", "mappings": {"default": {"default": "combining m"}}}, {"key": "036C", "mappings": {"default": {"default": "combining r"}}}, {"key": "036D", "mappings": {"default": {"default": "combining t"}}}, {"key": "036E", "mappings": {"default": {"default": "combining v"}}}, {"key": "036F", "mappings": {"default": {"default": "combining x"}}}, {"key": "1D62", "mappings": {"default": {"default": "subscript i"}}}, {"key": "1D63", "mappings": {"default": {"default": "subscript r"}}}, {"key": "1D64", "mappings": {"default": {"default": "subscript u"}}}, {"key": "1D65", "mappings": {"default": {"default": "subscript v"}}}, {"key": "1DCA", "mappings": {"default": {"default": "combining r below"}}}, {"key": "1DD3", "mappings": {"default": {"default": "combining flattened open a above"}}}, {"key": "1DD4", "mappings": {"default": {"default": "combining ae"}}}, {"key": "1DD5", "mappings": {"default": {"default": "combining ao"}}}, {"key": "1DD6", "mappings": {"default": {"default": "combining av"}}}, {"key": "1DD7", "mappings": {"default": {"default": "combining c cedilla"}}}, {"key": "1DD8", "mappings": {"default": {"default": "combining insular d"}}}, {"key": "1DD9", "mappings": {"default": {"default": "combining eth"}}}, {"key": "1DDA", "mappings": {"default": {"default": "combining g"}}}, {"key": "1DDB", "mappings": {"default": {"default": "combining small cap G"}, "mathspeak": {"default": "combining small upper G"}}}, {"key": "1DDC", "mappings": {"default": {"default": "combining k"}}}, {"key": "1DDD", "mappings": {"default": {"default": "combining l"}}}, {"key": "1DDE", "mappings": {"default": {"default": "combining small cap L"}, "mathspeak": {"default": "combining small upper L"}}}, {"key": "1DDF", "mappings": {"default": {"default": "combining small cap M"}, "mathspeak": {"default": "combining small upper M"}}}, {"key": "1DE0", "mappings": {"default": {"default": "combining n"}}}, {"key": "1DE1", "mappings": {"default": {"default": "combining small cap N"}, "mathspeak": {"default": "combining small upper N"}}}, {"key": "1DE2", "mappings": {"default": {"default": "combining small cap R"}, "mathspeak": {"default": "combining small upper R"}}}, {"key": "1DE3", "mappings": {"default": {"default": "combining r rotunda"}}}, {"key": "1DE4", "mappings": {"default": {"default": "combining s"}}}, {"key": "1DE5", "mappings": {"default": {"default": "combining long s"}}}, {"key": "1DE6", "mappings": {"default": {"default": "combining z"}}}, {"key": "2071", "mappings": {"default": {"default": "superscript i"}}}, {"key": "207F", "mappings": {"default": {"default": "superscript n"}}}, {"key": "2090", "mappings": {"default": {"default": "subscript a"}}}, {"key": "2091", "mappings": {"default": {"default": "subscript e"}}}, {"key": "2092", "mappings": {"default": {"default": "subscript o"}}}, {"key": "2093", "mappings": {"default": {"default": "subscript x"}}}, {"key": "2094", "mappings": {"default": {"default": "subscript schwa"}}}, {"key": "2095", "mappings": {"default": {"default": "subscript h"}}}, {"key": "2096", "mappings": {"default": {"default": "subscript k"}}}, {"key": "2097", "mappings": {"default": {"default": "subscript l"}}}, {"key": "2098", "mappings": {"default": {"default": "subscript m"}}}, {"key": "2099", "mappings": {"default": {"default": "subscript n"}}}, {"key": "209A", "mappings": {"default": {"default": "subscript p"}}}, {"key": "209B", "mappings": {"default": {"default": "subscript s"}}}, {"key": "209C", "mappings": {"default": {"default": "subscript t"}}}, {"key": "2C7C", "mappings": {"default": {"default": "subscript j"}}}, {"key": "1F12A", "mappings": {"default": {"default": "tortoise shell bracketed cap S"}, "mathspeak": {"default": "tortoise shell bracketed upper S"}}}, {"key": "1F12B", "mappings": {"default": {"default": "circled italic cap C"}, "mathspeak": {"default": "circled italic upper C"}}}, {"key": "1F12C", "mappings": {"default": {"default": "circled italic cap R"}, "mathspeak": {"default": "circled italic upper R"}}}, {"key": "1F18A", "mappings": {"default": {"default": "crossed negative squared cap P"}, "mathspeak": {"default": "crossed negative squared upper P"}}}], "en/symbols/latin-upper-double-accent.min": [{"locale": "en"}, {"key": "01D5", "mappings": {"default": {"default": "cap U double overdot overbar"}, "mathspeak": {"default": "upper U double overdot overbar"}}}, {"key": "01D7", "mappings": {"default": {"default": "cap U double overdot acute"}, "mathspeak": {"default": "upper U double overdot acute"}}}, {"key": "01D9", "mappings": {"default": {"default": "cap U double overdot caron"}, "mathspeak": {"default": "upper U double overdot caron"}}}, {"key": "01DB", "mappings": {"default": {"default": "cap U double overdot grave"}, "mathspeak": {"default": "upper U double overdot grave"}}}, {"key": "01DE", "mappings": {"default": {"default": "cap A double overdot overbar"}, "mathspeak": {"default": "upper A double overdot overbar"}}}, {"key": "01E0", "mappings": {"default": {"default": "cap A overdot overbar"}, "mathspeak": {"default": "upper A overdot overbar"}}}, {"key": "01EC", "mappings": {"default": {"default": "cap O ogonek overbar"}, "mathspeak": {"default": "upper O ogonek overbar"}}}, {"key": "01FA", "mappings": {"default": {"default": "cap A ring acute"}, "mathspeak": {"default": "upper A ring acute"}}}, {"key": "022A", "mappings": {"default": {"default": "cap O double overdot overbar"}, "mathspeak": {"default": "upper O double overdot overbar"}}}, {"key": "022C", "mappings": {"default": {"default": "cap O tilde overbar"}, "mathspeak": {"default": "upper O tilde overbar"}}}, {"key": "0230", "mappings": {"default": {"default": "cap O overdot overbar"}, "mathspeak": {"default": "upper O overdot overbar"}}}, {"key": "1E08", "mappings": {"default": {"default": "cap C cedilla acute"}, "mathspeak": {"default": "upper C cedilla acute"}}}, {"key": "1E14", "mappings": {"default": {"default": "cap E overbar grave"}, "mathspeak": {"default": "upper E overbar grave"}}}, {"key": "1E16", "mappings": {"default": {"default": "cap E overbar acute"}, "mathspeak": {"default": "upper E overbar acute"}}}, {"key": "1E1C", "mappings": {"default": {"default": "cap E cedilla breve"}, "mathspeak": {"default": "upper E cedilla breve"}}}, {"key": "1E2E", "mappings": {"default": {"default": "cap I double overdot acute"}, "mathspeak": {"default": "upper I double overdot acute"}}}, {"key": "1E38", "mappings": {"default": {"default": "cap L underdot overbar"}, "mathspeak": {"default": "upper L underdot overbar"}}}, {"key": "1E4C", "mappings": {"default": {"default": "cap O tilde acute"}, "mathspeak": {"default": "upper O tilde acute"}}}, {"key": "1E4E", "mappings": {"default": {"default": "cap O tilde double overdot"}, "mathspeak": {"default": "upper O tilde double overdot"}}}, {"key": "1E50", "mappings": {"default": {"default": "cap O overbar grave"}, "mathspeak": {"default": "upper O overbar grave"}}}, {"key": "1E52", "mappings": {"default": {"default": "cap O overbar acute"}, "mathspeak": {"default": "upper O overbar acute"}}}, {"key": "1E5C", "mappings": {"default": {"default": "cap R overbar underdot"}, "mathspeak": {"default": "upper R overbar underdot"}}}, {"key": "1E64", "mappings": {"default": {"default": "cap S acute overdot"}, "mathspeak": {"default": "upper S acute overdot"}}}, {"key": "1E66", "mappings": {"default": {"default": "cap S caron overdot"}, "mathspeak": {"default": "upper S caron overdot"}}}, {"key": "1E68", "mappings": {"default": {"default": "cap S underdot overdot"}, "mathspeak": {"default": "upper S underdot overdot"}}}, {"key": "1E78", "mappings": {"default": {"default": "cap U tilde acute"}, "mathspeak": {"default": "upper U tilde acute"}}}, {"key": "1E7A", "mappings": {"default": {"default": "cap U overbar double overdot"}, "mathspeak": {"default": "upper U overbar double overdot"}}}, {"key": "1EA4", "mappings": {"default": {"default": "cap A hat acute"}, "mathspeak": {"default": "upper A hat acute"}}}, {"key": "1EA6", "mappings": {"default": {"default": "cap A hat grave"}, "mathspeak": {"default": "upper A hat grave"}}}, {"key": "1EA8", "mappings": {"default": {"default": "cap A hat hook"}, "mathspeak": {"default": "upper A hat hook"}}}, {"key": "1EAA", "mappings": {"default": {"default": "cap A hat tilde"}, "mathspeak": {"default": "upper A hat tilde"}}}, {"key": "1EAC", "mappings": {"default": {"default": "cap A hat underdot"}, "mathspeak": {"default": "upper A hat underdot"}}}, {"key": "1EAE", "mappings": {"default": {"default": "cap A breve acute"}, "mathspeak": {"default": "upper A breve acute"}}}, {"key": "1EB0", "mappings": {"default": {"default": "cap A breve grave"}, "mathspeak": {"default": "upper A breve grave"}}}, {"key": "1EB2", "mappings": {"default": {"default": "cap A breve hook"}, "mathspeak": {"default": "upper A breve hook"}}}, {"key": "1EB4", "mappings": {"default": {"default": "cap A breve tilde"}, "mathspeak": {"default": "upper A breve tilde"}}}, {"key": "1EB6", "mappings": {"default": {"default": "cap A breve underdot"}, "mathspeak": {"default": "upper A breve underdot"}}}, {"key": "1EBE", "mappings": {"default": {"default": "cap E hat acute"}, "mathspeak": {"default": "upper E hat acute"}}}, {"key": "1EC0", "mappings": {"default": {"default": "cap E hat grave"}, "mathspeak": {"default": "upper E hat grave"}}}, {"key": "1EC2", "mappings": {"default": {"default": "cap E hat hook"}, "mathspeak": {"default": "upper E hat hook"}}}, {"key": "1EC4", "mappings": {"default": {"default": "cap E hat tilde"}, "mathspeak": {"default": "upper E hat tilde"}}}, {"key": "1EC6", "mappings": {"default": {"default": "cap E hat underdot"}, "mathspeak": {"default": "upper E hat underdot"}}}, {"key": "1ED0", "mappings": {"default": {"default": "cap O hat acute"}, "mathspeak": {"default": "upper O hat acute"}}}, {"key": "1ED2", "mappings": {"default": {"default": "cap O hat grave"}, "mathspeak": {"default": "upper O hat grave"}}}, {"key": "1ED4", "mappings": {"default": {"default": "cap O hat hook"}, "mathspeak": {"default": "upper O hat hook"}}}, {"key": "1ED6", "mappings": {"default": {"default": "cap O hat tilde"}, "mathspeak": {"default": "upper O hat tilde"}}}, {"key": "1ED8", "mappings": {"default": {"default": "cap O hat underdot"}, "mathspeak": {"default": "upper O hat underdot"}}}, {"key": "1EDA", "mappings": {"default": {"default": "cap O acute prime"}, "mathspeak": {"default": "upper O acute prime"}}}, {"key": "1EDC", "mappings": {"default": {"default": "cap O grave prime"}, "mathspeak": {"default": "upper O grave prime"}}}, {"key": "1EDE", "mappings": {"default": {"default": "cap O hook prime"}, "mathspeak": {"default": "upper O hook prime"}}}, {"key": "1EE0", "mappings": {"default": {"default": "cap O tilde prime"}, "mathspeak": {"default": "upper O tilde prime"}}}, {"key": "1EE2", "mappings": {"default": {"default": "cap O underdot prime"}, "mathspeak": {"default": "upper O underdot prime"}}}, {"key": "1EE8", "mappings": {"default": {"default": "cap U acute prime"}, "mathspeak": {"default": "upper U acute prime"}}}, {"key": "1EEA", "mappings": {"default": {"default": "cap U grave prime"}, "mathspeak": {"default": "upper U grave prime"}}}, {"key": "1EEC", "mappings": {"default": {"default": "cap U hook prime"}, "mathspeak": {"default": "upper U hook prime"}}}, {"key": "1EEE", "mappings": {"default": {"default": "cap U tilde prime"}, "mathspeak": {"default": "upper U tilde prime"}}}, {"key": "1EF0", "mappings": {"default": {"default": "cap U underdot prime"}, "mathspeak": {"default": "upper U underdot prime"}}}], "en/symbols/latin-upper-single-accent.min": [{"locale": "en"}, {"key": "00C0", "mappings": {"default": {"default": "cap A grave"}, "mathspeak": {"default": "modifying above upper A with grave", "brief": "mod above upper A with grave", "sbrief": "mod above upper A with grave"}}}, {"key": "00C1", "mappings": {"default": {"default": "cap A acute"}, "mathspeak": {"default": "modifying above upper A with acute", "brief": "mod above upper A with acute", "sbrief": "mod above upper A with acute"}}}, {"key": "00C2", "mappings": {"default": {"default": "cap A hat"}, "mathspeak": {"default": "modifying above upper A with caret", "brief": "mod above upper A with caret", "sbrief": "mod above upper A with caret"}}}, {"key": "00C3", "mappings": {"default": {"default": "cap A tilde"}, "mathspeak": {"default": "upper A overtilde", "brief": "upper A overtilde", "sbrief": "upper A overtilde"}}}, {"key": "00C4", "mappings": {"default": {"default": "cap A double overdot"}, "mathspeak": {"default": "modifying above upper A with double dot", "brief": "mod above upper A with double dot", "sbrief": "mod above upper A with double dot"}}}, {"key": "00C5", "mappings": {"default": {"default": "cap A ring"}, "mathspeak": {"default": "modifying above upper A with ring", "brief": "mod above upper A with ring", "sbrief": "mod above upper A with ring"}}}, {"key": "00C7", "mappings": {"default": {"default": "cap C cedilla"}, "mathspeak": {"default": "modifying above upper C with cedilla", "brief": "mod above upper C with cedilla", "sbrief": "mod above upper C with cedilla"}}}, {"key": "00C8", "mappings": {"default": {"default": "cap E grave"}, "mathspeak": {"default": "modifying above upper E with grave", "brief": "mod above upper E with grave", "sbrief": "mod above upper E with grave"}}}, {"key": "00C9", "mappings": {"default": {"default": "cap E acute"}, "mathspeak": {"default": "modifying above upper E with acute", "brief": "mod above upper E with acute", "sbrief": "mod above upper E with acute"}}}, {"key": "00CA", "mappings": {"default": {"default": "cap E hat"}, "mathspeak": {"default": "modifying above upper E with caret", "brief": "mod above upper E with caret", "sbrief": "mod above upper E with caret"}}}, {"key": "00CB", "mappings": {"default": {"default": "cap E double overdot"}, "mathspeak": {"default": "modifying above upper E with double dot", "brief": "mod above upper E with double dot", "sbrief": "mod above upper E with double dot"}}}, {"key": "00CC", "mappings": {"default": {"default": "cap I grave"}, "mathspeak": {"default": "modifying above upper I with grave", "brief": "mod above upper I with grave", "sbrief": "mod above upper I with grave"}}}, {"key": "00CD", "mappings": {"default": {"default": "cap I acute"}, "mathspeak": {"default": "modifying above upper I with acute", "brief": "mod above upper I with acute", "sbrief": "mod above upper I with acute"}}}, {"key": "00CE", "mappings": {"default": {"default": "cap I hat"}, "mathspeak": {"default": "modifying above upper I with caret", "brief": "mod above upper I with caret", "sbrief": "mod above upper I with caret"}}}, {"key": "00CF", "mappings": {"default": {"default": "cap I double overdot"}, "mathspeak": {"default": "modifying above upper I with double dot", "brief": "mod above upper I with double dot", "sbrief": "mod above upper I with double dot"}}}, {"key": "00D1", "mappings": {"default": {"default": "cap N tilde"}, "mathspeak": {"default": "upper N overtilde", "brief": "upper N overtilde", "sbrief": "upper N overtilde"}}}, {"key": "00D2", "mappings": {"default": {"default": "cap O grave"}, "mathspeak": {"default": "modifying above upper O with grave", "brief": "mod above upper O with grave", "sbrief": "mod above upper O with grave"}}}, {"key": "00D3", "mappings": {"default": {"default": "cap O acute"}, "mathspeak": {"default": "modifying above upper O with acute", "brief": "mod above upper O with acute", "sbrief": "mod above upper O with acute"}}}, {"key": "00D4", "mappings": {"default": {"default": "cap O hat"}, "mathspeak": {"default": "modifying above upper O with caret", "brief": "mod above upper O with caret", "sbrief": "mod above upper O with caret"}}}, {"key": "00D5", "mappings": {"default": {"default": "cap O tilde"}, "mathspeak": {"default": "upper O overtilde", "brief": "upper O overtilde", "sbrief": "upper O overtilde"}}}, {"key": "00D6", "mappings": {"default": {"default": "cap O double overdot"}, "mathspeak": {"default": "modifying above upper O with double dot", "brief": "mod above upper O with double dot", "sbrief": "mod above upper O with double dot"}}}, {"key": "00D9", "mappings": {"default": {"default": "cap U grave"}, "mathspeak": {"default": "modifying above upper U with grave", "brief": "mod above upper U with grave", "sbrief": "mod above upper U with grave"}}}, {"key": "00DA", "mappings": {"default": {"default": "cap U acute"}, "mathspeak": {"default": "modifying above upper U with acute", "brief": "mod above upper U with acute", "sbrief": "mod above upper U with acute"}}}, {"key": "00DB", "mappings": {"default": {"default": "cap U hat"}, "mathspeak": {"default": "modifying above upper U with caret", "brief": "mod above upper U with caret", "sbrief": "mod above upper U with caret"}}}, {"key": "00DC", "mappings": {"default": {"default": "cap U double overdot"}, "mathspeak": {"default": "modifying above upper U with double dot", "brief": "mod above upper U with double dot", "sbrief": "mod above upper U with double dot"}}}, {"key": "00DD", "mappings": {"default": {"default": "cap Y acute"}, "mathspeak": {"default": "modifying above upper Y with acute", "brief": "mod above upper Y with acute", "sbrief": "mod above upper Y with acute"}}}, {"key": "0100", "mappings": {"default": {"default": "cap A overbar"}, "mathspeak": {"default": "upper A overbar", "brief": "upper A overbar", "sbrief": "upper A overbar"}}}, {"key": "0102", "mappings": {"default": {"default": "cap A breve"}, "mathspeak": {"default": "modifying above upper A with breve", "brief": "mod above upper A with breve", "sbrief": "mod above upper A with breve"}}}, {"key": "0104", "mappings": {"default": {"default": "cap A ogonek"}, "mathspeak": {"default": "modifying above upper A with ogonek", "brief": "mod above upper A with ogonek", "sbrief": "mod above upper A with ogonek"}}}, {"key": "0106", "mappings": {"default": {"default": "cap C acute"}, "mathspeak": {"default": "modifying above upper C with acute", "brief": "mod above upper C with acute", "sbrief": "mod above upper C with acute"}}}, {"key": "0108", "mappings": {"default": {"default": "cap C hat"}, "mathspeak": {"default": "modifying above upper C with caret", "brief": "mod above upper C with caret", "sbrief": "mod above upper C with caret"}}}, {"key": "010A", "mappings": {"default": {"default": "cap C overdot"}, "mathspeak": {"default": "modifying above upper C with dot", "brief": "mod above upper C with dot", "sbrief": "mod above upper C with dot"}}}, {"key": "010C", "mappings": {"default": {"default": "cap C caron"}, "mathspeak": {"default": "modifying above upper C with caron", "brief": "mod above upper C with caron", "sbrief": "mod above upper C with caron"}}}, {"key": "010E", "mappings": {"default": {"default": "cap D caron"}, "mathspeak": {"default": "modifying above upper D with caron", "brief": "mod above upper D with caron", "sbrief": "mod above upper D with caron"}}}, {"key": "0112", "mappings": {"default": {"default": "cap E overbar"}, "mathspeak": {"default": "upper E overbar", "brief": "upper E overbar", "sbrief": "upper E overbar"}}}, {"key": "0114", "mappings": {"default": {"default": "cap E breve"}, "mathspeak": {"default": "modifying above upper E with breve", "brief": "mod above upper E with breve", "sbrief": "mod above upper E with breve"}}}, {"key": "0116", "mappings": {"default": {"default": "cap E overdot"}, "mathspeak": {"default": "modifying above upper E with dot", "brief": "mod above upper E with dot", "sbrief": "mod above upper E with dot"}}}, {"key": "0118", "mappings": {"default": {"default": "cap E ogonek"}, "mathspeak": {"default": "modifying above upper E with ogonek", "brief": "mod above upper E with ogonek", "sbrief": "mod above upper E with ogonek"}}}, {"key": "011A", "mappings": {"default": {"default": "cap E caron"}, "mathspeak": {"default": "modifying above upper E with caron", "brief": "mod above upper E with caron", "sbrief": "mod above upper E with caron"}}}, {"key": "011C", "mappings": {"default": {"default": "cap G hat"}, "mathspeak": {"default": "modifying above upper G with caret", "brief": "mod above upper G with caret", "sbrief": "mod above upper G with caret"}}}, {"key": "011E", "mappings": {"default": {"default": "cap G breve"}, "mathspeak": {"default": "modifying above upper G with breve", "brief": "mod above upper G with breve", "sbrief": "mod above upper G with breve"}}}, {"key": "0120", "mappings": {"default": {"default": "cap G overdot"}, "mathspeak": {"default": "modifying above upper G with dot", "brief": "mod above upper G with dot", "sbrief": "mod above upper G with dot"}}}, {"key": "0122", "mappings": {"default": {"default": "cap G cedilla"}, "mathspeak": {"default": "modifying above upper G with cedilla", "brief": "mod above upper G with cedilla", "sbrief": "mod above upper G with cedilla"}}}, {"key": "0124", "mappings": {"default": {"default": "cap H hat"}, "mathspeak": {"default": "modifying above upper H with caret", "brief": "mod above upper H with caret", "sbrief": "mod above upper H with caret"}}}, {"key": "0128", "mappings": {"default": {"default": "cap I tilde"}, "mathspeak": {"default": "upper I overtilde", "brief": "upper I overtilde", "sbrief": "upper I overtilde"}}}, {"key": "012A", "mappings": {"default": {"default": "cap I overbar"}, "mathspeak": {"default": "upper I overbar", "brief": "upper I overbar", "sbrief": "upper I overbar"}}}, {"key": "012C", "mappings": {"default": {"default": "cap I breve"}, "mathspeak": {"default": "modifying above upper I with breve", "brief": "mod above upper I with breve", "sbrief": "mod above upper I with breve"}}}, {"key": "012E", "mappings": {"default": {"default": "cap I ogonek"}, "mathspeak": {"default": "modifying above upper I with ogonek", "brief": "mod above upper I with ogonek", "sbrief": "mod above upper I with ogonek"}}}, {"key": "0130", "mappings": {"default": {"default": "cap I overdot"}, "mathspeak": {"default": "modifying above upper I with dot", "brief": "mod above upper I with dot", "sbrief": "mod above upper I with dot"}}}, {"key": "0134", "mappings": {"default": {"default": "cap J hat"}, "mathspeak": {"default": "modifying above upper J with caret", "brief": "mod above upper J with caret", "sbrief": "mod above upper J with caret"}}}, {"key": "0136", "mappings": {"default": {"default": "cap K cedilla"}, "mathspeak": {"default": "modifying above upper K with cedilla", "brief": "mod above upper K with cedilla", "sbrief": "mod above upper K with cedilla"}}}, {"key": "0139", "mappings": {"default": {"default": "cap L acute"}, "mathspeak": {"default": "modifying above upper L with acute", "brief": "mod above upper L with acute", "sbrief": "mod above upper L with acute"}}}, {"key": "013B", "mappings": {"default": {"default": "cap L cedilla"}, "mathspeak": {"default": "modifying above upper L with cedilla", "brief": "mod above upper L with cedilla", "sbrief": "mod above upper L with cedilla"}}}, {"key": "013D", "mappings": {"default": {"default": "cap L caron"}, "mathspeak": {"default": "modifying above upper L with caron", "brief": "mod above upper L with caron", "sbrief": "mod above upper L with caron"}}}, {"key": "013F", "mappings": {"default": {"default": "cap L middle dot"}, "mathspeak": {"default": "modifying above upper L with middle dot", "brief": "mod above upper L with middle dot", "sbrief": "mod above upper L with middle dot"}}}, {"key": "0143", "mappings": {"default": {"default": "cap N acute"}, "mathspeak": {"default": "modifying above upper N with acute", "brief": "mod above upper N with acute", "sbrief": "mod above upper N with acute"}}}, {"key": "0145", "mappings": {"default": {"default": "cap N cedilla"}, "mathspeak": {"default": "modifying above upper N with cedilla", "brief": "mod above upper N with cedilla", "sbrief": "mod above upper N with cedilla"}}}, {"key": "0147", "mappings": {"default": {"default": "cap N caron"}, "mathspeak": {"default": "modifying above upper N with caron", "brief": "mod above upper N with caron", "sbrief": "mod above upper N with caron"}}}, {"key": "014C", "mappings": {"default": {"default": "cap O overbar"}, "mathspeak": {"default": "upper O overbar", "brief": "upper O overbar", "sbrief": "upper O overbar"}}}, {"key": "014E", "mappings": {"default": {"default": "cap O breve"}, "mathspeak": {"default": "modifying above upper O with breve", "brief": "mod above upper O with breve", "sbrief": "mod above upper O with breve"}}}, {"key": "0150", "mappings": {"default": {"default": "cap O double acute"}, "mathspeak": {"default": "modifying above upper O with double acute", "brief": "mod above upper O with double acute", "sbrief": "mod above upper O with double acute"}}}, {"key": "0154", "mappings": {"default": {"default": "cap R acute"}, "mathspeak": {"default": "modifying above upper R with acute", "brief": "mod above upper R with acute", "sbrief": "mod above upper R with acute"}}}, {"key": "0156", "mappings": {"default": {"default": "cap R cedilla"}, "mathspeak": {"default": "modifying above upper R with cedilla", "brief": "mod above upper R with cedilla", "sbrief": "mod above upper R with cedilla"}}}, {"key": "0158", "mappings": {"default": {"default": "cap R caron"}, "mathspeak": {"default": "modifying above upper R with caron", "brief": "mod above upper R with caron", "sbrief": "mod above upper R with caron"}}}, {"key": "015A", "mappings": {"default": {"default": "cap S acute"}, "mathspeak": {"default": "modifying above upper S with acute", "brief": "mod above upper S with acute", "sbrief": "mod above upper S with acute"}}}, {"key": "015C", "mappings": {"default": {"default": "cap S hat"}, "mathspeak": {"default": "modifying above upper S with caret", "brief": "mod above upper S with caret", "sbrief": "mod above upper S with caret"}}}, {"key": "015E", "mappings": {"default": {"default": "cap S cedilla"}, "mathspeak": {"default": "modifying above upper S with cedilla", "brief": "mod above upper S with cedilla", "sbrief": "mod above upper S with cedilla"}}}, {"key": "0160", "mappings": {"default": {"default": "cap S caron"}, "mathspeak": {"default": "modifying above upper S with caron", "brief": "mod above upper S with caron", "sbrief": "mod above upper S with caron"}}}, {"key": "0162", "mappings": {"default": {"default": "cap T cedilla"}, "mathspeak": {"default": "modifying above upper T with cedilla", "brief": "mod above upper T with cedilla", "sbrief": "mod above upper T with cedilla"}}}, {"key": "0164", "mappings": {"default": {"default": "cap T caron"}, "mathspeak": {"default": "modifying above upper T with caron", "brief": "mod above upper T with caron", "sbrief": "mod above upper T with caron"}}}, {"key": "0168", "mappings": {"default": {"default": "cap U tilde"}, "mathspeak": {"default": "upper U overtilde", "brief": "upper U overtilde", "sbrief": "upper U overtilde"}}}, {"key": "016A", "mappings": {"default": {"default": "cap U overbar"}, "mathspeak": {"default": "upper U overbar", "brief": "upper U overbar", "sbrief": "upper U overbar"}}}, {"key": "016C", "mappings": {"default": {"default": "cap U breve"}, "mathspeak": {"default": "modifying above upper U with breve", "brief": "mod above upper U with breve", "sbrief": "mod above upper U with breve"}}}, {"key": "016E", "mappings": {"default": {"default": "cap U ring"}, "mathspeak": {"default": "modifying above upper U with ring", "brief": "mod above upper U with ring", "sbrief": "mod above upper U with ring"}}}, {"key": "0170", "mappings": {"default": {"default": "cap U double acute"}, "mathspeak": {"default": "modifying above upper U with double acute", "brief": "mod above upper U with double acute", "sbrief": "mod above upper U with double acute"}}}, {"key": "0172", "mappings": {"default": {"default": "cap U ogonek"}, "mathspeak": {"default": "modifying above upper U with ogonek", "brief": "mod above upper U with ogonek", "sbrief": "mod above upper U with ogonek"}}}, {"key": "0174", "mappings": {"default": {"default": "cap W hat"}, "mathspeak": {"default": "modifying above upper W with caret", "brief": "mod above upper W with caret", "sbrief": "mod above upper W with caret"}}}, {"key": "0176", "mappings": {"default": {"default": "cap Y hat"}, "mathspeak": {"default": "modifying above upper Y with caret", "brief": "mod above upper Y with caret", "sbrief": "mod above upper Y with caret"}}}, {"key": "0178", "mappings": {"default": {"default": "cap Y double overdot"}, "mathspeak": {"default": "modifying above upper Y with double dot", "brief": "mod above upper Y with double dot", "sbrief": "mod above upper Y with double dot"}}}, {"key": "0179", "mappings": {"default": {"default": "cap Z acute"}, "mathspeak": {"default": "modifying above upper Z with acute", "brief": "mod above upper Z with acute", "sbrief": "mod above upper Z with acute"}}}, {"key": "017B", "mappings": {"default": {"default": "cap Z overdot"}, "mathspeak": {"default": "modifying above upper Z with dot", "brief": "mod above upper Z with dot", "sbrief": "mod above upper Z with dot"}}}, {"key": "017D", "mappings": {"default": {"default": "cap Z caron"}, "mathspeak": {"default": "modifying above upper Z with caron", "brief": "mod above upper Z with caron", "sbrief": "mod above upper Z with caron"}}}, {"key": "01CD", "mappings": {"default": {"default": "cap A caron"}, "mathspeak": {"default": "modifying above upper A with caron", "brief": "mod above upper A with caron", "sbrief": "mod above upper A with caron"}}}, {"key": "01CF", "mappings": {"default": {"default": "cap I caron"}, "mathspeak": {"default": "modifying above upper I with caron", "brief": "mod above upper I with caron", "sbrief": "mod above upper I with caron"}}}, {"key": "01D1", "mappings": {"default": {"default": "cap O caron"}, "mathspeak": {"default": "modifying above upper O with caron", "brief": "mod above upper O with caron", "sbrief": "mod above upper O with caron"}}}, {"key": "01D3", "mappings": {"default": {"default": "cap U caron"}, "mathspeak": {"default": "modifying above upper U with caron", "brief": "mod above upper U with caron", "sbrief": "mod above upper U with caron"}}}, {"key": "01E6", "mappings": {"default": {"default": "cap G caron"}, "mathspeak": {"default": "modifying above upper G with caron", "brief": "mod above upper G with caron", "sbrief": "mod above upper G with caron"}}}, {"key": "01E8", "mappings": {"default": {"default": "cap K caron"}, "mathspeak": {"default": "modifying above upper K with caron", "brief": "mod above upper K with caron", "sbrief": "mod above upper K with caron"}}}, {"key": "01EA", "mappings": {"default": {"default": "cap O ogonek"}, "mathspeak": {"default": "modifying above upper O with ogonek", "brief": "mod above upper O with ogonek", "sbrief": "mod above upper O with ogonek"}}}, {"key": "01F4", "mappings": {"default": {"default": "cap G acute"}, "mathspeak": {"default": "modifying above upper G with acute", "brief": "mod above upper G with acute", "sbrief": "mod above upper G with acute"}}}, {"key": "01F8", "mappings": {"default": {"default": "cap N grave"}, "mathspeak": {"default": "modifying above upper N with grave", "brief": "mod above upper N with grave", "sbrief": "mod above upper N with grave"}}}, {"key": "0200", "mappings": {"default": {"default": "cap A double grave"}, "mathspeak": {"default": "modifying above upper A with double grave", "brief": "mod above upper A with double grave", "sbrief": "mod above upper A with double grave"}}}, {"key": "0202", "mappings": {"default": {"default": "cap A inverted breve"}, "mathspeak": {"default": "modifying above upper A with inverted breve", "brief": "mod above upper A with inverted breve", "sbrief": "mod above upper A with inverted breve"}}}, {"key": "0204", "mappings": {"default": {"default": "cap E double grave"}, "mathspeak": {"default": "modifying above upper E with double grave", "brief": "mod above upper E with double grave", "sbrief": "mod above upper E with double grave"}}}, {"key": "0206", "mappings": {"default": {"default": "cap E inverted breve"}, "mathspeak": {"default": "modifying above upper E with inverted breve", "brief": "mod above upper E with inverted breve", "sbrief": "mod above upper E with inverted breve"}}}, {"key": "0208", "mappings": {"default": {"default": "cap I double grave"}, "mathspeak": {"default": "modifying above upper I with double grave", "brief": "mod above upper I with double grave", "sbrief": "mod above upper I with double grave"}}}, {"key": "020A", "mappings": {"default": {"default": "cap I inverted breve"}, "mathspeak": {"default": "modifying above upper I with inverted breve", "brief": "mod above upper I with inverted breve", "sbrief": "mod above upper I with inverted breve"}}}, {"key": "020C", "mappings": {"default": {"default": "cap O double grave"}, "mathspeak": {"default": "modifying above upper O with double grave", "brief": "mod above upper O with double grave", "sbrief": "mod above upper O with double grave"}}}, {"key": "020E", "mappings": {"default": {"default": "cap O inverted breve"}, "mathspeak": {"default": "modifying above upper O with inverted breve", "brief": "mod above upper O with inverted breve", "sbrief": "mod above upper O with inverted breve"}}}, {"key": "0210", "mappings": {"default": {"default": "cap R double grave"}, "mathspeak": {"default": "modifying above upper R with double grave", "brief": "mod above upper R with double grave", "sbrief": "mod above upper R with double grave"}}}, {"key": "0212", "mappings": {"default": {"default": "cap R inverted breve"}, "mathspeak": {"default": "modifying above upper R with inverted breve", "brief": "mod above upper R with inverted breve", "sbrief": "mod above upper R with inverted breve"}}}, {"key": "0214", "mappings": {"default": {"default": "cap U double grave"}, "mathspeak": {"default": "modifying above upper U with double grave", "brief": "mod above upper U with double grave", "sbrief": "mod above upper U with double grave"}}}, {"key": "0216", "mappings": {"default": {"default": "cap U inverted breve"}, "mathspeak": {"default": "modifying above upper U with inverted breve", "brief": "mod above upper U with inverted breve", "sbrief": "mod above upper U with inverted breve"}}}, {"key": "0218", "mappings": {"default": {"default": "cap S comma below"}, "mathspeak": {"default": "modifying below upper S with comma below", "brief": "mod below upper S with comma below", "sbrief": "mod below upper S with comma below"}}}, {"key": "021A", "mappings": {"default": {"default": "cap T comma below"}, "mathspeak": {"default": "modifying below upper T with comma below", "brief": "mod below upper T with comma below", "sbrief": "mod below upper T with comma below"}}}, {"key": "021E", "mappings": {"default": {"default": "cap H caron"}, "mathspeak": {"default": "modifying above upper H with caron", "brief": "mod above upper H with caron", "sbrief": "mod above upper H with caron"}}}, {"key": "0226", "mappings": {"default": {"default": "cap A overdot"}, "mathspeak": {"default": "modifying above upper A with dot", "brief": "mod above upper A with dot", "sbrief": "mod above upper A with dot"}}}, {"key": "0228", "mappings": {"default": {"default": "cap E cedilla"}, "mathspeak": {"default": "modifying above upper E with cedilla", "brief": "mod above upper E with cedilla", "sbrief": "mod above upper E with cedilla"}}}, {"key": "022E", "mappings": {"default": {"default": "cap O overdot"}, "mathspeak": {"default": "modifying above upper O with dot", "brief": "mod above upper O with dot", "sbrief": "mod above upper O with dot"}}}, {"key": "0232", "mappings": {"default": {"default": "cap Y overbar"}, "mathspeak": {"default": "upper Y overbar", "brief": "upper Y overbar", "sbrief": "upper Y overbar"}}}, {"key": "1E00", "mappings": {"default": {"default": "cap A ring below"}, "mathspeak": {"default": "modifying below upper A with ring below", "brief": "mod below upper A with ring below", "sbrief": "mod below upper A with ring below"}}}, {"key": "1E02", "mappings": {"default": {"default": "cap B overdot"}, "mathspeak": {"default": "modifying above upper B with dot", "brief": "mod above upper B with dot", "sbrief": "mod above upper B with dot"}}}, {"key": "1E04", "mappings": {"default": {"default": "cap B underdot"}, "mathspeak": {"default": "modifying below upper B with dot", "brief": "mod below upper B with dot", "sbrief": "mod below upper B with dot"}}}, {"key": "1E06", "mappings": {"default": {"default": "cap B underbar"}, "mathspeak": {"default": "upper B underbar", "brief": "upper B underbar", "sbrief": "upper B underbar"}}}, {"key": "1E0A", "mappings": {"default": {"default": "cap D overdot"}, "mathspeak": {"default": "modifying above upper D with dot", "brief": "mod above upper D with dot", "sbrief": "mod above upper D with dot"}}}, {"key": "1E0C", "mappings": {"default": {"default": "cap D underdot"}, "mathspeak": {"default": "modifying below upper D with dot", "brief": "mod below upper D with dot", "sbrief": "mod below upper D with dot"}}}, {"key": "1E0E", "mappings": {"default": {"default": "cap D underbar"}, "mathspeak": {"default": "upper D underbar", "brief": "upper D underbar", "sbrief": "upper D underbar"}}}, {"key": "1E10", "mappings": {"default": {"default": "cap D cedilla"}, "mathspeak": {"default": "modifying above upper D with cedilla", "brief": "mod above upper D with cedilla", "sbrief": "mod above upper D with cedilla"}}}, {"key": "1E12", "mappings": {"default": {"default": "cap D underhat"}, "mathspeak": {"default": "modifying below upper D with caret", "brief": "mod below upper D with caret", "sbrief": "mod below upper D with caret"}}}, {"key": "1E18", "mappings": {"default": {"default": "cap E underhat"}, "mathspeak": {"default": "modifying below upper E with caret", "brief": "mod below upper E with caret", "sbrief": "mod below upper E with caret"}}}, {"key": "1E1A", "mappings": {"default": {"default": "cap E tilde below"}, "mathspeak": {"default": "upper E undertilde", "brief": "upper E undertilde", "sbrief": "upper E undertilde"}}}, {"key": "1E1E", "mappings": {"default": {"default": "cap F overdot"}, "mathspeak": {"default": "modifying above upper F with dot", "brief": "mod above upper F with dot", "sbrief": "mod above upper F with dot"}}}, {"key": "1E20", "mappings": {"default": {"default": "cap G overbar"}, "mathspeak": {"default": "upper G overbar", "brief": "upper G overbar", "sbrief": "upper G overbar"}}}, {"key": "1E22", "mappings": {"default": {"default": "cap H overdot"}, "mathspeak": {"default": "modifying above upper H with dot", "brief": "mod above upper H with dot", "sbrief": "mod above upper H with dot"}}}, {"key": "1E24", "mappings": {"default": {"default": "cap H underdot"}, "mathspeak": {"default": "modifying below upper H with dot", "brief": "mod below upper H with dot", "sbrief": "mod below upper H with dot"}}}, {"key": "1E26", "mappings": {"default": {"default": "cap H double overdot"}, "mathspeak": {"default": "modifying above upper H with double dot", "brief": "mod above upper H with double dot", "sbrief": "mod above upper H with double dot"}}}, {"key": "1E28", "mappings": {"default": {"default": "cap H cedilla"}, "mathspeak": {"default": "modifying above upper H with cedilla", "brief": "mod above upper H with cedilla", "sbrief": "mod above upper H with cedilla"}}}, {"key": "1E2A", "mappings": {"default": {"default": "cap H breve below"}, "mathspeak": {"default": "modifying below upper H with breve below", "brief": "mod below upper H with breve below", "sbrief": "mod below upper H with breve below"}}}, {"key": "1E2C", "mappings": {"default": {"default": "cap I tilde below"}, "mathspeak": {"default": "upper I undertilde", "brief": "upper I undertilde", "sbrief": "upper I undertilde"}}}, {"key": "1E30", "mappings": {"default": {"default": "cap K acute"}, "mathspeak": {"default": "modifying above upper K with acute", "brief": "mod above upper K with acute", "sbrief": "mod above upper K with acute"}}}, {"key": "1E32", "mappings": {"default": {"default": "cap K underdot"}, "mathspeak": {"default": "modifying below upper K with dot", "brief": "mod below upper K with dot", "sbrief": "mod below upper K with dot"}}}, {"key": "1E34", "mappings": {"default": {"default": "cap K underbar"}, "mathspeak": {"default": "upper K underbar", "brief": "upper K underbar", "sbrief": "upper K underbar"}}}, {"key": "1E36", "mappings": {"default": {"default": "cap L underdot"}, "mathspeak": {"default": "modifying below upper L with dot", "brief": "mod below upper L with dot", "sbrief": "mod below upper L with dot"}}}, {"key": "1E3A", "mappings": {"default": {"default": "cap L underbar"}, "mathspeak": {"default": "upper L underbar", "brief": "upper L underbar", "sbrief": "upper L underbar"}}}, {"key": "1E3C", "mappings": {"default": {"default": "cap L underhat"}, "mathspeak": {"default": "modifying below upper L with caret", "brief": "mod below upper L with caret", "sbrief": "mod below upper L with caret"}}}, {"key": "1E3E", "mappings": {"default": {"default": "cap M acute"}, "mathspeak": {"default": "modifying above upper M with acute", "brief": "mod above upper M with acute", "sbrief": "mod above upper M with acute"}}}, {"key": "1E40", "mappings": {"default": {"default": "cap M overdot"}, "mathspeak": {"default": "modifying above upper M with dot", "brief": "mod above upper M with dot", "sbrief": "mod above upper M with dot"}}}, {"key": "1E42", "mappings": {"default": {"default": "cap M underdot"}, "mathspeak": {"default": "modifying below upper M with dot", "brief": "mod below upper M with dot", "sbrief": "mod below upper M with dot"}}}, {"key": "1E44", "mappings": {"default": {"default": "cap N overdot"}, "mathspeak": {"default": "modifying above upper N with dot", "brief": "mod above upper N with dot", "sbrief": "mod above upper N with dot"}}}, {"key": "1E46", "mappings": {"default": {"default": "cap N underdot"}, "mathspeak": {"default": "modifying below upper N with dot", "brief": "mod below upper N with dot", "sbrief": "mod below upper N with dot"}}}, {"key": "1E48", "mappings": {"default": {"default": "cap N underbar"}, "mathspeak": {"default": "upper N underbar", "brief": "upper N underbar", "sbrief": "upper N underbar"}}}, {"key": "1E4A", "mappings": {"default": {"default": "cap N underhat"}, "mathspeak": {"default": "modifying below upper N with caret", "brief": "mod below upper N with caret", "sbrief": "mod below upper N with caret"}}}, {"key": "1E54", "mappings": {"default": {"default": "cap P acute"}, "mathspeak": {"default": "modifying above upper P with acute", "brief": "mod above upper P with acute", "sbrief": "mod above upper P with acute"}}}, {"key": "1E56", "mappings": {"default": {"default": "cap P overdot"}, "mathspeak": {"default": "modifying above upper P with dot", "brief": "mod above upper P with dot", "sbrief": "mod above upper P with dot"}}}, {"key": "1E58", "mappings": {"default": {"default": "cap R overdot"}, "mathspeak": {"default": "modifying above upper R with dot", "brief": "mod above upper R with dot", "sbrief": "mod above upper R with dot"}}}, {"key": "1E5A", "mappings": {"default": {"default": "cap R underdot"}, "mathspeak": {"default": "modifying below upper R with dot", "brief": "mod below upper R with dot", "sbrief": "mod below upper R with dot"}}}, {"key": "1E5E", "mappings": {"default": {"default": "cap R underbar"}, "mathspeak": {"default": "upper R underbar", "brief": "upper R underbar", "sbrief": "upper R underbar"}}}, {"key": "1E60", "mappings": {"default": {"default": "cap S overdot"}, "mathspeak": {"default": "modifying above upper S with dot", "brief": "mod above upper S with dot", "sbrief": "mod above upper S with dot"}}}, {"key": "1E62", "mappings": {"default": {"default": "cap S underdot"}, "mathspeak": {"default": "modifying below upper S with dot", "brief": "mod below upper S with dot", "sbrief": "mod below upper S with dot"}}}, {"key": "1E6A", "mappings": {"default": {"default": "cap T overdot"}, "mathspeak": {"default": "modifying above upper T with dot", "brief": "mod above upper T with dot", "sbrief": "mod above upper T with dot"}}}, {"key": "1E6C", "mappings": {"default": {"default": "cap T underdot"}, "mathspeak": {"default": "modifying below upper T with dot", "brief": "mod below upper T with dot", "sbrief": "mod below upper T with dot"}}}, {"key": "1E6E", "mappings": {"default": {"default": "cap T underbar"}, "mathspeak": {"default": "upper T underbar", "brief": "upper T underbar", "sbrief": "upper T underbar"}}}, {"key": "1E70", "mappings": {"default": {"default": "cap T underhat"}, "mathspeak": {"default": "modifying below upper T with caret", "brief": "mod below upper T with caret", "sbrief": "mod below upper T with caret"}}}, {"key": "1E72", "mappings": {"default": {"default": "cap U double underdot"}, "mathspeak": {"default": "modifying below upper U with double dot", "brief": "mod below upper U with double dot", "sbrief": "mod below upper U with double dot"}}}, {"key": "1E74", "mappings": {"default": {"default": "cap U tilde below"}, "mathspeak": {"default": "upper U undertilde", "brief": "upper U undertilde", "sbrief": "upper U undertilde"}}}, {"key": "1E76", "mappings": {"default": {"default": "cap U underhat"}, "mathspeak": {"default": "modifying below upper U with caret", "brief": "mod below upper U with caret", "sbrief": "mod below upper U with caret"}}}, {"key": "1E7C", "mappings": {"default": {"default": "cap V tilde"}, "mathspeak": {"default": "upper V overtilde", "brief": "upper V overtilde", "sbrief": "upper V overtilde"}}}, {"key": "1E7E", "mappings": {"default": {"default": "cap V underdot"}, "mathspeak": {"default": "modifying below upper V with dot", "brief": "mod below upper V with dot", "sbrief": "mod below upper V with dot"}}}, {"key": "1E80", "mappings": {"default": {"default": "cap W grave"}, "mathspeak": {"default": "modifying above upper W with grave", "brief": "mod above upper W with grave", "sbrief": "mod above upper W with grave"}}}, {"key": "1E82", "mappings": {"default": {"default": "cap W acute"}, "mathspeak": {"default": "modifying above upper W with acute", "brief": "mod above upper W with acute", "sbrief": "mod above upper W with acute"}}}, {"key": "1E84", "mappings": {"default": {"default": "cap W double overdot"}, "mathspeak": {"default": "modifying above upper W with double dot", "brief": "mod above upper W with double dot", "sbrief": "mod above upper W with double dot"}}}, {"key": "1E86", "mappings": {"default": {"default": "cap W overdot"}, "mathspeak": {"default": "modifying above upper W with dot", "brief": "mod above upper W with dot", "sbrief": "mod above upper W with dot"}}}, {"key": "1E88", "mappings": {"default": {"default": "cap W underdot"}, "mathspeak": {"default": "modifying below upper W with dot", "brief": "mod below upper W with dot", "sbrief": "mod below upper W with dot"}}}, {"key": "1E8A", "mappings": {"default": {"default": "cap X overdot"}, "mathspeak": {"default": "modifying above upper X with dot", "brief": "mod above upper X with dot", "sbrief": "mod above upper X with dot"}}}, {"key": "1E8C", "mappings": {"default": {"default": "cap X double overdot"}, "mathspeak": {"default": "modifying above upper X with double dot", "brief": "mod above upper X with double dot", "sbrief": "mod above upper X with double dot"}}}, {"key": "1E8E", "mappings": {"default": {"default": "cap Y overdot"}, "mathspeak": {"default": "modifying above upper Y with dot", "brief": "mod above upper Y with dot", "sbrief": "mod above upper Y with dot"}}}, {"key": "1E90", "mappings": {"default": {"default": "cap Z circumflex"}, "mathspeak": {"default": "modifying above upper Z with circumflex", "brief": "mod above upper Z with circumflex", "sbrief": "mod above upper Z with circumflex"}}}, {"key": "1E92", "mappings": {"default": {"default": "cap Z underdot"}, "mathspeak": {"default": "modifying below upper Z with dot", "brief": "mod below upper Z with dot", "sbrief": "mod below upper Z with dot"}}}, {"key": "1E94", "mappings": {"default": {"default": "cap Z underbar"}, "mathspeak": {"default": "upper Z underbar", "brief": "upper Z underbar", "sbrief": "upper Z underbar"}}}, {"key": "1EA0", "mappings": {"default": {"default": "cap A underdot"}, "mathspeak": {"default": "modifying below upper A with dot", "brief": "mod below upper A with dot", "sbrief": "mod below upper A with dot"}}}, {"key": "1EA2", "mappings": {"default": {"default": "cap A hook"}, "mathspeak": {"default": "modifying above upper A with hook", "brief": "mod above upper A with hook", "sbrief": "mod above upper A with hook"}}}, {"key": "1EB8", "mappings": {"default": {"default": "cap E underdot"}, "mathspeak": {"default": "modifying below upper E with dot", "brief": "mod below upper E with dot", "sbrief": "mod below upper E with dot"}}}, {"key": "1EBA", "mappings": {"default": {"default": "cap E hook"}, "mathspeak": {"default": "modifying above upper E with hook", "brief": "mod above upper E with hook", "sbrief": "mod above upper E with hook"}}}, {"key": "1EBC", "mappings": {"default": {"default": "cap E tilde"}, "mathspeak": {"default": "upper E overtilde", "brief": "upper E overtilde", "sbrief": "upper E overtilde"}}}, {"key": "1EC8", "mappings": {"default": {"default": "cap I hook"}, "mathspeak": {"default": "modifying above upper I with hook", "brief": "mod above upper I with hook", "sbrief": "mod above upper I with hook"}}}, {"key": "1ECA", "mappings": {"default": {"default": "cap I underdot"}, "mathspeak": {"default": "modifying below upper I with dot", "brief": "mod below upper I with dot", "sbrief": "mod below upper I with dot"}}}, {"key": "1ECC", "mappings": {"default": {"default": "cap O underdot"}, "mathspeak": {"default": "modifying below upper O with dot", "brief": "mod below upper O with dot", "sbrief": "mod below upper O with dot"}}}, {"key": "1ECE", "mappings": {"default": {"default": "cap O hook"}, "mathspeak": {"default": "modifying above upper O with hook", "brief": "mod above upper O with hook", "sbrief": "mod above upper O with hook"}}}, {"key": "1EE4", "mappings": {"default": {"default": "cap U underdot"}, "mathspeak": {"default": "modifying below upper U with dot", "brief": "mod below upper U with dot", "sbrief": "mod below upper U with dot"}}}, {"key": "1EE6", "mappings": {"default": {"default": "cap U hook"}, "mathspeak": {"default": "modifying above upper U with hook", "brief": "mod above upper U with hook", "sbrief": "mod above upper U with hook"}}}, {"key": "1EF2", "mappings": {"default": {"default": "cap Y grave"}, "mathspeak": {"default": "modifying above upper Y with grave", "brief": "mod above upper Y with grave", "sbrief": "mod above upper Y with grave"}}}, {"key": "1EF4", "mappings": {"default": {"default": "cap Y underdot"}, "mathspeak": {"default": "modifying below upper Y with dot", "brief": "mod below upper Y with dot", "sbrief": "mod below upper Y with dot"}}}, {"key": "1EF6", "mappings": {"default": {"default": "cap Y hook"}, "mathspeak": {"default": "modifying above upper Y with hook", "brief": "mod above upper Y with hook", "sbrief": "mod above upper Y with hook"}}}, {"key": "1EF8", "mappings": {"default": {"default": "cap Y tilde"}, "mathspeak": {"default": "upper Y overtilde", "brief": "upper Y overtilde", "sbrief": "upper Y overtilde"}}}], "en/symbols/math_angles.min": [{"locale": "en"}, {"key": "22BE", "mappings": {"default": {"default": "right angle with arc"}}}, {"key": "237C", "mappings": {"default": {"default": "right angle with downwards zigzag arrow"}}}, {"key": "27C0", "mappings": {"default": {"default": "three dimensional angle"}}}, {"key": "299B", "mappings": {"default": {"default": "measured angle opening left"}}}, {"key": "299C", "mappings": {"default": {"default": "right angle variant with square"}}}, {"key": "299D", "mappings": {"default": {"default": "measured right angle with dot"}}}, {"key": "299E", "mappings": {"default": {"default": "angle with s inside"}}}, {"key": "299F", "mappings": {"default": {"default": "acute angle"}}}, {"key": "29A0", "mappings": {"default": {"default": "spherical angle opening left"}}}, {"key": "29A1", "mappings": {"default": {"default": "spherical angle opening up"}}}, {"key": "29A2", "mappings": {"default": {"default": "turned angle"}}}, {"key": "29A3", "mappings": {"default": {"default": "reversed angle"}}}, {"key": "29A4", "mappings": {"default": {"default": "angle with underbar"}}}, {"key": "29A5", "mappings": {"default": {"default": "reversed angle with underbar"}}}, {"key": "29A6", "mappings": {"default": {"default": "oblique angle opening up"}}}, {"key": "29A7", "mappings": {"default": {"default": "oblique angle opening down"}}}, {"key": "29A8", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and right"}}}, {"key": "29A9", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and left"}}}, {"key": "29AA", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and right"}}}, {"key": "29AB", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and left"}}}, {"key": "29AC", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and up"}}}, {"key": "29AD", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and up"}}}, {"key": "29AE", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and down"}}}, {"key": "29AF", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and down"}}}], "en/symbols/math_arrows.min": [{"locale": "en"}, {"key": "2190", "mappings": {"default": {"default": "left arrow"}, "mathspeak": {"sbrief": "L arrow"}}}, {"key": "2191", "mappings": {"default": {"default": "up arrow"}, "mathspeak": {"sbrief": "U arrow"}}}, {"key": "2192", "mappings": {"default": {"default": "right arrow"}, "mathspeak": {"sbrief": "R arrow"}}}, {"key": "2193", "mappings": {"default": {"default": "down arrow"}, "mathspeak": {"sbrief": "D arrow"}}}, {"key": "2194", "mappings": {"default": {"default": "left right arrow"}, "mathspeak": {"sbrief": "L R arrow"}}}, {"key": "2195", "mappings": {"default": {"default": "up down arrow"}, "mathspeak": {"sbrief": "U D arrow"}}}, {"key": "2196", "mappings": {"default": {"default": "up left arrow"}, "mathspeak": {"sbrief": "U L arrow"}}}, {"key": "2197", "mappings": {"default": {"default": "up right arrow"}, "mathspeak": {"sbrief": "U R arrow"}}}, {"key": "2198", "mappings": {"default": {"default": "down right arrow"}, "mathspeak": {"sbrief": "D R arrow"}}}, {"key": "2199", "mappings": {"default": {"default": "down left arrow"}, "mathspeak": {"sbrief": "D L arrow"}}}, {"key": "219A", "mappings": {"default": {"default": "left arrow with stroke"}, "mathspeak": {"sbrief": "L arrow with stroke"}}}, {"key": "219B", "mappings": {"default": {"default": "right arrow with stroke"}, "mathspeak": {"sbrief": "R arrow with stroke"}}}, {"key": "219C", "mappings": {"default": {"default": "left wave arrow"}, "mathspeak": {"sbrief": "L wave arrow"}}}, {"key": "219D", "mappings": {"default": {"default": "right wave arrow"}, "mathspeak": {"sbrief": "R wave arrow"}}}, {"key": "219E", "mappings": {"default": {"default": "two headed left arrow"}, "mathspeak": {"sbrief": "two headed L arrow"}}}, {"key": "219F", "mappings": {"default": {"default": "two headed up arrow"}, "mathspeak": {"sbrief": "two headed U arrow"}}}, {"key": "21A0", "mappings": {"default": {"default": "two headed right arrow"}, "mathspeak": {"sbrief": "two headed R arrow"}}}, {"key": "21A1", "mappings": {"default": {"default": "two headed down arrow"}, "mathspeak": {"sbrief": "two headed D arrow"}}}, {"key": "21A2", "mappings": {"default": {"default": "left arrow with tail"}, "mathspeak": {"sbrief": "L arrow with tail"}}}, {"key": "21A3", "mappings": {"default": {"default": "right arrow with tail"}, "mathspeak": {"sbrief": "R arrow with tail"}}}, {"key": "21A4", "mappings": {"default": {"default": "left arrow from bar"}, "mathspeak": {"sbrief": "L arrow from bar"}}}, {"key": "21A5", "mappings": {"default": {"default": "up arrow from bar"}, "mathspeak": {"sbrief": "U arrow from bar"}}}, {"key": "21A6", "mappings": {"default": {"default": "right arrow from bar"}, "mathspeak": {"sbrief": "R arrow from bar"}}}, {"key": "21A7", "mappings": {"default": {"default": "down arrow from bar"}, "mathspeak": {"sbrief": "D arrow from bar"}}}, {"key": "21A8", "mappings": {"default": {"default": "up down arrow with base"}, "mathspeak": {"sbrief": "U D arrow with base"}}}, {"key": "21A9", "mappings": {"default": {"default": "left arrow with hook"}, "mathspeak": {"sbrief": "L arrow with hook"}}}, {"key": "21AA", "mappings": {"default": {"default": "right arrow with hook"}, "mathspeak": {"sbrief": "R arrow with hook"}}}, {"key": "21AB", "mappings": {"default": {"default": "left arrow with loop"}, "mathspeak": {"sbrief": "L arrow with loop"}}}, {"key": "21AC", "mappings": {"default": {"default": "right arrow with loop"}, "mathspeak": {"sbrief": "R arrow with loop"}}}, {"key": "21AD", "mappings": {"default": {"default": "left right wave arrow"}, "mathspeak": {"sbrief": "L R wave arrow"}}}, {"key": "21AE", "mappings": {"default": {"default": "left right arrow with stroke"}, "mathspeak": {"sbrief": "L R arrow with stroke"}}}, {"key": "21AF", "mappings": {"default": {"default": "down zigzag arrow"}, "mathspeak": {"sbrief": "D zigzag arrow"}}}, {"key": "21B0", "mappings": {"default": {"default": "up arrow with tip left"}, "mathspeak": {"sbrief": "U arrow with tip left"}}}, {"key": "21B1", "mappings": {"default": {"default": "up arrow with tip right"}, "mathspeak": {"sbrief": "U arrow with tip right"}}}, {"key": "21B2", "mappings": {"default": {"default": "down arrow with tip left"}, "mathspeak": {"sbrief": "D arrow with tip left"}}}, {"key": "21B3", "mappings": {"default": {"default": "down arrow with tip right"}, "mathspeak": {"sbrief": "D arrow with tip right"}}}, {"key": "21B4", "mappings": {"default": {"default": "right arrow with corner down"}, "mathspeak": {"sbrief": "R arrow with corner down"}}}, {"key": "21B5", "mappings": {"default": {"default": "down arrow with corner left"}, "mathspeak": {"sbrief": "D arrow with corner left"}}}, {"key": "21B6", "mappings": {"default": {"default": "anticlockwise top semicircle arrow"}}}, {"key": "21B7", "mappings": {"default": {"default": "clockwise top semicircle arrow"}}}, {"key": "21B8", "mappings": {"default": {"default": "up left arrow to long bar"}, "mathspeak": {"sbrief": "U L arrow to long bar"}}}, {"key": "21B9", "mappings": {"default": {"default": "left arrow to bar over right arrow to bar"}, "mathspeak": {"sbrief": "L arrow to bar over R arrow to bar"}}}, {"key": "21BA", "mappings": {"default": {"default": "anticlockwise open circle arrow"}}}, {"key": "21BB", "mappings": {"default": {"default": "clockwise open circle arrow"}}}, {"key": "21C4", "mappings": {"default": {"default": "right arrow over left arrow"}, "mathspeak": {"sbrief": "R arrow over L arrow"}}}, {"key": "21C5", "mappings": {"default": {"default": "up arrow left of down arrow"}, "mathspeak": {"sbrief": "U arrow L of D arrow"}}}, {"key": "21C6", "mappings": {"default": {"default": "left arrow over right arrow"}, "mathspeak": {"sbrief": "L arrow over R arrow"}}}, {"key": "21C7", "mappings": {"default": {"default": "left paired arrows"}, "mathspeak": {"sbrief": "L paired arrows"}}}, {"key": "21C8", "mappings": {"default": {"default": "up paired arrows"}, "mathspeak": {"sbrief": "U paired arrows"}}}, {"key": "21C9", "mappings": {"default": {"default": "right paired arrows"}, "mathspeak": {"sbrief": "R paired arrows"}}}, {"key": "21CA", "mappings": {"default": {"default": "down paired arrows"}, "mathspeak": {"sbrief": "D paired arrows"}}}, {"key": "21CD", "mappings": {"default": {"default": "left double arrow with stroke"}, "mathspeak": {"sbrief": "L double arrow with stroke"}}}, {"key": "21CE", "mappings": {"default": {"default": "left right double arrow with stroke"}, "mathspeak": {"sbrief": "L R double arrow with stroke"}}}, {"key": "21CF", "mappings": {"default": {"default": "right double arrow with stroke"}, "mathspeak": {"sbrief": "R double arrow with stroke"}}}, {"key": "21D0", "mappings": {"default": {"default": "left double arrow"}, "mathspeak": {"sbrief": "L double arrow"}}}, {"key": "21D1", "mappings": {"default": {"default": "up double arrow"}, "mathspeak": {"sbrief": "U double arrow"}}}, {"key": "21D2", "mappings": {"default": {"default": "right double arrow"}, "mathspeak": {"sbrief": "R double arrow"}}}, {"key": "21D3", "mappings": {"default": {"default": "down double arrow"}, "mathspeak": {"sbrief": "D double arrow"}}}, {"key": "21D4", "mappings": {"default": {"default": "left right double arrow"}, "mathspeak": {"sbrief": "L R double arrow"}}}, {"key": "21D5", "mappings": {"default": {"default": "up down double arrow"}, "mathspeak": {"sbrief": "U D double arrow"}}}, {"key": "21D6", "mappings": {"default": {"default": "up left double arrow"}, "mathspeak": {"sbrief": "U L double arrow"}}}, {"key": "21D7", "mappings": {"default": {"default": "up right double arrow"}, "mathspeak": {"sbrief": "U R double arrow"}}}, {"key": "21D8", "mappings": {"default": {"default": "down right double arrow"}, "mathspeak": {"sbrief": "D R double arrow"}}}, {"key": "21D9", "mappings": {"default": {"default": "down left double arrow"}, "mathspeak": {"sbrief": "D L double arrow"}}}, {"key": "21DA", "mappings": {"default": {"default": "left triple arrow"}, "mathspeak": {"sbrief": "L triple arrow"}}}, {"key": "21DB", "mappings": {"default": {"default": "right triple arrow"}, "mathspeak": {"sbrief": "R triple arrow"}}}, {"key": "21DC", "mappings": {"default": {"default": "left squiggle arrow"}, "mathspeak": {"sbrief": "L squiggle arrow"}}}, {"key": "21DD", "mappings": {"default": {"default": "right squiggle arrow"}, "mathspeak": {"sbrief": "R squiggle arrow"}}}, {"key": "21DE", "mappings": {"default": {"default": "up arrow with double stroke"}, "mathspeak": {"sbrief": "U arrow with double stroke"}}}, {"key": "21DF", "mappings": {"default": {"default": "down arrow with double stroke"}, "mathspeak": {"sbrief": "D arrow with double stroke"}}}, {"key": "21E0", "mappings": {"default": {"default": "left dashed arrow"}, "mathspeak": {"sbrief": "L dashed arrow"}}}, {"key": "21E1", "mappings": {"default": {"default": "up dashed arrow"}, "mathspeak": {"sbrief": "U dashed arrow"}}}, {"key": "21E2", "mappings": {"default": {"default": "right dashed arrow"}, "mathspeak": {"sbrief": "R dashed arrow"}}}, {"key": "21E3", "mappings": {"default": {"default": "down dashed arrow"}, "mathspeak": {"sbrief": "D dashed arrow"}}}, {"key": "21E4", "mappings": {"default": {"default": "left arrow to bar"}, "mathspeak": {"sbrief": "L arrow to bar"}}}, {"key": "21E5", "mappings": {"default": {"default": "right arrow to bar"}, "mathspeak": {"sbrief": "R arrow to bar"}}}, {"key": "21E6", "mappings": {"default": {"default": "white left arrow"}, "mathspeak": {"sbrief": "white L arrow"}}}, {"key": "21E7", "mappings": {"default": {"default": "white up arrow"}, "mathspeak": {"sbrief": "white U arrow"}}}, {"key": "21E8", "mappings": {"default": {"default": "white right arrow"}, "mathspeak": {"sbrief": "white R arrow"}}}, {"key": "21E9", "mappings": {"default": {"default": "white down arrow"}, "mathspeak": {"sbrief": "white D arrow"}}}, {"key": "21EA", "mappings": {"default": {"default": "white up arrow from bar"}, "mathspeak": {"sbrief": "white U arrow from bar"}}}, {"key": "21EB", "mappings": {"default": {"default": "white up arrow on pedestal"}, "mathspeak": {"sbrief": "white U arrow on pedestal"}}}, {"key": "21EC", "mappings": {"default": {"default": "white up arrow on pedestal with horizontal bar"}, "mathspeak": {"sbrief": "white U arrow on pedestal with horizontal bar"}}}, {"key": "21ED", "mappings": {"default": {"default": "white up arrow on pedestal with vertical bar"}, "mathspeak": {"sbrief": "white U arrow on pedestal with vertical bar"}}}, {"key": "21EE", "mappings": {"default": {"default": "white double up arrow"}, "mathspeak": {"sbrief": "white double U arrow"}}}, {"key": "21EF", "mappings": {"default": {"default": "white double up arrow on pedestal"}, "mathspeak": {"sbrief": "white double U arrow on pedestal"}}}, {"key": "21F0", "mappings": {"default": {"default": "white right arrow from wall"}, "mathspeak": {"sbrief": "white R arrow from wall"}}}, {"key": "21F1", "mappings": {"default": {"default": "north west arrow to corner"}}}, {"key": "21F2", "mappings": {"default": {"default": "south east arrow to corner"}}}, {"key": "21F3", "mappings": {"default": {"default": "up down white arrow"}, "mathspeak": {"sbrief": "U D white arrow"}}}, {"key": "21F4", "mappings": {"default": {"default": "right arrow with small circle"}, "mathspeak": {"sbrief": "R arrow with small circle"}}}, {"key": "21F5", "mappings": {"default": {"default": "down arrow left of up arrow"}, "mathspeak": {"sbrief": "D arrow L of U arrow"}}}, {"key": "21F6", "mappings": {"default": {"default": "three right arrows"}, "mathspeak": {"sbrief": "three R arrows"}}}, {"key": "21F7", "mappings": {"default": {"default": "left arrow with vertical stroke"}, "mathspeak": {"sbrief": "L arrow with vertical stroke"}}}, {"key": "21F8", "mappings": {"default": {"default": "right arrow with vertical stroke"}, "mathspeak": {"sbrief": "R arrow with vertical stroke"}}}, {"key": "21F9", "mappings": {"default": {"default": "left right arrow with vertical stroke"}, "mathspeak": {"sbrief": "L R arrow with vertical stroke"}}}, {"key": "21FA", "mappings": {"default": {"default": "left arrow with double vertical stroke"}, "mathspeak": {"sbrief": "L arrow with double vertical stroke"}}}, {"key": "21FB", "mappings": {"default": {"default": "right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "R arrow with double vertical stroke"}}}, {"key": "21FC", "mappings": {"default": {"default": "left right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "L R arrow with double vertical stroke"}}}, {"key": "21FD", "mappings": {"default": {"default": "left open headed arrow"}, "mathspeak": {"sbrief": "L open headed arrow"}}}, {"key": "21FE", "mappings": {"default": {"default": "right open headed arrow"}, "mathspeak": {"sbrief": "r open headed arrow"}}}, {"key": "21FF", "mappings": {"default": {"default": "left right open headed arrow"}, "mathspeak": {"sbrief": "L R open headed arrow"}}}, {"key": "2301", "mappings": {"default": {"default": "electric arrow"}}}, {"key": "2303", "mappings": {"default": {"default": "up arrowhead"}, "mathspeak": {"sbrief": "U arrowhead"}}}, {"key": "2304", "mappings": {"default": {"default": "down arrowhead"}, "mathspeak": {"sbrief": "D arrowhead"}}}, {"key": "2324", "mappings": {"default": {"default": "up arrowhead between two horizontal bars", "alternative": "enter key"}, "mathspeak": {"sbrief": "U arrowhead between two horizontal bars"}}}, {"key": "238B", "mappings": {"default": {"default": "broken circle with northwest arrow"}}}, {"key": "2794", "mappings": {"default": {"default": "heavy wide headed right arrow"}, "mathspeak": {"sbrief": "heavy wide headed R arrow"}}}, {"key": "2798", "mappings": {"default": {"default": "heavy down right arrow"}, "mathspeak": {"sbrief": "heavy D R arrow"}}}, {"key": "2799", "mappings": {"default": {"default": "heavy right arrow"}, "mathspeak": {"sbrief": "heavy R arrow"}}}, {"key": "279A", "mappings": {"default": {"default": "heavy up right arrow"}, "mathspeak": {"sbrief": "heavy U R arrow"}}}, {"key": "279B", "mappings": {"default": {"default": "drafting point right arrow"}, "mathspeak": {"sbrief": "drafting point R arrow"}}}, {"key": "279C", "mappings": {"default": {"default": "heavy round tipped right arrow"}, "mathspeak": {"sbrief": "heavy round tipped R arrow"}}}, {"key": "279D", "mappings": {"default": {"default": "triangle headed right arrow"}, "mathspeak": {"sbrief": "triangle headed R arrow"}}}, {"key": "279E", "mappings": {"default": {"default": "heavy triangle headed right arrow"}, "mathspeak": {"sbrief": "heavy triangle headed R arrow"}}}, {"key": "279F", "mappings": {"default": {"default": "dashed triangle headed right arrow"}, "mathspeak": {"sbrief": "dashed triangle headed R arrow"}}}, {"key": "27A0", "mappings": {"default": {"default": "heavy dashed triangle headed right arrow"}, "mathspeak": {"sbrief": "heavy dashed triangle headed R arrow"}}}, {"key": "27A1", "mappings": {"default": {"default": "black right arrow"}, "mathspeak": {"sbrief": "black R arrow"}}}, {"key": "27A2", "mappings": {"default": {"default": "three d top lighted right arrowhead"}, "mathspeak": {"sbrief": "three d top lighted R arrowhead"}}}, {"key": "27A3", "mappings": {"default": {"default": "three d bottom lighted right arrowhead"}, "mathspeak": {"sbrief": "three d bottom lighted R arrowhead"}}}, {"key": "27A4", "mappings": {"default": {"default": "black right arrowhead"}, "mathspeak": {"sbrief": "black R arrowhead"}}}, {"key": "27A5", "mappings": {"default": {"default": "heavy black curved down and right arrow"}, "mathspeak": {"sbrief": "heavy black curved D and R arrow"}}}, {"key": "27A6", "mappings": {"default": {"default": "heavy black curved up and right arrow"}, "mathspeak": {"sbrief": "heavy black curved U and R arrow"}}}, {"key": "27A7", "mappings": {"default": {"default": "squat black right arrow"}, "mathspeak": {"sbrief": "squat black R arrow"}}}, {"key": "27A8", "mappings": {"default": {"default": "heavy concave pointed black right arrow"}, "mathspeak": {"sbrief": "heavy concave pointed black R arrow"}}}, {"key": "27A9", "mappings": {"default": {"default": "right shaded white right arrow"}, "mathspeak": {"sbrief": "right shaded white R arrow"}}}, {"key": "27AA", "mappings": {"default": {"default": "left shaded white right arrow"}, "mathspeak": {"sbrief": "left shaded white R arrow"}}}, {"key": "27AB", "mappings": {"default": {"default": "back tilted shadowed white right arrow"}, "mathspeak": {"sbrief": "back tilted shadowed white R arrow"}}}, {"key": "27AC", "mappings": {"default": {"default": "front tilted shadowed white right arrow"}, "mathspeak": {"sbrief": "front tilted shadowed white R arrow"}}}, {"key": "27AD", "mappings": {"default": {"default": "heavy lower right shadowed white right arrow"}, "mathspeak": {"sbrief": "heavy lower right shadowed white R arrow"}}}, {"key": "27AE", "mappings": {"default": {"default": "heavy upper right shadowed white right arrow"}, "mathspeak": {"sbrief": "heavy upper right shadowed white R arrow"}}}, {"key": "27AF", "mappings": {"default": {"default": "notched lower right shadowed white right arrow"}, "mathspeak": {"sbrief": "notched lower right shadowed white R arrow"}}}, {"key": "27B1", "mappings": {"default": {"default": "notched upper right shadowed white right arrow"}, "mathspeak": {"sbrief": "notched upper right shadowed white R arrow"}}}, {"key": "27B2", "mappings": {"default": {"default": "circled heavy white right arrow"}, "mathspeak": {"sbrief": "circled heavy white R arrow"}}}, {"key": "27B3", "mappings": {"default": {"default": "white feathered right arrow"}, "mathspeak": {"sbrief": "white feathered R arrow"}}}, {"key": "27B4", "mappings": {"default": {"default": "black feathered down right arrow"}, "mathspeak": {"sbrief": "black feathered D R arrow"}}}, {"key": "27B5", "mappings": {"default": {"default": "black feathered right arrow"}, "mathspeak": {"sbrief": "black feathered R arrow"}}}, {"key": "27B6", "mappings": {"default": {"default": "black feathered up right arrow"}, "mathspeak": {"sbrief": "black feathered U R arrow"}}}, {"key": "27B7", "mappings": {"default": {"default": "heavy black feathered down right arrow"}, "mathspeak": {"sbrief": "heavy black feathered D R arrow"}}}, {"key": "27B8", "mappings": {"default": {"default": "heavy black feathered right arrow"}, "mathspeak": {"sbrief": "heavy black feathered R arrow"}}}, {"key": "27B9", "mappings": {"default": {"default": "heavy black feathered up right arrow"}, "mathspeak": {"sbrief": "heavy black feathered U R arrow"}}}, {"key": "27BA", "mappings": {"default": {"default": "teardrop barbed right arrow"}, "mathspeak": {"sbrief": "teardrop barbed R arrow"}}}, {"key": "27BB", "mappings": {"default": {"default": "heavy teardrop shanked right arrow"}, "mathspeak": {"sbrief": "heavy teardrop shanked R arrow"}}}, {"key": "27BC", "mappings": {"default": {"default": "wedge tailed right arrow"}, "mathspeak": {"sbrief": "wedge tailed R arrow"}}}, {"key": "27BD", "mappings": {"default": {"default": "heavy wedge tailed right arrow"}, "mathspeak": {"sbrief": "heavy wedge tailed R arrow"}}}, {"key": "27BE", "mappings": {"default": {"default": "open outlined right arrow"}, "mathspeak": {"sbrief": "open outlined R arrow"}}}, {"key": "27F0", "mappings": {"default": {"default": "up quadruple arrow"}, "mathspeak": {"sbrief": "U quadruple arrow"}}}, {"key": "27F1", "mappings": {"default": {"default": "down quadruple arrow"}, "mathspeak": {"sbrief": "D quadrule arrow"}}}, {"key": "27F2", "mappings": {"default": {"default": "anticlockwise gapped circle arrow"}}}, {"key": "27F3", "mappings": {"default": {"default": "clockwise gapped circle arrow"}}}, {"key": "27F4", "mappings": {"default": {"default": "right arrow with circled plus"}, "mathspeak": {"sbrief": "R arrow with circled plus"}}}, {"key": "27F5", "mappings": {"default": {"default": "long left arrow"}, "mathspeak": {"sbrief": "long L arrow"}}}, {"key": "27F6", "mappings": {"default": {"default": "long right arrow"}, "mathspeak": {"sbrief": "long R arrow"}}}, {"key": "27F7", "mappings": {"default": {"default": "long left right arrow"}, "mathspeak": {"sbrief": "long L R arrow"}}}, {"key": "27F8", "mappings": {"default": {"default": "long left double arrow"}, "mathspeak": {"sbrief": "long l double arrow"}}}, {"key": "27F9", "mappings": {"default": {"default": "long right double arrow"}, "mathspeak": {"sbrief": "long R double arrow"}}}, {"key": "27FA", "mappings": {"default": {"default": "long left right double arrow"}, "mathspeak": {"sbrief": "long L R double arrow"}}}, {"key": "27FB", "mappings": {"default": {"default": "long left arrow from bar"}, "mathspeak": {"sbrief": "long L arrow from bar"}}}, {"key": "27FC", "mappings": {"default": {"default": "long right arrow from bar"}, "mathspeak": {"sbrief": "long R arrow from bar"}}}, {"key": "27FD", "mappings": {"default": {"default": "long left double arrow from bar"}, "mathspeak": {"sbrief": "long l double arrow from bar"}}}, {"key": "27FE", "mappings": {"default": {"default": "long right double arrow from bar"}, "mathspeak": {"sbrief": "long R double arrow from bar"}}}, {"key": "27FF", "mappings": {"default": {"default": "long right squiggle arrow"}, "mathspeak": {"sbrief": "long r squiggle arrow"}}}, {"key": "2900", "mappings": {"default": {"default": "two headed right arrow with vertical stroke"}, "mathspeak": {"sbrief": "two headed R arrow with vertical stroke"}}}, {"key": "2901", "mappings": {"default": {"default": "two headed right arrow with double vertical stroke"}, "mathspeak": {"sbrief": "two headed R arrow with double vertical stroke"}}}, {"key": "2902", "mappings": {"default": {"default": "double left arrow with vertical stroke"}, "mathspeak": {"sbrief": "double L arrow with vertical stroke"}}}, {"key": "2903", "mappings": {"default": {"default": "double right arrow with vertical stroke"}, "mathspeak": {"sbrief": "double R arrow with vertical stroke"}}}, {"key": "2904", "mappings": {"default": {"default": "double left right arrow with vertical stroke"}, "mathspeak": {"sbrief": "double L R arrow with vertical stroke"}}}, {"key": "2905", "mappings": {"default": {"default": "two headed right arrow from bar"}, "mathspeak": {"sbrief": "two headed R arrow from bar"}}}, {"key": "2906", "mappings": {"default": {"default": "double left arrow from bar"}, "mathspeak": {"sbrief": "double L arrow from bar"}}}, {"key": "2907", "mappings": {"default": {"default": "double right arrow from bar"}, "mathspeak": {"sbrief": "double R arrow from bar"}}}, {"key": "2908", "mappings": {"default": {"default": "arrow down with horizontal stroke"}}}, {"key": "2909", "mappings": {"default": {"default": "up arrow with horizontal stroke"}, "mathspeak": {"sbrief": "U arrow with horizontal stroke"}}}, {"key": "290A", "mappings": {"default": {"default": "up triple arrow"}, "mathspeak": {"sbrief": "U triple arrow"}}}, {"key": "290B", "mappings": {"default": {"default": "down triple arrow"}, "mathspeak": {"sbrief": "D triple arrow"}}}, {"key": "290C", "mappings": {"default": {"default": "left double dash arrow"}, "mathspeak": {"sbrief": "L double dash arrow"}}}, {"key": "290D", "mappings": {"default": {"default": "right double dash arrow"}, "mathspeak": {"sbrief": "R double dash arrow"}}}, {"key": "290E", "mappings": {"default": {"default": "left triple dash arrow"}, "mathspeak": {"sbrief": "L triple dash arrow"}}}, {"key": "290F", "mappings": {"default": {"default": "right triple dash arrow"}, "mathspeak": {"sbrief": "r triple dash arrow"}}}, {"key": "2910", "mappings": {"default": {"default": "right two headed triple dash arrow"}, "mathspeak": {"sbrief": "r two headed triple dash arrow"}}}, {"key": "2911", "mappings": {"default": {"default": "right arrow with dotted stem"}, "mathspeak": {"sbrief": "R arrow with dotted stem"}}}, {"key": "2912", "mappings": {"default": {"default": "up arrow to bar"}, "mathspeak": {"sbrief": "U arrow to bar"}}}, {"key": "2913", "mappings": {"default": {"default": "down arrow to bar"}, "mathspeak": {"sbrief": "D arrow to bar"}}}, {"key": "2914", "mappings": {"default": {"default": "right arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "R arrow with tail with vertical stroke"}}}, {"key": "2915", "mappings": {"default": {"default": "right arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "R arrow with tail with double vertical stroke"}}}, {"key": "2916", "mappings": {"default": {"default": "right two headed arrow with tail"}, "mathspeak": {"sbrief": "R two headed arrow with tail"}}}, {"key": "2917", "mappings": {"default": {"default": "right two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "R two headed arrow with tail with vertical stroke"}}}, {"key": "2918", "mappings": {"default": {"default": "right two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "R two headed arrow with tail with double vertical stroke"}}}, {"key": "2919", "mappings": {"default": {"default": "left arrow tail"}, "mathspeak": {"sbrief": "L arrow tail"}}}, {"key": "291A", "mappings": {"default": {"default": "right arrow tail"}, "mathspeak": {"sbrief": "R arrow tail"}}}, {"key": "291B", "mappings": {"default": {"default": "left double arrow tail"}, "mathspeak": {"sbrief": "L double arrow tail"}}}, {"key": "291C", "mappings": {"default": {"default": "right double arrow tail"}, "mathspeak": {"sbrief": "R double arrow tail"}}}, {"key": "291D", "mappings": {"default": {"default": "left arrow to black diamond"}, "mathspeak": {"sbrief": "L arrow to black diamond"}}}, {"key": "291E", "mappings": {"default": {"default": "right arrow to black diamond"}, "mathspeak": {"sbrief": "R arrow to black diamond"}}}, {"key": "291F", "mappings": {"default": {"default": "left arrow from bar to black diamond"}, "mathspeak": {"sbrief": "L arrow from bar to black diamond"}}}, {"key": "2920", "mappings": {"default": {"default": "right arrow from bar to black diamond"}, "mathspeak": {"sbrief": "R arrow from bar to black diamond"}}}, {"key": "2921", "mappings": {"default": {"default": "north west and south east arrow"}}}, {"key": "2922", "mappings": {"default": {"default": "north east and south west arrow"}}}, {"key": "2923", "mappings": {"default": {"default": "north west arrow with hook"}}}, {"key": "2924", "mappings": {"default": {"default": "north east arrow with hook"}}}, {"key": "2925", "mappings": {"default": {"default": "south east arrow with hook"}}}, {"key": "2926", "mappings": {"default": {"default": "south west arrow with hook"}}}, {"key": "2927", "mappings": {"default": {"default": "north west arrow and north east arrow"}}}, {"key": "2928", "mappings": {"default": {"default": "north east arrow and south east arrow"}}}, {"key": "2929", "mappings": {"default": {"default": "south east arrow and south west arrow"}}}, {"key": "292A", "mappings": {"default": {"default": "south west arrow and north west arrow"}}}, {"key": "292D", "mappings": {"default": {"default": "south east arrow crossing north east arrow"}}}, {"key": "292E", "mappings": {"default": {"default": "north east arrow crossing south east arrow"}}}, {"key": "292F", "mappings": {"default": {"default": "falling diagonal crossing north east arrow"}}}, {"key": "2930", "mappings": {"default": {"default": "rising diagonal crossing south east arrow"}}}, {"key": "2931", "mappings": {"default": {"default": "north east arrow crossing north west arrow"}}}, {"key": "2932", "mappings": {"default": {"default": "north west arrow crossing north east arrow"}}}, {"key": "2933", "mappings": {"default": {"default": "wave arrow pointing directly right"}}}, {"key": "2934", "mappings": {"default": {"default": "arrow pointing right then curving up"}}}, {"key": "2935", "mappings": {"default": {"default": "arrow pointing right then curving down"}}}, {"key": "2936", "mappings": {"default": {"default": "arrow pointing down then curving left"}}}, {"key": "2937", "mappings": {"default": {"default": "arrow pointing down then curving right"}}}, {"key": "2938", "mappings": {"default": {"default": "right side arc clockwise arrow"}, "mathspeak": {"sbrief": "R side arc clockwise arrow"}}}, {"key": "2939", "mappings": {"default": {"default": "left side arc anticlockwise arrow"}, "mathspeak": {"sbrief": "L side arc anticlockwise arrow"}}}, {"key": "293A", "mappings": {"default": {"default": "top arc anticlockwise arrow"}}}, {"key": "293B", "mappings": {"default": {"default": "bottom arc anticlockwise arrow"}}}, {"key": "293C", "mappings": {"default": {"default": "top arc clockwise arrow with minus"}}}, {"key": "293D", "mappings": {"default": {"default": "top arc anticlockwise arrow with plus"}}}, {"key": "293E", "mappings": {"default": {"default": "down right semicircular clockwise arrow"}, "mathspeak": {"sbrief": "D R semicircular clockwise arrow"}}}, {"key": "293F", "mappings": {"default": {"default": "down left semicircular anticlockwise arrow"}, "mathspeak": {"sbrief": "D L semicircular anticlockwise arrow"}}}, {"key": "2940", "mappings": {"default": {"default": "anticlockwise closed circle arrow"}}}, {"key": "2941", "mappings": {"default": {"default": "clockwise closed circle arrow"}}}, {"key": "2942", "mappings": {"default": {"default": "right arrow above short left arrow"}, "mathspeak": {"sbrief": "R arrow above short L arrow"}}}, {"key": "2943", "mappings": {"default": {"default": "left arrow above short right arrow"}, "mathspeak": {"sbrief": "L arrow above short R arrow"}}}, {"key": "2944", "mappings": {"default": {"default": "short right arrow above left arrow"}, "mathspeak": {"sbrief": "short R arrow above L arrow"}}}, {"key": "2945", "mappings": {"default": {"default": "right arrow with plus below"}, "mathspeak": {"sbrief": "R arrow with plus below"}}}, {"key": "2946", "mappings": {"default": {"default": "left arrow with plus below"}, "mathspeak": {"sbrief": "L arrow with plus below"}}}, {"key": "2947", "mappings": {"default": {"default": "right arrow through x"}, "mathspeak": {"sbrief": "R arrow through x"}}}, {"key": "2948", "mappings": {"default": {"default": "left right arrow through small circle"}, "mathspeak": {"sbrief": "L R arrow through small circle"}}}, {"key": "2949", "mappings": {"default": {"default": "up two headed arrow from small circle"}, "mathspeak": {"sbrief": "U two headed arrow from small circle"}}}, {"key": "2970", "mappings": {"default": {"default": "right double arrow with rounded head"}, "mathspeak": {"sbrief": "R double arrow with rounded head"}}}, {"key": "2971", "mappings": {"default": {"default": "equals sign above right arrow"}, "mathspeak": {"sbrief": "equals sign above R arrow"}}}, {"key": "2972", "mappings": {"default": {"default": "tilde operator above right arrow"}, "mathspeak": {"sbrief": "tilde operator above R arrow"}}}, {"key": "2973", "mappings": {"default": {"default": "left arrow above tilde operator"}, "mathspeak": {"sbrief": "L arrow above tilde operator"}}}, {"key": "2974", "mappings": {"default": {"default": "right arrow above tilde operator"}, "mathspeak": {"sbrief": "R arrow above tilde operator"}}}, {"key": "2975", "mappings": {"default": {"default": "right arrow above almost equals"}, "mathspeak": {"sbrief": "R arrow above almost equals"}}}, {"key": "2976", "mappings": {"default": {"default": "less than above left arrow"}, "mathspeak": {"sbrief": "less than above L arrow"}}}, {"key": "2977", "mappings": {"default": {"default": "left arrow through less than"}, "mathspeak": {"sbrief": "L arrow through less than"}}}, {"key": "2978", "mappings": {"default": {"default": "greater than above right arrow"}, "mathspeak": {"sbrief": "greater than above R arrow"}}}, {"key": "2979", "mappings": {"default": {"default": "subset above right arrow"}, "mathspeak": {"sbrief": "subset above R arrow"}}}, {"key": "297A", "mappings": {"default": {"default": "left arrow through subset"}, "mathspeak": {"sbrief": "L arrow through subset"}}}, {"key": "297B", "mappings": {"default": {"default": "superset above left arrow"}, "mathspeak": {"sbrief": "suerset above L arrow"}}}, {"key": "29B3", "mappings": {"default": {"default": "empty set with right arrow above"}, "mathspeak": {"sbrief": "empty set with R arrow above"}}}, {"key": "29B4", "mappings": {"default": {"default": "empty set with left arrow above"}, "mathspeak": {"sbrief": "empty set with L arrow above"}}}, {"key": "29BD", "mappings": {"default": {"default": "up arrow through circle"}, "mathspeak": {"sbrief": "U arrow through circle"}}}, {"key": "29EA", "mappings": {"default": {"default": "black diamond with down arrow"}, "mathspeak": {"sbrief": "black diamond with D arrow"}}}, {"key": "29EC", "mappings": {"default": {"default": "white circle with down arrow"}, "mathspeak": {"sbrief": "white circle with D arrow"}}}, {"key": "29ED", "mappings": {"default": {"default": "black circle with down arrow"}, "mathspeak": {"sbrief": "black circle with D arrow"}}}, {"key": "2A17", "mappings": {"default": {"default": "integral with left arrow with hook"}, "mathspeak": {"sbrief": "integral with L arrow with hook"}}}, {"key": "2B00", "mappings": {"default": {"default": "north east white arrow"}}}, {"key": "2B01", "mappings": {"default": {"default": "north west white arrow"}}}, {"key": "2B02", "mappings": {"default": {"default": "south east white arrow"}}}, {"key": "2B03", "mappings": {"default": {"default": "south west white arrow"}}}, {"key": "2B04", "mappings": {"default": {"default": "left right white arrow"}, "mathspeak": {"sbrief": "L R white arrow"}}}, {"key": "2B05", "mappings": {"default": {"default": "left black arrow"}, "mathspeak": {"sbrief": "L black arrow"}}}, {"key": "2B06", "mappings": {"default": {"default": "up black arrow"}, "mathspeak": {"sbrief": "U black arrow"}}}, {"key": "2B07", "mappings": {"default": {"default": "down black arrow"}, "mathspeak": {"sbrief": "D black arrow"}}}, {"key": "2B08", "mappings": {"default": {"default": "north east black arrow"}}}, {"key": "2B09", "mappings": {"default": {"default": "north west black arrow"}}}, {"key": "2B0A", "mappings": {"default": {"default": "south east black arrow"}}}, {"key": "2B0B", "mappings": {"default": {"default": "south west black arrow"}}}, {"key": "2B0C", "mappings": {"default": {"default": "left right black arrow"}, "mathspeak": {"sbrief": "L R black arrow"}}}, {"key": "2B0D", "mappings": {"default": {"default": "up down black arrow"}, "mathspeak": {"sbrief": "U D black arrow"}}}, {"key": "2B0E", "mappings": {"default": {"default": "right arrow with tip down"}, "mathspeak": {"sbrief": "R arrow with tip down"}}}, {"key": "2B0F", "mappings": {"default": {"default": "right arrow with tip up"}, "mathspeak": {"sbrief": "R arrow with tip up"}}}, {"key": "2B10", "mappings": {"default": {"default": "left arrow with tip down"}, "mathspeak": {"sbrief": "L arrow with tip down"}}}, {"key": "2B11", "mappings": {"default": {"default": "left arrow with tip up"}, "mathspeak": {"sbrief": "L arrow with tip up"}}}, {"key": "2B30", "mappings": {"default": {"default": "left arrow with small circle"}, "mathspeak": {"sbrief": "L arrow with small circle"}}}, {"key": "2B31", "mappings": {"default": {"default": "three left arrows"}, "mathspeak": {"sbrief": "three L arrows"}}}, {"key": "2B32", "mappings": {"default": {"default": "left arrow with circled plus"}, "mathspeak": {"sbrief": "L arrow with circled plus"}}}, {"key": "2B33", "mappings": {"default": {"default": "long left squiggle arrow"}, "mathspeak": {"sbrief": "long l squiggle arrow"}}}, {"key": "2B34", "mappings": {"default": {"default": "left two headed arrow with vertical stroke"}, "mathspeak": {"sbrief": "L two headed arrow with vertical stroke"}}}, {"key": "2B35", "mappings": {"default": {"default": "left two headed arrow with double vertical stroke"}, "mathspeak": {"sbrief": "L two headed arrow with double vertical stroke"}}}, {"key": "2B36", "mappings": {"default": {"default": "left two headed arrow from bar"}, "mathspeak": {"sbrief": "L two headed arrow from bar"}}}, {"key": "2B37", "mappings": {"default": {"default": "left two headed triple dash arrow"}, "mathspeak": {"sbrief": "L two headed triple dash arrow"}}}, {"key": "2B38", "mappings": {"default": {"default": "left arrow with dotted stem"}, "mathspeak": {"sbrief": "L arrow with dotted stem"}}}, {"key": "2B39", "mappings": {"default": {"default": "left arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "L arrow with tail with vertical stroke"}}}, {"key": "2B3A", "mappings": {"default": {"default": "left arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "L arrow with tail with double vertical stroke"}}}, {"key": "2B3B", "mappings": {"default": {"default": "left two headed arrow with tail"}, "mathspeak": {"sbrief": "L two headed arrow with tail"}}}, {"key": "2B3C", "mappings": {"default": {"default": "left two headed arrow with tail with vertical stroke"}, "mathspeak": {"sbrief": "L two headed arrow with tail with vertical stroke"}}}, {"key": "2B3D", "mappings": {"default": {"default": "left two headed arrow with tail with double vertical stroke"}, "mathspeak": {"sbrief": "L two headed arrow with tail with double vertical stroke"}}}, {"key": "2B3E", "mappings": {"default": {"default": "left arrow through x"}, "mathspeak": {"sbrief": "L arrow through x"}}}, {"key": "2B3F", "mappings": {"default": {"default": "wave arrow pointing directly left"}}}, {"key": "2B40", "mappings": {"default": {"default": "equals sign above left arrow"}, "mathspeak": {"sbrief": "equals sign above L arrow"}}}, {"key": "2B41", "mappings": {"default": {"default": "reverse tilde operator above left arrow"}, "mathspeak": {"sbrief": "reverse tilde operator above L arrow"}}}, {"key": "2B42", "mappings": {"default": {"default": "left arrow above reverse almost equals"}, "mathspeak": {"sbrief": "L arrow above reverse almost equals"}}}, {"key": "2B43", "mappings": {"default": {"default": "right arrow through greater than"}, "mathspeak": {"sbrief": "R arrow through greater than"}}}, {"key": "2B44", "mappings": {"default": {"default": "right arrow through superset"}, "mathspeak": {"sbrief": "R arrow through superset"}}}, {"key": "2B45", "mappings": {"default": {"default": "left quadruple arrow"}, "mathspeak": {"sbrief": "L quadrule arrow"}}}, {"key": "2B46", "mappings": {"default": {"default": "right quadruple arrow"}, "mathspeak": {"sbrief": "R quadrule arrow"}}}, {"key": "2B47", "mappings": {"default": {"default": "reverse tilde operator above right arrow"}, "mathspeak": {"sbrief": "reverse tilde operator above R arrow"}}}, {"key": "2B48", "mappings": {"default": {"default": "right arrow above reverse almost equals"}, "mathspeak": {"sbrief": "R arrow above reverse almost equals"}}}, {"key": "2B49", "mappings": {"default": {"default": "tilde operator above left arrow"}, "mathspeak": {"sbrief": "tilde operator above L arrow"}}}, {"key": "2B4A", "mappings": {"default": {"default": "left arrow above almost equals"}, "mathspeak": {"sbrief": "L arrow above almost equals"}}}, {"key": "2B4B", "mappings": {"default": {"default": "left arrow above reverse tilde operator"}, "mathspeak": {"sbrief": "L arrow above reverse tilde operator"}}}, {"key": "2B4C", "mappings": {"default": {"default": "right arrow above reverse tilde operator"}, "mathspeak": {"sbrief": "R arrow above reverse tilde operator"}}}, {"key": "FFE9", "mappings": {"default": {"default": "halfwidth left arrow"}, "mathspeak": {"sbrief": "halfwidth L arrow"}}}, {"key": "FFEA", "mappings": {"default": {"default": "halfwidth up arrow"}, "mathspeak": {"sbrief": "halfwidth U arrow"}}}, {"key": "FFEB", "mappings": {"default": {"default": "halfwidth right arrow"}, "mathspeak": {"sbrief": "halfwidth R arrow"}}}, {"key": "FFEC", "mappings": {"default": {"default": "halfwidth down arrow"}, "mathspeak": {"sbrief": "halfwidth D arrow"}}}], "en/symbols/math_characters.min": [{"locale": "en"}, {"key": "2113", "mappings": {"default": {"default": "script l"}}}, {"key": "2118", "mappings": {"default": {"default": "script cap P"}, "mathspeak": {"default": "script upper P"}}}, {"key": "213C", "mappings": {"default": {"default": "double struck pi"}}}, {"key": "213D", "mappings": {"default": {"default": "double struck gamma"}}}, {"key": "213E", "mappings": {"default": {"default": "double struck cap Gamma"}, "mathspeak": {"default": "double struck upper Gamma"}}}, {"key": "213F", "mappings": {"default": {"default": "double struck cap Pi"}, "mathspeak": {"default": "double struck upper Pi"}}}, {"key": "2140", "mappings": {"default": {"default": "double struck sum"}, "mathspeak": {"default": "double struck sigma summation"}}}, {"key": "2145", "mappings": {"default": {"default": "double struck italic cap D"}, "mathspeak": {"default": "double struck italic upper D"}}}, {"key": "2146", "mappings": {"default": {"default": "double struck italic d"}}}, {"key": "2147", "mappings": {"default": {"default": "double struck italic e"}}}, {"key": "2148", "mappings": {"default": {"default": "double struck italic i"}}}, {"key": "2149", "mappings": {"default": {"default": "double struck italic j"}}}, {"key": "1D6A4", "mappings": {"default": {"default": "italic dotless i"}}}, {"key": "1D6A5", "mappings": {"default": {"default": "italic dotless j"}}}], "en/symbols/math_delimiters.min": [{"locale": "en"}, {"key": "0028", "mappings": {"default": {"default": "left parenthesis"}, "mathspeak": {"brief": "left p'ren", "sbrief": "L p'ren"}, "clearspeak": {"default": "open paren"}}}, {"key": "0029", "mappings": {"default": {"default": "right parenthesis"}, "mathspeak": {"brief": "right p'ren", "sbrief": "R p'ren"}, "clearspeak": {"default": "close paren"}}}, {"key": "005B", "mappings": {"default": {"default": "left bracket"}, "mathspeak": {"brief": "left brack", "sbrief": "L brack"}, "clearspeak": {"default": "open bracket"}}}, {"key": "005D", "mappings": {"default": {"default": "right bracket"}, "mathspeak": {"brief": "right brack", "sbrief": "R brack"}, "clearspeak": {"default": "close bracket"}}}, {"key": "007B", "mappings": {"default": {"default": "left brace"}, "mathspeak": {"sbrief": "L brace"}, "clearspeak": {"default": "open brace"}}}, {"key": "007D", "mappings": {"default": {"default": "right brace"}, "mathspeak": {"sbrief": "R brace"}, "clearspeak": {"default": "close brace"}}}, {"key": "2045", "mappings": {"default": {"default": "left bracket with quill"}, "mathspeak": {"brief": "left brack with quill", "sbrief": "L brack with quill"}}}, {"key": "2046", "mappings": {"default": {"default": "right bracket with quill"}, "mathspeak": {"brief": "right brack with quill", "sbrief": "R brack with quill"}}}, {"key": "2308", "mappings": {"default": {"default": "left ceiling"}}}, {"key": "2309", "mappings": {"default": {"default": "right ceiling"}}}, {"key": "230A", "mappings": {"default": {"default": "left floor"}}}, {"key": "230B", "mappings": {"default": {"default": "right floor"}}}, {"key": "230C", "mappings": {"default": {"default": "bottom right crop"}}}, {"key": "230D", "mappings": {"default": {"default": "bottom left crop"}}}, {"key": "230E", "mappings": {"default": {"default": "top right crop"}}}, {"key": "230F", "mappings": {"default": {"default": "top left crop"}}}, {"key": "231C", "mappings": {"default": {"default": "top left corner"}}}, {"key": "231D", "mappings": {"default": {"default": "top right corner"}}}, {"key": "231E", "mappings": {"default": {"default": "bottom left corner"}}}, {"key": "231F", "mappings": {"default": {"default": "bottom right corner"}}}, {"key": "2320", "mappings": {"default": {"default": "top half integral"}}}, {"key": "2321", "mappings": {"default": {"default": "bottom half integral"}}}, {"key": "2329", "mappings": {"default": {"default": "left pointing angle bracket", "physics": "bra"}}}, {"key": "232A", "mappings": {"default": {"default": "right pointing angle bracket", "physics": "ket"}}}, {"key": "239B", "mappings": {"default": {"default": "left parenthesis upper hook"}, "mathspeak": {"brief": "left p'ren upper hook", "sbrief": "L p'ren upper hook"}}}, {"key": "239C", "mappings": {"default": {"default": "left parenthesis extension"}, "mathspeak": {"brief": "left p'ren extension", "sbrief": "L p'ren extension"}}}, {"key": "239D", "mappings": {"default": {"default": "left parenthesis lower hook"}, "mathspeak": {"brief": "left p'ren lower hook", "sbrief": "L p'ren lower hook"}}}, {"key": "239E", "mappings": {"default": {"default": "right parenthesis upper hook"}, "mathspeak": {"brief": "right p'ren upper hook", "sbrief": "R p'ren upper hook"}}}, {"key": "239F", "mappings": {"default": {"default": "right parenthesis extension"}, "mathspeak": {"brief": "right p'ren extension", "sbrief": "R p'ren extension"}}}, {"key": "23A0", "mappings": {"default": {"default": "right parenthesis lower hook"}, "mathspeak": {"brief": "right p'ren lower hook", "sbrief": "R p'ren lower hook"}}}, {"key": "23A1", "mappings": {"default": {"default": "left bracket upper corner"}, "mathspeak": {"brief": "left brack upper corner", "sbrief": "L brack upper corner"}}}, {"key": "23A2", "mappings": {"default": {"default": "left bracket extension"}, "mathspeak": {"brief": "left brack extension", "sbrief": "L brack extension"}}}, {"key": "23A3", "mappings": {"default": {"default": "left bracket lower corner"}, "mathspeak": {"brief": "left brack lower corner", "sbrief": "L brack lower corner"}}}, {"key": "23A4", "mappings": {"default": {"default": "right bracket upper corner"}, "mathspeak": {"brief": "right brack upper corner", "sbrief": "R brack upper corner"}}}, {"key": "23A5", "mappings": {"default": {"default": "right bracket extension"}, "mathspeak": {"brief": "right brack extension", "sbrief": "R brack extension"}}}, {"key": "23A6", "mappings": {"default": {"default": "right bracket lower corner"}, "mathspeak": {"brief": "right brack lower corner", "sbrief": "R brack lower corner"}}}, {"key": "23A7", "mappings": {"default": {"default": "left brace upper hook"}, "mathspeak": {"sbrief": "L brace upper hook"}}}, {"key": "23A8", "mappings": {"default": {"default": "left brace middle piece"}, "mathspeak": {"sbrief": "L brace middle piece"}}}, {"key": "23A9", "mappings": {"default": {"default": "left brace lower hook"}, "mathspeak": {"sbrief": "L brace lower hook"}}}, {"key": "23AA", "mappings": {"default": {"default": "brace extension"}}}, {"key": "23AB", "mappings": {"default": {"default": "right brace upper hook"}, "mathspeak": {"sbrief": "R brace upper hook"}}}, {"key": "23AC", "mappings": {"default": {"default": "right brace middle piece"}, "mathspeak": {"sbrief": "R brace middle piece"}}}, {"key": "23AD", "mappings": {"default": {"default": "right brace lower hook"}, "mathspeak": {"sbrief": "R brace lower hook"}}}, {"key": "23AE", "mappings": {"default": {"default": "integral extension"}}}, {"key": "23AF", "mappings": {"default": {"default": "horizontal line extension"}}}, {"key": "23B0", "mappings": {"default": {"default": "upper left or lower right brace section"}}}, {"key": "23B1", "mappings": {"default": {"default": "upper right or lower left brace section"}}}, {"key": "23B2", "mappings": {"default": {"default": "summation top"}}}, {"key": "23B3", "mappings": {"default": {"default": "summation bottom"}}}, {"key": "23B4", "mappings": {"default": {"default": "top bracket"}, "mathspeak": {"brief": "top brack", "sbrief": "T brack"}}}, {"key": "23B5", "mappings": {"default": {"default": "bottom bracket"}, "mathspeak": {"brief": "bottom brack", "sbrief": "B brack"}}}, {"key": "23B6", "mappings": {"default": {"default": "bottom bracket over top bracket"}, "mathspeak": {"brief": "bottom brack over top brack", "sbrief": "B brack over T brack"}}}, {"key": "23B7", "mappings": {"default": {"default": "radical symbol bottom"}}}, {"key": "23B8", "mappings": {"default": {"default": "left vertical box line"}}}, {"key": "23B9", "mappings": {"default": {"default": "right vertical box line"}}}, {"key": "23DC", "mappings": {"default": {"default": "top parenthesis"}, "mathspeak": {"brief": "top p'ren", "sbrief": "t p'ren"}}}, {"key": "23DD", "mappings": {"default": {"default": "bottom parenthesis"}, "mathspeak": {"brief": "bottom p'ren", "sbrief": "b p'ren"}}}, {"key": "23DE", "mappings": {"default": {"default": "top brace"}, "mathspeak": {"sbrief": "T brace"}}}, {"key": "23DF", "mappings": {"default": {"default": "bottom brace"}, "mathspeak": {"sbrief": "B brace"}}}, {"key": "23E0", "mappings": {"default": {"default": "top tortoise shell bracket"}}}, {"key": "23E1", "mappings": {"default": {"default": "bottom tortoise shell bracket"}}}, {"key": "2768", "mappings": {"default": {"default": "medium left parenthesis ornament"}, "mathspeak": {"brief": "medium left p'ren ornament", "sbrief": "medium L p'ren ornament"}}}, {"key": "2769", "mappings": {"default": {"default": "medium right parenthesis ornament"}, "mathspeak": {"brief": "medium right p'ren ornament", "sbrief": "medium R p'ren ornament"}}}, {"key": "276A", "mappings": {"default": {"default": "medium flattened left parenthesis ornament"}, "mathspeak": {"brief": "medium flattened left p'ren ornament", "sbrief": "medium flattened L p'ren ornament"}}}, {"key": "276B", "mappings": {"default": {"default": "medium flattened right parenthesis ornament"}, "mathspeak": {"brief": "medium flattened right p'ren ornament", "sbrief": "medium flattened R p'ren ornament"}}}, {"key": "276C", "mappings": {"default": {"default": "medium left pointing angle bracket ornament"}}}, {"key": "276D", "mappings": {"default": {"default": "medium right pointing angle bracket ornament"}}}, {"key": "276E", "mappings": {"default": {"default": "heavy left pointing angle quotation mark ornament"}}}, {"key": "276F", "mappings": {"default": {"default": "heavy right pointing angle quotation mark ornament"}}}, {"key": "2770", "mappings": {"default": {"default": "heavy left pointing angle bracket ornament"}}}, {"key": "2771", "mappings": {"default": {"default": "heavy right pointing angle bracket ornament"}}}, {"key": "2772", "mappings": {"default": {"default": "light left tortoise shell bracket ornament"}}}, {"key": "2773", "mappings": {"default": {"default": "light right tortoise shell bracket ornament"}}}, {"key": "2774", "mappings": {"default": {"default": "medium left brace ornament"}, "mathspeak": {"sbrief": "medium L brace ornament"}}}, {"key": "2775", "mappings": {"default": {"default": "medium right brace ornament"}, "mathspeak": {"sbrief": "medium R brace ornament"}}}, {"key": "27C5", "mappings": {"default": {"default": "left s shaped bag delimiter"}}}, {"key": "27C6", "mappings": {"default": {"default": "right s shaped bag delimiter"}}}, {"key": "27E6", "mappings": {"default": {"default": "left white bracket"}}}, {"key": "27E7", "mappings": {"default": {"default": "right white bracket"}}}, {"key": "27E8", "mappings": {"default": {"default": "left angle bracket"}, "mathspeak": {"sbrief": "l angle bracket"}}}, {"key": "27E9", "mappings": {"default": {"default": "right angle bracket"}, "mathspeak": {"sbrief": "r angle bracket"}}}, {"key": "27EA", "mappings": {"default": {"default": "left double angle bracket"}}}, {"key": "27EB", "mappings": {"default": {"default": "right double angle bracket"}}}, {"key": "27EC", "mappings": {"default": {"default": "left white tortoise shell bracket"}}}, {"key": "27ED", "mappings": {"default": {"default": "right white tortoise shell bracket"}}}, {"key": "27EE", "mappings": {"default": {"default": "flattened left parenthesis"}, "mathspeak": {"brief": "flattened left p'ren", "sbrief": "flattened L p'ren"}}}, {"key": "27EF", "mappings": {"default": {"default": "flattened right parenthesis"}, "mathspeak": {"brief": "flattened right p'ren", "sbrief": "flattened R p'ren"}}}, {"key": "2983", "mappings": {"default": {"default": "left white brace"}}}, {"key": "2984", "mappings": {"default": {"default": "right white brace"}}}, {"key": "2985", "mappings": {"default": {"default": "white left parenthesis"}, "mathspeak": {"brief": "white left p'ren", "sbrief": "white L p'ren"}}}, {"key": "2986", "mappings": {"default": {"default": "white right parenthesis"}, "mathspeak": {"brief": "white right p'ren", "sbrief": "white R p'ren"}}}, {"key": "2987", "mappings": {"default": {"default": "z notation left image bracket"}}}, {"key": "2988", "mappings": {"default": {"default": "z notation right image bracket"}}}, {"key": "2989", "mappings": {"default": {"default": "z notation left binding bracket"}}}, {"key": "298A", "mappings": {"default": {"default": "z notation right binding bracket"}}}, {"key": "298B", "mappings": {"default": {"default": "left bracket with underbar"}, "mathspeak": {"brief": "left brack with underbar", "sbrief": "L brack with underbar"}}}, {"key": "298C", "mappings": {"default": {"default": "right bracket with underbar"}, "mathspeak": {"brief": "right brack with underbar", "sbrief": "R brack with underbar"}}}, {"key": "298D", "mappings": {"default": {"default": "left bracket with tick in top corner"}, "mathspeak": {"brief": "left brack with tick in top corner", "sbrief": "L brack with tick in top corner"}}}, {"key": "298E", "mappings": {"default": {"default": "right bracket with tick in bottom corner"}, "mathspeak": {"brief": "right brack with tick in bottom corner", "sbrief": "R brack with tick in bottom corner"}}}, {"key": "298F", "mappings": {"default": {"default": "left bracket with tick in bottom corner"}, "mathspeak": {"brief": "left brack with tick in bottom corner", "sbrief": "L brack with tick in bottom corner"}}}, {"key": "2990", "mappings": {"default": {"default": "right bracket with tick in top corner"}, "mathspeak": {"brief": "right brack with tick in top corner", "sbrief": "R brack with tick in top corner"}}}, {"key": "2991", "mappings": {"default": {"default": "left angle bracket with dot"}, "mathspeak": {"sbrief": "l angle bracket with dot"}}}, {"key": "2992", "mappings": {"default": {"default": "right angle bracket with dot"}, "mathspeak": {"sbrief": "r angle bracket with dot"}}}, {"key": "2993", "mappings": {"default": {"default": "left arc less than bracket"}}}, {"key": "2994", "mappings": {"default": {"default": "right arc greater than bracket"}}}, {"key": "2995", "mappings": {"default": {"default": "double left arc greater than bracket"}}}, {"key": "2996", "mappings": {"default": {"default": "double right arc less than bracket"}}}, {"key": "2997", "mappings": {"default": {"default": "left black tortoise shell bracket"}}}, {"key": "2998", "mappings": {"default": {"default": "right black tortoise shell bracket"}}}, {"key": "29D8", "mappings": {"default": {"default": "left wiggly fence"}}}, {"key": "29D9", "mappings": {"default": {"default": "right wiggly fence"}}}, {"key": "29DA", "mappings": {"default": {"default": "left double wiggly fence"}}}, {"key": "29DB", "mappings": {"default": {"default": "right double wiggly fence"}}}, {"key": "29FC", "mappings": {"default": {"default": "left pointing curved angle bracket"}}}, {"key": "29FD", "mappings": {"default": {"default": "right pointing curved angle bracket"}}}, {"key": "2E22", "mappings": {"default": {"default": "top half left bracket"}, "mathspeak": {"brief": "top half left brack", "sbrief": "top half L brack"}}}, {"key": "2E23", "mappings": {"default": {"default": "top half right bracket"}, "mathspeak": {"brief": "top half right brack", "sbrief": "top half R brack"}}}, {"key": "2E24", "mappings": {"default": {"default": "bottom half left bracket"}, "mathspeak": {"brief": "bottom half left brack", "sbrief": "bottom half L brack"}}}, {"key": "2E25", "mappings": {"default": {"default": "bottom half right bracket"}, "mathspeak": {"brief": "bottom half right brack", "sbrief": "bottom half R brack"}}}, {"key": "2E26", "mappings": {"default": {"default": "left sideways U bracket"}}}, {"key": "2E27", "mappings": {"default": {"default": "right sideways U bracket"}}}, {"key": "2E28", "mappings": {"default": {"default": "double left parenthesis"}, "mathspeak": {"brief": "double left p'ren", "sbrief": "double L p'ren"}}}, {"key": "2E29", "mappings": {"default": {"default": "double right parenthesis"}, "mathspeak": {"brief": "double right p'ren", "sbrief": "double R p'ren"}}}, {"key": "3008", "mappings": {"default": {"default": "left angle bracket"}, "mathspeak": {"sbrief": "l angle bracket"}}}, {"key": "3009", "mappings": {"default": {"default": "right angle bracket"}, "mathspeak": {"sbrief": "r angle bracket"}}}, {"key": "300A", "mappings": {"default": {"default": "left double angle bracket"}}}, {"key": "300B", "mappings": {"default": {"default": "right double angle bracket"}}}, {"key": "300C", "mappings": {"default": {"default": "left corner bracket"}}}, {"key": "300D", "mappings": {"default": {"default": "right corner bracket"}}}, {"key": "300E", "mappings": {"default": {"default": "left white corner bracket"}}}, {"key": "300F", "mappings": {"default": {"default": "right white corner bracket"}}}, {"key": "3010", "mappings": {"default": {"default": "left black lenticular bracket"}}}, {"key": "3011", "mappings": {"default": {"default": "right black lenticular bracket"}}}, {"key": "3014", "mappings": {"default": {"default": "left tortoise shell bracket"}}}, {"key": "3015", "mappings": {"default": {"default": "right tortoise shell bracket"}}}, {"key": "3016", "mappings": {"default": {"default": "left white lenticular bracket"}}}, {"key": "3017", "mappings": {"default": {"default": "right white lenticular bracket"}}}, {"key": "3018", "mappings": {"default": {"default": "left white tortoise shell bracket"}}}, {"key": "3019", "mappings": {"default": {"default": "right white tortoise shell bracket"}}}, {"key": "301A", "mappings": {"default": {"default": "left white bracket"}}}, {"key": "301B", "mappings": {"default": {"default": "right white bracket"}}}, {"key": "301D", "mappings": {"default": {"default": "reversed double prime quotation mark"}}}, {"key": "301E", "mappings": {"default": {"default": "double prime quotation mark"}}}, {"key": "301F", "mappings": {"default": {"default": "low double prime quotation mark"}}}, {"key": "FD3E", "mappings": {"default": {"default": "ornate left parenthesis"}, "mathspeak": {"brief": "ornate left p'ren", "sbrief": "ornate L p'ren"}}}, {"key": "FD3F", "mappings": {"default": {"default": "ornate right parenthesis"}, "mathspeak": {"brief": "ornate right p'ren", "sbrief": "ornate R p'ren"}}}, {"key": "FE17", "mappings": {"default": {"default": "presentation form for vertical left white lenticular bracket"}}}, {"key": "FE18", "mappings": {"default": {"default": "presentation form for vertical right white lenticular bracket"}}}, {"key": "FE35", "mappings": {"default": {"default": "presentation form for vertical left parenthesis"}, "mathspeak": {"brief": "presentation form for vertical left p'ren", "sbrief": "presentation form for vertical L p'ren"}}}, {"key": "FE36", "mappings": {"default": {"default": "presentation form for vertical right parenthesis"}, "mathspeak": {"brief": "presentation form for vertical right p'ren", "sbrief": "presentation form for vertical R p'ren"}}}, {"key": "FE37", "mappings": {"default": {"default": "presentation form for vertical left brace"}, "mathspeak": {"sbrief": "presentation form for vertical L brace"}}}, {"key": "FE38", "mappings": {"default": {"default": "presentation form for vertical right brace"}, "mathspeak": {"sbrief": "presentation form for vertical R brace"}}}, {"key": "FE39", "mappings": {"default": {"default": "presentation form for vertical left tortoise shell bracket"}}}, {"key": "FE3A", "mappings": {"default": {"default": "presentation form for vertical right tortoise shell bracket"}}}, {"key": "FE3B", "mappings": {"default": {"default": "presentation form for vertical left black lenticular bracket"}}}, {"key": "FE3C", "mappings": {"default": {"default": "presentation form for vertical right black lenticular bracket"}}}, {"key": "FE3D", "mappings": {"default": {"default": "presentation form for vertical left double angle bracket"}}}, {"key": "FE3E", "mappings": {"default": {"default": "presentation form for vertical right double angle bracket"}}}, {"key": "FE3F", "mappings": {"default": {"default": "presentation form for vertical left angle bracket"}, "mathspeak": {"sbrief": "presentation form for vertical L angle bracket"}}}, {"key": "FE40", "mappings": {"default": {"default": "presentation form for vertical right angle bracket"}, "mathspeak": {"sbrief": "presentation form for vertical R angle bracket"}}}, {"key": "FE41", "mappings": {"default": {"default": "presentation form for vertical left corner bracket"}}}, {"key": "FE42", "mappings": {"default": {"default": "presentation form for vertical right corner bracket"}}}, {"key": "FE43", "mappings": {"default": {"default": "presentation form for vertical left white corner bracket"}}}, {"key": "FE44", "mappings": {"default": {"default": "presentation form for vertical right white corner bracket"}}}, {"key": "FE47", "mappings": {"default": {"default": "presentation form for vertical left bracket"}, "mathspeak": {"brief": "presentation form for vertical left brack", "sbrief": "presentation form for vertical L brack"}}}, {"key": "FE48", "mappings": {"default": {"default": "presentation form for vertical right bracket"}, "mathspeak": {"brief": "presentation form for vertical right brack", "sbrief": "presentation form for vertical R brack"}}}, {"key": "FE59", "mappings": {"default": {"default": "small left parenthesis"}, "mathspeak": {"brief": "small left p'ren", "sbrief": "small L p'ren"}}}, {"key": "FE5A", "mappings": {"default": {"default": "small right parenthesis"}, "mathspeak": {"brief": "small right p'ren", "sbrief": "small R p'ren"}}}, {"key": "FE5B", "mappings": {"default": {"default": "small left brace"}, "mathspeak": {"sbrief": "small L brace"}}}, {"key": "FE5C", "mappings": {"default": {"default": "small right brace"}, "mathspeak": {"sbrief": "small R brace"}}}, {"key": "FE5D", "mappings": {"default": {"default": "small left tortoise shell bracket"}}}, {"key": "FE5E", "mappings": {"default": {"default": "small right tortoise shell bracket"}}}, {"key": "FF08", "mappings": {"default": {"default": "fullwidth left parenthesis"}, "mathspeak": {"brief": "fullwidth left p'ren", "sbrief": "fullwidth L p'ren"}}}, {"key": "FF09", "mappings": {"default": {"default": "fullwidth right parenthesis"}, "mathspeak": {"brief": "fullwidth right p'ren", "sbrief": "fullwidth R p'ren"}}}, {"key": "FF3B", "mappings": {"default": {"default": "fullwidth left bracket"}, "mathspeak": {"brief": "fullwidth left brack", "sbrief": "fullwidth L brack"}}}, {"key": "FF3D", "mappings": {"default": {"default": "fullwidth right bracket"}, "mathspeak": {"brief": "fullwidth right brack", "sbrief": "fullwidth R brack"}}}, {"key": "FF5B", "mappings": {"default": {"default": "fullwidth left brace"}, "mathspeak": {"sbrief": "fullwidth L brace"}}}, {"key": "FF5D", "mappings": {"default": {"default": "fullwidth right brace"}, "mathspeak": {"sbrief": "fullwidth R brace"}}}, {"key": "FF5F", "mappings": {"default": {"default": "fullwidth white left parenthesis"}, "mathspeak": {"brief": "fullwidth white left p'ren", "sbrief": "fullwidth white L p'ren"}}}, {"key": "FF60", "mappings": {"default": {"default": "fullwidth white right parenthesis"}, "mathspeak": {"brief": "fullwidth white right p'ren", "sbrief": "fullwidth white R p'ren"}}}, {"key": "FF62", "mappings": {"default": {"default": "halfwidth left corner bracket"}}}, {"key": "FF63", "mappings": {"default": {"default": "halfwidth right corner bracket"}}}], "en/symbols/math_geometry.min": [{"locale": "en"}, {"key": "2500", "mappings": {"default": {"default": "box drawings light horizontal"}}}, {"key": "2501", "mappings": {"default": {"default": "box drawings heavy horizontal"}}}, {"key": "2502", "mappings": {"default": {"default": "box drawings light vertical"}}}, {"key": "2503", "mappings": {"default": {"default": "box drawings heavy vertical"}}}, {"key": "2504", "mappings": {"default": {"default": "box drawings light triple dash horizontal"}}}, {"key": "2505", "mappings": {"default": {"default": "box drawings heavy triple dash horizontal"}}}, {"key": "2506", "mappings": {"default": {"default": "box drawings light triple dash vertical"}}}, {"key": "2507", "mappings": {"default": {"default": "box drawings heavy triple dash vertical"}}}, {"key": "2508", "mappings": {"default": {"default": "box drawings light quadruple dash horizontal"}}}, {"key": "2509", "mappings": {"default": {"default": "box drawings heavy quadruple dash horizontal"}}}, {"key": "250A", "mappings": {"default": {"default": "box drawings light quadruple dash vertical"}}}, {"key": "250B", "mappings": {"default": {"default": "box drawings heavy quadruple dash vertical"}}}, {"key": "250C", "mappings": {"default": {"default": "box drawings light down and right"}}}, {"key": "250D", "mappings": {"default": {"default": "box drawings down light and right heavy"}}}, {"key": "250E", "mappings": {"default": {"default": "box drawings down heavy and right light"}}}, {"key": "250F", "mappings": {"default": {"default": "box drawings heavy down and right"}}}, {"key": "2510", "mappings": {"default": {"default": "box drawings light down and left"}}}, {"key": "2511", "mappings": {"default": {"default": "box drawings down light and left heavy"}}}, {"key": "2512", "mappings": {"default": {"default": "box drawings down heavy and left light"}}}, {"key": "2513", "mappings": {"default": {"default": "box drawings heavy down and left"}}}, {"key": "2514", "mappings": {"default": {"default": "box drawings light up and right"}}}, {"key": "2515", "mappings": {"default": {"default": "box drawings up light and right heavy"}}}, {"key": "2516", "mappings": {"default": {"default": "box drawings up heavy and right light"}}}, {"key": "2517", "mappings": {"default": {"default": "box drawings heavy up and right"}}}, {"key": "2518", "mappings": {"default": {"default": "box drawings light up and left"}}}, {"key": "2519", "mappings": {"default": {"default": "box drawings up light and left heavy"}}}, {"key": "251A", "mappings": {"default": {"default": "box drawings up heavy and left light"}}}, {"key": "251B", "mappings": {"default": {"default": "box drawings heavy up and left"}}}, {"key": "251C", "mappings": {"default": {"default": "box drawings light vertical and right"}}}, {"key": "251D", "mappings": {"default": {"default": "box drawings vertical light and right heavy"}}}, {"key": "251E", "mappings": {"default": {"default": "box drawings up heavy and right down light"}}}, {"key": "251F", "mappings": {"default": {"default": "box drawings down heavy and right up light"}}}, {"key": "2520", "mappings": {"default": {"default": "box drawings vertical heavy and right light"}}}, {"key": "2521", "mappings": {"default": {"default": "box drawings down light and right up heavy"}}}, {"key": "2522", "mappings": {"default": {"default": "box drawings up light and right down heavy"}}}, {"key": "2523", "mappings": {"default": {"default": "box drawings heavy vertical and right"}}}, {"key": "2524", "mappings": {"default": {"default": "box drawings light vertical and left"}}}, {"key": "2525", "mappings": {"default": {"default": "box drawings vertical light and left heavy"}}}, {"key": "2526", "mappings": {"default": {"default": "box drawings up heavy and left down light"}}}, {"key": "2527", "mappings": {"default": {"default": "box drawings down heavy and left up light"}}}, {"key": "2528", "mappings": {"default": {"default": "box drawings vertical heavy and left light"}}}, {"key": "2529", "mappings": {"default": {"default": "box drawings down light and left up heavy"}}}, {"key": "252A", "mappings": {"default": {"default": "box drawings up light and left down heavy"}}}, {"key": "252B", "mappings": {"default": {"default": "box drawings heavy vertical and left"}}}, {"key": "252C", "mappings": {"default": {"default": "box drawings light down and horizontal"}}}, {"key": "252D", "mappings": {"default": {"default": "box drawings left heavy and right down light"}}}, {"key": "252E", "mappings": {"default": {"default": "box drawings right heavy and left down light"}}}, {"key": "252F", "mappings": {"default": {"default": "box drawings down light and horizontal heavy"}}}, {"key": "2530", "mappings": {"default": {"default": "box drawings down heavy and horizontal light"}}}, {"key": "2531", "mappings": {"default": {"default": "box drawings right light and left down heavy"}}}, {"key": "2532", "mappings": {"default": {"default": "box drawings left light and right down heavy"}}}, {"key": "2533", "mappings": {"default": {"default": "box drawings heavy down and horizontal"}}}, {"key": "2534", "mappings": {"default": {"default": "box drawings light up and horizontal"}}}, {"key": "2535", "mappings": {"default": {"default": "box drawings left heavy and right up light"}}}, {"key": "2536", "mappings": {"default": {"default": "box drawings right heavy and left up light"}}}, {"key": "2537", "mappings": {"default": {"default": "box drawings up light and horizontal heavy"}}}, {"key": "2538", "mappings": {"default": {"default": "box drawings up heavy and horizontal light"}}}, {"key": "2539", "mappings": {"default": {"default": "box drawings right light and left up heavy"}}}, {"key": "253A", "mappings": {"default": {"default": "box drawings left light and right up heavy"}}}, {"key": "253B", "mappings": {"default": {"default": "box drawings heavy up and horizontal"}}}, {"key": "253C", "mappings": {"default": {"default": "box drawings light vertical and horizontal"}}}, {"key": "253D", "mappings": {"default": {"default": "box drawings left heavy and right vertical light"}}}, {"key": "253E", "mappings": {"default": {"default": "box drawings right heavy and left vertical light"}}}, {"key": "253F", "mappings": {"default": {"default": "box drawings vertical light and horizontal heavy"}}}, {"key": "2540", "mappings": {"default": {"default": "box drawings up heavy and down horizontal light"}}}, {"key": "2541", "mappings": {"default": {"default": "box drawings down heavy and up horizontal light"}}}, {"key": "2542", "mappings": {"default": {"default": "box drawings vertical heavy and horizontal light"}}}, {"key": "2543", "mappings": {"default": {"default": "box drawings left up heavy and right down light"}}}, {"key": "2544", "mappings": {"default": {"default": "box drawings right up heavy and left down light"}}}, {"key": "2545", "mappings": {"default": {"default": "box drawings left down heavy and right up light"}}}, {"key": "2546", "mappings": {"default": {"default": "box drawings right down heavy and left up light"}}}, {"key": "2547", "mappings": {"default": {"default": "box drawings down light and up horizontal heavy"}}}, {"key": "2548", "mappings": {"default": {"default": "box drawings up light and down horizontal heavy"}}}, {"key": "2549", "mappings": {"default": {"default": "box drawings right light and left vertical heavy"}}}, {"key": "254A", "mappings": {"default": {"default": "box drawings left light and right vertical heavy"}}}, {"key": "254B", "mappings": {"default": {"default": "box drawings heavy vertical and horizontal"}}}, {"key": "254C", "mappings": {"default": {"default": "box drawings light double dash horizontal"}}}, {"key": "254D", "mappings": {"default": {"default": "box drawings heavy double dash horizontal"}}}, {"key": "254E", "mappings": {"default": {"default": "box drawings light double dash vertical"}}}, {"key": "254F", "mappings": {"default": {"default": "box drawings heavy double dash vertical"}}}, {"key": "2550", "mappings": {"default": {"default": "box drawings double horizontal"}}}, {"key": "2551", "mappings": {"default": {"default": "box drawings double vertical"}}}, {"key": "2552", "mappings": {"default": {"default": "box drawings down single and right double"}}}, {"key": "2553", "mappings": {"default": {"default": "box drawings down double and right single"}}}, {"key": "2554", "mappings": {"default": {"default": "box drawings double down and right"}}}, {"key": "2555", "mappings": {"default": {"default": "box drawings down single and left double"}}}, {"key": "2556", "mappings": {"default": {"default": "box drawings down double and left single"}}}, {"key": "2557", "mappings": {"default": {"default": "box drawings double down and left"}}}, {"key": "2558", "mappings": {"default": {"default": "box drawings up single and right double"}}}, {"key": "2559", "mappings": {"default": {"default": "box drawings up double and right single"}}}, {"key": "255A", "mappings": {"default": {"default": "box drawings double up and right"}}}, {"key": "255B", "mappings": {"default": {"default": "box drawings up single and left double"}}}, {"key": "255C", "mappings": {"default": {"default": "box drawings up double and left single"}}}, {"key": "255D", "mappings": {"default": {"default": "box drawings double up and left"}}}, {"key": "255E", "mappings": {"default": {"default": "box drawings vertical single and right double"}}}, {"key": "255F", "mappings": {"default": {"default": "box drawings vertical double and right single"}}}, {"key": "2560", "mappings": {"default": {"default": "box drawings double vertical and right"}}}, {"key": "2561", "mappings": {"default": {"default": "box drawings vertical single and left double"}}}, {"key": "2562", "mappings": {"default": {"default": "box drawings vertical double and left single"}}}, {"key": "2563", "mappings": {"default": {"default": "box drawings double vertical and left"}}}, {"key": "2564", "mappings": {"default": {"default": "box drawings down single and horizontal double"}}}, {"key": "2565", "mappings": {"default": {"default": "box drawings down double and horizontal single"}}}, {"key": "2566", "mappings": {"default": {"default": "box drawings double down and horizontal"}}}, {"key": "2567", "mappings": {"default": {"default": "box drawings up single and horizontal double"}}}, {"key": "2568", "mappings": {"default": {"default": "box drawings up double and horizontal single"}}}, {"key": "2569", "mappings": {"default": {"default": "box drawings double up and horizontal"}}}, {"key": "256A", "mappings": {"default": {"default": "box drawings vertical single and horizontal double"}}}, {"key": "256B", "mappings": {"default": {"default": "box drawings vertical double and horizontal single"}}}, {"key": "256C", "mappings": {"default": {"default": "box drawings double vertical and horizontal"}}}, {"key": "256D", "mappings": {"default": {"default": "box drawings light arc down and right"}}}, {"key": "256E", "mappings": {"default": {"default": "box drawings light arc down and left"}}}, {"key": "256F", "mappings": {"default": {"default": "box drawings light arc up and left"}}}, {"key": "2570", "mappings": {"default": {"default": "box drawings light arc up and right"}}}, {"key": "2571", "mappings": {"default": {"default": "box drawings light diagonal upper right to lower left"}}}, {"key": "2572", "mappings": {"default": {"default": "box drawings light diagonal upper left to lower right"}}}, {"key": "2573", "mappings": {"default": {"default": "box drawings light diagonal cross"}}}, {"key": "2574", "mappings": {"default": {"default": "box drawings light left"}}}, {"key": "2575", "mappings": {"default": {"default": "box drawings light up"}}}, {"key": "2576", "mappings": {"default": {"default": "box drawings light right"}}}, {"key": "2577", "mappings": {"default": {"default": "box drawings light down"}}}, {"key": "2578", "mappings": {"default": {"default": "box drawings heavy left"}}}, {"key": "2579", "mappings": {"default": {"default": "box drawings heavy up"}}}, {"key": "257A", "mappings": {"default": {"default": "box drawings heavy right"}}}, {"key": "257B", "mappings": {"default": {"default": "box drawings heavy down"}}}, {"key": "257C", "mappings": {"default": {"default": "box drawings light left and heavy right"}}}, {"key": "257D", "mappings": {"default": {"default": "box drawings light up and heavy down"}}}, {"key": "257E", "mappings": {"default": {"default": "box drawings heavy left and light right"}}}, {"key": "257F", "mappings": {"default": {"default": "box drawings heavy up and light down"}}}, {"key": "2580", "mappings": {"default": {"default": "upper half block"}}}, {"key": "2581", "mappings": {"default": {"default": "lower one eighth block"}}}, {"key": "2582", "mappings": {"default": {"default": "lower one quarter block"}}}, {"key": "2583", "mappings": {"default": {"default": "lower three eighths block"}}}, {"key": "2584", "mappings": {"default": {"default": "lower half block"}}}, {"key": "2585", "mappings": {"default": {"default": "lower five eighths block"}}}, {"key": "2586", "mappings": {"default": {"default": "lower three quarters block"}}}, {"key": "2587", "mappings": {"default": {"default": "lower seven eighths block"}}}, {"key": "2588", "mappings": {"default": {"default": "full block"}}}, {"key": "2589", "mappings": {"default": {"default": "left seven eighths block"}}}, {"key": "258A", "mappings": {"default": {"default": "left three quarters block"}}}, {"key": "258B", "mappings": {"default": {"default": "left five eighths block"}}}, {"key": "258C", "mappings": {"default": {"default": "left half block"}}}, {"key": "258D", "mappings": {"default": {"default": "left three eighths block"}}}, {"key": "258E", "mappings": {"default": {"default": "left one quarter block"}}}, {"key": "258F", "mappings": {"default": {"default": "left one eighth block"}}}, {"key": "2590", "mappings": {"default": {"default": "right half block"}}}, {"key": "2591", "mappings": {"default": {"default": "light shade"}}}, {"key": "2592", "mappings": {"default": {"default": "medium shade"}}}, {"key": "2593", "mappings": {"default": {"default": "dark shade"}}}, {"key": "2594", "mappings": {"default": {"default": "upper one eighth block"}}}, {"key": "2595", "mappings": {"default": {"default": "right one eighth block"}}}, {"key": "2596", "mappings": {"default": {"default": "quadrant lower left"}}}, {"key": "2597", "mappings": {"default": {"default": "quadrant lower right"}}}, {"key": "2598", "mappings": {"default": {"default": "quadrant upper left"}}}, {"key": "2599", "mappings": {"default": {"default": "quadrant upper left and lower left and lower right"}}}, {"key": "259A", "mappings": {"default": {"default": "quadrant upper left and lower right"}}}, {"key": "259B", "mappings": {"default": {"default": "quadrant upper left and upper right and lower left"}}}, {"key": "259C", "mappings": {"default": {"default": "quadrant upper left and upper right and lower right"}}}, {"key": "259D", "mappings": {"default": {"default": "quadrant upper right"}}}, {"key": "259E", "mappings": {"default": {"default": "quadrant upper right and lower left"}}}, {"key": "259F", "mappings": {"default": {"default": "quadrant upper right and lower left and lower right"}}}, {"key": "25A0", "mappings": {"default": {"default": "black square"}}}, {"key": "25A1", "mappings": {"default": {"default": "white square"}}}, {"key": "25A2", "mappings": {"default": {"default": "white square with rounded corners"}}}, {"key": "25A3", "mappings": {"default": {"default": "white square containing black small square"}}}, {"key": "25A4", "mappings": {"default": {"default": "square with horizontal fill"}}}, {"key": "25A5", "mappings": {"default": {"default": "square with vertical fill"}}}, {"key": "25A6", "mappings": {"default": {"default": "square with orthogonal crosshatch fill"}}}, {"key": "25A7", "mappings": {"default": {"default": "square with upper left to lower right fill"}}}, {"key": "25A8", "mappings": {"default": {"default": "square with upper right to lower left fill"}}}, {"key": "25A9", "mappings": {"default": {"default": "square with diagonal crosshatch fill"}}}, {"key": "25AA", "mappings": {"default": {"default": "black small square"}}}, {"key": "25AB", "mappings": {"default": {"default": "white small square"}}}, {"key": "25AC", "mappings": {"default": {"default": "black rectangle"}}}, {"key": "25AD", "mappings": {"default": {"default": "white rectangle"}}}, {"key": "25AE", "mappings": {"default": {"default": "black vertical rectangle"}}}, {"key": "25AF", "mappings": {"default": {"default": "white vertical rectangle"}}}, {"key": "25B0", "mappings": {"default": {"default": "black parallelogram"}}}, {"key": "25B1", "mappings": {"default": {"default": "white parallelogram"}}}, {"key": "25B2", "mappings": {"default": {"default": "black up pointing triangle"}}}, {"key": "25B3", "mappings": {"default": {"default": "white up pointing triangle"}}}, {"key": "25B4", "mappings": {"default": {"default": "black up pointing small triangle"}}}, {"key": "25B5", "mappings": {"default": {"default": "white up pointing small triangle"}}}, {"key": "25B6", "mappings": {"default": {"default": "black right pointing triangle"}}}, {"key": "25B7", "mappings": {"default": {"default": "white right pointing triangle"}}}, {"key": "25B8", "mappings": {"default": {"default": "black right pointing small triangle"}}}, {"key": "25B9", "mappings": {"default": {"default": "white right pointing small triangle"}}}, {"key": "25BA", "mappings": {"default": {"default": "black right pointing pointer"}}}, {"key": "25BB", "mappings": {"default": {"default": "white right pointing pointer"}}}, {"key": "25BC", "mappings": {"default": {"default": "black down pointing triangle"}}}, {"key": "25BD", "mappings": {"default": {"default": "white down pointing triangle"}}}, {"key": "25BE", "mappings": {"default": {"default": "black down pointing small triangle"}}}, {"key": "25BF", "mappings": {"default": {"default": "white down pointing small triangle"}}}, {"key": "25C0", "mappings": {"default": {"default": "black left pointing triangle"}}}, {"key": "25C1", "mappings": {"default": {"default": "white left pointing triangle"}}}, {"key": "25C2", "mappings": {"default": {"default": "black left pointing small triangle"}}}, {"key": "25C3", "mappings": {"default": {"default": "white left pointing small triangle"}}}, {"key": "25C4", "mappings": {"default": {"default": "black left pointing pointer"}}}, {"key": "25C5", "mappings": {"default": {"default": "white left pointing pointer"}}}, {"key": "25C6", "mappings": {"default": {"default": "black diamond"}}}, {"key": "25C7", "mappings": {"default": {"default": "white diamond"}}}, {"key": "25C8", "mappings": {"default": {"default": "white diamond containing black small diamond"}}}, {"key": "25C9", "mappings": {"default": {"default": "fisheye"}}}, {"key": "25CA", "mappings": {"default": {"default": "lozenge"}}}, {"key": "25CB", "mappings": {"default": {"default": "white circle"}}}, {"key": "25CC", "mappings": {"default": {"default": "dotted circle"}}}, {"key": "25CD", "mappings": {"default": {"default": "circle with vertical fill"}}}, {"key": "25CE", "mappings": {"default": {"default": "bullseye"}}}, {"key": "25CF", "mappings": {"default": {"default": "black circle"}}}, {"key": "25D0", "mappings": {"default": {"default": "circle with left half black"}}}, {"key": "25D1", "mappings": {"default": {"default": "circle with right half black"}}}, {"key": "25D2", "mappings": {"default": {"default": "circle with lower half black"}}}, {"key": "25D3", "mappings": {"default": {"default": "circle with upper half black"}}}, {"key": "25D4", "mappings": {"default": {"default": "circle with upper right quadrant black"}}}, {"key": "25D5", "mappings": {"default": {"default": "circle with all but upper left quadrant black"}}}, {"key": "25D6", "mappings": {"default": {"default": "left half black circle"}}}, {"key": "25D7", "mappings": {"default": {"default": "right half black circle"}}}, {"key": "25D8", "mappings": {"default": {"default": "inverse bullet"}}}, {"key": "25D9", "mappings": {"default": {"default": "inverse white circle"}}}, {"key": "25DA", "mappings": {"default": {"default": "upper half inverse white circle"}}}, {"key": "25DB", "mappings": {"default": {"default": "lower half inverse white circle"}}}, {"key": "25DC", "mappings": {"default": {"default": "upper left quadrant circular arc"}}}, {"key": "25DD", "mappings": {"default": {"default": "upper right quadrant circular arc"}}}, {"key": "25DE", "mappings": {"default": {"default": "lower right quadrant circular arc"}}}, {"key": "25DF", "mappings": {"default": {"default": "lower left quadrant circular arc"}}}, {"key": "25E0", "mappings": {"default": {"default": "upper half circle"}}}, {"key": "25E1", "mappings": {"default": {"default": "lower half circle"}}}, {"key": "25E2", "mappings": {"default": {"default": "black lower right triangle"}}}, {"key": "25E3", "mappings": {"default": {"default": "black lower left triangle"}}}, {"key": "25E4", "mappings": {"default": {"default": "black upper left triangle"}}}, {"key": "25E5", "mappings": {"default": {"default": "black upper right triangle"}}}, {"key": "25E6", "mappings": {"default": {"default": "white bullet"}}}, {"key": "25E7", "mappings": {"default": {"default": "square with left half black"}}}, {"key": "25E8", "mappings": {"default": {"default": "square with right half black"}}}, {"key": "25E9", "mappings": {"default": {"default": "square with upper left diagonal half black"}}}, {"key": "25EA", "mappings": {"default": {"default": "square with lower right diagonal half black"}}}, {"key": "25EB", "mappings": {"default": {"default": "white square with vertical bisecting line"}}}, {"key": "25EC", "mappings": {"default": {"default": "white up pointing triangle with dot"}}}, {"key": "25ED", "mappings": {"default": {"default": "up pointing triangle with left half black"}}}, {"key": "25EE", "mappings": {"default": {"default": "up pointing triangle with right half black"}}}, {"key": "25EF", "mappings": {"default": {"default": "large circle"}}}, {"key": "25F0", "mappings": {"default": {"default": "white square with upper left quadrant"}}}, {"key": "25F1", "mappings": {"default": {"default": "white square with lower left quadrant"}}}, {"key": "25F2", "mappings": {"default": {"default": "white square with lower right quadrant"}}}, {"key": "25F3", "mappings": {"default": {"default": "white square with upper right quadrant"}}}, {"key": "25F4", "mappings": {"default": {"default": "white circle with upper left quadrant"}}}, {"key": "25F5", "mappings": {"default": {"default": "white circle with lower left quadrant"}}}, {"key": "25F6", "mappings": {"default": {"default": "white circle with lower right quadrant"}}}, {"key": "25F7", "mappings": {"default": {"default": "white circle with upper right quadrant"}}}, {"key": "25F8", "mappings": {"default": {"default": "upper left triangle"}}}, {"key": "25F9", "mappings": {"default": {"default": "upper right triangle"}}}, {"key": "25FA", "mappings": {"default": {"default": "lower left triangle"}}}, {"key": "25FB", "mappings": {"default": {"default": "white medium square"}}}, {"key": "25FC", "mappings": {"default": {"default": "black medium square"}}}, {"key": "25FD", "mappings": {"default": {"default": "white medium small square"}}}, {"key": "25FE", "mappings": {"default": {"default": "black medium small square"}}}, {"key": "25FF", "mappings": {"default": {"default": "lower right triangle"}}}, {"key": "2B12", "mappings": {"default": {"default": "square with top half black"}}}, {"key": "2B13", "mappings": {"default": {"default": "square with bottom half black"}}}, {"key": "2B14", "mappings": {"default": {"default": "square with upper right diagonal half black"}}}, {"key": "2B15", "mappings": {"default": {"default": "square with lower left diagonal half black"}}}, {"key": "2B16", "mappings": {"default": {"default": "diamond with left half black"}}}, {"key": "2B17", "mappings": {"default": {"default": "diamond with right half black"}}}, {"key": "2B18", "mappings": {"default": {"default": "diamond with top half black"}}}, {"key": "2B19", "mappings": {"default": {"default": "diamond with bottom half black"}}}, {"key": "2B1A", "mappings": {"default": {"default": "dotted square"}}}, {"key": "2B1B", "mappings": {"default": {"default": "black large square"}}}, {"key": "2B1C", "mappings": {"default": {"default": "white large square"}}}, {"key": "2B1D", "mappings": {"default": {"default": "black very small square"}}}, {"key": "2B1E", "mappings": {"default": {"default": "white very small square"}}}, {"key": "2B1F", "mappings": {"default": {"default": "black pentagon"}}}, {"key": "2B20", "mappings": {"default": {"default": "white pentagon"}}}, {"key": "2B21", "mappings": {"default": {"default": "white hexagon"}}}, {"key": "2B22", "mappings": {"default": {"default": "black hexagon"}}}, {"key": "2B23", "mappings": {"default": {"default": "horizontal black hexagon"}}}, {"key": "2B24", "mappings": {"default": {"default": "black large circle"}}}, {"key": "2B25", "mappings": {"default": {"default": "black medium diamond"}}}, {"key": "2B26", "mappings": {"default": {"default": "white medium diamond"}}}, {"key": "2B27", "mappings": {"default": {"default": "black medium lozenge"}}}, {"key": "2B28", "mappings": {"default": {"default": "white medium lozenge"}}}, {"key": "2B29", "mappings": {"default": {"default": "black small diamond"}}}, {"key": "2B2A", "mappings": {"default": {"default": "black small lozenge"}}}, {"key": "2B2B", "mappings": {"default": {"default": "white small lozenge"}}}, {"key": "2B2C", "mappings": {"default": {"default": "black horizontal ellipse"}}}, {"key": "2B2D", "mappings": {"default": {"default": "white horizontal ellipse"}}}, {"key": "2B2E", "mappings": {"default": {"default": "black vertical ellipse"}}}, {"key": "2B2F", "mappings": {"default": {"default": "white vertical ellipse"}}}, {"key": "2B50", "mappings": {"default": {"default": "white medium star"}}}, {"key": "2B51", "mappings": {"default": {"default": "black small star"}}}, {"key": "2B52", "mappings": {"default": {"default": "white small star"}}}, {"key": "2B53", "mappings": {"default": {"default": "black right pointing pentagon"}}}, {"key": "2B54", "mappings": {"default": {"default": "white right pointing pentagon"}}}, {"key": "2B55", "mappings": {"default": {"default": "heavy large circle"}}}, {"key": "2B56", "mappings": {"default": {"default": "heavy oval with oval inside"}}}, {"key": "2B57", "mappings": {"default": {"default": "heavy circle with circle inside"}}}, {"key": "2B58", "mappings": {"default": {"default": "heavy circle"}}}, {"key": "2B59", "mappings": {"default": {"default": "heavy circled saltire"}}}], "en/symbols/math_harpoons.min": [{"locale": "en"}, {"key": "21BC", "mappings": {"default": {"default": "left harpoon with barb up"}}}, {"key": "21BD", "mappings": {"default": {"default": "left harpoon with barb down"}}}, {"key": "21BE", "mappings": {"default": {"default": "up harpoon with barb right"}}}, {"key": "21BF", "mappings": {"default": {"default": "up harpoon with barb left"}}}, {"key": "21C0", "mappings": {"default": {"default": "right harpoon with barb up"}}}, {"key": "21C1", "mappings": {"default": {"default": "right harpoon with barb down"}}}, {"key": "21C2", "mappings": {"default": {"default": "down harpoon with barb right"}}}, {"key": "21C3", "mappings": {"default": {"default": "down harpoon with barb left"}}}, {"key": "21CB", "mappings": {"default": {"default": "left harpoon over right harpoon"}}}, {"key": "21CC", "mappings": {"default": {"default": "right harpoon over left harpoon"}}}, {"key": "294A", "mappings": {"default": {"default": "left barb up right barb down harpoon"}}}, {"key": "294B", "mappings": {"default": {"default": "left barb down right barb up harpoon"}}}, {"key": "294C", "mappings": {"default": {"default": "up barb right down barb left harpoon"}}}, {"key": "294D", "mappings": {"default": {"default": "up barb left down barb right harpoon"}}}, {"key": "294E", "mappings": {"default": {"default": "left barb up right barb up harpoon"}}}, {"key": "294F", "mappings": {"default": {"default": "up barb right down barb right harpoon"}}}, {"key": "2950", "mappings": {"default": {"default": "left barb down right barb down harpoon"}}}, {"key": "2951", "mappings": {"default": {"default": "up barb left down barb left harpoon"}}}, {"key": "2952", "mappings": {"default": {"default": "left harpoon with barb up to bar"}}}, {"key": "2953", "mappings": {"default": {"default": "right harpoon with barb up to bar"}}}, {"key": "2954", "mappings": {"default": {"default": "up harpoon with barb right to bar"}}}, {"key": "2955", "mappings": {"default": {"default": "down harpoon with barb right to bar"}}}, {"key": "2956", "mappings": {"default": {"default": "left harpoon with barb down to bar"}}}, {"key": "2957", "mappings": {"default": {"default": "right harpoon with barb down to bar"}}}, {"key": "2958", "mappings": {"default": {"default": "up harpoon with barb left to bar"}}}, {"key": "2959", "mappings": {"default": {"default": "down harpoon with barb left to bar"}}}, {"key": "295A", "mappings": {"default": {"default": "left harpoon with barb up from bar"}}}, {"key": "295B", "mappings": {"default": {"default": "right harpoon with barb up from bar"}}}, {"key": "295C", "mappings": {"default": {"default": "up harpoon with barb right from bar"}}}, {"key": "295D", "mappings": {"default": {"default": "down harpoon with barb right from bar"}}}, {"key": "295E", "mappings": {"default": {"default": "left harpoon with barb down from bar"}}}, {"key": "295F", "mappings": {"default": {"default": "right harpoon with barb down from bar"}}}, {"key": "2960", "mappings": {"default": {"default": "up harpoon with barb left from bar"}}}, {"key": "2961", "mappings": {"default": {"default": "down harpoon with barb left from bar"}}}, {"key": "2962", "mappings": {"default": {"default": "left harpoon with barb up above left harpoon with barb down"}}}, {"key": "2963", "mappings": {"default": {"default": "up harpoon with barb left beside up harpoon with barb right"}}}, {"key": "2964", "mappings": {"default": {"default": "right harpoon with barb up above right harpoon with barb down"}}}, {"key": "2965", "mappings": {"default": {"default": "down harpoon with barb left beside down harpoon with barb right"}}}, {"key": "2966", "mappings": {"default": {"default": "left harpoon with barb up above right harpoon with barb up"}}}, {"key": "2967", "mappings": {"default": {"default": "left harpoon with barb down above right harpoon with barb down"}}}, {"key": "2968", "mappings": {"default": {"default": "right harpoon with barb up above left harpoon with barb up"}}}, {"key": "2969", "mappings": {"default": {"default": "right harpoon with barb down above left harpoon with barb down"}}}, {"key": "296A", "mappings": {"default": {"default": "left harpoon with barb up above long dash"}}}, {"key": "296B", "mappings": {"default": {"default": "left harpoon with barb down below long dash"}}}, {"key": "296C", "mappings": {"default": {"default": "right harpoon with barb up above long dash"}}}, {"key": "296D", "mappings": {"default": {"default": "right harpoon with barb down below long dash"}}}, {"key": "296E", "mappings": {"default": {"default": "up harpoon with barb left beside down harpoon with barb right"}}}, {"key": "296F", "mappings": {"default": {"default": "down harpoon with barb left beside up harpoon with barb right"}}}, {"key": "297C", "mappings": {"default": {"default": "left fish tail"}}}, {"key": "297D", "mappings": {"default": {"default": "right fish tail"}}}, {"key": "297E", "mappings": {"default": {"default": "up fish tail"}}}, {"key": "297F", "mappings": {"default": {"default": "down fish tail"}}}], "en/symbols/math_non_characters.min": [{"locale": "en"}, {"key": "210F", "mappings": {"default": {"default": "italic h over two pi", "physics": "planck constant over two pi"}}}, {"key": "2114", "mappings": {"default": {"default": "l b bar"}}}, {"key": "2116", "mappings": {"default": {"default": "numero"}}}, {"key": "2117", "mappings": {"default": {"default": "sound recording copyright"}}}, {"key": "211E", "mappings": {"default": {"default": "prescription take"}}}, {"key": "211F", "mappings": {"default": {"default": "response"}}}, {"key": "2120", "mappings": {"default": {"default": "service mark"}}}, {"key": "2121", "mappings": {"default": {"default": "telephone sign", "alternative": "t e l symbol"}}}, {"key": "2122", "mappings": {"default": {"default": "trade mark"}}}, {"key": "2123", "mappings": {"default": {"default": "versicle"}}}, {"key": "2125", "mappings": {"default": {"default": "ounce"}}}, {"key": "2126", "mappings": {"default": {"default": "ohm"}}}, {"key": "2127", "mappings": {"default": {"default": "inverted ohm"}}}, {"key": "212A", "mappings": {"default": {"default": "kelvin"}}}, {"key": "212B", "mappings": {"default": {"default": "angstrom"}}}, {"key": "212E", "mappings": {"default": {"default": "estimated"}}}, {"key": "2132", "mappings": {"default": {"default": "turned cap F"}, "mathspeak": {"default": "turned upper F"}}}, {"key": "2139", "mappings": {"default": {"default": "information source"}}}, {"key": "213A", "mappings": {"default": {"default": "rotated cap Q"}, "mathspeak": {"default": "rotated upper Q"}}}, {"key": "213B", "mappings": {"default": {"default": "facsimile sign"}}}, {"key": "2141", "mappings": {"default": {"default": "turned sans serif cap G"}, "mathspeak": {"default": "turned sans serif upper G"}}}, {"key": "2142", "mappings": {"default": {"default": "turned sans serif cap L"}, "mathspeak": {"default": "turned sans serif upper L"}}}, {"key": "2143", "mappings": {"default": {"default": "reversed sans serif cap L"}, "mathspeak": {"default": "reversed sans serif upper L"}}}, {"key": "2144", "mappings": {"default": {"default": "turned sans serif cap Y"}, "mathspeak": {"default": "turned sans serif upper Y"}}}], "en/symbols/math_symbols.min": [{"locale": "en"}, {"key": "0021", "mappings": {"default": {"default": "exclamation mark"}}}, {"key": "0022", "mappings": {"default": {"default": "quotation mark"}}}, {"key": "0023", "mappings": {"default": {"default": "number sign", "alternative": "hash"}, "mathspeak": {"brief": "num sign", "sbrief": "num sign"}}}, {"key": "0024", "mappings": {"default": {"default": "dollar sign"}}}, {"key": "0025", "mappings": {"default": {"default": "percent sign"}}}, {"key": "0026", "mappings": {"default": {"default": "ampersand"}}}, {"key": "0027", "mappings": {"default": {"default": "prime"}}}, {"key": "002A", "mappings": {"default": {"default": "asterisk"}}}, {"key": "002B", "mappings": {"default": {"default": "plus"}}}, {"key": "002C", "mappings": {"default": {"default": "comma"}}}, {"key": "002D", "mappings": {"default": {"default": "minus"}, "mathspeak": {"default": "hyphen"}}}, {"key": "002E", "mappings": {"default": {"default": "period"}}}, {"key": "002F", "mappings": {"default": {"default": "slash", "alternative": "solidus"}, "emacspeak": {"default": "slash"}}}, {"key": "003A", "mappings": {"default": {"default": "colon"}}}, {"key": "003B", "mappings": {"default": {"default": "semicolon"}}}, {"key": "003C", "mappings": {"default": {"default": "less than"}, "clearspeak": {"default": "is less than"}}}, {"key": "003D", "mappings": {"default": {"default": "equals"}}}, {"key": "003E", "mappings": {"default": {"default": "greater than"}, "clearspeak": {"default": "is greater than"}}}, {"key": "003F", "mappings": {"default": {"default": "question mark"}}}, {"key": "0040", "mappings": {"default": {"default": "at"}}}, {"key": "005C", "mappings": {"default": {"default": "backslash"}}}, {"key": "005E", "mappings": {"default": {"default": "hat"}, "mathspeak": {"default": "caret"}}}, {"key": "005F", "mappings": {"default": {"default": "bar", "alternative": "underline"}}}, {"key": "0060", "mappings": {"default": {"default": "grave"}, "mathspeak": {"default": "grave"}}}, {"key": "007C", "mappings": {"default": {"default": "vertical bar"}}}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}}, {"key": "00A1", "mappings": {"default": {"default": "inverted exclamation mark"}}}, {"key": "00A2", "mappings": {"default": {"default": "cent sign"}}}, {"key": "00A3", "mappings": {"default": {"default": "pound sign"}}}, {"key": "00A4", "mappings": {"default": {"default": "currency sign"}}}, {"key": "00A5", "mappings": {"default": {"default": "yen sign"}}}, {"key": "00A6", "mappings": {"default": {"default": "broken vertical bar"}}}, {"key": "00A7", "mappings": {"default": {"default": "section sign"}}}, {"key": "00A8", "mappings": {"default": {"default": "two dots"}}}, {"key": "00A9", "mappings": {"default": {"default": "copyright sign"}}}, {"key": "00AA", "mappings": {"default": {"default": "feminine ordinal indicator"}}}, {"key": "00AB", "mappings": {"default": {"default": "left pointing guillemet"}}}, {"key": "00AC", "mappings": {"default": {"default": "not sign"}}}, {"key": "00AE", "mappings": {"default": {"default": "registered sign"}, "mathspeak": {"default": "registered trade mark sign"}, "clearspeak": {"default": "trade mark sign"}}}, {"key": "00AF", "mappings": {"default": {"default": "bar"}}}, {"key": "00B0", "mappings": {"default": {"default": "degree"}, "clearspeak": {"default": "degrees"}}}, {"key": "00B1", "mappings": {"default": {"default": "plus or minus"}}}, {"key": "00B4", "mappings": {"default": {"default": "acute"}}}, {"key": "00B5", "mappings": {"default": {"default": "micro sign"}}}, {"key": "00B6", "mappings": {"default": {"default": "paragraph sign"}}}, {"key": "00B7", "mappings": {"default": {"default": "dot"}, "clearspeak": {"default": "times"}}}, {"key": "00B8", "mappings": {"default": {"default": "cedilla"}}}, {"key": "00BA", "mappings": {"default": {"default": "masculine ordinal indicator"}}}, {"key": "00BB", "mappings": {"default": {"default": "right pointing guillemet"}}}, {"key": "00BF", "mappings": {"default": {"default": "inverted question mark"}}}, {"key": "00D7", "mappings": {"default": {"default": "times"}, "clearspeak": {"MultsymbolX_By": "by", "MultsymbolX_Cross": "cross"}}}, {"key": "00F7", "mappings": {"default": {"default": "division sign"}}}, {"key": "02D8", "mappings": {"default": {"default": "breve"}}}, {"key": "02B9", "mappings": {"default": {"default": "prime"}}}, {"key": "02BA", "mappings": {"default": {"default": "double prime"}}}, {"key": "02D9", "mappings": {"default": {"default": "dot above"}, "mathspeak": {"default": "dot"}}}, {"key": "02DA", "mappings": {"default": {"default": "ring above"}}}, {"key": "02DB", "mappings": {"default": {"default": "ogonek"}}}, {"key": "02DC", "mappings": {"default": {"default": "tilde"}}}, {"key": "02DD", "mappings": {"default": {"default": "double acute"}}}, {"key": "2010", "mappings": {"default": {"default": "hyphen"}}}, {"key": "2011", "mappings": {"default": {"default": "non breaking hyphen"}}}, {"key": "2012", "mappings": {"default": {"default": "figure dash"}}}, {"key": "2013", "mappings": {"default": {"default": "en dash"}}}, {"key": "2014", "mappings": {"default": {"default": "em dash"}}}, {"key": "2015", "mappings": {"default": {"default": "horizontal bar"}, "mathspeak": {"default": "quotation dash"}}}, {"key": "2016", "mappings": {"default": {"default": "double vertical bar"}}}, {"key": "2017", "mappings": {"default": {"default": "double underline"}}}, {"key": "2018", "mappings": {"default": {"default": "left single quotation mark"}}}, {"key": "2019", "mappings": {"default": {"default": "right single quotation mark"}}}, {"key": "201A", "mappings": {"default": {"default": "low right single quotation mark"}}}, {"key": "201B", "mappings": {"default": {"default": "left reversed single quotation mark"}}}, {"key": "201C", "mappings": {"default": {"default": "left double quotation mark"}}}, {"key": "201D", "mappings": {"default": {"default": "right double quotation mark"}}}, {"key": "201E", "mappings": {"default": {"default": "low right double quotation mark"}}}, {"key": "201F", "mappings": {"default": {"default": "left reversed double quotation mark"}}}, {"key": "2020", "mappings": {"default": {"default": "dagger"}}}, {"key": "2021", "mappings": {"default": {"default": "double dagger"}}}, {"key": "2022", "mappings": {"default": {"default": "bullet"}}}, {"key": "2023", "mappings": {"default": {"default": "triangular bullet"}}}, {"key": "2024", "mappings": {"default": {"default": "one dot leader"}}}, {"key": "2025", "mappings": {"default": {"default": "two dot leader"}}}, {"key": "2026", "mappings": {"default": {"default": "ellipsis"}, "clearspeak": {"default": "dot dot dot"}}}, {"key": "2027", "mappings": {"default": {"default": "hyphenation point"}}}, {"key": "2030", "mappings": {"default": {"default": "per mille"}}}, {"key": "2031", "mappings": {"default": {"default": "per ten thousand"}}}, {"key": "2032", "mappings": {"default": {"default": "prime"}}}, {"key": "2033", "mappings": {"default": {"default": "double prime"}}}, {"key": "2034", "mappings": {"default": {"default": "triple prime"}}}, {"key": "2035", "mappings": {"default": {"default": "reversed prime"}}}, {"key": "2036", "mappings": {"default": {"default": "reversed double prime"}}}, {"key": "2037", "mappings": {"default": {"default": "reversed triple prime"}}}, {"key": "2038", "mappings": {"default": {"default": "caret"}}}, {"key": "2039", "mappings": {"default": {"default": "left pointing single guillemet"}}}, {"key": "203A", "mappings": {"default": {"default": "right pointing single guillemet"}}}, {"key": "203B", "mappings": {"default": {"default": "reference mark"}}}, {"key": "203C", "mappings": {"default": {"default": "double exclamation mark"}}}, {"key": "203D", "mappings": {"default": {"default": "interrobang"}}}, {"key": "203E", "mappings": {"default": {"default": "overline"}, "mathspeak": {"default": "bar"}}}, {"key": "203F", "mappings": {"default": {"default": "undertie"}}}, {"key": "2040", "mappings": {"default": {"default": "character tie"}}}, {"key": "2041", "mappings": {"default": {"default": "caret insertion point"}}}, {"key": "2042", "mappings": {"default": {"default": "asterism"}}}, {"key": "2043", "mappings": {"default": {"default": "hyphen bullet"}}}, {"key": "2044", "mappings": {"default": {"default": "fraction slash"}}}, {"key": "2047", "mappings": {"default": {"default": "double question mark"}}}, {"key": "2048", "mappings": {"default": {"default": "question exclamation mark"}}}, {"key": "2049", "mappings": {"default": {"default": "exclamation question mark"}}}, {"key": "204B", "mappings": {"default": {"default": "reversed pilcrow"}}}, {"key": "204C", "mappings": {"default": {"default": "black leftwards bullet"}}}, {"key": "204D", "mappings": {"default": {"default": "black rightwards bullet"}}}, {"key": "204E", "mappings": {"default": {"default": "low asterisk"}}}, {"key": "204F", "mappings": {"default": {"default": "reversed semicolon"}}}, {"key": "2050", "mappings": {"default": {"default": "close up"}}}, {"key": "2051", "mappings": {"default": {"default": "two asterisks aligned vertically"}}}, {"key": "2052", "mappings": {"default": {"default": "commercial minus"}}}, {"key": "2053", "mappings": {"default": {"default": "swung dash"}}}, {"key": "2054", "mappings": {"default": {"default": "inverted undertie"}}}, {"key": "2055", "mappings": {"default": {"default": "flower punctuation mark"}}}, {"key": "2056", "mappings": {"default": {"default": "three dot punctuation"}}}, {"key": "2057", "mappings": {"default": {"default": "quadruple prime"}}}, {"key": "2058", "mappings": {"default": {"default": "four dot punctuation"}}}, {"key": "2059", "mappings": {"default": {"default": "five dot punctuation"}}}, {"key": "205A", "mappings": {"default": {"default": "two dot punctuation"}}}, {"key": "205B", "mappings": {"default": {"default": "four dot mark"}}}, {"key": "205C", "mappings": {"default": {"default": "dotted cross"}}}, {"key": "205D", "mappings": {"default": {"default": "tricolon"}}}, {"key": "205E", "mappings": {"default": {"default": "vertical four dots"}}}, {"key": "207A", "mappings": {"default": {"default": "superscript plus"}}}, {"key": "207B", "mappings": {"default": {"default": "superscript minus"}}}, {"key": "207C", "mappings": {"default": {"default": "superscript equals"}}}, {"key": "207D", "mappings": {"default": {"default": "superscript left parenthesis"}}}, {"key": "207E", "mappings": {"default": {"default": "superscript right parenthesis"}}}, {"key": "208A", "mappings": {"default": {"default": "subscript plus"}}}, {"key": "208B", "mappings": {"default": {"default": "subscript minus"}}}, {"key": "208C", "mappings": {"default": {"default": "subscript equals"}}}, {"key": "208D", "mappings": {"default": {"default": "subscript left parenthesis"}}}, {"key": "208E", "mappings": {"default": {"default": "subscript right parenthesis"}}}, {"key": "214A", "mappings": {"default": {"default": "property line"}}}, {"key": "214B", "mappings": {"default": {"default": "turned ampersand"}}}, {"key": "214C", "mappings": {"default": {"default": "per"}}}, {"key": "214D", "mappings": {"default": {"default": "aktieselskab"}}}, {"key": "214E", "mappings": {"default": {"default": "turned small f"}}}, {"key": "2200", "mappings": {"default": {"default": "for all"}}}, {"key": "2201", "mappings": {"default": {"default": "complement"}}}, {"key": "2203", "mappings": {"default": {"default": "there exists"}}}, {"key": "2204", "mappings": {"default": {"default": "there does not exist"}}}, {"key": "2205", "mappings": {"default": {"default": "empty set"}}}, {"key": "2206", "mappings": {"default": {"default": "increment"}}}, {"key": "2208", "mappings": {"default": {"default": "element of"}, "clearspeak": {"default": "is a member of"}}}, {"key": "2209", "mappings": {"default": {"default": "not an element of"}, "clearspeak": {"default": "is not a member of"}}}, {"key": "220A", "mappings": {"default": {"default": "element of"}, "clearspeak": {"default": "is a member of"}}}, {"key": "220B", "mappings": {"default": {"default": "contains as member"}}}, {"key": "220C", "mappings": {"default": {"default": "does not contain as member"}}}, {"key": "220D", "mappings": {"default": {"default": "contains as member"}}}, {"key": "220E", "mappings": {"default": {"default": "end of proof"}}}, {"key": "220F", "mappings": {"default": {"default": "product"}}}, {"key": "2210", "mappings": {"default": {"default": "coproduct"}}}, {"key": "2211", "mappings": {"default": {"default": "sum"}, "mathspeak": {"default": "sigma summation"}}}, {"key": "2212", "mappings": {"default": {"default": "minus"}}}, {"key": "2213", "mappings": {"default": {"default": "minus or plus"}}}, {"key": "2214", "mappings": {"default": {"default": "dot plus"}}}, {"key": "2215", "mappings": {"default": {"default": "division slash"}}}, {"key": "2216", "mappings": {"default": {"default": "set minus"}}}, {"key": "2217", "mappings": {"default": {"default": "asterisk"}}}, {"key": "2218", "mappings": {"default": {"default": "ring"}, "clearspeak": {"default": "composed with"}}}, {"key": "2219", "mappings": {"default": {"default": "bullet"}}}, {"key": "221A", "mappings": {"default": {"default": "square root"}}}, {"key": "221B", "mappings": {"default": {"default": "cube root"}}}, {"key": "221C", "mappings": {"default": {"default": "fourth root"}}}, {"key": "221D", "mappings": {"default": {"default": "proportional to"}}}, {"key": "221E", "mappings": {"default": {"default": "infinity"}}}, {"key": "221F", "mappings": {"default": {"default": "right angle"}}}, {"key": "2220", "mappings": {"default": {"default": "angle"}}}, {"key": "2221", "mappings": {"default": {"default": "measured angle"}}}, {"key": "2222", "mappings": {"default": {"default": "spherical angle"}}}, {"key": "2223", "mappings": {"default": {"default": "vertical bar", "alternative": "divides"}}}, {"key": "2224", "mappings": {"default": {"default": "does not divide"}}}, {"key": "2225", "mappings": {"default": {"default": "parallel to"}}}, {"key": "2226", "mappings": {"default": {"default": "not parallel to"}}}, {"key": "2227", "mappings": {"default": {"default": "and"}}}, {"key": "2228", "mappings": {"default": {"default": "or"}}}, {"key": "2229", "mappings": {"default": {"default": "intersection"}}}, {"key": "222A", "mappings": {"default": {"default": "union"}}}, {"key": "222B", "mappings": {"default": {"default": "integral"}}}, {"key": "222C", "mappings": {"default": {"default": "double integral"}}}, {"key": "222D", "mappings": {"default": {"default": "triple integral"}}}, {"key": "222E", "mappings": {"default": {"default": "contour integral"}}}, {"key": "222F", "mappings": {"default": {"default": "surface integral"}}}, {"key": "2230", "mappings": {"default": {"default": "volume integral"}}}, {"key": "2231", "mappings": {"default": {"default": "clockwise integral"}}}, {"key": "2232", "mappings": {"default": {"default": "clockwise contour integral"}}}, {"key": "2233", "mappings": {"default": {"default": "anticlockwise contour integral"}}}, {"key": "2234", "mappings": {"default": {"default": "therefore"}}}, {"key": "2235", "mappings": {"default": {"default": "because"}}}, {"key": "2236", "mappings": {"default": {"default": "ratio"}}}, {"key": "2237", "mappings": {"default": {"default": "proportion"}}}, {"key": "2238", "mappings": {"default": {"default": "dot minus"}}}, {"key": "2239", "mappings": {"default": {"default": "excess"}}}, {"key": "223A", "mappings": {"default": {"default": "geometric proportion"}}}, {"key": "223B", "mappings": {"default": {"default": "homothetic"}}}, {"key": "223C", "mappings": {"default": {"default": "tilde"}}}, {"key": "223D", "mappings": {"default": {"default": "reversed tilde"}}}, {"key": "223E", "mappings": {"default": {"default": "inverted lazy s"}}}, {"key": "223F", "mappings": {"default": {"default": "sine wave"}}}, {"key": "2240", "mappings": {"default": {"default": "wreath product"}}}, {"key": "2241", "mappings": {"default": {"default": "not tilde"}}}, {"key": "2242", "mappings": {"default": {"default": "minus tilde"}}}, {"key": "2243", "mappings": {"default": {"default": "asymptotically equals"}}}, {"key": "2244", "mappings": {"default": {"default": "not asymptotically equals"}}}, {"key": "2245", "mappings": {"default": {"default": "approximately equals"}}}, {"key": "2246", "mappings": {"default": {"default": "approximately but not actually equals"}}}, {"key": "2247", "mappings": {"default": {"default": "neither approximately nor actually equals"}}}, {"key": "2248", "mappings": {"default": {"default": "almost equals"}}}, {"key": "2249", "mappings": {"default": {"default": "not almost equals"}}}, {"key": "224A", "mappings": {"default": {"default": "almost equal or equals"}}}, {"key": "224B", "mappings": {"default": {"default": "triple tilde"}}}, {"key": "224C", "mappings": {"default": {"default": "all equals"}}}, {"key": "224D", "mappings": {"default": {"default": "equivalent to"}}}, {"key": "224E", "mappings": {"default": {"default": "geometrically equivalent to"}}}, {"key": "224F", "mappings": {"default": {"default": "difference between"}}}, {"key": "2250", "mappings": {"default": {"default": "approaches the limit"}}}, {"key": "2251", "mappings": {"default": {"default": "geometrically equals"}}}, {"key": "2252", "mappings": {"default": {"default": "approximately equals or the image of"}}}, {"key": "2253", "mappings": {"default": {"default": "image of or approximately equals"}}}, {"key": "2254", "mappings": {"default": {"default": "colon equals"}}}, {"key": "2255", "mappings": {"default": {"default": "equals colon"}}}, {"key": "2256", "mappings": {"default": {"default": "ring in equals"}}}, {"key": "2257", "mappings": {"default": {"default": "ring equals"}}}, {"key": "2258", "mappings": {"default": {"default": "corresponds to"}}}, {"key": "2259", "mappings": {"default": {"default": "estimates"}}}, {"key": "225A", "mappings": {"default": {"default": "equiangular to"}, "clearspeak": {"default": "is equiangular to"}}}, {"key": "225B", "mappings": {"default": {"default": "star equals"}}}, {"key": "225C", "mappings": {"default": {"default": "delta equals"}}}, {"key": "225D", "mappings": {"default": {"default": "equals by definition"}, "clearspeak": {"default": "is defined to be"}}}, {"key": "225E", "mappings": {"default": {"default": "measured by"}, "clearspeak": {"default": "is measured by"}}}, {"key": "225F", "mappings": {"default": {"default": "questioned equals"}}}, {"key": "2260", "mappings": {"default": {"default": "not equals"}, "clearspeak": {"default": "is not equal to"}}}, {"key": "2261", "mappings": {"default": {"default": "identical to"}, "clearspeak": {"default": "is identical to"}}}, {"key": "2262", "mappings": {"default": {"default": "not identical to"}, "clearspeak": {"default": "is not identical to"}}}, {"key": "2263", "mappings": {"default": {"default": "strictly equivalent to"}, "clearspeak": {"default": "is strictly equivalent to"}}}, {"key": "2264", "mappings": {"default": {"default": "less than or equals"}, "clearspeak": {"default": "is less than or equal to"}}}, {"key": "2265", "mappings": {"default": {"default": "greater than or equals"}, "clearspeak": {"default": "is greater than or equal to"}}}, {"key": "2266", "mappings": {"default": {"default": "less than over equals"}, "clearspeak": {"default": "is less than over equals"}}}, {"key": "2267", "mappings": {"default": {"default": "greater than over equals"}, "clearspeak": {"default": "is greater than over equals"}}}, {"key": "2268", "mappings": {"default": {"default": "less than but not equals"}, "clearspeak": {"default": "is less than but not equal to"}}}, {"key": "2269", "mappings": {"default": {"default": "greater than but not equals"}, "clearspeak": {"default": "is greater than but not equal to"}}}, {"key": "226A", "mappings": {"default": {"default": "much less than"}, "clearspeak": {"default": "is much less than"}}}, {"key": "226B", "mappings": {"default": {"default": "much greater than"}, "clearspeak": {"default": "is much greater than"}}}, {"key": "226C", "mappings": {"default": {"default": "between"}}}, {"key": "226D", "mappings": {"default": {"default": "not equivalent to"}, "clearspeak": {"default": "is not equivalent to"}}}, {"key": "226E", "mappings": {"default": {"default": "not less than"}, "clearspeak": {"default": "is not less than"}}}, {"key": "226F", "mappings": {"default": {"default": "not greater than"}, "clearspeak": {"default": "is not greater than"}}}, {"key": "2270", "mappings": {"default": {"default": "neither less than nor equals"}, "clearspeak": {"default": "is neither less than nor equal to"}}}, {"key": "2271", "mappings": {"default": {"default": "neither greater than nor equals"}, "clearspeak": {"default": "is neither greater than nor equal to"}}}, {"key": "2272", "mappings": {"default": {"default": "less than or equivalent to"}, "clearspeak": {"default": "is less than or equivalent to"}}}, {"key": "2273", "mappings": {"default": {"default": "greater than or equivalent to"}, "clearspeak": {"default": "is greater than or equivalent to"}}}, {"key": "2274", "mappings": {"default": {"default": "neither less than nor equivalent to"}, "clearspeak": {"default": "is neither less than nor equivalent to"}}}, {"key": "2275", "mappings": {"default": {"default": "neither greater than nor equivalent to"}, "clearspeak": {"default": "is neither greater than nor equivalent to"}}}, {"key": "2276", "mappings": {"default": {"default": "less than or greater than"}, "clearspeak": {"default": "is less than or greater than"}}}, {"key": "2277", "mappings": {"default": {"default": "greater than or less than"}, "clearspeak": {"default": "is greater than or less than"}}}, {"key": "2278", "mappings": {"default": {"default": "neither less than nor greater than"}, "clearspeak": {"default": "is neither less than nor greater than"}}}, {"key": "2279", "mappings": {"default": {"default": "neither greater than nor less than"}, "clearspeak": {"default": "is neither greater than nor less than"}}}, {"key": "227A", "mappings": {"default": {"default": "precedes"}}}, {"key": "227B", "mappings": {"default": {"default": "succeeds"}}}, {"key": "227C", "mappings": {"default": {"default": "precedes or equal to"}}}, {"key": "227D", "mappings": {"default": {"default": "succeeds or equal to"}}}, {"key": "227E", "mappings": {"default": {"default": "precedes or equivalent to"}}}, {"key": "227F", "mappings": {"default": {"default": "succeeds or equivalent to"}}}, {"key": "2280", "mappings": {"default": {"default": "does not precede"}}}, {"key": "2281", "mappings": {"default": {"default": "does not succeed"}}}, {"key": "2282", "mappings": {"default": {"default": "subset of"}}}, {"key": "2283", "mappings": {"default": {"default": "superset of"}}}, {"key": "2284", "mappings": {"default": {"default": "not a subset of"}}}, {"key": "2285", "mappings": {"default": {"default": "not a superset of"}}}, {"key": "2286", "mappings": {"default": {"default": "subset of or equal to"}}}, {"key": "2287", "mappings": {"default": {"default": "superset of or equal to"}}}, {"key": "2288", "mappings": {"default": {"default": "neither a subset of nor equal to"}}}, {"key": "2289", "mappings": {"default": {"default": "neither a superset of nor equal to"}}}, {"key": "228A", "mappings": {"default": {"default": "subset of or not equals"}}}, {"key": "228B", "mappings": {"default": {"default": "superset of or not equals"}}}, {"key": "228C", "mappings": {"default": {"default": "multiset"}}}, {"key": "228D", "mappings": {"default": {"default": "multiset multiplication"}}}, {"key": "228E", "mappings": {"default": {"default": "multiset union"}}}, {"key": "228F", "mappings": {"default": {"default": "square image of"}}}, {"key": "2290", "mappings": {"default": {"default": "square original of"}}}, {"key": "2291", "mappings": {"default": {"default": "square image of or equal to"}}}, {"key": "2292", "mappings": {"default": {"default": "square original of or equal to"}}}, {"key": "2293", "mappings": {"default": {"default": "square cap"}}}, {"key": "2294", "mappings": {"default": {"default": "square cup"}}}, {"key": "2295", "mappings": {"default": {"default": "circled plus"}}}, {"key": "2296", "mappings": {"default": {"default": "circled minus"}}}, {"key": "2297", "mappings": {"default": {"default": "circled times"}}}, {"key": "2298", "mappings": {"default": {"default": "circled division slash"}}}, {"key": "2299", "mappings": {"default": {"default": "circled dot"}}}, {"key": "229A", "mappings": {"default": {"default": "circled ring"}}}, {"key": "229B", "mappings": {"default": {"default": "circled asterisk"}}}, {"key": "229C", "mappings": {"default": {"default": "circled equals"}}}, {"key": "229D", "mappings": {"default": {"default": "circled dash"}}}, {"key": "229E", "mappings": {"default": {"default": "squared plus"}}}, {"key": "229F", "mappings": {"default": {"default": "squared minus"}}}, {"key": "22A0", "mappings": {"default": {"default": "squared times"}}}, {"key": "22A1", "mappings": {"default": {"default": "squared dot"}}}, {"key": "22A2", "mappings": {"default": {"default": "right tack"}}}, {"key": "22A3", "mappings": {"default": {"default": "left tack"}}}, {"key": "22A4", "mappings": {"default": {"default": "down tack"}}}, {"key": "22A5", "mappings": {"default": {"default": "up tack"}}}, {"key": "22A6", "mappings": {"default": {"default": "assertion"}}}, {"key": "22A7", "mappings": {"default": {"default": "models"}}}, {"key": "22A8", "mappings": {"default": {"default": "true"}}}, {"key": "22A9", "mappings": {"default": {"default": "forces"}}}, {"key": "22AA", "mappings": {"default": {"default": "triple vertical bar right turnstile"}}}, {"key": "22AB", "mappings": {"default": {"default": "double vertical bar double right turnstile"}}}, {"key": "22AC", "mappings": {"default": {"default": "does not prove"}}}, {"key": "22AD", "mappings": {"default": {"default": "not true"}}}, {"key": "22AE", "mappings": {"default": {"default": "does not force"}}}, {"key": "22AF", "mappings": {"default": {"default": "negated double vertical bar double right turnstile"}}}, {"key": "22B0", "mappings": {"default": {"default": "precedes under relation"}}}, {"key": "22B1", "mappings": {"default": {"default": "succeeds under relation"}}}, {"key": "22B2", "mappings": {"default": {"default": "normal subgroup of"}}}, {"key": "22B3", "mappings": {"default": {"default": "contains as normal subgroup"}}}, {"key": "22B4", "mappings": {"default": {"default": "normal subgroup of or equal to"}}}, {"key": "22B5", "mappings": {"default": {"default": "contains as normal subgroup or equal to"}}}, {"key": "22B6", "mappings": {"default": {"default": "original of"}}}, {"key": "22B7", "mappings": {"default": {"default": "image of"}}}, {"key": "22B8", "mappings": {"default": {"default": "multimap"}}}, {"key": "22B9", "mappings": {"default": {"default": "hermitian conjugate matrix"}}}, {"key": "22BA", "mappings": {"default": {"default": "intercalate"}}}, {"key": "22BB", "mappings": {"default": {"default": "xor"}}}, {"key": "22BC", "mappings": {"default": {"default": "nand"}}}, {"key": "22BD", "mappings": {"default": {"default": "nor"}}}, {"key": "22BF", "mappings": {"default": {"default": "right triangle"}}}, {"key": "22C0", "mappings": {"default": {"default": "n ary and"}, "mathspeak": {"default": "and"}}}, {"key": "22C1", "mappings": {"default": {"default": "n ary or"}, "mathspeak": {"default": "or"}}}, {"key": "22C2", "mappings": {"default": {"default": "n ary intersection"}, "mathspeak": {"default": "intersection"}}}, {"key": "22C3", "mappings": {"default": {"default": "n ary union"}, "mathspeak": {"default": "union"}}}, {"key": "22C4", "mappings": {"default": {"default": "diamond"}}}, {"key": "22C5", "mappings": {"default": {"default": "dot"}, "clearspeak": {"default": "times", "MultsymbolDot_Dot": "dot"}}}, {"key": "22C6", "mappings": {"default": {"default": "star"}}}, {"key": "22C7", "mappings": {"default": {"default": "division times"}}}, {"key": "22C8", "mappings": {"default": {"default": "bowtie"}}}, {"key": "22C9", "mappings": {"default": {"default": "left normal factor semidirect product"}}}, {"key": "22CA", "mappings": {"default": {"default": "right normal factor semidirect product"}}}, {"key": "22CB", "mappings": {"default": {"default": "left semidirect product"}}}, {"key": "22CC", "mappings": {"default": {"default": "right semidirect product"}}}, {"key": "22CD", "mappings": {"default": {"default": "reversed tilde equals"}}}, {"key": "22CE", "mappings": {"default": {"default": "curly or"}}}, {"key": "22CF", "mappings": {"default": {"default": "curly and"}}}, {"key": "22D0", "mappings": {"default": {"default": "double subset"}}}, {"key": "22D1", "mappings": {"default": {"default": "double superset"}}}, {"key": "22D2", "mappings": {"default": {"default": "double intersection"}}}, {"key": "22D3", "mappings": {"default": {"default": "double union"}}}, {"key": "22D4", "mappings": {"default": {"default": "pitchfork"}}}, {"key": "22D5", "mappings": {"default": {"default": "equal and parallel to"}, "clearspeak": {"default": "is equal and parallel to"}}}, {"key": "22D6", "mappings": {"default": {"default": "less than dot"}, "clearspeak": {"default": "is less than dot"}}}, {"key": "22D7", "mappings": {"default": {"default": "greater than dot"}, "clearspeak": {"default": "is greater than dot"}}}, {"key": "22D8", "mappings": {"default": {"default": "very much less than"}, "clearspeak": {"default": "is very much less than"}}}, {"key": "22D9", "mappings": {"default": {"default": "very much greater than"}, "clearspeak": {"default": "is very much greater than"}}}, {"key": "22DA", "mappings": {"default": {"default": "less than equals or greater than"}, "clearspeak": {"default": "is less than equal to or greater than"}}}, {"key": "22DB", "mappings": {"default": {"default": "greater than equals or less than"}, "clearspeak": {"default": "is greater than equal to or less than"}}}, {"key": "22DC", "mappings": {"default": {"default": "equals or less than"}, "clearspeak": {"default": "is equal to or less than"}}}, {"key": "22DD", "mappings": {"default": {"default": "equals or greater than"}, "clearspeak": {"default": "is equal to or greater than"}}}, {"key": "22DE", "mappings": {"default": {"default": "equals or precedes"}, "clearspeak": {"default": "is equal to or precedes"}}}, {"key": "22DF", "mappings": {"default": {"default": "equals or succeeds"}, "clearspeak": {"default": "is equal to or succeeds"}}}, {"key": "22E0", "mappings": {"default": {"default": "does not precede or equal"}}}, {"key": "22E1", "mappings": {"default": {"default": "does not succeed or equal"}}}, {"key": "22E2", "mappings": {"default": {"default": "not square image of or equals"}, "clearspeak": {"default": "is not square image of or equal to"}}}, {"key": "22E3", "mappings": {"default": {"default": "not square original of or equals"}, "clearspeak": {"default": "is not square original of or equal to"}}}, {"key": "22E4", "mappings": {"default": {"default": "square image of or not equals"}, "clearspeak": {"default": "is square image of or not equal to"}}}, {"key": "22E5", "mappings": {"default": {"default": "square original of or not equals"}, "clearspeak": {"default": "is square original of or not equal to"}}}, {"key": "22E6", "mappings": {"default": {"default": "less than but not equivalent to"}, "clearspeak": {"default": "is less than but not equivalent to"}}}, {"key": "22E7", "mappings": {"default": {"default": "greater than but not equivalent to"}, "clearspeak": {"default": "is greater than but not equivalent to"}}}, {"key": "22E8", "mappings": {"default": {"default": "precedes but not equivalent to"}}}, {"key": "22E9", "mappings": {"default": {"default": "succeeds but not equivalent to"}}}, {"key": "22EA", "mappings": {"default": {"default": "not normal subgroup of"}, "clearspeak": {"default": "is not normal subgroup of"}}}, {"key": "22EB", "mappings": {"default": {"default": "does not contain as normal subgroup"}}}, {"key": "22EC", "mappings": {"default": {"default": "not normal subgroup of or equals"}, "clearspeak": {"default": "is not normal subgroup of or equal to"}}}, {"key": "22ED", "mappings": {"default": {"default": "does not contain as normal subgroup or equal"}}}, {"key": "22EE", "mappings": {"default": {"default": "vertical ellipsis"}}}, {"key": "22EF", "mappings": {"default": {"default": "midline horizontal ellipsis"}, "clearspeak": {"default": "dot dot dot"}}}, {"key": "22F0", "mappings": {"default": {"default": "up right diagonal ellipsis"}}}, {"key": "22F1", "mappings": {"default": {"default": "down right diagonal ellipsis"}}}, {"key": "22F2", "mappings": {"default": {"default": "element of with long horizontal stroke"}}}, {"key": "22F3", "mappings": {"default": {"default": "element of with vertical bar at end of horizontal stroke"}}}, {"key": "22F4", "mappings": {"default": {"default": "element of with vertical bar at end of horizontal stroke"}}}, {"key": "22F5", "mappings": {"default": {"default": "element of with dot above"}}}, {"key": "22F6", "mappings": {"default": {"default": "element of with overbar"}}}, {"key": "22F7", "mappings": {"default": {"default": "element of with overbar"}}}, {"key": "22F8", "mappings": {"default": {"default": "element of with underbar"}}}, {"key": "22F9", "mappings": {"default": {"default": "element of with two horizontal strokes"}}}, {"key": "22FA", "mappings": {"default": {"default": "contains with long horizontal stroke"}}}, {"key": "22FB", "mappings": {"default": {"default": "contains with vertical bar at end of horizontal stroke"}}}, {"key": "22FC", "mappings": {"default": {"default": "contains with vertical bar at end of horizontal stroke"}}}, {"key": "22FD", "mappings": {"default": {"default": "contains with overbar"}}}, {"key": "22FE", "mappings": {"default": {"default": "contains with overbar"}}}, {"key": "22FF", "mappings": {"default": {"default": "z notation bag membership"}}}, {"key": "2300", "mappings": {"default": {"default": "diameter sign"}}}, {"key": "2302", "mappings": {"default": {"default": "house"}}}, {"key": "2305", "mappings": {"default": {"default": "projective"}}}, {"key": "2306", "mappings": {"default": {"default": "perspective"}}}, {"key": "2307", "mappings": {"default": {"default": "wavy line"}}}, {"key": "2310", "mappings": {"default": {"default": "reversed not"}}}, {"key": "2311", "mappings": {"default": {"default": "square lozenge"}}}, {"key": "2312", "mappings": {"default": {"default": "arc"}}}, {"key": "2313", "mappings": {"default": {"default": "segment"}}}, {"key": "2314", "mappings": {"default": {"default": "sector"}}}, {"key": "2795", "mappings": {"default": {"default": "bold plus"}}}, {"key": "2796", "mappings": {"default": {"default": "bold minus"}}}, {"key": "2797", "mappings": {"default": {"default": "bold division"}}}, {"key": "27B0", "mappings": {"default": {"default": "curly loop"}}}, {"key": "27BF", "mappings": {"default": {"default": "double curly loop"}}}, {"key": "27C1", "mappings": {"default": {"default": "white triangle containing small white triangle"}}}, {"key": "27C2", "mappings": {"default": {"default": "perpendicular"}}}, {"key": "27C3", "mappings": {"default": {"default": "open subset"}}}, {"key": "27C4", "mappings": {"default": {"default": "open superset"}}}, {"key": "27C7", "mappings": {"default": {"default": "or with dot inside"}}}, {"key": "27C8", "mappings": {"default": {"default": "backslash preceding subset"}}}, {"key": "27C9", "mappings": {"default": {"default": "superset preceding solidus"}}}, {"key": "27CA", "mappings": {"default": {"default": "vertical bar with horizontal stroke"}}}, {"key": "27CB", "mappings": {"default": {"default": "rising diagonal"}}}, {"key": "27CC", "mappings": {"default": {"default": "long division"}}}, {"key": "27CD", "mappings": {"default": {"default": "falling diagonal"}}}, {"key": "27CE", "mappings": {"default": {"default": "squared and"}}}, {"key": "27CF", "mappings": {"default": {"default": "squared or"}}}, {"key": "27D0", "mappings": {"default": {"default": "white diamond with centered dot"}}}, {"key": "27D1", "mappings": {"default": {"default": "and with dot"}}}, {"key": "27D2", "mappings": {"default": {"default": "element of opening upwards"}}}, {"key": "27D3", "mappings": {"default": {"default": "lower right corner with dot"}}}, {"key": "27D4", "mappings": {"default": {"default": "upper left corner with dot"}}}, {"key": "27D5", "mappings": {"default": {"default": "left outer join"}}}, {"key": "27D6", "mappings": {"default": {"default": "right outer join"}}}, {"key": "27D7", "mappings": {"default": {"default": "full outer join"}}}, {"key": "27D8", "mappings": {"default": {"default": "large up tack"}}}, {"key": "27D9", "mappings": {"default": {"default": "large down tack"}}}, {"key": "27DA", "mappings": {"default": {"default": "left and right double turnstile"}}}, {"key": "27DB", "mappings": {"default": {"default": "left and right tack"}}}, {"key": "27DC", "mappings": {"default": {"default": "left multimap"}}}, {"key": "27DD", "mappings": {"default": {"default": "long right tack"}}}, {"key": "27DE", "mappings": {"default": {"default": "long left tack"}}}, {"key": "27DF", "mappings": {"default": {"default": "up tack with circle above"}}}, {"key": "27E0", "mappings": {"default": {"default": "lozenge divided by horizontal rule"}}}, {"key": "27E1", "mappings": {"default": {"default": "white concave sided diamond"}}}, {"key": "27E2", "mappings": {"default": {"default": "white concave sided diamond with leftwards tick"}}}, {"key": "27E3", "mappings": {"default": {"default": "white concave sided diamond with rightwards tick"}}}, {"key": "27E4", "mappings": {"default": {"default": "white square with leftwards tick"}}}, {"key": "27E5", "mappings": {"default": {"default": "white square with rightwards tick"}}}, {"key": "292B", "mappings": {"default": {"default": "rising diagonal crossing falling diagonal"}}}, {"key": "292C", "mappings": {"default": {"default": "falling diagonal crossing rising diagonal"}}}, {"key": "2980", "mappings": {"default": {"default": "triple vertical bar"}}}, {"key": "2981", "mappings": {"default": {"default": "z notation spot"}}}, {"key": "2982", "mappings": {"default": {"default": "z notation type colon"}}}, {"key": "2999", "mappings": {"default": {"default": "dotted fence"}}}, {"key": "299A", "mappings": {"default": {"default": "vertical zigzag line"}}}, {"key": "29B0", "mappings": {"default": {"default": "reversed empty set"}}}, {"key": "29B1", "mappings": {"default": {"default": "empty set with overbar"}}}, {"key": "29B2", "mappings": {"default": {"default": "empty set with small circle above"}}}, {"key": "29B5", "mappings": {"default": {"default": "circle with horizontal bar"}}}, {"key": "29B6", "mappings": {"default": {"default": "circled vertical bar"}}}, {"key": "29B7", "mappings": {"default": {"default": "circled parallel"}}}, {"key": "29B8", "mappings": {"default": {"default": "circled backslash"}}}, {"key": "29B9", "mappings": {"default": {"default": "circled perpendicular"}}}, {"key": "29BA", "mappings": {"default": {"default": "circle divided by horizontal bar and top half divided by vertical bar"}}}, {"key": "29BB", "mappings": {"default": {"default": "circle with superimposed x"}}}, {"key": "29BC", "mappings": {"default": {"default": "circled anticlockwise rotated division"}}}, {"key": "29BE", "mappings": {"default": {"default": "circled white bullet"}}}, {"key": "29BF", "mappings": {"default": {"default": "circled bullet"}}}, {"key": "29C0", "mappings": {"default": {"default": "circled less than"}}}, {"key": "29C1", "mappings": {"default": {"default": "circled greater than"}}}, {"key": "29C2", "mappings": {"default": {"default": "circle with small circle to the right"}}}, {"key": "29C3", "mappings": {"default": {"default": "circle with two horizontal strokes to the right"}}}, {"key": "29C4", "mappings": {"default": {"default": "squared rising diagonal slash"}}}, {"key": "29C5", "mappings": {"default": {"default": "squared falling diagonal slash"}}}, {"key": "29C6", "mappings": {"default": {"default": "squared asterisk"}}}, {"key": "29C7", "mappings": {"default": {"default": "squared small circle"}}}, {"key": "29C8", "mappings": {"default": {"default": "squared square"}}}, {"key": "29C9", "mappings": {"default": {"default": "two joined squares"}}}, {"key": "29CA", "mappings": {"default": {"default": "triangle with dot above"}}}, {"key": "29CB", "mappings": {"default": {"default": "triangle with underbar"}}}, {"key": "29CC", "mappings": {"default": {"default": "s in triangle"}}}, {"key": "29CD", "mappings": {"default": {"default": "triangle with serifs at bottom"}}}, {"key": "29CE", "mappings": {"default": {"default": "right triangle above left triangle"}}}, {"key": "29CF", "mappings": {"default": {"default": "left triangle beside vertical bar"}}}, {"key": "29D0", "mappings": {"default": {"default": "vertical bar beside right triangle"}}}, {"key": "29D1", "mappings": {"default": {"default": "bowtie with left half black"}}}, {"key": "29D2", "mappings": {"default": {"default": "bowtie with right half black"}}}, {"key": "29D3", "mappings": {"default": {"default": "black bowtie"}}}, {"key": "29D4", "mappings": {"default": {"default": "times with left half black"}}}, {"key": "29D5", "mappings": {"default": {"default": "times with right half black"}}}, {"key": "29D6", "mappings": {"default": {"default": "white hourglass"}}}, {"key": "29D7", "mappings": {"default": {"default": "black hourglass"}}}, {"key": "29DC", "mappings": {"default": {"default": "incomplete infinity"}}}, {"key": "29DD", "mappings": {"default": {"default": "tie over infinity"}}}, {"key": "29DE", "mappings": {"default": {"default": "infinity negated with vertical bar"}}}, {"key": "29DF", "mappings": {"default": {"default": "double ended multimap"}}}, {"key": "29E0", "mappings": {"default": {"default": "square with contoured outline"}}}, {"key": "29E1", "mappings": {"default": {"default": "increases as"}}}, {"key": "29E2", "mappings": {"default": {"default": "shuffle product"}}}, {"key": "29E3", "mappings": {"default": {"default": "equals and slanted parallel"}}}, {"key": "29E4", "mappings": {"default": {"default": "equals and slanted parallel with tilde above"}}}, {"key": "29E5", "mappings": {"default": {"default": "identical to and slanted parallel"}}}, {"key": "29E6", "mappings": {"default": {"default": "gleich stark"}}}, {"key": "29E7", "mappings": {"default": {"default": "thermodynamic"}}}, {"key": "29E8", "mappings": {"default": {"default": "down pointing triangle with left half black"}}}, {"key": "29E9", "mappings": {"default": {"default": "down pointing triangle with right half black"}}}, {"key": "29EB", "mappings": {"default": {"default": "black lozenge"}}}, {"key": "29EE", "mappings": {"default": {"default": "error barred white square"}}}, {"key": "29EF", "mappings": {"default": {"default": "error barred black square"}}}, {"key": "29F0", "mappings": {"default": {"default": "error barred white diamond"}}}, {"key": "29F1", "mappings": {"default": {"default": "error barred black diamond"}}}, {"key": "29F2", "mappings": {"default": {"default": "error barred white circle"}}}, {"key": "29F3", "mappings": {"default": {"default": "error barred black circle"}}}, {"key": "29F4", "mappings": {"default": {"default": "rule delayed"}}}, {"key": "29F5", "mappings": {"default": {"default": "backslash"}}}, {"key": "29F6", "mappings": {"default": {"default": "solidus with overbar"}}}, {"key": "29F7", "mappings": {"default": {"default": "backslash with horizontal stroke"}}}, {"key": "29F8", "mappings": {"default": {"default": "big solidus"}, "mathspeak": {"default": "solidus"}}}, {"key": "29F9", "mappings": {"default": {"default": "big backslash"}, "mathspeak": {"default": "backslash"}}}, {"key": "29FA", "mappings": {"default": {"default": "double plus"}}}, {"key": "29FB", "mappings": {"default": {"default": "triple plus"}}}, {"key": "29FE", "mappings": {"default": {"default": "tiny"}}}, {"key": "29FF", "mappings": {"default": {"default": "miny"}}}, {"key": "2A00", "mappings": {"default": {"default": "n ary circled dot"}, "mathspeak": {"default": "circled dot"}}}, {"key": "2A01", "mappings": {"default": {"default": "n ary circled plus"}, "mathspeak": {"default": "circled plus"}}}, {"key": "2A02", "mappings": {"default": {"default": "n ary circled times"}, "mathspeak": {"default": "circled times"}}}, {"key": "2A03", "mappings": {"default": {"default": "n ary union with dot"}, "mathspeak": {"default": "union with dot"}}}, {"key": "2A04", "mappings": {"default": {"default": "n ary union operator with plus"}, "mathspeak": {"default": "union with plus"}}}, {"key": "2A05", "mappings": {"default": {"default": "n ary square intersection"}, "mathspeak": {"default": "square intersection"}}}, {"key": "2A06", "mappings": {"default": {"default": "n ary square union"}, "mathspeak": {"default": "square union"}}}, {"key": "2A07", "mappings": {"default": {"default": "two and"}}}, {"key": "2A08", "mappings": {"default": {"default": "two or"}}}, {"key": "2A09", "mappings": {"default": {"default": "n ary times"}, "mathspeak": {"default": "times"}}}, {"key": "2A0A", "mappings": {"default": {"default": "modulo two sum"}}}, {"key": "2A0B", "mappings": {"default": {"default": "summation with integral"}}}, {"key": "2A0C", "mappings": {"default": {"default": "quadruple integral"}}}, {"key": "2A0D", "mappings": {"default": {"default": "finite part integral"}}}, {"key": "2A0E", "mappings": {"default": {"default": "integral with double stroke"}}}, {"key": "2A0F", "mappings": {"default": {"default": "integral average with slash"}}}, {"key": "2A10", "mappings": {"default": {"default": "circulation function"}}}, {"key": "2A11", "mappings": {"default": {"default": "anticlockwise integration"}}}, {"key": "2A12", "mappings": {"default": {"default": "line integration with rectangular path around pole"}}}, {"key": "2A13", "mappings": {"default": {"default": "line integration with semicircular path around pole"}}}, {"key": "2A14", "mappings": {"default": {"default": "line integration not including the pole"}}}, {"key": "2A15", "mappings": {"default": {"default": "integral around a point"}}}, {"key": "2A16", "mappings": {"default": {"default": "quaternion integral"}}}, {"key": "2A18", "mappings": {"default": {"default": "integral with times"}}}, {"key": "2A19", "mappings": {"default": {"default": "integral with intersection"}}}, {"key": "2A1A", "mappings": {"default": {"default": "integral with union"}}}, {"key": "2A1B", "mappings": {"default": {"default": "integral with overbar"}}}, {"key": "2A1C", "mappings": {"default": {"default": "integral with underbar"}}}, {"key": "2A1D", "mappings": {"default": {"default": "join"}}}, {"key": "2A1E", "mappings": {"default": {"default": "large left triangle"}}}, {"key": "2A1F", "mappings": {"default": {"default": "z notation schema composition"}}}, {"key": "2A20", "mappings": {"default": {"default": "z notation schema piping"}}}, {"key": "2A21", "mappings": {"default": {"default": "z notation schema projection"}}}, {"key": "2A22", "mappings": {"default": {"default": "plus with circle above"}}}, {"key": "2A23", "mappings": {"default": {"default": "plus hat"}}}, {"key": "2A24", "mappings": {"default": {"default": "plus tilde"}}}, {"key": "2A25", "mappings": {"default": {"default": "plus underdot"}}}, {"key": "2A26", "mappings": {"default": {"default": "plus sign with tilde below"}}}, {"key": "2A27", "mappings": {"default": {"default": "plus sign with subscript two"}}}, {"key": "2A28", "mappings": {"default": {"default": "plus sign with black triangle"}}}, {"key": "2A29", "mappings": {"default": {"default": "minus sign with comma above"}}}, {"key": "2A2A", "mappings": {"default": {"default": "minus sign with dot below"}}}, {"key": "2A2B", "mappings": {"default": {"default": "minus sign with falling dots"}}}, {"key": "2A2C", "mappings": {"default": {"default": "minus sign with rising dots"}}}, {"key": "2A2D", "mappings": {"default": {"default": "plus sign in left half circle"}}}, {"key": "2A2E", "mappings": {"default": {"default": "plus sign in right half circle"}}}, {"key": "2A2F", "mappings": {"default": {"default": "vector or cross product"}}}, {"key": "2A30", "mappings": {"default": {"default": "multiplication sign with dot above"}}}, {"key": "2A31", "mappings": {"default": {"default": "multiplication sign with underbar"}}}, {"key": "2A32", "mappings": {"default": {"default": "semidirect product with bottom closed"}}}, {"key": "2A33", "mappings": {"default": {"default": "smash product"}}}, {"key": "2A34", "mappings": {"default": {"default": "multiplication sign in left half circle"}}}, {"key": "2A35", "mappings": {"default": {"default": "multiplication sign in right half circle"}}}, {"key": "2A36", "mappings": {"default": {"default": "circled multiplication sign with circumflex accent"}}}, {"key": "2A37", "mappings": {"default": {"default": "multiplication sign in double circle"}}}, {"key": "2A38", "mappings": {"default": {"default": "circled division"}}}, {"key": "2A39", "mappings": {"default": {"default": "plus sign in triangle"}}}, {"key": "2A3A", "mappings": {"default": {"default": "minus sign in triangle"}}}, {"key": "2A3B", "mappings": {"default": {"default": "multiplication sign in triangle"}}}, {"key": "2A3C", "mappings": {"default": {"default": "interior product"}}}, {"key": "2A3D", "mappings": {"default": {"default": "righthand interior product"}}}, {"key": "2A3E", "mappings": {"default": {"default": "z notation relational composition"}}}, {"key": "2A3F", "mappings": {"default": {"default": "amalgamation or coproduct"}}}, {"key": "2A40", "mappings": {"default": {"default": "intersection with dot"}}}, {"key": "2A41", "mappings": {"default": {"default": "union with minus"}}}, {"key": "2A42", "mappings": {"default": {"default": "union with overbar"}}}, {"key": "2A43", "mappings": {"default": {"default": "intersection with overbar"}}}, {"key": "2A44", "mappings": {"default": {"default": "intersection with and"}}}, {"key": "2A45", "mappings": {"default": {"default": "union with or"}}}, {"key": "2A46", "mappings": {"default": {"default": "union above intersection"}}}, {"key": "2A47", "mappings": {"default": {"default": "intersection above union"}}}, {"key": "2A48", "mappings": {"default": {"default": "union above bar above intersection"}}}, {"key": "2A49", "mappings": {"default": {"default": "intersection above bar above union"}}}, {"key": "2A4A", "mappings": {"default": {"default": "union beside and joined with union"}}}, {"key": "2A4B", "mappings": {"default": {"default": "intersection beside and joined with intersection"}}}, {"key": "2A4C", "mappings": {"default": {"default": "closed union with serifs"}}}, {"key": "2A4D", "mappings": {"default": {"default": "closed intersection with serifs"}}}, {"key": "2A4E", "mappings": {"default": {"default": "double square intersection"}}}, {"key": "2A4F", "mappings": {"default": {"default": "double square union"}}}, {"key": "2A50", "mappings": {"default": {"default": "closed union with serifs and smash product"}}}, {"key": "2A51", "mappings": {"default": {"default": "and with dot above"}}}, {"key": "2A52", "mappings": {"default": {"default": "or with dot above"}}}, {"key": "2A53", "mappings": {"default": {"default": "double and"}}}, {"key": "2A54", "mappings": {"default": {"default": "double or"}}}, {"key": "2A55", "mappings": {"default": {"default": "two intersecting and"}}}, {"key": "2A56", "mappings": {"default": {"default": "two intersecting or"}}}, {"key": "2A57", "mappings": {"default": {"default": "sloping large or"}}}, {"key": "2A58", "mappings": {"default": {"default": "sloping large and"}}}, {"key": "2A59", "mappings": {"default": {"default": "or overlapping and"}}}, {"key": "2A5A", "mappings": {"default": {"default": "and with middle stem"}}}, {"key": "2A5B", "mappings": {"default": {"default": "or with middle stem"}}}, {"key": "2A5C", "mappings": {"default": {"default": "and with horizontal dash"}}}, {"key": "2A5D", "mappings": {"default": {"default": "or with horizontal dash"}}}, {"key": "2A5E", "mappings": {"default": {"default": "and with double overbar"}}}, {"key": "2A5F", "mappings": {"default": {"default": "and with underbar"}}}, {"key": "2A60", "mappings": {"default": {"default": "and with double underbar"}}}, {"key": "2A61", "mappings": {"default": {"default": "small vee with underbar"}}}, {"key": "2A62", "mappings": {"default": {"default": "or with double overbar"}}}, {"key": "2A63", "mappings": {"default": {"default": "or with double underbar"}}}, {"key": "2A64", "mappings": {"default": {"default": "z notation domain antirestriction"}}}, {"key": "2A65", "mappings": {"default": {"default": "z notation range antirestriction"}}}, {"key": "2A66", "mappings": {"default": {"default": "equals sign with dot below"}}}, {"key": "2A67", "mappings": {"default": {"default": "identical with dot above"}}}, {"key": "2A68", "mappings": {"default": {"default": "triple horizontal bar with double vertical stroke"}}}, {"key": "2A69", "mappings": {"default": {"default": "triple horizontal bar with triple vertical stroke"}}}, {"key": "2A6A", "mappings": {"default": {"default": "tilde with dot above"}}}, {"key": "2A6B", "mappings": {"default": {"default": "tilde with rising dots"}}}, {"key": "2A6C", "mappings": {"default": {"default": "similar minus similar"}}}, {"key": "2A6D", "mappings": {"default": {"default": "congruent with dot above"}}}, {"key": "2A6E", "mappings": {"default": {"default": "equals with asterisk"}}}, {"key": "2A6F", "mappings": {"default": {"default": "almost equal hat"}}}, {"key": "2A70", "mappings": {"default": {"default": "approximately equal or equal to"}}}, {"key": "2A71", "mappings": {"default": {"default": "equals above plus"}}}, {"key": "2A72", "mappings": {"default": {"default": "plus above equals"}}}, {"key": "2A73", "mappings": {"default": {"default": "equals above tilde"}}}, {"key": "2A74", "mappings": {"default": {"default": "double colon equal"}}}, {"key": "2A75", "mappings": {"default": {"default": "two consecutive equals"}}}, {"key": "2A76", "mappings": {"default": {"default": "three consecutive equals"}}}, {"key": "2A77", "mappings": {"default": {"default": "equals sign with two dots above and two dots below"}}}, {"key": "2A78", "mappings": {"default": {"default": "equivalent with four dots above"}}}, {"key": "2A79", "mappings": {"default": {"default": "less than with circle inside"}}}, {"key": "2A7A", "mappings": {"default": {"default": "greater than with circle inside"}}}, {"key": "2A7B", "mappings": {"default": {"default": "less than with question mark above"}}}, {"key": "2A7C", "mappings": {"default": {"default": "greater than with question mark above"}}}, {"key": "2A7D", "mappings": {"default": {"default": "less than or slanted equals"}}}, {"key": "2A7E", "mappings": {"default": {"default": "greater than or slanted equals"}}}, {"key": "2A7F", "mappings": {"default": {"default": "less than or slanted equals with dot inside"}}}, {"key": "2A80", "mappings": {"default": {"default": "greater than or slanted equals with dot inside"}}}, {"key": "2A81", "mappings": {"default": {"default": "less than or slanted equals with dot above"}}}, {"key": "2A82", "mappings": {"default": {"default": "greater than or slanted equals with dot above"}}}, {"key": "2A83", "mappings": {"default": {"default": "less than or slanted equals with dot above right"}}}, {"key": "2A84", "mappings": {"default": {"default": "greater than or slanted equals with dot above left"}}}, {"key": "2A85", "mappings": {"default": {"default": "less than or approximate"}}}, {"key": "2A86", "mappings": {"default": {"default": "greater than or approximate"}}}, {"key": "2A87", "mappings": {"default": {"default": "less than and single line not equals"}}}, {"key": "2A88", "mappings": {"default": {"default": "greater than and single line not equals"}}}, {"key": "2A89", "mappings": {"default": {"default": "less than and not approximate"}}}, {"key": "2A8A", "mappings": {"default": {"default": "greater than and not approximate"}}}, {"key": "2A8B", "mappings": {"default": {"default": "less than above double line equal above greater than"}}}, {"key": "2A8C", "mappings": {"default": {"default": "greater than above double line equal above less than"}}}, {"key": "2A8D", "mappings": {"default": {"default": "less than above similar or equal"}}}, {"key": "2A8E", "mappings": {"default": {"default": "greater than above similar or equal"}}}, {"key": "2A8F", "mappings": {"default": {"default": "less than above similar above greater than"}}}, {"key": "2A90", "mappings": {"default": {"default": "greater than above similar above less than"}}}, {"key": "2A91", "mappings": {"default": {"default": "less than above greater than above double line equal"}}}, {"key": "2A92", "mappings": {"default": {"default": "greater than above less than above double line equal"}}}, {"key": "2A93", "mappings": {"default": {"default": "less than above slanted equal above greater than above slanted equal"}}}, {"key": "2A94", "mappings": {"default": {"default": "greater than above slanted equal above less than above slanted equal"}}}, {"key": "2A95", "mappings": {"default": {"default": "slanted equals or less than"}}}, {"key": "2A96", "mappings": {"default": {"default": "slanted equals or greater than"}}}, {"key": "2A97", "mappings": {"default": {"default": "slanted equals or less than with dot inside"}}}, {"key": "2A98", "mappings": {"default": {"default": "slanted equals or greater than with dot inside"}}}, {"key": "2A99", "mappings": {"default": {"default": "double line equals or less than"}}}, {"key": "2A9A", "mappings": {"default": {"default": "double line equals or greater than"}}}, {"key": "2A9B", "mappings": {"default": {"default": "double line slanted equals or less than"}}}, {"key": "2A9C", "mappings": {"default": {"default": "double line slanted equals or greater than"}}}, {"key": "2A9D", "mappings": {"default": {"default": "similar or less than"}}}, {"key": "2A9E", "mappings": {"default": {"default": "similar or greater than"}}}, {"key": "2A9F", "mappings": {"default": {"default": "similar above less than above equals sign"}}}, {"key": "2AA0", "mappings": {"default": {"default": "similar above greater than above equals sign"}}}, {"key": "2AA1", "mappings": {"default": {"default": "double nested less than"}}}, {"key": "2AA2", "mappings": {"default": {"default": "double nested greater than"}}}, {"key": "2AA3", "mappings": {"default": {"default": "double nested less than with underbar"}}}, {"key": "2AA4", "mappings": {"default": {"default": "greater than overlapping less than"}}}, {"key": "2AA5", "mappings": {"default": {"default": "greater than beside less than"}}}, {"key": "2AA6", "mappings": {"default": {"default": "less than closed by curve"}}}, {"key": "2AA7", "mappings": {"default": {"default": "greater than closed by curve"}}}, {"key": "2AA8", "mappings": {"default": {"default": "less than closed by curve above slanted equal"}}}, {"key": "2AA9", "mappings": {"default": {"default": "greater than closed by curve above slanted equal"}}}, {"key": "2AAA", "mappings": {"default": {"default": "smaller than"}, "clearspeak": {"default": "is smaller than"}}}, {"key": "2AAB", "mappings": {"default": {"default": "larger than"}, "clearspeak": {"default": "is larger than"}}}, {"key": "2AAC", "mappings": {"default": {"default": "smaller than or equals"}, "clearspeak": {"default": "is smaller than or equal to"}}}, {"key": "2AAD", "mappings": {"default": {"default": "larger than or equal"}, "clearspeak": {"default": "is larger than or equal to"}}}, {"key": "2AAE", "mappings": {"default": {"default": "equals with bumpy above"}}}, {"key": "2AAF", "mappings": {"default": {"default": "precedes above single line equals sign"}}}, {"key": "2AB0", "mappings": {"default": {"default": "succeeds above single line equals sign"}}}, {"key": "2AB1", "mappings": {"default": {"default": "precedes above single line not equals"}}}, {"key": "2AB2", "mappings": {"default": {"default": "succeeds above single line not equals"}}}, {"key": "2AB3", "mappings": {"default": {"default": "precedes above equals"}}}, {"key": "2AB4", "mappings": {"default": {"default": "succeeds above equals"}}}, {"key": "2AB5", "mappings": {"default": {"default": "precedes above not equals"}}}, {"key": "2AB6", "mappings": {"default": {"default": "succeeds above not equals"}}}, {"key": "2AB7", "mappings": {"default": {"default": "precedes above almost equals"}}}, {"key": "2AB8", "mappings": {"default": {"default": "succeeds above almost equals"}}}, {"key": "2AB9", "mappings": {"default": {"default": "precedes above not almost equals"}}}, {"key": "2ABA", "mappings": {"default": {"default": "succeeds above not almost equals"}}}, {"key": "2ABB", "mappings": {"default": {"default": "double precedes"}}}, {"key": "2ABC", "mappings": {"default": {"default": "double succeeds"}}}, {"key": "2ABD", "mappings": {"default": {"default": "subset with dot"}}}, {"key": "2ABE", "mappings": {"default": {"default": "superset with dot"}}}, {"key": "2ABF", "mappings": {"default": {"default": "subset with plus sign below"}}}, {"key": "2AC0", "mappings": {"default": {"default": "superset with plus sign below"}}}, {"key": "2AC1", "mappings": {"default": {"default": "subset with multiplication sign below"}}}, {"key": "2AC2", "mappings": {"default": {"default": "superset with multiplication sign below"}}}, {"key": "2AC3", "mappings": {"default": {"default": "subset of or equals with dot above"}}}, {"key": "2AC4", "mappings": {"default": {"default": "superset of or equals with dot above"}}}, {"key": "2AC5", "mappings": {"default": {"default": "subset of above equals sign"}}}, {"key": "2AC6", "mappings": {"default": {"default": "superset of above equals sign"}}}, {"key": "2AC7", "mappings": {"default": {"default": "subset of above tilde"}}}, {"key": "2AC8", "mappings": {"default": {"default": "superset of above tilde"}}}, {"key": "2AC9", "mappings": {"default": {"default": "subset of above almost equals"}}}, {"key": "2ACA", "mappings": {"default": {"default": "superset of above almost equals"}}}, {"key": "2ACB", "mappings": {"default": {"default": "subset of above not equals"}}}, {"key": "2ACC", "mappings": {"default": {"default": "superset of above not equals"}}}, {"key": "2ACD", "mappings": {"default": {"default": "square left open box"}}}, {"key": "2ACE", "mappings": {"default": {"default": "square right open box"}}}, {"key": "2ACF", "mappings": {"default": {"default": "closed subset"}}}, {"key": "2AD0", "mappings": {"default": {"default": "closed superset"}}}, {"key": "2AD1", "mappings": {"default": {"default": "closed subset or equals"}}}, {"key": "2AD2", "mappings": {"default": {"default": "closed superset or equals"}}}, {"key": "2AD3", "mappings": {"default": {"default": "subset above superset"}}}, {"key": "2AD4", "mappings": {"default": {"default": "superset above subset"}}}, {"key": "2AD5", "mappings": {"default": {"default": "subset above subset"}}}, {"key": "2AD6", "mappings": {"default": {"default": "superset above superset"}}}, {"key": "2AD7", "mappings": {"default": {"default": "superset beside subset"}}}, {"key": "2AD8", "mappings": {"default": {"default": "superset beside and joined by dash with subset"}}}, {"key": "2AD9", "mappings": {"default": {"default": "element of opening downwards"}}}, {"key": "2ADA", "mappings": {"default": {"default": "pitchfork with tee top"}}}, {"key": "2ADB", "mappings": {"default": {"default": "transversal intersection"}}}, {"key": "2ADC", "mappings": {"default": {"default": "forking"}}}, {"key": "2ADD", "mappings": {"default": {"default": "nonforking"}}}, {"key": "2ADE", "mappings": {"default": {"default": "short left tack"}}}, {"key": "2ADF", "mappings": {"default": {"default": "short down tack"}}}, {"key": "2AE0", "mappings": {"default": {"default": "short up tack"}}}, {"key": "2AE1", "mappings": {"default": {"default": "perpendicular with s"}}}, {"key": "2AE2", "mappings": {"default": {"default": "vertical bar triple right turnstile"}}}, {"key": "2AE3", "mappings": {"default": {"default": "double vertical bar left turnstile"}}}, {"key": "2AE4", "mappings": {"default": {"default": "vertical bar double left turnstile"}}}, {"key": "2AE5", "mappings": {"default": {"default": "double vertical bar double left turnstile"}}}, {"key": "2AE6", "mappings": {"default": {"default": "long dash from left member of double vertical"}}}, {"key": "2AE7", "mappings": {"default": {"default": "short down tack with overbar"}}}, {"key": "2AE8", "mappings": {"default": {"default": "short up tack with underbar"}}}, {"key": "2AE9", "mappings": {"default": {"default": "short up tack above short down tack"}}}, {"key": "2AEA", "mappings": {"default": {"default": "double down tack"}}}, {"key": "2AEB", "mappings": {"default": {"default": "double up tack"}}}, {"key": "2AEC", "mappings": {"default": {"default": "double stroke not sign"}}}, {"key": "2AED", "mappings": {"default": {"default": "reversed double stroke not sign"}}}, {"key": "2AEE", "mappings": {"default": {"default": "does not divide with reversed negation slash"}}}, {"key": "2AEF", "mappings": {"default": {"default": "vertical line with circle above"}}}, {"key": "2AF0", "mappings": {"default": {"default": "vertical line with circle below"}}}, {"key": "2AF1", "mappings": {"default": {"default": "down tack with circle below"}}}, {"key": "2AF2", "mappings": {"default": {"default": "parallel with horizontal stroke"}}}, {"key": "2AF3", "mappings": {"default": {"default": "parallel with tilde"}}}, {"key": "2AF4", "mappings": {"default": {"default": "triple vertical bar"}}}, {"key": "2AF5", "mappings": {"default": {"default": "triple vertical bar with horizontal stroke"}}}, {"key": "2AF6", "mappings": {"default": {"default": "triple colon"}}}, {"key": "2AF7", "mappings": {"default": {"default": "triple nested less than"}}}, {"key": "2AF8", "mappings": {"default": {"default": "triple nested greater than"}}}, {"key": "2AF9", "mappings": {"default": {"default": "double line slanted less than or equals"}}}, {"key": "2AFA", "mappings": {"default": {"default": "double line slanted greater than or equals"}}}, {"key": "2AFB", "mappings": {"default": {"default": "triple solidus"}}}, {"key": "2AFC", "mappings": {"default": {"default": "large triple vertical bar"}}}, {"key": "2AFD", "mappings": {"default": {"default": "double solidus"}}}, {"key": "2AFE", "mappings": {"default": {"default": "white vertical bar"}}}, {"key": "2AFF", "mappings": {"default": {"default": "n ary white vertical bar"}, "mathspeak": {"default": "white vertical bar"}}}, {"key": "301C", "mappings": {"default": {"default": "wave dash"}}}, {"key": "FE10", "mappings": {"default": {"default": "presentation form for vertical comma"}}}, {"key": "FE13", "mappings": {"default": {"default": "presentation form for vertical colon"}}}, {"key": "FE14", "mappings": {"default": {"default": "presentation form for vertical semicolon"}}}, {"key": "FE15", "mappings": {"default": {"default": "presentation form for vertical exclamation mark"}}}, {"key": "FE16", "mappings": {"default": {"default": "presentation form for vertical question mark"}}}, {"key": "FE19", "mappings": {"default": {"default": "presentation form for vertical horizontal ellipsis"}}}, {"key": "FE30", "mappings": {"default": {"default": "glyph for vertical two dot leader"}}}, {"key": "FE31", "mappings": {"default": {"default": "glyph for vertical em dash"}}}, {"key": "FE32", "mappings": {"default": {"default": "glyph for vertical en dash"}}}, {"key": "FE33", "mappings": {"default": {"default": "glyph for vertical underline"}}}, {"key": "FE34", "mappings": {"default": {"default": "glyph for vertical wavy underline"}}}, {"key": "FE45", "mappings": {"default": {"default": "sesame dot"}}}, {"key": "FE46", "mappings": {"default": {"default": "white sesame dot"}}}, {"key": "FE49", "mappings": {"default": {"default": "dashed overline"}}}, {"key": "FE4A", "mappings": {"default": {"default": "dash dot overline"}}}, {"key": "FE4B", "mappings": {"default": {"default": "wavy overline"}}}, {"key": "FE4C", "mappings": {"default": {"default": "double wavy overline"}}}, {"key": "FE4D", "mappings": {"default": {"default": "dashed underline"}}}, {"key": "FE4E", "mappings": {"default": {"default": "dash dot underline"}}}, {"key": "FE4F", "mappings": {"default": {"default": "wavy underline"}}}, {"key": "FE50", "mappings": {"default": {"default": "small comma"}}}, {"key": "FE52", "mappings": {"default": {"default": "small period"}}}, {"key": "FE54", "mappings": {"default": {"default": "small semicolon"}}}, {"key": "FE55", "mappings": {"default": {"default": "small colon"}}}, {"key": "FE56", "mappings": {"default": {"default": "small question mark"}}}, {"key": "FE57", "mappings": {"default": {"default": "small exclamation mark"}}}, {"key": "FE58", "mappings": {"default": {"default": "small em dash"}}}, {"key": "FE5F", "mappings": {"default": {"default": "small number sign"}}}, {"key": "FE60", "mappings": {"default": {"default": "small ampersand"}}}, {"key": "FE61", "mappings": {"default": {"default": "small asterisk"}}}, {"key": "FE62", "mappings": {"default": {"default": "small plus sign"}}}, {"key": "FE63", "mappings": {"default": {"default": "small hyphen minus"}}}, {"key": "FE64", "mappings": {"default": {"default": "small less than sign"}}}, {"key": "FE65", "mappings": {"default": {"default": "small greater than sign"}}}, {"key": "FE66", "mappings": {"default": {"default": "small equals"}}}, {"key": "FE68", "mappings": {"default": {"default": "small backslash", "alternative": "small reverse solidus"}}}, {"key": "FE69", "mappings": {"default": {"default": "small dollar sign"}}}, {"key": "FE6A", "mappings": {"default": {"default": "small percent sign"}}}, {"key": "FE6B", "mappings": {"default": {"default": "small commercial at"}}}, {"key": "FF01", "mappings": {"default": {"default": "exclamation mark"}}}, {"key": "FF02", "mappings": {"default": {"default": "quotation mark"}}}, {"key": "FF03", "mappings": {"default": {"default": "number sign"}}}, {"key": "FF04", "mappings": {"default": {"default": "dollar sign"}}}, {"key": "FF05", "mappings": {"default": {"default": "percent sign"}}}, {"key": "FF06", "mappings": {"default": {"default": "ampersand"}}}, {"key": "FF07", "mappings": {"default": {"default": "apostrophe"}}}, {"key": "FF0A", "mappings": {"default": {"default": "asterisk"}}}, {"key": "FF0B", "mappings": {"default": {"default": "plus sign"}}}, {"key": "FF0C", "mappings": {"default": {"default": "comma"}}}, {"key": "FF0D", "mappings": {"default": {"default": "hyphen minus"}}}, {"key": "FF0E", "mappings": {"default": {"default": "period"}}}, {"key": "FF0F", "mappings": {"default": {"default": "slash", "alternative": "solidus"}}}, {"key": "FF1A", "mappings": {"default": {"default": "colon"}}}, {"key": "FF1B", "mappings": {"default": {"default": "semicolon"}}}, {"key": "FF1C", "mappings": {"default": {"default": "less than"}}}, {"key": "FF1D", "mappings": {"default": {"default": "equals"}}}, {"key": "FF1E", "mappings": {"default": {"default": "greater than"}}}, {"key": "FF1F", "mappings": {"default": {"default": "question mark"}}}, {"key": "FF20", "mappings": {"default": {"default": "commercial at"}}}, {"key": "FF3C", "mappings": {"default": {"default": "backslash", "alternative": "reverse solidus"}}}, {"key": "FF3E", "mappings": {"default": {"default": "caret", "alternative": "circumflex"}}}, {"key": "FF3F", "mappings": {"default": {"default": "bar", "alternative": "underline"}}}, {"key": "FF40", "mappings": {"default": {"default": "grave"}}}, {"key": "FF5C", "mappings": {"default": {"default": "vertical bar"}}}, {"key": "FF5E", "mappings": {"default": {"default": "tilde"}}}, {"key": "FFE0", "mappings": {"default": {"default": "cent sign"}}}, {"key": "FFE1", "mappings": {"default": {"default": "pound sign"}}}, {"key": "FFE2", "mappings": {"default": {"default": "not sign"}}}, {"key": "FFE3", "mappings": {"default": {"default": "macron"}, "mathspeak": {"default": "bar"}}}, {"key": "FFE4", "mappings": {"default": {"default": "broken vertical bar"}}}, {"key": "FFE5", "mappings": {"default": {"default": "yen sign"}}}, {"key": "FFE6", "mappings": {"default": {"default": "won sign"}}}, {"key": "FFE8", "mappings": {"default": {"default": "halfwidth forms light vertical"}}}, {"key": "FFED", "mappings": {"default": {"default": "halfwidth black square"}}}, {"key": "FFEE", "mappings": {"default": {"default": "halfwidth white circle"}}}], "en/symbols/math_whitespace.min": [{"locale": "en"}, {"key": "0020", "mappings": {"default": {"default": "space"}}}, {"key": "00A0", "mappings": {"default": {"default": "no break space", "alternative": "non breaking space"}}}, {"key": "00AD", "mappings": {"default": {"default": "soft hyphen"}}}, {"key": "2000", "mappings": {"default": {"default": "en quad"}}}, {"key": "2001", "mappings": {"default": {"default": "em quad"}}}, {"key": "2002", "mappings": {"default": {"default": "en space"}}}, {"key": "2003", "mappings": {"default": {"default": "em space"}}}, {"key": "2004", "mappings": {"default": {"default": "three per em space"}}}, {"key": "2005", "mappings": {"default": {"default": "four per em space"}}}, {"key": "2006", "mappings": {"default": {"default": "six per em space"}}}, {"key": "2007", "mappings": {"default": {"default": "figure space"}}}, {"key": "2008", "mappings": {"default": {"default": "punctuation space"}}}, {"key": "2009", "mappings": {"default": {"default": "thin space"}}}, {"key": "200A", "mappings": {"default": {"default": "hair space"}}}, {"key": "200B", "mappings": {"default": {"default": "zero width space"}}}, {"key": "200C", "mappings": {"default": {"default": "zero width non joiner"}}}, {"key": "200D", "mappings": {"default": {"default": "zero width joiner"}}}, {"key": "200E", "mappings": {"default": {"default": "left to right mark"}}}, {"key": "200F", "mappings": {"default": {"default": "right to left mark"}}}, {"key": "2028", "mappings": {"default": {"default": "line separator"}}}, {"key": "2029", "mappings": {"default": {"default": "paragraph separator"}}}, {"key": "202A", "mappings": {"default": {"default": "left to right embedding"}}}, {"key": "202B", "mappings": {"default": {"default": "right to left embedding"}}}, {"key": "202C", "mappings": {"default": {"default": "pop directional formatting"}}}, {"key": "202D", "mappings": {"default": {"default": "left to right override"}}}, {"key": "202E", "mappings": {"default": {"default": "right to left override"}}}, {"key": "202F", "mappings": {"default": {"default": "narrow no break space"}}}, {"key": "205F", "mappings": {"default": {"default": "medium mathematical space"}}}, {"key": "2060", "mappings": {"default": {"default": "word joiner"}}}, {"key": "2061", "mappings": {"default": {"default": "of", "alternative": "function application"}}}, {"key": "2062", "mappings": {"default": {"default": "times", "alternative": "invisible times"}}}, {"key": "2063", "mappings": {"default": {"default": "comma", "alternative": "invisible separator"}}}, {"key": "2064", "mappings": {"default": {"default": "plus", "alternative": "invisible plus"}}}, {"key": "206A", "mappings": {"default": {"default": "inhibit symmetric swapping"}}}, {"key": "206B", "mappings": {"default": {"default": "activate symmetric swapping"}}}, {"key": "206E", "mappings": {"default": {"default": "national digit shapes"}}}, {"key": "206F", "mappings": {"default": {"default": "nominal digit shapes"}}}, {"key": "FEFF", "mappings": {"default": {"default": "zero width no break space", "alternative": "byte order mark"}}}, {"key": "FFF9", "mappings": {"default": {"default": "interlinear annotation anchor"}}}, {"key": "FFFA", "mappings": {"default": {"default": "interlinear annotation separator"}}}, {"key": "FFFB", "mappings": {"default": {"default": "interlinear annotation terminator"}}}], "en/symbols/other_stars.min": [{"locale": "en"}, {"key": "23E8", "mappings": {"default": {"default": "decimal exponent symbol"}}}, {"key": "2605", "mappings": {"default": {"default": "black star"}}}, {"key": "2606", "mappings": {"default": {"default": "white star"}}}, {"key": "26AA", "mappings": {"default": {"default": "white circle", "alternative": "medium white circle"}}}, {"key": "26AB", "mappings": {"default": {"default": "black circle", "alternative": "medium black circle"}}}, {"key": "2705", "mappings": {"default": {"default": "white check mark", "alternative": "white heavy check mark"}}}, {"key": "2713", "mappings": {"default": {"default": "check mark"}}}, {"key": "2714", "mappings": {"default": {"default": "heavy check mark"}}}, {"key": "2715", "mappings": {"default": {"default": "multiplication x"}}}, {"key": "2716", "mappings": {"default": {"default": "heavy multiplication x"}}}, {"key": "2717", "mappings": {"default": {"default": "ballot x"}}}, {"key": "2718", "mappings": {"default": {"default": "heavy ballot x"}}}, {"key": "271B", "mappings": {"default": {"default": "open center cross"}}}, {"key": "271C", "mappings": {"default": {"default": "heavy open center cross"}}}, {"key": "2720", "mappings": {"default": {"default": "maltese cross"}}}, {"key": "2721", "mappings": {"default": {"default": "star of david"}}}, {"key": "2722", "mappings": {"default": {"default": "four teardrop spoked asterisk"}}}, {"key": "2723", "mappings": {"default": {"default": "four balloon spoked asterisk"}}}, {"key": "2724", "mappings": {"default": {"default": "heavy four balloon spoked asterisk"}}}, {"key": "2725", "mappings": {"default": {"default": "four club spoked asterisk"}}}, {"key": "2726", "mappings": {"default": {"default": "black four pointed star"}}}, {"key": "2727", "mappings": {"default": {"default": "white four pointed star"}}}, {"key": "2728", "mappings": {"default": {"default": "sparkles"}}}, {"key": "2729", "mappings": {"default": {"default": "stress outlined white star"}}}, {"key": "272A", "mappings": {"default": {"default": "circled white star"}}}, {"key": "272B", "mappings": {"default": {"default": "open center black star"}}}, {"key": "272C", "mappings": {"default": {"default": "black center white star"}}}, {"key": "272D", "mappings": {"default": {"default": "outlined black star"}}}, {"key": "272E", "mappings": {"default": {"default": "heavy outlined black star"}}}, {"key": "272F", "mappings": {"default": {"default": "pinwheel star"}}}, {"key": "2730", "mappings": {"default": {"default": "shadowed white star"}}}, {"key": "2731", "mappings": {"default": {"default": "heavy asterisk"}}}, {"key": "2732", "mappings": {"default": {"default": "open center asterisk"}}}, {"key": "2733", "mappings": {"default": {"default": "eight spoked asterisk"}}}, {"key": "2734", "mappings": {"default": {"default": "eight pointed black star"}}}, {"key": "2735", "mappings": {"default": {"default": "eight pointed pinwheel star"}}}, {"key": "2736", "mappings": {"default": {"default": "six pointed black star"}}}, {"key": "2739", "mappings": {"default": {"default": "twelve pointed black star"}}}, {"key": "273A", "mappings": {"default": {"default": "sixteen pointed asterisk"}}}, {"key": "273B", "mappings": {"default": {"default": "teardrop spoked asterisk"}}}, {"key": "273C", "mappings": {"default": {"default": "open center teardrop spoked asterisk"}}}, {"key": "273D", "mappings": {"default": {"default": "heavy teardrop spoked asterisk"}}}, {"key": "273E", "mappings": {"default": {"default": "six petalled black and white florette"}}}, {"key": "273F", "mappings": {"default": {"default": "black florette"}}}, {"key": "2740", "mappings": {"default": {"default": "white florette"}}}, {"key": "2741", "mappings": {"default": {"default": "eight petalled outlined black florette"}}}, {"key": "2742", "mappings": {"default": {"default": "circled open center eight pointed star"}}}, {"key": "2743", "mappings": {"default": {"default": "heavy teardrop spoked pinwheel asterisk"}}}, {"key": "2744", "mappings": {"default": {"default": "snowflake"}}}, {"key": "2745", "mappings": {"default": {"default": "tight trifoliate snowflake"}}}, {"key": "2746", "mappings": {"default": {"default": "heavy chevron snowflake"}}}, {"key": "2747", "mappings": {"default": {"default": "sparkle"}}}, {"key": "2748", "mappings": {"default": {"default": "heavy sparkle"}}}, {"key": "2749", "mappings": {"default": {"default": "balloon spoked asterisk"}}}, {"key": "274A", "mappings": {"default": {"default": "eight teardrop spoked propeller asterisk"}}}, {"key": "274B", "mappings": {"default": {"default": "heavy eight teardrop spoked propeller asterisk"}}}, {"key": "274C", "mappings": {"default": {"default": "cross mark"}}}, {"key": "274D", "mappings": {"default": {"default": "shadowed white circle"}}}], "en/units/area.min": [{"locale": "en"}, {"key": "sq", "mappings": {"default": {"default": "square", "plural": "square"}}}, {"key": "sq inch", "mappings": {"default": {"default": "square inch", "plural": "square inches"}}}, {"key": "sq rd", "mappings": {"default": {"default": "square rod"}}}, {"key": "sq ft", "mappings": {"default": {"default": "square foot", "plural": "square feet"}}}, {"key": "sq yd", "mappings": {"default": {"default": "square yard"}}}, {"key": "sq mi", "mappings": {"default": {"default": "square mile"}}}, {"key": "acr", "mappings": {"default": {"default": "acre"}}}, {"key": "ha", "mappings": {"default": {"default": "hectare"}}}], "en/units/currency.min": [{"locale": "en"}, {"key": "$", "mappings": {"default": {"default": "dollar"}}}, {"key": "£", "mappings": {"default": {"default": "pound"}}}, {"key": "¥", "mappings": {"default": {"default": "yen"}}}, {"key": "€", "mappings": {"default": {"default": "euro"}}}, {"key": "₡", "mappings": {"default": {"default": "colon"}}}, {"key": "₢", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON>"}}}, {"key": "₣", "mappings": {"default": {"default": "franc"}}}, {"key": "₤", "mappings": {"default": {"default": "lira"}}}, {"key": "₥", "mappings": {"default": {"default": "mill"}}}, {"key": "₦", "mappings": {"default": {"default": "naira"}}}, {"key": "₧", "mappings": {"default": {"default": "peseta"}}}, {"key": "₨", "mappings": {"default": {"default": "rupee"}}}, {"key": "₩", "mappings": {"default": {"default": "won"}}}, {"key": "₪", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "₫", "mappings": {"default": {"default": "dong"}}}, {"key": "₭", "mappings": {"default": {"default": "kip"}}}, {"key": "₮", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₯", "mappings": {"default": {"default": "drachma"}}}, {"key": "₰", "mappings": {"default": {"default": "Pfennig"}}}, {"key": "₱", "mappings": {"default": {"default": "peso"}}}, {"key": "₲", "mappings": {"default": {"default": "guaranis"}}}, {"key": "₳", "mappings": {"default": {"default": "austral"}}}, {"key": "₴", "mappings": {"default": {"default": "hryvnia"}}}, {"key": "₵", "mappings": {"default": {"default": "cedi"}}}, {"key": "₸", "mappings": {"default": {"default": "tenge", "plural": "tenge"}}}, {"key": "₺", "mappings": {"default": {"default": "turkish lira", "plural": "turkish lira"}}}, {"key": "元", "mappings": {"default": {"default": "yuan"}}}, {"key": "¢", "mappings": {"default": {"default": "cent"}}}], "en/units/energy.min": [{"locale": "en"}, {"key": "W", "mappings": {"default": {"default": "watt"}}}, {"key": "kwh", "mappings": {"default": {"default": "kilowatt hour"}}}, {"key": "J", "mappings": {"default": {"default": "joule"}}}, {"key": "N", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "A", "mappings": {"default": {"default": "ampere"}}}, {"key": "V", "mappings": {"default": {"default": "volt"}}}, {"key": "ohm", "mappings": {"default": {"default": "ohm"}}}, {"key": "Ω", "mappings": {"default": {"default": "ohm"}}}, {"key": "MΩ", "mappings": {"default": {"default": "megohm"}}, "names": ["MΩ"]}, {"key": "kΩ", "mappings": {"default": {"default": "kilohm"}}, "names": ["kΩ"]}], "en/units/length.min": [{"locale": "en"}, {"key": "ft", "mappings": {"default": {"default": "foot", "plural": "feet"}}}, {"key": "in", "mappings": {"default": {"default": "inch", "plural": "inches"}}}, {"key": "mi", "mappings": {"default": {"default": "mile"}}}, {"key": "yd", "mappings": {"default": {"default": "yard"}}}, {"key": "link", "mappings": {"default": {"default": "link"}}}, {"key": "rod", "mappings": {"default": {"default": "rod"}}}, {"key": "chain", "mappings": {"default": {"default": "chain"}}}, {"key": "furlong", "mappings": {"default": {"default": "furlong"}}}, {"key": "n.m.", "mappings": {"default": {"default": "nautical mile"}}}, {"key": "m", "mappings": {"default": {"default": "meter"}}}], "en/units/memory.min": [{"locale": "en"}, {"key": "b", "mappings": {"default": {"default": "bit"}}}, {"key": "B", "mappings": {"default": {"default": "byte"}}}, {"key": "KB", "mappings": {"default": {"default": "kilobyte"}}}], "en/units/other.min": [{"locale": "en"}, {"key": "doz", "mappings": {"default": {"default": "dozen", "plural": "dozen"}}}], "en/units/speed.min": [{"locale": "en"}, {"key": "kt", "mappings": {"default": {"default": "knot"}}}, {"key": "mph", "mappings": {"default": {"default": "mile per hour", "plural": "miles per hour"}}}, {"key": "rpm", "mappings": {"default": {"default": "revolution per minute", "plural": "revolutions per minute"}}}, {"key": "kmh", "mappings": {"default": {"default": "kilometer per hour", "plural": "kilometers per hour"}}}], "en/units/temperature.min": [{"locale": "en"}, {"key": "F", "mappings": {"default": {"default": "Fahrenheit", "plural": "Fahrenheit"}}}, {"key": "C", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>", "alternative": "Centigrade"}}}, {"key": "K", "mappings": {"default": {"default": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>"}}}], "en/units/time.min": [{"locale": "en"}, {"key": "s", "mappings": {"default": {"default": "second"}}}, {"key": "″", "mappings": {"default": {"default": "second"}}}, {"key": "min", "mappings": {"default": {"default": "minute"}}}, {"key": "°", "mappings": {"default": {"default": "degree"}}}, {"key": "h", "mappings": {"default": {"default": "hour"}}}], "en/units/volume.min": [{"locale": "en"}, {"key": "cu", "mappings": {"default": {"default": "cubic", "plural": "cubic"}}}, {"key": "cu inch", "mappings": {"default": {"default": "cubic inch", "plural": "cubic inches"}}}, {"key": "cu ft", "mappings": {"default": {"default": "cubic foot", "plural": "cubic feet"}}}, {"key": "cu yd", "mappings": {"default": {"default": "cubic yard"}}}, {"key": "bbl", "mappings": {"default": {"default": "barrel"}}}, {"key": "fl. oz.", "mappings": {"default": {"default": "fluid ounce"}}}, {"key": "gal", "mappings": {"default": {"default": "gallon"}}}, {"key": "pt", "mappings": {"default": {"default": "pint"}}}, {"key": "qt", "mappings": {"default": {"default": "quart"}}}, {"key": "fluid dram", "mappings": {"default": {"default": "fluid dram"}}}, {"key": "tbsp", "mappings": {"default": {"default": "tablespoon"}}}, {"key": "tsp", "mappings": {"default": {"default": "teaspoon"}}}, {"key": "cup", "mappings": {"default": {"default": "cup"}}}, {"key": "cc", "mappings": {"default": {"default": "cubic centimeter"}}}, {"key": "l", "mappings": {"default": {"default": "liter"}}}], "en/units/weight.min": [{"locale": "en"}, {"key": "dram", "mappings": {"default": {"default": "dram"}}}, {"key": "oz", "mappings": {"default": {"default": "ounce"}}}, {"key": "lb", "mappings": {"default": {"default": "pound"}}}, {"key": "st", "mappings": {"default": {"default": "stone"}}}, {"key": "qtr", "mappings": {"default": {"default": "quarter"}}}, {"key": "cwt", "mappings": {"default": {"default": "hundredweight"}}}, {"key": "LT", "mappings": {"default": {"default": "long ton"}}}, {"key": "gr", "mappings": {"default": {"default": "gram"}}}, {"key": "g", "mappings": {"default": {"default": "gram"}}}, {"key": "mcg", "mappings": {"default": {"default": "microgram"}}}, {"key": "t", "mappings": {"default": {"default": "ton"}}}], "en/rules/chromevox.min": {"locale": "en", "domain": "chromevox", "modality": "speech", "rules": [["Precondition", "collapsed", "default", "self::*[@alternative]", "not(contains(@grammar, \"collapsed\"))"], ["Precondition", "stree", "default", "self::stree"], ["Precondition", "factorial", "default", "self::punctuation", "text()=\"!\"", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "variable-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "multi-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)>2"], ["Precondition", "equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2"], ["Precondition", "simple-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2", "./children/identifier or ./children/number"], ["Precondition", "simple-equality2", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2", "./children/function or ./children/appl"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "binary-operation", "default", "self::infixop"], ["Precondition", "variable-addition", "default", "self::infixop[@role=\"addition\"]", "count(children/*)>2", "children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "multi-addition", "default", "self::infixop[@role=\"addition\"]", "count(./children/*)>2"], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "negative", "default", "self::prefixop", "self::prefixop[@role=\"negative\"]"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "identifier", "default", "self::identifier"], ["Precondition", "number", "default", "self::number"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "font", "default", "self::*[@font!=\"normal\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "font-identifier-short", "default", "self::identifier", "string-length(text())=1", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font=\"normal\"", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")", "@role!=\"unit\""], ["Precondition", "font-identifier", "default", "self::identifier", "string-length(text())=1", "@font", "@font=\"normal\"", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "omit-font", "default", "self::identifier[@font=\"italic\"]", "string-length(text())=1", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "ellipsis", "default", "self::punctuation", "self::punctuation[@role=\"ellipsis\"]"], ["Precondition", "fence-single", "default", "self::punctuation", "self::punctuation[@role=\"openfence\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"closefence\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"vbar\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"application\"]"], ["Precondition", "omit-empty", "default", "self::empty"], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["Precondition", "fences-open-close-in-appl", "default", "self::fenced[@role=\"leftright\"]", "./parent::children/parent::appl"], ["Precondition", "fences-neutral", "default", "self::fenced[@role=\"neutral\"]"], ["Precondition", "omit-fences", "default", "self::fenced"], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-row", "default", "self::row[@role=\"matrix\"]"], ["Precondition", "matrix-cell", "default", "self::cell[@role=\"matrix\"]"], ["Precondition", "vector", "default", "self::vector"], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "cases-row", "default", "self::row[@role=\"cases\"]"], ["Precondition", "cases-cell", "default", "self::cell[@role=\"cases\"]"], ["Precondition", "row", "default", "self::row"], ["Precondition", "cases-end", "default", "self::cases", "following-sibling::*"], ["Precondition", "multiline", "default", "self::multiline"], ["Precondition", "multiline-ineq", "default", "self::multiline", "@role=\"inequality\""], ["Precondition", "line", "default", "self::line"], ["Precondition", "table", "default", "self::table"], ["Precondition", "table-ineq", "default", "self::table", "@role=\"inequality\""], ["Precondition", "table-row", "default", "self::row[@role=\"table\"]"], ["<PERSON><PERSON>", "cases-cell", "self::cell[@role=\"table\"]"], ["Precondition", "empty-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "end-punct", "default", "self::punctuated", "@role=\"endpunct\""], ["Precondition", "start-punct", "default", "self::punctuated", "@role=\"startpunct\""], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "function", "default", "self::function"], ["Precondition", "appl", "default", "self::appl"], ["Precondition", "sum-only", "default", "self::limboth", "self::limboth[@role=\"sum\"]"], ["Precondition", "limboth", "default", "self::limboth"], ["Precondition", "limlower", "default", "self::lim<PERSON>er"], ["Precondition", "limupper", "default", "self::limupper"], ["Precondition", "largeop", "default", "self::largeop"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "integral", "default", "self::integral"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "square", "default", "self::superscript", "children/*[2][text()=2]", "name(./children/*[1])!=\"text\""], ["Precondition", "cube", "default", "self::superscript", "children/*[2][text()=3]", "name(./children/*[1])!=\"text\""], ["Precondition", "root", "default", "self::root"], ["Precondition", "text", "default", "self::text"], ["Precondition", "unit", "default", "self::identifier", "@role=\"unit\""], ["Precondition", "unit-square", "default", "self::superscript", "@role=\"unit\"", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-cubic", "default", "self::superscript", "@role=\"unit\"", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Precondition", "reciprocal", "default", "self::superscript", "@role=\"unit\"", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "count(preceding-sibling::*)=0 or preceding-sibling::*[@role!=\"unit\"]"], ["Precondition", "per", "default", "self::superscript", "@role=\"unit\"", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "preceding-sibling::*[@role=\"unit\"]"], ["Precondition", "unit-combine", "default", "self::infixop", "@role=\"unit\""], ["Precondition", "unit-divide", "default", "self::fraction", "@role=\"unit\""]]}, "en/rules/chromevox_actions.min": {"locale": "en", "domain": "chromevox", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"collapsed\"; [n] . (engine:modality=summary,grammar:collapsed)"], ["Action", "stree", "[n] ./*[1]"], ["Action", "factorial", "[t] \"factorial\""], ["Action", "mult<PERSON>", "[t] \"multirelation\"; [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "variable-equality", "[t] \"equation sequence\"; [m] children/* (context:\"part\",ctxtFunc:CTFnodeCounter,sepFunc:CTFcontentIterator)"], ["Action", "multi-equality", "[t] \"equation sequence\"; [m] children/* (context:\"part\",ctxtFunc:CTFnodeCounter,sepFunc:CTFcontentIterator)"], ["Action", "equality", "[n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[n] children/*[2]"], ["Action", "simple-equality", "[n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[n] children/*[2]"], ["Action", "simple-equality2", "[n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[n] children/*[2]"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "binary-operation", "[m] children/* (sepFunc:CTFcontentIterator);"], ["Action", "variable-addition", "[t] \"sum with variable number of summands\" (pause:400); [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "multi-addition", "[t] \"sum with\"; [t] count(./children/*); [t] \"summands\" (pause:400); [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "prefix", "[t] \"prefix\"; [m] content/* (pause 150);[n] children/*[1]"], ["Action", "negative", "[t] \"negative\"; [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [t] \"postfix\"; [m] content/* (pause 300)"], ["Action", "identifier", "[n] text()"], ["Action", "number", "[n] text()"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"and\"; [n] children/*[2]; "], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-identifier-short", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-identifier", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "omit-font", "[n] . (grammar:ignoreFont=@font)"], ["Action", "fraction", "[p] (pause:250); [n] children/*[1] (rate:0.35, pause:250); [t] \"divided by\"; [n] children/*[2] (rate:-0.35, pause:400)"], ["Action", "superscript", "[n] children/*[1]; [t] \"super\"; [n] children/*[2] (pitch:0.35, pause:300)"], ["Action", "subscript", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2] (pitch:-0.35, pause:300)"], ["Action", "ellipsis", "[p] (pause:200); [t] \"ellipsis\" (pause:300)"], ["Action", "fence-single", "[n] text()"], ["Action", "omit-empty", "[p] (pause:100)"], ["Action", "fences-open-close", "[p] (pause:100); [n] content/*[1]; [n] children/*[1]; [n] content/*[2] (pause:100)"], ["Action", "fences-open-close-in-appl", "[p] (pause:200); [n] children/*[1] (pause:200);"], ["Action", "fences-neutral", "[p] (pause:100); [t] \"absolute value of\"; [n] children/*[1] (pause:350);"], ["Action", "omit-fences", "[p] (pause:500); [n] children/*[1] (pause:200);"], ["Action", "matrix", "[t] \"matrix\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:100)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter,context:\"column\",pause:100)"], ["Action", "matrix-cell", "[n] children/*[1]"], ["Action", "vector", "[t] \"vector\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"element\",pause:100)"], ["Action", "cases", "[t] \"case statement\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"case\",pause:100)"], ["Action", "cases-row", "[m] children/*"], ["Action", "cases-cell", "[n] children/*[1]"], ["Action", "row", "[m] ./* (ctxtFunc:CTFnodeCounter,context:\"column\",pause:100)"], ["Action", "cases-end", "[t] \"case statement\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"case\",pause:100);[t] \"end cases\""], ["Action", "multiline", "[t] \"multiline equation\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"line\",pause:100)"], ["Action", "multiline-ineq", "[t] \"multiline inequality\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:100)"], ["Action", "line", "[m] children/*"], ["Action", "table", "[t] \"multiline equation\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:200)"], ["Action", "table-ineq", "[t] \"multiline inequality\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:200)"], ["Action", "table-row", "[m] children/* (pause:100)"], ["Action", "empty-cell", "[t] \"Blank\""], ["Action", "end-punct", "[m] children/* (pause:300)"], ["Action", "start-punct", "[n] content/*[1] (pause:200); [m] children/*[position()>1]"], ["Action", "punctuated", "[m] children/* (pause:100)"], ["Action", "function", "[n] text()"], ["Action", "appl", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2]"], ["Action", "sum-only", "[n] children/*[1]; [t] \"from\"; [n] children/*[2]; [t] \"to\";[n] children/*[3]"], ["Action", "limboth", "[n] children/*[1] (pause 100); [t] \"over\"; [n] children/*[2];[t] \"under\"; [n] children/*[3] (pause 250);"], ["Action", "limlower", "[n] children/*[1]; [t] \"over\"; [n] children/*[2];"], ["Action", "limupper", "[n] children/*[1]; [t] \"under\"; [n] children/*[2];"], ["Action", "largeop", "[n] text()"], ["Action", "bigop", "[n] children/*[1] (pause 100); [t] \"over\"; [n] children/*[2] (pause 250);"], ["Action", "integral", "[n] children/*[1] (pause 100); [n] children/*[2] (pause 200); [n] children/*[3] (rate:0.35);"], ["Action", "sqrt", "[t] \"Square root of\"; [n] children/*[1] (rate:0.35, pause:400)"], ["Action", "square", "[n] children/*[1]; [t] \"squared\" (pitch:0.35, pause:300)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubed\" (pitch:0.35, pause:300)"], ["Action", "root", "[t] \"root of order\"; [n] children/*[1];[t] \"over\"; [n] children/*[2] (rate:0.35, pause:400)"], ["Action", "text", "[n] text() (pause:200)"], ["Action", "unit", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-square", "[t] \"square\"; [n] children/*[1]"], ["Action", "unit-cubic", "[t] \"cubic\"; [n] children/*[1]"], ["Action", "reciprocal", "[t] \"reciprocal\"; [n] children/*[1]"], ["Action", "per", "[t] \"per\"; [n] children/*[1]"], ["Action", "unit-combine", "[m] children/*"], ["Action", "unit-divide", "[n] children/*[1] (pitch:0.3); [t] \"per\"; [n] children/*[2] (pitch:-0.3)"]]}, "en/rules/clearspeak_english.min": {"domain": "clearspeak", "locale": "en", "modality": "speech", "inherits": "base", "rules": [["Precondition", "german-font", "default", "self::*[@font=\"fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "german-font-bold", "default", "self::*[@font=\"bold-fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "unit-square", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\"", "children/*[1][@category=\"unit:length\"]"], ["Precondition", "unit-cubic", "default", "self::superscript[@role=\"unit\"]", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\"", "children/*[1][@category=\"unit:length\"]"], ["Rule", "caley-table", "default", "[p] (pause:short); [t] \"cayley table with\"; [t] count(children/*)-1; [t] \"elements\";  [n] . (grammar:?layoutSummary)", "self::table[@role=\"cayley\"]", "not(contains(@grammar, \"layoutSummary\"))"], ["Rule", "caley-table-operator", "default", "[p] (pause:short); [t] \"cayley table for\"; [n] children/*[1]/children/*[1]/children/*[1]; [t] \"with\"; [t] count(children/*)-1; [t] \"elements\";  [n] . (grammar:?layoutSummary)", "self::table[@role=\"cayley\"]", "name(children/*[1]/children/*[1]/children/*[1])=\"operator\"", "not(contains(@grammar, \"layoutSummary\"))"]]}, "en/rules/clearspeak_english_actions.min": {"domain": "clearspeak", "locale": "en", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"collapsed\"; [n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "font", "[t] @font (grammar:localFont); [n] . (pause:short, grammar:ignoreFont=@font)"], ["Action", "german-font", "[t] \"German\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font-bold", "[t] \"bold German\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "ellipsis", "[t] \"and so on\""], ["Action", "ellipsis-andsoon", "[t] \"and so on up to\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"evaluated at\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [t] \"evaluated at\"; [n] content/*[1]/children/*[2] (pause:short); [t] \"minus the same expression evaluated at\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-such-that", "[t] \"such that\""], ["Action", "vbar-divides", "[t] \"divides\""], ["Action", "vbar-always-divides", "[t] \"divides\""], ["Action", "vbar-given", "[t] \"given\""], ["Action", "member", "[t] \"is a member of\""], ["Action", "member-member", "[t] \"is a member of\""], ["Action", "member-element", "[t] \"is an element of\""], ["Action", "member-in", "[t] \"is in\""], ["Action", "member-belongs", "[t] \"belongs to\""], ["Action", "not-member", "[t] \"is not a member of\""], ["Action", "not-member-member", "[t] \"is not a member of\""], ["Action", "not-member-element", "[t] \"is not an element of\""], ["Action", "not-member-in", "[t] \"is not in\""], ["Action", "not-member-belongs", "[t] \"does not belong to\""], ["Action", "set-member", "[t] \"in\""], ["Action", "set-member-member", "[t] \"member of\""], ["Action", "set-member-element", "[t] \"element of\""], ["Action", "set-member-in", "[t] \"in\""], ["Action", "set-member-belongs", "[t] \"belonging to\""], ["Action", "set-not-member", "[t] \"not in\""], ["Action", "set-not-member-member", "[t] \"not a member of\""], ["Action", "set-not-member-element", "[t] \"not an element of\""], ["Action", "set-not-member-in", "[t] \"not in\""], ["Action", "set-not-member-belongs", "[t] \"not belonging to\""], ["Action", "article", "[t] \"the\"; [n] . (grammar:noArticle)"], ["Action", "appl", "[n] children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"of\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"of\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"times\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1] (grammar:addArticle); [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1] (grammar:addArticle); [t] \"of\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"of\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"of\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"the inverse\"; [n] children/*[1]/children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1]; [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"inverse\""], ["Action", "superscript-prefix-function", "[n] children/*[2] (grammar:ordinal:addArticle); [t] \"power of\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"raised to the exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"end exponent\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"raised to the\"; [n] children/*[2] (grammar:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"raised to the\"; [n] children/*[2] (grammar:noArticle); [t] \"power\""], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (grammar:ordinal:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (grammar:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-simple-function", "[n] children/*[2] (grammar:ordinal:addArticle); [t] \"power of\" (pause:short); [n] children/*[1]"], ["Action", "superscript-simple-function-none", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (pause:short, grammar:ordinal:noArticle)"], ["Action", "superscript-ordinal-negative", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (pause:short, grammar:noArticle)"], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"raised to the exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"end exponent\" (pause:short)"], ["Action", "superscript-ordinal-power-number", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (grammar:ordinal:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-ordinal-power-negative", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (grammar:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-ordinal-power-identifier", "[n] children/*[1]; [t] \"to the\"; [n] children/*[2] (grammar:ordinal:noArticle); [t] \"power\" (pause:short)"], ["Action", "superscript-ordinal-power-default", "[n] children/*[1]; [t] \"raised to the exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"end exponent\" (pause:short)"], ["Action", "superscript-power", "[n] children/*[1]; [t] \"raised to the power\"; [n] children/*[2] (pause:short, grammar:afterPower)"], ["Action", "superscript-power-default", "[n] children/*[1]; [t] \"raised to the exponent\" (pause:short); [n] children/*[2] (pause:short); [t] \"end exponent\" (pause:short)"], ["Action", "exponent", "[n] text() (join:\"-\"); [t] \"th\""], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"zero\""], ["Action", "exponent-ordinalpower", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinalpower-zero", "[t] \"zero\""], ["Action", "square", "[n] children/*[1]; [t] \"squared\" (span:children/*[2])"], ["Action", "cube", "[n] children/*[1]; [t] \"cubed\" (span:children/*[2])"], ["Action", "fences-points", "[t] \"the point with coordinates\"; [n] children/*[1]"], ["Action", "fences-interval", "[t] \"the interval from\"; [n] children/*[1]/children/*[1]; [t] \"to\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"not including\"; [n] children/*[1]/children/*[1]; [t] \"or\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open", "[t] \"including\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"but not including\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-closed", "[t] \"not including\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"but including\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed", "[t] \"including\"; [n] children/*[1]/children/*[1]; [t] \"and\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-inf-r", "[t] \"not including\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-inf-l", "[t] \"not including\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open-inf", "[t] \"including\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-closed-inf", "[t] \"including\"; [n] children/*[1]/children/*[3]"], ["Action", "set-empty", "[t] \"the empty set\""], ["Action", "set-extended", "[t] \"the set of all\"; [n] children/*[1]/children/*[1]; [t] \"such that\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"the set\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[t] \"the set of\"; [n] children/*[1]/children/*[1]; [t] \"such that\"; [n] children/*[1]/children/*[3]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"sub\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"the\" (grammar:article); [t] \"fraction with numerator\"; [n] children/*[1] (pause:short); [t] \"and denominator\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"the\" (grammar:article); [t] \"fraction with numerator\"; [n] children/*[1] (pause:short); [t] \"and denominator\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short); [t] \"end fraction\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"the fraction\"; [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"the fraction with numerator\"; [n] children/*[1] (pause:short); [t] \"and denominator\"; [n] children/*[2] (pause:short); [t] \"end fraction\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"the fraction with numerator\"; [n] children/*[1] (pause:short); [t] \"and denominator\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"end fraction\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction"], ["Action", "sqrt", "[t] \"the square root of\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested", "[p] (pause:\"short\"); [t] \"the square root of\"; [n] children/*[1] (pause:short)"], ["Action", "negative-sqrt", "[t] \"the negative square root of\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "negative-sqrt-default", "[p] (pause:\"short\"); [t] \"the negative square root of\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus", "[t] \"the positive square root of\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:\"short\"); [t] \"the positive square root of\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"the positive square root of\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:\"short\"); [t] \"the positive square root of\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "cubic", "[t] \"the cube root of\"; [n] children/*[2] (pause:short)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"the cube root of\"; [n] children/*[2] (pause:short)"], ["Action", "root", "[n] children/*[1] (grammar:ordinal:addArticle); [t] \"root of\"; [n] children/*[2] (pause:short)"], ["Action", "root-nested", "[p] (pause:short); [n] children/*[1] (grammar:ordinal:addArticle); [t] \"root of\"; [n] children/*[2] (pause:short)"], ["Action", "root-endroot", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"end root\" (pause:short)"], ["Action", "negative", "[t] \"negative\" (span:content/*[1]); [n] children/*[1]"], ["Action", "positive", "[t] \"positive\" (span:content/*[1]); [n] children/*[1]"], ["Action", "angle-measure", "[t] \"the measure of\"; [n] content/*[1]; [n] children/*[2] (grammar:angle)"], ["Action", "set-prefix-operators", "[n] . (grammar:addArticle); [t] \"of\""], ["Action", "division", "[n] children/*[1]; [t] \"divided by\" (span:content/*[1]); [n] children/*[2]"], ["Action", "operators-after-power", "[m] children/* (rate:\"0.5\")"], ["Action", "natural-numbers", "[t] \"the natural numbers\""], ["Action", "integers", "[t] \"the integers\""], ["Action", "rational-numbers", "[t] \"the rational numbers\""], ["Action", "real-numbers", "[t] \"the real numbers\""], ["Action", "complex-numbers", "[t] \"the complex numbers\""], ["Action", "natural-numbers-with-zero", "[t] \"the natural numbers with zero\""], ["Action", "positive-integers", "[t] \"the positive integers\""], ["Action", "negative-integers", "[t] \"the negative integers\""], ["Action", "positive-rational-numbers", "[t] \"the positive rational numbers\""], ["Action", "negative-rational-numbers", "[t] \"the negative rational numbers\""], ["Action", "fences-neutral", "[p] (pause:short); [t] \"the absolute value of\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"the absolute value of\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"end absolute value\" (span:content/*[1], pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [t] \"the cardinality of\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"the determinant of\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"the metric of\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"the metric of\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"end metric\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"matrix\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long)"], ["Action", "matrix-simple", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"matrix\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"the 1 by 1 matrix with entry\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"the determinant of the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"matrix\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"the determinant of the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"matrix\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long)"], ["Action", "matrix-vector", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"column matrix\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"column matrix\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"column matrix\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"row matrix\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Column-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"row matrix\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"row matrix\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"Column-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"end matrix\""], ["Action", "matrix-end-vector", "[n] . (grammar:?EndMatrix); [t] \"end matrix\""], ["Action", "matrix-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"end determinant\""], ["Action", "vector", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"column vector\" (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"column vector\" (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"row vector\" (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Column-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] \"the\"; [t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"row vector\" (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"end matrix\""], ["Action", "vector-end-vector", "[n] . (grammar:?EndMatrix); [t] \"end vector\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:?EndMatrix); [t] \"end vector\""], ["Action", "vector-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"end determinant\""], ["Action", "binomial", "[n] children/*[1]/children/*[1]; [t] \"choose\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"lines\"; [n] . (grammar:?layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"cases\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Line-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"blank\""], ["Action", "blank-line", "[t] \"blank\""], ["Action", "blank-cell-empty", "[t] \"blank\""], ["Action", "blank-line-empty", "[t] \"blank\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Case-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"cases\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Case-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"equations\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Equation-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \"steps\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Step-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"rows\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Row-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"constraints\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Constraint-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[n] children/*[1] (grammar:addArticle); [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"from\"; [n] children/*[2]; [t] \"to\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"under\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[n] children/*[1] (grammar:addArticle); [t] \"of\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[n] children/*[1] (grammar:addArticle); [t] \"of\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"under\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"to\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"over\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"from\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"and\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"number\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"the repeating decimal\"; [n] children/*[1] (grammar:spaceout); [t] \"point followed by repeating digits\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-float", "[t] \"the repeating decimal\"; [n] children/*[1] (grammar:spaceout); [t] \"followed by repeating digits\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular", "[t] \"the repeating decimal\"; [n] children/*[1] (grammar:spaceout); [t] \"point followed by repeating digit\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular-float", "[t] \"the repeating decimal\"; [n] children/*[1] (grammar:spaceout); [t] \"followed by repeating digit\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-point", "[t] \"point\""], ["Action", "line-segment", "[t] \"the line segment\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"the complex conjugate of\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"is defined to be\" (pause:short)"], ["Action", "adorned-sign", "[n] children/*[1]; [t] \"sign with\"; [n] children/*[2]; [t] \"over it\""], ["Action", "factorial", "[t] \"factorial\""], ["Action", "left-super", "[t] \"left super\"; [n] text()"], ["Action", "left-super-list", "[t] \"left super\"; [m] children/*"], ["Action", "left-sub", "[t] \"left sub\"; [n] text()"], ["Action", "left-sub-list", "[t] \"left sub\"; [m] children/*"], ["Action", "right-super", "[t] \"right super\"; [n] text()"], ["Action", "right-super-list", "[t] \"right super\"; [m] children/*"], ["Action", "right-sub", "[t] \"right sub\"; [n] text()"], ["Action", "right-sub-list", "[t] \"right sub\"; [m] children/*"], ["Action", "choose", "[n] children/*[2] (grammar:combinatorics); [t] \"choose\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[n] children/*[2] (grammar:combinatorics); [t] \"permute\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[t] \"square\"; [n] children/*[1]"], ["Action", "unit-cubic", "[t] \"cubic\"; [n] children/*[1]"], ["Action", "unit-reciprocal", "[t] \"reciprocal\"; [n] children/*[1]"], ["Action", "unit-reciprocal-singular", "[t] \"per\"; [n] children/*[1] (grammar:singular)"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"per\"; [n] children/*[2] (grammar:singular)"], ["Action", "enclose", "[t] \"enclosed with\"; [t] @role (grammar:localEnclose); [n] children/*[1]"], ["Action", "enclose-end", "[t] \"enclosed with\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"end enclosed\""], ["Action", "overbar", "[n] children/*[1]; [t] \"horizontal bar\""], ["Action", "underbar", "[n] children/*[1]; [t] \"over horizontal bar\""], ["Action", "leftbar", "[t] \"vertical bar\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"vertical bar\""], ["Action", "crossout", "[t] \"crossed out\"; [n] children/*[1]"], ["Action", "crossout-end", "[t] \"crossed out\"; [n] children/*[1]; [t] \"end crossout\""], ["Action", "cancel-over", "[n] children/*[1]/children/*[1]; [t] \"crossed out with\"; [n] children/*[2]"], ["Action", "cancel-under", "[n] children/*[2]/children/*[1]; [t] \"crossed out with\"; [n] children/*[1]"], ["Action", "cancel-over-end", "[t] \"crossed out\"; [n] children/*[1]/children/*[1]; [t] \"with\"; [n] children/*[2]; [t] \"end crossout\""], ["Action", "cancel-under-end", "[t] \"crossed out\"; [n] children/*[2]/children/*[1]; [t] \"with\"; [n] children/*[1]; [t] \"end crossout\""]]}, "en/rules/emacspeak.min": {"domain": "emacspeak", "locale": "en", "modality": "speech", "rules": [["Precondition", "stree", "default", "self::stree"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "variable-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)>2", "./children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "multi-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)>2"], ["Precondition", "equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2"], ["Precondition", "simple-equality", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2", "./children/identifier or ./children/number"], ["Precondition", "simple-equality2", "default", "self::relseq[@role=\"equality\"]", "count(./children/*)=2", "./children/function or ./children/appl"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "implicit", "default", "self::infixop", "@role=\"implicit\"", "children/*[1][@role=\"latinletter\"] or children/*[1][@role=\"greekletter\"] or children/*[1][@role=\"otherletter\"] or name(children/*[1])=\"number\"", "children/*[2][@role=\"latinletter\"] or children/*[2][@role=\"greekletter\"] or children/*[2][@role=\"otherletter\"] or name(children/*[2])=\"number\""], ["Precondition", "binary-operation", "default", "self::infixop"], ["Precondition", "variable-addition", "default", "self::infixop[@role=\"addition\"]", "count(children/*)>2", "children/punctuation[@role=\"ellipsis\"]"], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "negative", "default", "self::prefixop", "self::prefixop[@role=\"negative\"]"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "identifier", "default", "self::identifier"], ["Precondition", "number", "default", "self::number"], ["Precondition", "font", "default", "self::*", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\""], ["Precondition", "font-identifier-short", "default", "self::identifier", "string-length(text())=1", "@font", "@font=\"normal\"", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")", "@role!=\"unit\""], ["Precondition", "font-identifier", "default", "self::identifier", "string-length(text())=1", "@font", "@font=\"normal\"", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\""], ["Precondition", "omit-font", "default", "self::identifier", "string-length(text())=1", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font=\"italic\""], ["Precondition", "simple-fraction", "default", "self::fraction", "name(children/*[1])=\"number\" or name(children/*[1])=\"identifier\"", "name(children/*[2])=\"number\" or name(children/*[2])=\"identifier\""], ["Precondition", "vulgar-fraction", "default", "self::fraction", "@role=\"vulgar\"", "CQFvulgarFractionSmall"], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "ellipsis", "default", "self::punctuation", "self::punctuation[@role=\"ellipsis\"]"], ["Precondition", "fence-single", "default", "self::punctuation", "self::punctuation[@role=\"openfence\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"closefence\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"vbar\"]"], ["<PERSON><PERSON>", "fence-single", "self::punctuation", "self::punctuation[@role=\"application\"]"], ["Precondition", "omit-empty", "default", "self::empty"], ["Precondition", "fences-open-close", "default", "self::fenced", "@role=\"leftright\""], ["Precondition", "fences-open-close-in-appl", "default", "self::fenced[@role=\"leftright\"]", "./parent::children/parent::appl"], ["Precondition", "fences-neutral", "default", "self::fenced[@role=\"neutral\"]"], ["Precondition", "omit-fences", "default", "self::fenced"], ["Precondition", "matrix", "default", "self::matrix"], ["Precondition", "matrix-row", "default", "self::row[@role=\"matrix\"]"], ["Precondition", "matrix-cell", "default", "self::cell[@role=\"matrix\"]"], ["Precondition", "vector", "default", "self::vector"], ["Precondition", "cases", "default", "self::cases"], ["Precondition", "cases-row", "default", "self::row[@role=\"cases\"]"], ["Precondition", "cases-cell", "default", "self::cell[@role=\"cases\"]"], ["Precondition", "row", "default", "self::row"], ["Precondition", "cases-end", "default", "self::cases", "following-sibling::*"], ["Precondition", "multiline", "default", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "table", "default", "self::table"], ["Precondition", "table-row", "default", "self::row[@role=\"table\"]"], ["<PERSON><PERSON>", "cases-cell", "self::cell[@role=\"table\"]"], ["Precondition", "end-punct", "default", "self::punctuated", "@role=\"endpunct\""], ["Precondition", "start-punct", "default", "self::punctuated", "@role=\"startpunct\""], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "function", "default", "self::function"], ["Precondition", "appl", "default", "self::appl"], ["Precondition", "sum-only", "default", "self::limboth", "@role=\"sum\" or @role=\"integral\""], ["Precondition", "limboth", "default", "self::limboth"], ["Precondition", "limlower", "default", "self::lim<PERSON>er"], ["Precondition", "limupper", "default", "self::limupper"], ["Precondition", "largeop", "default", "self::largeop"], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "integral", "default", "self::integral"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "square", "default", "self::superscript", "children/*[2][text()=2]", "name(./children/*[1])!=\"text\""], ["Precondition", "cube", "default", "self::superscript", "children/*[2][text()=3]", "name(./children/*[1])!=\"text\""], ["Precondition", "root", "default", "self::root"], ["Precondition", "text-no-mult", "default", "self::infixop", "children/text"], ["Precondition", "text", "default", "self::text"], ["Precondition", "unit", "default", "self::identifier", "@role=\"unit\""], ["Precondition", "unit-square", "default", "self::superscript", "@role=\"unit\"", "children/*[2][text()=2]", "name(children/*[1])=\"identifier\""], ["Precondition", "unit-cubic", "default", "self::superscript", "@role=\"unit\"", "children/*[2][text()=3]", "name(children/*[1])=\"identifier\""], ["Precondition", "reciprocal", "default", "self::superscript", "@role=\"unit\"", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "count(preceding-sibling::*)=0 or preceding-sibling::*[@role!=\"unit\"]"], ["Precondition", "per", "default", "self::superscript", "@role=\"unit\"", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "preceding-sibling::*[@role=\"unit\"]"], ["Precondition", "unit-combine", "default", "self::infixop", "@role=\"unit\""], ["Precondition", "unit-divide", "default", "self::fraction", "@role=\"unit\""]]}, "en/rules/emacspeak_actions.min": {"domain": "emacspeak", "locale": "en", "modality": "speech", "kind": "actions", "rules": [["Action", "stree", "[n] ./*[1]"], ["Action", "mult<PERSON>", "[t] \"multirelation\"; [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "variable-equality", "[t] \"equation sequence\"; [m] children/* (context:\"part\",ctxtFunc:CTFnodeCounter,sepFunc:CTFcontentIterator)"], ["Action", "multi-equality", "[t] \"equation sequence\"; [m] children/* (context:\"part\",ctxtFunc:CTFnodeCounter,sepFunc:CTFcontentIterator)"], ["Action", "equality", "[t] \"equation\"; [t] \"left hand side\"; [n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[t] \"right hand side\"; [n] children/*[2]"], ["Action", "simple-equality", "[n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[n] children/*[2]"], ["Action", "simple-equality2", "[n] children/*[1] (pause:200); [n] content/*[1] (pause:200);[n] children/*[2]"], ["Action", "relseq", "[m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "implicit", "[m] children/*"], ["Action", "binary-operation", "[p] (pause:100); [m] children/* (sepFunc:CTFcontentIterator, pause:100);"], ["Action", "variable-addition", "[t] \"sum with variable number of summands\" (pause:400); [m] children/* (sepFunc:CTFcontentIterator)"], ["Action", "prefix", "[t] \"prefix\"; [n] text(); [t] \"of\" (pause 150);[n] children/*[1]"], ["Action", "negative", "[t] \"negative\"; [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [t] \"postfix\"; [n] text() (pause 300)"], ["Action", "identifier", "[n] text()"], ["Action", "number", "[n] text()"], ["Action", "font", "[t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "font-identifier-short", "[t] @font; [n] CQFhideFont; [t] CSFshowFont"], ["Action", "font-identifier", "[t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "omit-font", "[n] . (grammar:ignoreFont=@font)"], ["Action", "simple-fraction", "[p] (pause:100); [n] children/*[1] (rate:0.35); [t] \"over\";  [n] children/*[2] (rate:0.35, pause:100)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "fraction", "[p] (pause:250); [n] children/*[1] (rate:0.35, pause:250); [t] \"divided by\" (pause:250);  [n] children/*[2] (rate:0.35, pause:250)"], ["Action", "superscript", "[n] children/*[1]; [t] \"super\"; [n] children/*[2] (pitch:0.35, pause:300)"], ["Action", "subscript", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2] (pitch:-0.35, pause:300)"], ["Action", "ellipsis", "[p] (pause:200); [t] \"ellipsis\" (pause:300)"], ["Action", "fence-single", "[n] text()"], ["Action", "omit-empty", "[p] (pause:100)"], ["Action", "fences-open-close", "[p] (pause:200); [n] children/*[1] (rate:0.35, pause:200)"], ["Action", "fences-open-close-in-appl", "[p] (pause:200); [n] children/*[1] (pause:200);"], ["Action", "fences-neutral", "[p] (pause:100); [t] \"absolute value of\"; [n] children/*[1] (pause:350);"], ["Action", "omit-fences", "[p] (pause:500); [n] children/*[1] (pause:200);"], ["Action", "matrix", "[t] \"matrix\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:100)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter,context:\"column\",pause:100)"], ["Action", "matrix-cell", "[n] children/*[1]"], ["Action", "vector", "[t] \"vector\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"element\",pause:100)"], ["Action", "cases", "[t] \"case statement\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"case\",pause:100)"], ["Action", "cases-row", "[m] children/*"], ["Action", "cases-cell", "[n] children/*[1]"], ["Action", "row", "[m] ./* (ctxtFunc:CTFnodeCounter,context:\"column\",pause:100)"], ["Action", "cases-end", "[t] \"case statement\"; [m] children/* (ctxtFunc:CTFnodeCounter,context:\"case\",pause:100);[t] \"end cases\""], ["Action", "multiline", "[t] \"multiline equation\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"line\",pause:100)"], ["Action", "line", "[m] children/*"], ["Action", "table", "[t] \"multiline equation\";[m] children/* (ctxtFunc:CTFnodeCounter,context:\"row\",pause:200)"], ["Action", "table-row", "[m] children/* (pause:100)"], ["Action", "end-punct", "[m] children/* (pause:300)"], ["Action", "start-punct", "[n] content/*[1] (pause:200); [m] children/*[position()>1]"], ["Action", "punctuated", "[m] children/* (pause:100)"], ["Action", "function", "[n] text()"], ["Action", "appl", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2]"], ["Action", "sum-only", "[n] children/*[1]; [t] \"from\"; [n] children/*[2]; [t] \"to\";[n] children/*[3]"], ["Action", "limboth", "[n] children/*[1] (pause 100); [t] \"over\"; [n] children/*[2];[t] \"under\"; [n] children/*[3] (pause 250);"], ["Action", "limlower", "[n] children/*[1]; [t] \"over\"; [n] children/*[2];"], ["Action", "limupper", "[n] children/*[1]; [t] \"under\"; [n] children/*[2];"], ["Action", "largeop", "[n] text()"], ["Action", "bigop", "[n] children/*[1] (pause 100); [t] \"over\"; [n] children/*[2] (pause 250);"], ["Action", "integral", "[n] children/*[1] (pause 100); [n] children/*[2] (pause 200); [n] children/*[3] (rate:0.35);"], ["Action", "sqrt", "[t] \"Square root of\"; [n] children/*[1] (rate:0.35, pause:400)"], ["Action", "square", "[n] children/*[1]; [t] \"squared\" (pitch:0.35, pause:200)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubed\" (pitch:0.35, pause:200)"], ["Action", "root", "[t] \"root of order\"; [n] children/*[1];[t] \"over\"; [n] children/*[1] (rate:0.35, pause:400)"], ["Action", "text-no-mult", "[n] children/*[1] (pause:200); [n] children/*[2]"], ["Action", "text", "[n] text() (pause:200)"], ["Action", "unit", "[t] text() (annotation:unit, preprocess)"], ["Action", "unit-square", "[t] \"square\"; [n] children/*[1]"], ["Action", "unit-cubic", "[t] \"cubic\"; [n] children/*[1]"], ["Action", "reciprocal", "[t] \"reciprocal\"; [n] children/*[1]"], ["Action", "per", "[t] \"per\"; [n] children/*[1]"], ["Action", "unit-combine", "[m] children/*"], ["Action", "unit-divide", "[n] children/*[1] (pitch:0.3); [t] \"per\"; [n] children/*[2] (pitch:-0.3)"]]}, "en/rules/mathspeak_english.min": {"domain": "mathspeak", "locale": "en", "modality": "speech", "inherits": "base", "rules": [["Precondition", "german-font", "default", "self::*[@font=\"fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"], ["Precondition", "german-font-bold", "default", "self::*[@font=\"bold-fraktur\"]", "not(contains(@grammar, \"ignoreFont\"))"]]}, "en/rules/mathspeak_english_actions.min": {"domain": "mathspeak", "locale": "en", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[t] \"collapsed\"; [n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "blank-cell-empty", "[t] \"Blank\""], ["Action", "blank-line-empty", "[t] \"Blank\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font", "[t] \"German\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "german-font-bold", "[t] \"bold German\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"and\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"Number\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"Num\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"UpperWord\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"Baseline\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"Base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"Baseline\"; [t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "number-baseline-font-brief", "[t] \"Base\"; [t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "negative-number", "[t] \"negative\"; [n] children/*[1]"], ["Action", "negative", "[t] \"minus\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"divided by\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"minus\")"], ["Action", "fences-neutral", "[t] \"StartAbsoluteValue\"; [n] children/*[1]; [t] \"EndAbsoluteValue\""], ["Action", "fences-neutral-sbrief", "[t] \"AbsoluteValue\"; [n] children/*[1]; [t] \"EndAbsoluteValue\""], ["Action", "fences-metric", "[t] \"StartMetric\"; [n] children/*[1]; [t] \"EndMetric\""], ["Action", "fences-metric-sbrief", "[t] \"Metric\"; [n] children/*[1]; [t] \"EndMetric\""], ["Action", "empty-set", "[t] \"empty set\""], ["Action", "fences-set", "[t] \"StartSet\"; [n] children/*[1]; [t] \"EndSet\""], ["Action", "fences-set-sbrief", "[t] \"Set\"; [n] children/*[1]; [t] \"EndSet\""], ["Action", "factorial", "[t] \"factorial\""], ["Action", "minus", "[t] \"minus\""], ["Action", "continued-fraction-outer", "[t] \"ContinuedFraction\"; [n] children/*[1]; [t] \"Over\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"ContinuedFrac\"; [n] children/*[1]; [t] \"Over\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"StartFraction\"; [n] children/*[1]; [t] \"Over\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"StartFrac\"; [n] children/*[1]; [t] \"Over\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"Frac\"; [n] children/*[1]; [t] \"Over\"; [n] children/*[2]"], ["Action", "integral", "[n] children/*[1]; [t] \"Subscript\"; [n] children/*[2]; [t] \"Superscript\"; [n] children/*[3]; [t] \"Baseline\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"Sub\"; [n] children/*[2]; [t] \"Sup\"; [n] children/*[3]; [t] \"Base\""], ["Action", "square", "[n] children/*[1]; [t] \"squared\" (span:children/*[2])"], ["Action", "cube", "[n] children/*[1]; [t] \"cubed\" (span:children/*[2])"], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"prime\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"prime\""], ["Action", "overscore", "[t] \"ModifyingAbove\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"ModAbove\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"ModifyingAbove Above\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"ModAbove Above\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"ModifyingBelow\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"ModBelow\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"ModifyingBelow Below\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"ModBelow Below\"; [n] children/*[1]; [t] \"With\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"overbar\""], ["Action", "underbar", "[n] children/*[1]; [t] \"underbar\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"overtilde\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"undertilde\""], ["Action", "matrix", "[t] \"Start\"; [t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Matrix\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndMatrix\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Matrix\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndMatrix\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"Column\", pause:200)"], ["Action", "row-with-label", "[t] \"with Label\"; [n] content/*[1]; [t] \"EndLabel\" (pause:200); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Column\")"], ["Action", "row-with-label-brief", "[t] \"Label\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Column\")"], ["Action", "row-with-text-label", "[t] \"Label\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Column\")"], ["Action", "empty-row", "[t] \"Blank\""], ["Action", "empty-cell", "[t] \"Blank\" (pause:300)"], ["Action", "determinant", "[t] \"Start\"; [t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Determinant\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndDeterminant\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Determinant\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndDeterminant\""], ["Action", "determinant-simple", "[t] \"Start\"; [t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Determinant\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row\", grammar:simpleDet); [t] \"EndDeterminant\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"By\"; [t] count(children/*[1]/children/*); [t] \"Determinant\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row\", grammar:simpleDet); [t] \"EndDeterminant\""], ["Action", "layout", "[t] \"StartLayout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndLayout\""], ["Action", "layout-sbrief", "[t] \"Layout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndLayout\""], ["Action", "binomial", "[t] \"StartBinomialOrMatrix\"; [n] children/*[1]/children/*[1]; [t] \"Choose\"; [n] children/*[2]/children/*[1]; [t] \"EndBinomialOrMatrix\""], ["Action", "binomial-sbrief", "[t] \"BinomialOrMatrix\"; [n] children/*[1]/children/*[1]; [t] \"Choose\"; [n] children/*[2]/children/*[1]; [t] \"EndBinomialOrMatrix\""], ["Action", "cases", "[t] \"StartLayout\"; [t] \"Enlarged\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndLayout\""], ["Action", "cases-sbrief", "[t] \"Layout\"; [t] \"Enlarged\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"Row \"); [t] \"EndLayout\""], ["Action", "line-with-label", "[t] \"with Label\"; [n] content/*[1]; [t] \"EndLabel\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"Label\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"Label\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"Blank\""], ["Action", "empty-line-with-label", "[t] \"with Label\"; [n] content/*[1]; [t] \"EndLabel\" (pause:200); [t] \"Blank\""], ["Action", "empty-line-with-label-brief", "[t] \"Label\"; [n] content/*[1] (pause:200); [t] \"Blank\""], ["Action", "enclose", "[t] \"StartEnclose\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"EndEnclose\""], ["Action", "leftbar", "[t] \"vertical bar\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"vertical bar\""], ["Action", "crossout", "[t] \"CrossOut\"; [n] children/*[1]; [t] \"EndCrossOut\""], ["Action", "cancel", "[t] \"CrossOut\"; [n] children/*[1]/children/*[1]; [t] \"With\"; [n] children/*[2]; [t] \"EndCrossOut\""], ["Action", "cancel-reverse", "[t] \"CrossOut\"; [n] children/*[2]/children/*[1]; [t] \"With\"; [n] children/*[1]; [t] \"EndCrossOut\""], ["Action", "multi-inference", "[t] \"inference rule\"; [m] content/*; [t] \"with conclusion\"; [n] children/*[1]; [t] \"and\"; [t] count(children/*[2]/children/*); [t] \"premises\""], ["Action", "inference", "[t] \"inference rule\"; [m] content/*; [t] \"with conclusion\"; [n] children/*[1]; [t] \"and\"; [t] count(children/*[2]/children/*); [t] \"premise\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"premise \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"label\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"axiom\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"empty axiom\""]]}, "en/rules/prefix_english.min": {"modality": "prefix", "domain": "default", "locale": "en", "inherits": "base", "rules": []}, "en/rules/prefix_english_actions.min": {"modality": "prefix", "domain": "default", "locale": "en", "kind": "actions", "rules": [["Action", "numerator", "[t] \"Numerator\" (pause:200)"], ["Action", "denominator", "[t] \"Denominator\" (pause:200)"], ["Action", "base", "[t] \"Base\" (pause:200)"], ["Action", "exponent", "[t] \"Exponent\" (pause:200)"], ["Action", "subscript", "[t] \"Subscript\" (pause:200)"], ["Action", "overscript", "[t] \"Overscript\" (pause:200)"], ["Action", "underscript", "[t] \"Underscript\" (pause:200)"], ["Action", "radicand", "[t] \"Radicand\" (pause:200)"], ["Action", "index", "[t] \"Index\" (pause:200)"], ["Action", "leftsub", "[t] \"Left Subscript\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition; [t] \"Left Subscript\" (pause:200)"], ["Action", "leftsuper", "[t] \"Left Superscript\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition; [t] \"Left Superscript\" (pause:200)"], ["Action", "rightsub", "[t] \"Right Subscript\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition; [t] \"Right Subscript\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"Right Superscript\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition; [t] \"Right Superscript\" (pause:200)"], ["Action", "choice", "[t] \"Choice Quantity\" (pause:200)"], ["Action", "select", "[t] \"Selection Quantity\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition; [t] \"Row\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition; [t] \"Column\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition; [t] \"Column\" (pause:200)"]]}, "en/rules/summary_english.min": {"locale": "en", "modality": "summary", "inherits": "base", "rules": []}, "en/rules/summary_english_actions.min": {"locale": "en", "modality": "summary", "kind": "actions", "rules": [["Action", "abstr-identifier-long", "[t] \"long identifier\""], ["Action", "abstr-identifier", "[t] \"identifier\""], ["Action", "abstr-number-long", "[t] \"long number\""], ["Action", "abstr-number", "[t] \"number\""], ["Action", "abstr-mixed-number-long", "[t] \"long mixed number\""], ["Action", "abstr-mixed-number", "[t] \"mixed number\""], ["Action", "abstr-text", "[t] \"text\""], ["Action", "abstr-function", "[t] \"functional expression\""], ["Action", "abstr-function-brief", "[t] \"function\""], ["Action", "abstr-lim", "[t] \"limit function\""], ["Action", "abstr-lim-brief", "[t] \"lim\""], ["Action", "abstr-fraction", "[t] \"fraction\""], ["Action", "abstr-fraction-brief", "[t] \"frac\""], ["Action", "abstr-continued-fraction", "[t] \"continued fraction\""], ["Action", "abstr-continued-fraction-brief", "[t] \"continued frac\""], ["Action", "abstr-sqrt", "[t] \"square root\""], ["Action", "abstr-sqrt-nested", "[t] \"nested square root\""], ["Action", "abstr-root-end", "[t] \"root of index\"; [n] children/*[1] (engine:modality=speech); [t] \"endindex\""], ["Action", "abstr-root", "[t] \"root of index\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-brief", "[t] \"root\""], ["Action", "abstr-root-nested-end", "[t] \"nested root of index\"; [n] children/*[1] (engine:modality=speech); [t] \"endindex\""], ["Action", "abstr-root-nested", "[t] \"nested root of index\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-nested-brief", "[t] \"nested root\""], ["Action", "abstr-superscript", "[t] \"power\""], ["Action", "abstr-subscript", "[t] \"subscript\""], ["Action", "abstr-subsup", "[t] \"power with subscript\""], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"with\"; [t] count(./children/*); [t] \"elements\""], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"with variable number of elements\""], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole)"], ["Action", "abstr-addition", "[t] \"sum with\"; [t] count(./children/*); [t] \"summands\""], ["Action", "abstr-addition-brief", "[t] \"sum\""], ["Action", "abstr-addition-var", "[t] \"sum with variable number of summands\""], ["Action", "abstr-multiplication", "[t] \"product with\"; [t] count(./children/*); [t] \"factors\""], ["Action", "abstr-multiplication-brief", "[t] \"product\""], ["Action", "abstr-multiplication-var", "[t] \"product with variable number of factors\""], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"dimensional vector\""], ["Action", "abstr-vector-brief", "[t] \"vector\""], ["Action", "abstr-vector-var", "[t] \"n dimensional vector\""], ["Action", "abstr-binomial", "[t] \"binomial\""], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"dimensional determinant\""], ["Action", "abstr-determinant-brief", "[t] \"determinant\""], ["Action", "abstr-determinant-var", "[t] \"n dimensional determinant\""], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"dimensional square matrix\""], ["Action", "abstr-squarematrix-brief", "[t] \"square matrix\""], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"dimensional row vector\""], ["Action", "abstr-rowvector-brief", "[t] \"row vector\""], ["Action", "abstr-rowvector-var", "[t] \"n dimensional row vector\""], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"by\"; [t] count(children/*[1]/children/*); [t] \"matrix\""], ["Action", "abstr-matrix-brief", "[t] \"matrix\""], ["Action", "abstr-matrix-var", "[t] \"n by m dimensional matrix\""], ["Action", "abstr-cases", "[t] \"case statement\"; [t] \"with\"; [t] count(children/*); [t] \"cases\""], ["Action", "abstr-cases-brief", "[t] \"case statement\""], ["Action", "abstr-cases-var", "[t] \"case statement with variable number of cases\""], ["Action", "abstr-punctuated", "[n] content/*[1]; [t] \"separated list\"; [t] \"of length\"; [t] count(children/*) - count(content/*)"], ["Action", "abstr-punctuated-brief", "[n] content/*[1]; [t] \"separated list\""], ["Action", "abstr-punctuated-var", "[n] content/*[1]; [t] \"separated list\"; [t] \"of variable length\""], ["Action", "abstr-bigop", "[n] content/*[1]"], ["Action", "abstr-integral", "[t] \"integral\""], ["Action", "abstr-relation", "[t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq", "[t] @role (grammar:localRole); [t] \"sequence\"; [t] \"with\"; [t] count(./children/*); [t] \"elements\""], ["Action", "abstr-relation-seq-brief", "[t] @role (grammar:localRole); [t] \"sequence\""], ["Action", "abstr-relation-var", "[t] @role (grammar:localRole); [t] \"sequence\"; [t] \"with variable number of elements\""], ["Action", "abstr-multirel", "[t] \"relation sequence\"; [t] \"with\"; [t] count(./children/*); [t] \"elements\""], ["Action", "abstr-multirel-brief", "[t] \"relation sequence\""], ["Action", "abstr-multirel-var", "[t] \"relation sequence with variable number of elements\""], ["Action", "abstr-table", "[t] \"table with\"; [t] count(children/*); [t] \"rows and\"; [t] count(children/*[1]/children/*); [t] \"columns\""], ["Action", "abstr-line", "[t] \"in\"; [t] @role (grammar:localRole)"], ["Action", "abstr-row", "[t] \"in\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"with\"; [t] count(children/*); [t] \"columns\""], ["Action", "abstr-cell", "[t] \"in\"; [t] @role (grammar:localRole)"]]}}