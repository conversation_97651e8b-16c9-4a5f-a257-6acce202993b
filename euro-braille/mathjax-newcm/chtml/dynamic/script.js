(()=>{"use strict";const s=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","script",s({S:{normal:{119964:[.674,.015,.855,{sk:.364}],8492:[.687,.015,.95,{sk:.324}],119966:[.687,.015,.797,{sk:.275}],119967:[.687,.015,.885,{sk:.285}],8496:[.687,.015,.75,{sk:.244}],8497:[.68,0,.919,{sk:.21}],119970:[.687,.015,.773,{sk:.237}],8459:[.687,.015,.997,{sk:.224}],8464:[.675,.015,.897,{sk:.26}],119973:[.674,.177,.802,{sk:.229}],119974:[.687,.015,1.009,{sk:.28}],8466:[.687,.015,.946,{sk:.349}],8499:[.674,.015,1.072,{sk:.326}],119977:[.687,.015,.97,{sk:.228}],119978:[.68,.015,.692,{sk:.172}],119979:[.687,.015,.91,{sk:.28}],119980:[.68,.038,.692,{sk:.146}],8475:[.687,.015,.944,{sk:.255}],119982:[.68,.015,.743,{sk:.183}],119983:[.687,.015,.912,{sk:.2}],119984:[.687,.015,.842,{sk:.15}],119985:[.687,.015,.932,{sk:.212}],119986:[.687,.015,1.078,{sk:.229}],119987:[.687,.015,.891,{sk:.165}],119988:[.687,.226,.926,{sk:.191}],119989:[.687,.015,.932,{sk:.144}],119990:[.441,.011,.819,{sk:.083}],119991:[.687,.012,.58,{sk:.197}],119992:[.441,.011,.662,{sk:.151}],119993:[.687,.011,.845,{sk:.359}],8495:[.441,.011,.627,{sk:.086}],119995:[.687,.209,.685,{sk:.239}],8458:[.441,.219,.738,{sk:.103}],119997:[.687,.011,.753,{sk:.136}],119998:[.653,.011,.496,{sk:.194}],119999:[.653,.219,.73,{sk:.311}],12e4:[.687,.011,.726,{sk:.144}],120001:[.687,.011,.579,{sk:.21}],120002:[.441,.011,1.038,{sk:.088}],120003:[.441,.011,.761,{sk:.098}],8500:[.441,.011,.697],120005:[.441,.209,.773,{sk:.1}],120006:[.441,.209,.78,{sk:.103}],120007:[.444,0,.58,{sk:.114}],120008:[.531,.011,.515,{sk:.111}],120009:[.658,.011,.551,{sk:.141}],120010:[.424,.011,.753,{sk:.054}],120011:[.441,.011,.618,{sk:.08}],120012:[.441,.011,.888,{sk:.119}],120013:[.441,.011,.752,{sk:.095}],120014:[.424,.219,.658,{sk:.095}],120015:[.478,.011,.691,{sk:.069}],8467:[.705,.012,.417,{sk:.102}],8472:[.453,.216,.636]},script:{}},SB:{normal:{120016:[.699,.021,.984,{sk:.37}],120017:[.699,.021,1.06,{sk:.268}],120018:[.699,.021,.912,{sk:.28}],120019:[.699,.021,.991,{sk:.243}],120020:[.699,.021,.826,{sk:.238}],120021:[.699,.021,1.042,{sk:.232}],120022:[.699,.021,.834,{sk:.237}],120023:[.699,.021,1.171,{sk:.288}],120024:[.699,.021,.997,{sk:.291}],120025:[.699,.224,.906,{sk:.237}],120026:[.699,.021,1.154,{sk:.349}],120027:[.699,.021,1.036,{sk:.374}],120028:[.699,.021,1.3,{sk:.35}],120029:[.699,.021,1.095,{sk:.28}],120030:[.699,.021,.809,{sk:.147}],120031:[.699,.021,1.025,{sk:.247}],120032:[.699,.052,.809,{sk:.147}],120033:[.699,.021,1.048,{sk:.234}],120034:[.699,.021,.816,{sk:.205}],120035:[.699,.021,1.03,{sk:.21}],120036:[.699,.021,.964,{sk:.15}],120037:[.699,.021,1.04,{sk:.262}],120038:[.699,.021,1.32,{sk:.289}],120039:[.699,.021,1.033,{sk:.15}],120040:[.699,.224,.989,{sk:.172}],120041:[.699,.021,.996,{sk:.179}],120042:[.462,.014,.942,{sk:.114}],120043:[.699,.014,.646,{sk:.198}],120044:[.462,.014,.764,{sk:.174}],120045:[.699,.014,.949,{sk:.369}],120046:[.462,.014,.726,{sk:.161}],120047:[.699,.205,.768,{sk:.229}],120048:[.462,.224,.819,{sk:.145}],120049:[.699,.014,.838,{sk:.139}],120050:[.698,.014,.558,{sk:.191}],120051:[.698,.223,.84,{sk:.34}],120052:[.699,.014,.81,{sk:.151}],120053:[.699,.014,.65,{sk:.201}],120054:[.462,.014,1.137,{sk:.12}],120055:[.462,.014,.851,{sk:.12}],120056:[.462,.014,.848,{sk:.148}],120057:[.462,.205,.885,{sk:.093}],120058:[.462,.205,.913,{sk:.104}],120059:[.462,0,.677,{sk:.126}],120060:[.557,.014,.562,{sk:.094}],120061:[.669,.014,.618,{sk:.169}],120062:[.45,.014,.842,{sk:.073}],120063:[.458,.014,.732,{sk:.099}],120064:[.458,.014,1.012,{sk:.147}],120065:[.462,.014,.82,{sk:.084}],120066:[.45,.224,.784,{sk:.114}],120067:[.493,.014,.782,{sk:.053}]},"bold-script":{}}},"NCM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/script","4.0.0-beta.7","dynamic-font")})();