(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","braille-d",t({brd:{normal:{10240:[0,0,.725],10241:[.651,-.036,.725],10242:[.615,-.036,.725],10243:[.651,-.036,.725],10244:[.615,0,.725],10245:[.651,0,.725],10246:[.615,0,.725],10247:[.651,0,.725],10248:[.651,-.036,.725],10249:[.651,-.036,.725],10250:[.651,-.036,.725],10251:[.651,-.036,.725],10252:[.651,0,.725],10253:[.651,0,.725],10254:[.651,0,.725],10255:[.651,0,.725],10256:[.615,-.036,.725],10257:[.651,-.036,.725],10258:[.615,-.036,.725],10259:[.651,-.036,.725],10260:[.615,0,.725],10261:[.651,0,.725],10262:[.615,0,.725],10263:[.651,0,.725],10264:[.651,-.036,.725],10265:[.651,-.036,.725],10266:[.651,-.036,.725],10267:[.651,-.036,.725],10268:[.651,0,.725],10269:[.651,0,.725],10270:[.651,0,.725],10271:[.651,0,.725],10272:[.615,0,.725],10273:[.651,0,.725],10274:[.615,0,.725],10275:[.651,0,.725],10276:[.615,0,.725],10277:[.651,0,.725],10278:[.615,0,.725],10279:[.651,0,.725],10280:[.651,0,.725],10281:[.651,0,.725],10282:[.651,0,.725],10283:[.651,0,.725],10284:[.651,0,.725],10285:[.651,0,.725],10286:[.651,0,.725],10287:[.651,0,.725],10288:[.615,0,.725],10289:[.651,0,.725],10290:[.615,0,.725],10291:[.651,0,.725],10292:[.615,0,.725],10293:[.651,0,.725],10294:[.615,0,.725],10295:[.651,0,.725],10296:[.651,0,.725],10297:[.651,0,.725],10298:[.651,0,.725],10299:[.651,0,.725],10300:[.651,0,.725],10301:[.651,0,.725],10302:[.651,0,.725],10303:[.651,0,.725],10304:[.615,.246,.725],10305:[.651,.246,.725],10306:[.615,.246,.725],10307:[.651,.246,.725],10308:[.615,.246,.725],10309:[.651,.246,.725],10310:[.615,.246,.725],10311:[.651,.246,.725],10312:[.651,.246,.725],10313:[.651,.246,.725],10314:[.651,.246,.725],10315:[.651,.246,.725],10316:[.651,.246,.725],10317:[.651,.246,.725],10318:[.651,.246,.725],10319:[.651,.246,.725],10320:[.615,.246,.725],10321:[.651,.246,.725],10322:[.615,.246,.725],10323:[.651,.246,.725],10324:[.615,.246,.725],10325:[.651,.246,.725],10326:[.615,.246,.725],10327:[.651,.246,.725],10328:[.651,.246,.725],10329:[.651,.246,.725],10330:[.651,.246,.725],10331:[.651,.246,.725],10332:[.651,.246,.725],10333:[.651,.246,.725],10334:[.651,.246,.725],10335:[.651,.246,.725],10336:[.615,.246,.725],10337:[.651,.246,.725],10338:[.615,.246,.725],10339:[.651,.246,.725],10340:[.615,.246,.725],10341:[.651,.246,.725],10342:[.615,.246,.725],10343:[.651,.246,.725],10344:[.651,.246,.725],10345:[.651,.246,.725],10346:[.651,.246,.725],10347:[.651,.246,.725],10348:[.651,.246,.725],10349:[.651,.246,.725],10350:[.651,.246,.725],10351:[.651,.246,.725],10352:[.615,.246,.725],10353:[.651,.246,.725],10354:[.615,.246,.725],10355:[.651,.246,.725],10356:[.615,.246,.725],10357:[.651,.246,.725],10358:[.615,.246,.725],10359:[.651,.246,.725],10360:[.651,.246,.725],10361:[.651,.246,.725],10362:[.651,.246,.725],10363:[.651,.246,.725],10364:[.651,.246,.725],10365:[.651,.246,.725],10366:[.651,.246,.725],10367:[.651,.246,.725],10368:[.615,.246,.725],10369:[.651,.246,.725],10370:[.615,.246,.725],10371:[.651,.246,.725],10372:[.615,.246,.725],10373:[.651,.246,.725],10374:[.615,.246,.725],10375:[.651,.246,.725],10376:[.651,.246,.725],10377:[.651,.246,.725],10378:[.651,.246,.725],10379:[.651,.246,.725],10380:[.651,.246,.725],10381:[.651,.246,.725],10382:[.651,.246,.725],10383:[.651,.246,.725],10384:[.615,.246,.725],10385:[.651,.246,.725],10386:[.615,.246,.725],10387:[.651,.246,.725],10388:[.615,.246,.725],10389:[.651,.246,.725],10390:[.615,.246,.725],10391:[.651,.246,.725],10392:[.651,.246,.725],10393:[.651,.246,.725],10394:[.651,.246,.725],10395:[.651,.246,.725],10396:[.651,.246,.725],10397:[.651,.246,.725],10398:[.651,.246,.725],10399:[.651,.246,.725],10400:[.615,.246,.725],10401:[.651,.246,.725],10402:[.615,.246,.725],10403:[.651,.246,.725],10404:[.615,.246,.725],10405:[.651,.246,.725],10406:[.615,.246,.725],10407:[.651,.246,.725],10408:[.651,.246,.725],10409:[.651,.246,.725],10410:[.651,.246,.725],10411:[.651,.246,.725],10412:[.651,.246,.725],10413:[.651,.246,.725],10414:[.651,.246,.725],10415:[.651,.246,.725],10416:[.615,.246,.725],10417:[.651,.246,.725],10418:[.615,.246,.725],10419:[.651,.246,.725],10420:[.615,.246,.725],10421:[.651,.246,.725],10422:[.615,.246,.725],10423:[.651,.246,.725],10424:[.651,.246,.725],10425:[.651,.246,.725],10426:[.651,.246,.725],10427:[.651,.246,.725],10428:[.651,.246,.725],10429:[.651,.246,.725],10430:[.651,.246,.725],10431:[.651,.246,.725],10432:[.615,.246,.725],10433:[.651,.246,.725],10434:[.615,.246,.725],10435:[.651,.246,.725],10436:[.615,.246,.725],10437:[.651,.246,.725],10438:[.615,.246,.725],10439:[.651,.246,.725],10440:[.651,.246,.725],10441:[.651,.246,.725],10442:[.651,.246,.725],10443:[.651,.246,.725],10444:[.651,.246,.725],10445:[.651,.246,.725],10446:[.651,.246,.725],10447:[.651,.246,.725],10448:[.615,.246,.725],10449:[.651,.246,.725],10450:[.615,.246,.725],10451:[.651,.246,.725],10452:[.615,.246,.725],10453:[.651,.246,.725],10454:[.615,.246,.725],10455:[.651,.246,.725],10456:[.651,.246,.725],10457:[.651,.246,.725],10458:[.651,.246,.725],10459:[.651,.246,.725],10460:[.651,.246,.725],10461:[.651,.246,.725],10462:[.651,.246,.725],10463:[.651,.246,.725],10464:[.615,.246,.725],10465:[.651,.246,.725],10466:[.615,.246,.725],10467:[.651,.246,.725],10468:[.615,.246,.725],10469:[.651,.246,.725],10470:[.615,.246,.725],10471:[.651,.246,.725],10472:[.651,.246,.725],10473:[.651,.246,.725],10474:[.651,.246,.725],10475:[.651,.246,.725],10476:[.651,.246,.725],10477:[.651,.246,.725],10478:[.651,.246,.725],10479:[.651,.246,.725],10480:[.615,.246,.725],10481:[.651,.246,.725],10482:[.615,.246,.725],10483:[.651,.246,.725],10484:[.615,.246,.725],10485:[.651,.246,.725],10486:[.615,.246,.725],10487:[.651,.246,.725],10488:[.651,.246,.725],10489:[.651,.246,.725],10490:[.651,.246,.725],10491:[.651,.246,.725],10492:[.651,.246,.725],10493:[.651,.246,.725],10494:[.651,.246,.725],10495:[.651,.246,.725]}}},"NCM"),{},["MJX-NCM-brd"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/braille-d","4.0.0-beta.7","dynamic-font")})();