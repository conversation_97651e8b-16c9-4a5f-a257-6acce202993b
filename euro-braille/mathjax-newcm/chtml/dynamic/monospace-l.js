(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","monospace-l",c({ML:{monospace:{192:[.807,0,.525],193:[.807,0,.525],194:[.766,0,.525],195:[.783,0,.525],196:[.753,0,.525],197:[.767,0,.525],198:[.611,0,.525],199:[.622,.208,.525],200:[.807,0,.525],201:[.807,0,.525],202:[.766,0,.525],203:[.753,0,.525],204:[.807,0,.525],205:[.807,0,.525],206:[.766,0,.525],207:[.753,0,.525],208:[.611,0,.525],209:[.783,0,.525],210:[.807,.011,.525],211:[.807,.011,.525],212:[.766,.011,.525],213:[.783,.011,.525],214:[.753,.011,.525],216:[.696,.085,.525],217:[.807,.011,.525],218:[.807,.011,.525],219:[.766,.011,.525],220:[.753,.011,.525],221:[.807,0,.525],222:[.611,0,.525],223:[.617,.006,.525],224:[.622,.006,.525],225:[.622,.006,.525],226:[.622,.006,.525],227:[.615,.006,.525],228:[.6,.006,.525],229:[.636,.006,.525],230:[.44,.006,.525],231:[.44,.208,.525],232:[.622,.006,.525],233:[.622,.006,.525],234:[.622,.006,.525],235:[.6,.006,.525],236:[.622,0,.525],237:[.622,0,.525],238:[.622,0,.525],239:[.6,0,.525],241:[.615,0,.525],242:[.622,.006,.525],243:[.622,.006,.525],244:[.622,.006,.525],245:[.615,.006,.525],246:[.6,.006,.525],248:[.571,.14,.525],249:[.622,.006,.525],250:[.622,.006,.525],251:[.622,.006,.525],252:[.6,.006,.525],253:[.622,.228,.525],254:[.611,.222,.525],255:[.6,.228,.525],256:[.746,0,.525],257:[.587,.006,.525],258:[.786,0,.525],259:[.612,.006,.525],260:[.623,.203,.525],261:[.44,.203,.525],262:[.807,.011,.525],263:[.622,.006,.525],264:[.766,.011,.525],265:[.622,.006,.525],266:[.774,.011,.525],267:[.605,.006,.525],268:[.766,.011,.525],269:[.623,.006,.525],270:[.766,0,.525],271:[.611,.006,.525,{ic:.066}],272:[.611,0,.525],273:[.611,.006,.525],274:[.746,0,.525],275:[.587,.006,.525],276:[.786,0,.525],277:[.612,.006,.525],278:[.774,0,.525],279:[.605,.006,.525],280:[.611,.203,.525],281:[.44,.203,.525],282:[.766,0,.525],283:[.623,.006,.525],284:[.766,.011,.525],285:[.622,.229,.525],286:[.786,.011,.525],287:[.612,.229,.525],288:[.774,.011,.525],289:[.605,.229,.525],290:[.622,.231,.525],291:[.665,.229,.525],292:[.766,0,.525],293:[.766,0,.525],294:[.611,0,.525],295:[.611,0,.525],296:[.783,0,.525],297:[.615,0,.525],298:[.746,0,.525],299:[.587,0,.525],300:[.786,0,.525],301:[.612,0,.525],302:[.611,.203,.525],303:[.612,.203,.525],304:[.774,0,.525],306:[.611,.011,.525],307:[.611,.227,.525],308:[.766,.011,.525],309:[.622,.228,.525],310:[.611,.231,.525],311:[.611,.231,.525],312:[.431,0,.525],313:[.807,0,.525],314:[.807,0,.525],315:[.611,.231,.525],316:[.611,.231,.525],317:[.611,0,.525],318:[.611,0,.525],319:[.611,0,.525],320:[.611,0,.525,{ic:.05}],321:[.611,0,.525],322:[.611,0,.525],323:[.807,0,.525],324:[.622,0,.525],325:[.611,.231,.525],326:[.437,.231,.525],327:[.766,0,.525],328:[.623,0,.525],329:[.791,0,.525],330:[.622,.011,.525],331:[.437,.233,.525],332:[.746,.011,.525],333:[.587,.006,.525],334:[.786,.011,.525],335:[.612,.006,.525],336:[.813,.011,.525],337:[.625,.006,.525],338:[.622,.011,.525],339:[.44,.006,.525],340:[.807,.011,.525],341:[.622,0,.525],342:[.611,.231,.525],343:[.437,.231,.525],344:[.766,.011,.525],345:[.623,0,.525],346:[.807,.011,.525],347:[.622,.006,.525],348:[.766,.011,.525],349:[.622,.006,.525],350:[.622,.219,.525],351:[.44,.208,.525],352:[.766,.011,.525],353:[.623,.006,.525],354:[.611,.211,.525],355:[.554,.211,.525],356:[.766,0,.525],357:[.667,.006,.525],358:[.611,0,.525],359:[.554,.006,.525],360:[.783,.011,.525],361:[.615,.006,.525],362:[.746,.011,.525],363:[.587,.006,.525],364:[.786,.011,.525],365:[.612,.006,.525],366:[.767,.011,.525],367:[.636,.006,.525],368:[.813,.011,.525],369:[.625,.006,.525],370:[.611,.203,.525],371:[.431,.203,.525,{ic:.006}],372:[.766,.008,.525],373:[.622,.004,.525],374:[.766,0,.525],375:[.622,.228,.525],376:[.753,0,.525],377:[.807,0,.525],378:[.622,0,.525],379:[.774,0,.525],380:[.605,0,.525],381:[.766,0,.525],382:[.623,0,.525],383:[.617,0,.525],384:[.611,.005,.525],385:[.611,0,.525],386:[.611,0,.525],387:[.611,.006,.525],388:[.669,0,.525],389:[.669,.006,.525],390:[.621,.012,.525],391:[.726,.011,.525,{ic:.11}],392:[.586,.005,.525,{ic:.038}],393:[.611,0,.525],394:[.611,0,.525],395:[.611,0,.525],396:[.611,.006,.525],397:[.438,.201,.525],398:[.611,0,.525],399:[.622,.011,.525],400:[.622,.011,.525,{ic:.004}],401:[.611,.228,.525],402:[.617,0,.525],403:[.726,.011,.525,{ic:.09}],404:[.611,.233,.525],405:[.611,.005,.525],406:[.61,.005,.525],407:[.611,0,.525],408:[.611,0,.525],409:[.617,0,.525],410:[.611,0,.525],411:[.611,.005,.525],412:[.611,0,.525],413:[.611,.228,.525],414:[.436,.222,.525,{ic:.013}],415:[.622,.011,.525],416:[.711,.011,.525],417:[.53,.006,.525],418:[.622,.222,.525],419:[.44,.171,.525],420:[.611,0,.525],421:[.617,.222,.525],422:[.611,.011,.525],423:[.622,.011,.525],424:[.439,.006,.525],425:[.611,0,.525],426:[.617,.228,.525],427:[.554,.228,.525],428:[.611,0,.525],429:[.617,.005,.525],430:[.611,.228,.525],431:[.711,.011,.525],432:[.53,.006,.525],433:[.622,0,.525],434:[.611,.011,.525],435:[.614,0,.525],436:[.432,.228,.528],437:[.611,0,.525],438:[.431,0,.525],439:[.611,.011,.525],440:[.611,.011,.525],441:[.43,.227,.525],442:[.43,.227,.525],443:[.622,0,.525],444:[.611,.011,.525],445:[.431,.191,.525],446:[.554,.006,.525],447:[.434,.261,.525],448:[.611,.222,.525],449:[.611,.222,.525],450:[.611,.222,.525],451:[.622,0,.525],452:[.772,0,.525],453:[.622,0,.525],454:[.622,.006,.525],455:[.611,.011,.525],456:[.611,.228,.525],457:[.611,.228,.525],458:[.611,.011,.525],459:[.611,.228,.525],460:[.605,.228,.525],461:[.766,0,.525],462:[.623,.006,.525],463:[.766,0,.525],464:[.623,0,.525],465:[.766,.011,.525],466:[.623,.006,.525],467:[.766,.011,.525],468:[.623,.006,.525],469:[.876,.011,.525],470:[.723,.006,.525],471:[.948,.011,.525],472:[.807,.006,.525],473:[.907,.011,.525],474:[.766,.006,.525],475:[.948,.011,.525],476:[.807,.006,.525],477:[.44,.006,.525],478:[.876,0,.525],479:[.723,.006,.525],480:[.895,0,.525],481:[.734,.006,.525],482:[.801,0,.525,{ic:.029}],483:[.582,.005,.525],484:[.622,.011,.525],485:[.442,.229,.525],486:[.766,.011,.525],487:[.623,.229,.525],488:[.804,0,.525],489:[.81,0,.525],490:[.622,.203,.525],491:[.44,.203,.525],492:[.745,.203,.525],493:[.563,.203,.525],494:[.804,.011,.525],495:[.639,.228,.525],496:[.623,.228,.525],497:[.613,0,.525],498:[.611,0,.525],499:[.611,.006,.525],500:[.807,.011,.525],501:[.622,.229,.525],502:[.611,.005,.525],503:[.615,.26,.525],504:[.807,0,.525],505:[.639,0,.525],506:[.921,0,.525],507:[.79,.006,.525],508:[.807,0,.525],509:[.622,.006,.525],510:[.807,.085,.525],511:[.622,.14,.525],512:[.813,0,.525],513:[.625,.006,.525],514:[.819,0,.525],515:[.639,.006,.525],516:[.813,0,.525],517:[.625,.006,.525],518:[.819,0,.525],519:[.639,.006,.525],520:[.813,0,.525],521:[.625,0,.525],522:[.819,0,.525],523:[.639,0,.525],524:[.813,.011,.525],525:[.625,.006,.525],526:[.819,.011,.525],527:[.639,.006,.525],528:[.813,.011,.525],529:[.625,0,.525],530:[.819,.011,.525],531:[.639,0,.525],532:[.813,.011,.525],533:[.625,.006,.525],534:[.819,.011,.525],535:[.639,.006,.525],536:[.622,.231,.525],537:[.44,.231,.525],538:[.611,.231,.525],539:[.554,.231,.525],540:[.672,.029,.525],541:[.432,.269,.525],542:[.804,0,.525],543:[.81,0,.525],544:[.617,.221,.525],545:[.611,.195,.525],546:[.622,.011,.525],547:[.48,.011,.525],548:[.611,.208,.525],549:[.43,.208,.525],550:[.772,0,.525],551:[.611,.006,.525],552:[.611,.256,.525],553:[.44,.262,.525],554:[.876,.011,.525],555:[.723,.006,.525],556:[.906,.011,.525],557:[.738,.006,.525],558:[.772,.011,.525],559:[.611,.006,.525],560:[.895,.011,.525],561:[.734,.006,.525],562:[.801,0,.525],563:[.582,.228,.525],564:[.611,.195,.525],565:[.436,.195,.525],566:[.554,.195,.525],568:[.611,.005,.525],569:[.436,.222,.525],570:[.704,.073,.525],571:[.704,.073,.525],572:[.534,.073,.525],573:[.611,0,.525],574:[.704,.073,.525],575:[.44,.212,.525],576:[.431,.212,.525],577:[.617,0,.525],578:[.431,0,.525],579:[.611,0,.525],580:[.611,.011,.525],581:[.618,.001,.525],582:[.704,.073,.525],583:[.534,.073,.525],584:[.611,.011,.525],585:[.605,.228,.525],586:[.622,.228,.525,{ic:.045}],587:[.437,.228,.525,{ic:.045}],588:[.611,.011,.525],589:[.437,0,.525],590:[.611,0,.525],591:[.431,.228,.525],11377:[.436,.008,.525],7680:[.623,.284,.525],7681:[.44,.23,.525],7682:[.772,0,.525],7683:[.782,.006,.525],7684:[.611,.157,.525],7685:[.611,.172,.525],7686:[.611,.139,.525],7687:[.611,.154,.525],7688:[.807,.208,.525],7689:[.639,.208,.525],7690:[.772,0,.525],7691:[.782,.006,.525],7692:[.611,.2,.525],7693:[.611,.2,.525],7694:[.611,.181,.525],7695:[.611,.181,.525],7696:[.611,.235,.525],7697:[.611,.235,.525],7698:[.611,.212,.525],7699:[.611,.212,.525],7704:[.611,.212,.525],7705:[.44,.212,.525],7706:[.611,.176,.525],7707:[.44,.176,.525],7710:[.772,0,.525],7711:[.782,0,.525],7712:[.801,.011,.525],7713:[.582,.229,.525],7714:[.772,0,.525],7715:[.782,0,.525],7716:[.611,.2,.525],7717:[.611,.2,.525],7718:[.753,0,.525],7719:[.753,0,.525],7722:[.611,.206,.525],7723:[.611,.206,.525],7724:[.611,.176,.525],7725:[.612,.176,.525],7726:[.948,0,.525],7727:[.807,0,.525],7728:[.807,0,.525],7729:[.81,0,.525],7730:[.611,.157,.525],7731:[.611,.172,.525],7732:[.611,.139,.525],7733:[.611,.154,.525],7734:[.611,.2,.525],7735:[.611,.2,.525],7736:[.746,.2,.525],7737:[.746,.2,.525],7738:[.611,.139,.525],7739:[.611,.154,.525],7740:[.611,.212,.525],7741:[.611,.212,.525],7742:[.807,0,.525],7743:[.639,0,.525,{ic:.003}],7744:[.772,0,.525],7745:[.611,0,.525,{ic:.003}],7746:[.611,.2,.525],7747:[.437,.2,.525],7748:[.763,0,.525],7749:[.605,0,.525],7750:[.611,.2,.525],7751:[.437,.2,.525],7752:[.611,.139,.525],7753:[.437,.154,.525],7754:[.611,.212,.525],7755:[.437,.212,.525],7764:[.807,0,.525],7765:[.639,.222,.525],7766:[.772,0,.525],7767:[.611,.222,.525],7768:[.774,.011,.525],7769:[.605,0,.525],7770:[.611,.2,.525],7771:[.437,.2,.525],7772:[.746,.2,.525],7773:[.587,.2,.525],7774:[.611,.139,.525],7775:[.437,.154,.525],7776:[.772,.011,.525],7777:[.611,.006,.525],7778:[.622,.2,.525],7779:[.44,.2,.525],7784:[.772,.157,.525],7785:[.611,.172,.525],7786:[.772,0,.525],7787:[.725,.006,.525],7788:[.611,.2,.525],7789:[.554,.2,.525],7790:[.611,.181,.525],7791:[.554,.181,.525],7792:[.611,.212,.525],7793:[.554,.212,.525],7794:[.611,.157,.525],7795:[.431,.172,.525],7796:[.611,.176,.525],7797:[.431,.176,.525],7798:[.611,.212,.525],7799:[.431,.212,.525],7804:[.819,.008,.525],7805:[.611,.004,.525],7806:[.611,.157,.525],7807:[.431,.172,.525],7808:[.807,.008,.525],7809:[.622,.004,.525],7810:[.807,.008,.525],7811:[.622,.004,.525],7812:[.753,.008,.525],7813:[.6,.004,.525],7814:[.772,.008,.525],7815:[.611,.004,.525],7816:[.611,.157,.525],7817:[.431,.172,.525],7818:[.772,0,.525],7819:[.611,0,.525],7820:[.819,0,.525],7821:[.611,0,.525],7822:[.772,0,.525],7823:[.611,.228,.525],7824:[.824,0,.525],7825:[.644,0,.525],7826:[.611,.2,.525],7827:[.431,.2,.525],7828:[.611,.139,.525,{ic:.001}],7829:[.431,.154,.525],7830:[.611,.154,.525],7831:[.695,.006,.525],7832:[.647,.004,.525],7833:[.647,.228,.525],7835:[.782,0,.525],7838:[.617,.006,.525],7840:[.623,.2,.525],7841:[.44,.2,.525],7842:[.904,0,.525],7843:[.67,.006,.525],7844:[.919,0,.525],7845:[.743,.006,.525],7846:[.919,0,.525],7847:[.743,.006,.525],7848:[.995,0,.525],7849:[.819,.006,.525],7850:[.925,0,.525],7851:[.749,.006,.525],7852:[.766,.2,.525],7853:[.622,.2,.525],7854:[.94,0,.525],7855:[.766,.006,.525],7856:[.94,0,.525],7857:[.766,.006,.525],7858:[1.016,0,.525],7859:[.842,.006,.525],7860:[.946,0,.525],7861:[.772,.006,.525],7862:[.786,.2,.525],7863:[.612,.2,.525],7864:[.611,.2,.525],7865:[.44,.2,.525],7866:[.904,0,.525],7867:[.67,.006,.525],7868:[.783,0,.525],7869:[.615,.006,.525],7870:[.919,0,.525],7871:[.743,.006,.525],7872:[.919,0,.525],7873:[.743,.006,.525],7874:[.995,0,.525],7875:[.819,.006,.525],7876:[.925,0,.525],7877:[.749,.006,.525],7878:[.766,.2,.525],7879:[.622,.2,.525],7880:[.904,0,.525],7881:[.67,0,.525],7882:[.611,.2,.525],7883:[.605,.2,.525],7884:[.622,.2,.525],7885:[.44,.2,.525],7886:[.904,.011,.525],7887:[.67,.006,.525],7888:[.919,.011,.525],7889:[.743,.006,.525],7890:[.919,.011,.525],7891:[.743,.006,.525],7892:[.995,.011,.525],7893:[.819,.006,.525],7894:[.925,.011,.525],7895:[.749,.006,.525],7896:[.766,.2,.525],7897:[.622,.2,.525],7898:[.807,.011,.525],7899:[.622,.006,.525],7900:[.807,.011,.525],7901:[.622,.006,.525],7902:[.904,.011,.525],7903:[.67,.006,.525],7904:[.783,.011,.525],7905:[.615,.006,.525],7906:[.711,.2,.525],7907:[.53,.2,.525],7908:[.611,.2,.525],7909:[.431,.2,.525],7910:[.904,.011,.525],7911:[.67,.006,.525],7912:[.807,.011,.525],7913:[.622,.006,.525],7914:[.807,.011,.525],7915:[.622,.006,.525],7916:[.904,.011,.525],7917:[.67,.006,.525],7918:[.783,.011,.525],7919:[.615,.006,.525],7920:[.711,.2,.525],7921:[.53,.2,.525],7922:[.807,0,.525],7923:[.622,.228,.525],7924:[.611,.2,.525],7925:[.431,.228,.525],7926:[.904,0,.525],7927:[.67,.228,.525],7928:[.783,0,.525],7929:[.615,.228,.525]}}},"NCM"),{},["MJX-NCM-ML"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/monospace-l","4.0.0-beta.7","dynamic-font")})();