(()=>{"use strict";const d=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","devanagari",d({DV:{normal:{2304:[.906,-.683,0,{dx:.044}],2305:[.896,-.683,0,{dx:.028}],2306:[.828,-.718,0,{dx:.147}],2307:[.493,-.054,.252],2308:[1.003,.083,.917],2309:[.68,.083,.917],2310:[.68,.083,1.22],2311:[.68,.227,.72],2312:[.968,.227,.72],2313:[.68,0,.705],2314:[.68,0,.915],2315:[.68,.083,1.042],2316:[.68,.078,1.042],2317:[.922,.151,.709],2318:[1.003,.151,.709],2319:[.68,.151,.709],2320:[1.032,.151,.709],2321:[.922,.083,1.139],2322:[1.003,.083,1.22],2323:[1.032,.083,1.22],2324:[1.032,.083,1.219],2325:[.68,.083,1.041],2326:[.68,.081,.994],2327:[.68,.083,.787],2328:[.68,.081,.869],2329:[.68,.007,.788,{ic:.06}],2330:[.68,.081,.894],2331:[.68,-.008,.861],2332:[.68,.083,.921],2333:[.68,.227,.994],2334:[.68,.081,1.07],2335:[.68,-.043,.735],2336:[.68,-.043,.808],2337:[.68,.007,.788],2338:[.68,-.043,.731],2339:[.68,.081,.922],2340:[.68,.081,.793],2341:[.704,.081,.764],2342:[.68,.033,.68],2343:[.68,.081,.715],2344:[.68,.083,.789],2345:[.68,.083,.789],2346:[.68,.081,.784],2347:[.68,.081,1.025],2348:[.68,.081,.79],2349:[.704,.081,.774],2350:[.68,.081,.774],2351:[.68,.081,.787],2352:[.68,.015,.623],2353:[.68,.015,.623],2354:[.68,.081,.929],2355:[.68,-.063,.979],2356:[.68,.089,.979],2357:[.68,.081,.79],2358:[.68,.091,.865],2359:[.68,.081,.784],2360:[.68,.081,.892],2361:[.68,.173,.715],2362:[1.002,-.616,0,{dx:.128}],2363:[1.002,.081,.301,{ic:.06}],2364:[-.081,.204,0,{dx:.277}],2365:[.68,-.08,.435],2366:[.68,.081,.301,{ic:.06}],2367:[.978,.081,.301,{ic:.384}],2368:[.977,.081,.279,{ic:.06}],2369:[.038,.288,0,{dx:.232}],2370:[.024,.31,0,{dx:.074}],2371:[.032,.309,0,{dx:.166}],2372:[.065,.372,0,{dx:.16}],2373:[.922,-.749,0,{dx:0}],2374:[1.003,-.639,0,{dx:.223}],2375:[1.032,-.664,0,{dx:.178}],2376:[1.032,-.664,0,{dx:.184}],2377:[.922,.081,.301,{ic:.06}],2378:[1.003,.081,.301,{ic:.06}],2379:[1.032,.081,.301,{ic:.06}],2380:[1.032,.081,.301,{ic:.06}],2381:[-.044,.309,0,{dx:-.011}],2382:[.68,.081,.26,{ic:.06}],2383:[1.091,.081,.3,{ic:.06}],2384:[.865,-.081,.984],2385:[1.072,-.686,0,{dx:.138}],2386:[-.085,.153,0,{dx:.263}],2387:[.96,-.733,0,{dx:.172}],2388:[.96,-.733,0,{dx:.086}],2389:[1.028,-.737,0,{dx:.039}],2390:[-.048,.221,0,{dx:.328}],2391:[-.048,.388,0,{dx:.328}],2392:[.68,.083,1.041],2393:[.68,.081,.994],2394:[.68,.083,.787],2395:[.68,.083,.921],2396:[.68,.173,.788],2397:[.68,.148,.731],2398:[.68,.081,1.025],2399:[.68,.081,.787],2400:[.68,.081,1.043],2401:[.68,.258,1.042],2402:[.029,.344,0,{dx:.072}],2403:[.029,.431,0,{dx:.074}],2404:[.78,.03,.311],2405:[.78,.03,.498],2406:[.541,-.149,.487],2407:[.68,.025,.409],2408:[.68,.025,.481],2409:[.68,.025,.481],2410:[.67,.002,.646],2411:[.679,.013,.523],2412:[.68,.046,.464],2413:[.661,-.029,.685],2414:[.67,0,.454],2415:[.68,.017,.404],2416:[.437,-.155,.427],2417:[.704,-.582,.338],2418:[.922,.083,.917],2419:[1.002,.083,.917],2420:[1.002,.083,1.139],2421:[1.091,.083,1.138],2422:[.68,.221,.917],2423:[.68,.388,.917],2424:[.68,.028,.765],2425:[.68,.183,.921],2426:[.68,.081,.787],2427:[.68,.083,.8],2428:[.68,.083,.921],2429:[.689,.018,.522],2430:[.68,.173,.788],2431:[.68,.081,.79]}},DVB:{bold:{}},DVI:{italic:{}},DVBI:{"bold-italic":{}}},"NCM"),{},["MJX-NCM-DV","MJX-NCM-DVB","MJX-NCM-DVI","MJX-NCM-DVBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/devanagari","4.0.0-beta.7","dynamic-font")})();