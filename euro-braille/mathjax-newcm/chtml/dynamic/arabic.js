(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","arabic",c({AB:{normal:{126464:[.782,-.013,.21],126465:[.453,.07,.825,{ic:.061}],126466:[.518,.455,.864],126467:[.48,-.113,.348],126468:[.488,-.152,.279],126469:[.354,.186,.547],126470:[.583,.271,.656],126471:[.518,.455,.886],126472:[.851,-.075,.813],126473:[.555,.328,.833],126474:[.881,-.065,.65],126475:[.788,.158,.563,{ic:.005}],126476:[.466,.444,.442],126477:[.716,.044,.567,{ic:.025}],126478:[.346,.172,.806,{ic:.023}],126479:[.744,.446,.83,{ic:.068}],126480:[.76,-.078,1.008,{ic:.016}],126481:[.396,.186,1.057,{ic:.02}],126482:[.567,.137,.588,{ic:.019}],126483:[.36,.271,.625],126484:[.695,.172,.83,{ic:.017}],126485:[.537,-.078,.888,{ic:.026}],126486:[.65,-.078,.882,{ic:.022}],126487:[.819,.455,.911],126488:[.671,-.099,.385],126489:[.546,.216,1.047,{ic:.02}],126490:[.851,-.075,.789],126491:[.915,.457,.907,{ic:.045}],126492:[.453,-.078,.794],126493:[.489,.044,.567,{ic:.025}],126494:[.579,-.078,.871],126495:[.364,.137,.575,{ic:.036}],126496:[.589,-.15,.359],126497:[.488,.041,.279],126498:[.49,-.05,.793],126500:[.637,-.15,.586],126503:[.49,-.152,.743],126505:[.488,.04,.319],126506:[.96,-.152,.794],126507:[.961,-.148,.353],126508:[.529,-.152,.459],126509:[.704,-.152,.268],126510:[.434,-.152,.575],126511:[.632,-.151,.521,{ic:.037}],126512:[.828,-.15,.359],126513:[.433,-.117,.643],126514:[.846,-.15,.352],126516:[.733,-.152,.583],126517:[.697,-.152,.286],126518:[.799,-.152,.29],126519:[.757,-.152,.803],126521:[.624,-.117,.669],126523:[.859,-.151,.516,{ic:.042}],126530:[.548,.405,.792],126535:[.548,.405,.792],126537:[.555,.328,.947],126539:[.788,.157,.726],126541:[.567,.081,.715],126542:[.371,.154,.988],126543:[.744,.421,.785],126545:[.457,.125,1.23],126546:[.608,.123,.805],126548:[.679,.154,1.003],126551:[.794,.405,.776],126553:[.645,.135,1.226],126555:[.901,.421,.85],126557:[.459,.081,.723],126559:[.378,.123,.795],126561:[.925,-.031,.484],126562:[.931,-.129,.936],126564:[.931,-.225,.773],126567:[.931,-.225,.889],126568:[1.005,-.219,.865],126569:[.925,-.022,.511],126570:[1.03,-.219,1],126572:[.928,-.222,.655],126573:[.925,-.219,.448,{ic:.022}],126574:[.931,-.225,.771],126575:[.931,-.226,.718,{ic:.05}],126576:[.928,-.221,.502,{ic:.002}],126577:[.93,-.187,.858],126578:[.932,-.221,.527],126580:[.931,-.225,.778],126581:[.925,-.219,.52],126582:[.925,-.219,.507],126583:[.931,-.225,.947],126585:[.93,-.187,.863],126586:[1.005,-.219,.905],126587:[.932,-.226,.744,{ic:.024}],126588:[.925,-.219,.489],126590:[.928,-.221,.527],126591:[.455,-.001,.905],126592:[.822,-.066,.29],126593:[.455,.139,.905],126594:[.518,.551,.823],126595:[.48,-.067,.355],126596:[.6,-.066,.668],126597:[.354,.229,.599],126598:[.547,.334,.59],126599:[.518,.551,.835],126600:[.885,-.017,.871],126601:[.555,.328,.795],126603:[.816,.194,.567],126604:[.436,.469,.363],126605:[.705,.104,.571],126606:[.346,.256,.826],126607:[.744,.535,.831],126608:[.731,-.001,.937],126609:[.396,.267,1.054],126610:[.567,.217,.603],126611:[.36,.334,.59],126612:[.674,.256,.826],126613:[.583,-.001,.905],126614:[.674,-.001,.905],126615:[.831,.551,.823],126616:[.71,-.067,.355],126617:[.607,.267,1.054],126618:[.885,-.017,.811,{ic:.029}],126619:[.932,.535,.831],126625:[.453,.07,.825,{ic:.061}],126626:[.518,.455,.864],126627:[.48,-.113,.348],126629:[.354,.186,.547],126630:[.583,.271,.656],126631:[.518,.455,.886],126632:[.851,-.075,.813],126633:[.555,.328,.833],126635:[.788,.158,.563,{ic:.005}],126636:[.466,.444,.442],126637:[.716,.044,.567,{ic:.025}],126638:[.346,.172,.806,{ic:.023}],126639:[.744,.446,.83,{ic:.068}],126640:[.76,-.078,1.008,{ic:.016}],126641:[.396,.186,1.057,{ic:.02}],126642:[.567,.137,.588,{ic:.019}],126643:[.36,.271,.625],126644:[.695,.172,.83,{ic:.017}],126645:[.537,-.078,.888,{ic:.026}],126646:[.65,-.078,.882,{ic:.022}],126647:[.819,.455,.911],126648:[.671,-.113,.385],126649:[.546,.186,1.047,{ic:.03}],126650:[.851,-.075,.789],126651:[.915,.446,.907],126704:[.514,.012,1.368,{sk:-.058}],126705:[.506,.025,1.771]}},ABB:{bold:{}},ABI:{italic:{}},ABBI:{"bold-italic":{}}},"NCM"),{},["MJX-NCM-AB","MJX-NCM-ABB","MJX-NCM-ABI","MJX-NCM-ABBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/arabic","4.0.0-beta.7","dynamic-font")})();