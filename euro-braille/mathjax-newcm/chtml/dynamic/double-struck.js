(()=>{"use strict";const s=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","double-struck",s({DS:{normal:{8450:[.704,.019,.722,{sk:.036}],8461:[.685,0,.777,{sk:.011}],8469:[.685,.019,.722,{sk:.022}],8473:[.685,0,.611,{sk:.028}],8474:[.704,.179,.777],8477:[.685,0,.722],8484:[.685,0,.666,{sk:.057}],8508:[.431,0,.517],8509:[.431,.216,.472],8510:[.683,0,.611,{sk:.061}],8511:[.683,0,.667,{sk:.028}],8512:[.683,0,.667,{sk:.021}],8517:[.683,0,.694,{sk:.074}],8518:[.694,.022,.5,{ic:.048,sk:.051}],8519:[.453,.022,.472,{sk:.056}],8520:[.691,0,.279,{ic:.053,sk:.071}],8521:[.691,.216,.379,{ic:.063,sk:.117}],120120:[.704,0,.722,{sk:-.014}],120121:[.685,0,.666],120123:[.685,0,.722,{sk:-.02}],120124:[.685,0,.666],120125:[.685,0,.611,{sk:.03}],120126:[.704,.019,.777],120128:[.685,0,.388],120129:[.685,.075,.5,{sk:.055}],120130:[.685,0,.777,{sk:.015}],120131:[.685,0,.666,{sk:-.148}],120132:[.685,0,.944,{sk:.073}],120134:[.704,.019,.777],120138:[.704,.012,.555,{sk:-.013}],120139:[.685,0,.666],120140:[.685,.019,.722,{sk:.053}],120141:[.685,.019,.722,{sk:.072}],120142:[.685,.019,1,{sk:.048}],120143:[.685,0,.722,{sk:.026}],120144:[.685,0,.722,{sk:.038}],120146:[.453,.022,.5],120147:[.694,.022,.628,{sk:-.169}],120148:[.453,.022,.472],120149:[.694,.022,.5,{sk:.17}],120150:[.453,.022,.472],120151:[.716,0,.389,{sk:.028}],120152:[.453,.216,.5,{sk:-.013}],120153:[.694,0,.572,{sk:-.147}],120154:[.691,0,.279],120155:[.691,.216,.389,{sk:.057}],120156:[.694,0,.544,{sk:-.054}],120157:[.694,0,.279],120158:[.453,0,.722,{sk:.061}],120159:[.453,0,.572,{sk:.06}],120160:[.453,.022,.472],120161:[.453,.194,.628,{sk:.076}],120162:[.453,.194,.5,{sk:-.012}],120163:[.453,0,.544],120164:[.453,.022,.389],120165:[.694,.022,.417,{sk:-.059}],120166:[.431,.022,.528,{sk:.051}],120167:[.431,0,.472,{sk:.051}],120168:[.431,0,.667,{sk:.033}],120169:[.431,0,.472,{sk:.015}],120170:[.431,.216,.472,{sk:.062}],120171:[.431,0,.472,{sk:.038}],120792:[.666,.022,.556],120793:[.644,0,.556],120794:[.666,0,.556],120795:[.666,.022,.556],120796:[.644,0,.556,{sk:.117}],120797:[.644,.022,.556],120798:[.666,.022,.556],120799:[.644,0,.556,{sk:.031}],120800:[.666,.022,.556],120801:[.666,.022,.556]},"double-struck":{305:[.431,0,.279],567:[.431,.216,.389,{sk:.054}]}}},"NCM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/double-struck","4.0.0-beta.7","dynamic-font")})();