(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-newcm"].chtml_ts.MathJaxNewcmFont.dynamicSetup("","cherokee",c({CH:{normal:{5024:[.683,0,.764],5025:[.683,.022,.736],5026:[.677,0,.722],5027:[.66,.011,.769],5028:[.705,.022,1.099],5029:[.657,0,.278],5030:[.705,.022,.651],5031:[.705,.022,.778],5032:[.683,0,.685],5033:[.683,.022,.75],5034:[.716,0,.75],5035:[.683,.022,.514],5036:[.68,0,.681],5037:[.683,.022,.954],5038:[.683,0,.681],5039:[.683,.022,.744],5040:[.683,0,.541],5041:[.68,0,.625],5042:[.683,.014,.902],5043:[.683,.022,1.028],5044:[.657,.011,.708],5045:[.673,.002,.595],5046:[.705,.022,.785],5047:[.683,0,.917],5048:[.673,.002,.725],5049:[.66,.022,1],5050:[.705,.022,1.101],5051:[.683,0,.75],5052:[.66,.012,.753],5053:[.683,.022,.75],5054:[.705,.022,.777],5055:[.683,.195,.705],5056:[.705,.022,.785],5057:[.683,.013,.818],5058:[.68,.003,.742],5059:[.683,0,.611],5060:[.696,.001,.649],5061:[.705,.022,1.178],5062:[.677,0,.722],5063:[.671,.011,.945],5064:[.683,.014,.902],5065:[.683,.022,1.278],5066:[.671,.011,.945],5067:[.702,.012,.53],5068:[.683,.022,.75],5069:[.671,.011,1.03],5070:[.677,0,.5],5071:[.683,0,.708],5072:[.683,0,.661],5073:[.689,.016,.8],5074:[.683,0,.778],5075:[.673,.002,.595],5076:[.683,.022,1.039],5077:[.705,.022,.676],5078:[.677,.011,.786],5079:[.683,.012,.658],5080:[.69,.012,.726],5081:[.683,.022,.75],5082:[.705,.022,.556],5083:[.683,.014,.997],5084:[.679,.011,.748],5085:[.682,0,.625],5086:[.683,0,.625],5087:[.705,.022,.722],5088:[.683,.022,.906],5089:[.683,.014,.902],5090:[.683,0,.681],5091:[.705,.022,.786],5092:[.685,.022,.924],5093:[.68,.003,.868],5094:[.683,0,.778],5095:[.686,.011,.562],5096:[.705,.023,.919],5097:[.705,.022,.931],5098:[.683,.014,1.078],5099:[.705,.022,.778],5100:[.683,.014,.894],5101:[.704,.011,.569],5102:[.666,.011,.562],5103:[.671,.011,.945],5104:[.705,.244,.67],5105:[.66,.012,.753],5106:[.676,.003,.742],5107:[.705,.022,.919],5108:[.683,0,.708],5109:[.705,.022,.789],5112:[.564,.195,.536],5113:[.528,.01,.602],5114:[.541,.002,.593],5115:[.564,.018,.735],5116:[.546,0,.566],5117:[.564,.018,.631]}},CHB:{bold:{5024:[.686,0,.882],5025:[.686,.011,.863],5026:[.675,0,.8],5027:[.661,.011,.769],5028:[.697,.011,1.154],5029:[.657,.001,.278],5030:[.697,.011,.741],5031:[.705,.022,.778],5032:[.686,0,.726],5033:[.686,.011,.869],5034:[.698,0,.869],5035:[.686,.011,.594],5036:[.68,0,.756],5037:[.686,.023,.986],5038:[.686,0,.726],5039:[.683,.022,.744],5040:[.686,0,.529],5041:[.68,0,.692],5042:[.683,.014,.954],5043:[.686,.008,1.189],5044:[.657,.011,.708],5045:[.697,0,.594],5046:[.697,.011,.846],5047:[.686,0,1.092],5048:[.697,0,.758],5049:[.661,.022,1],5050:[.697,.011,1.218],5051:[.686,0,.9],5052:[.661,.012,.753],5053:[.686,.011,.869],5054:[.697,.011,.894],5055:[.683,.195,.705],5056:[.697,.011,.904],5057:[.683,.013,.818],5058:[.68,0,.719],5059:[.686,0,.703],5060:[.696,.001,.649],5061:[.697,.011,1.258],5062:[.675,0,.767],5063:[.671,.011,.945],5064:[.683,.014,.802],5065:[.686,.008,1.282],5066:[.671,.011,.945],5067:[.702,.012,.53],5068:[.686,.002,.885],5069:[.671,.011,1.03],5070:[.656,0,.575],5071:[.686,0,.786],5072:[.686,0,.769],5073:[.689,.016,.8],5074:[.686,.002,.809,{ic:.049}],5075:[.686,.011,.636],5076:[.686,.008,1.231],5077:[.697,.011,.746],5078:[.675,.011,.826],5079:[.686,.012,.695],5080:[.675,.012,.846],5081:[.686,.008,.869],5082:[.697,.011,.639],5083:[.683,.014,.997],5084:[.679,.011,.748],5085:[.682,0,.692],5086:[.686,0,.692],5087:[.697,.011,.831],5088:[.686,.002,.885],5089:[.683,.014,.942],5090:[.686,0,.786],5091:[.697,.011,.889],5092:[.678,.022,1.016],5093:[.68,0,.887],5094:[.686,0,.901],5095:[.687,.011,.562],5096:[.697,.011,.993],5097:[.697,.011,.983],5098:[.683,.014,1.078],5099:[.697,.011,.894],5100:[.683,.014,.894],5101:[.704,.011,.569],5102:[.655,.011,.575],5103:[.671,.011,.945],5104:[.705,.245,.67],5105:[.661,.012,.753],5106:[.676,0,.719],5107:[.697,.011,.998],5108:[.686,0,.818],5109:[.705,.011,.904],5112:[.564,.197,.536],5113:[.529,.01,.602],5114:[.541,0,.591],5115:[.523,.007,.774],5116:[.516,0,.66],5117:[.53,.007,.722]}},CHI:{italic:{5024:[.683,0,.764,{ic:.023}],5025:[.683,.022,.736,{ic:.015}],5026:[.677,0,.722,{ic:.089}],5027:[.66,.011,.769,{ic:.026}],5028:[.705,.022,1.099,{ic:.045}],5029:[.657,0,.278,{ic:.043}],5030:[.705,.022,.651,{ic:.031}],5031:[.705,.022,.778,{ic:.024}],5032:[.683,0,.685,{ic:.047}],5033:[.684,.022,.75,{ic:.126}],5034:[.716,0,.75],5035:[.683,.022,.514,{ic:.096}],5036:[.68,0,.681,{ic:.06}],5037:[.683,.022,.954,{ic:.056}],5038:[.683,0,.681,{ic:.053}],5039:[.683,.022,.744,{ic:.032}],5040:[.683,0,.541,{ic:.051}],5041:[.68,0,.625,{ic:.075}],5042:[.683,.014,.902,{ic:.019}],5043:[.683,.022,1.028,{ic:.126}],5044:[.657,.011,.708,{ic:.08}],5045:[.673,.002,.595,{ic:.067}],5046:[.705,.022,.785,{ic:.028}],5047:[.683,0,.917,{ic:.108}],5048:[.673,.002,.725],5049:[.66,.022,1,{ic:.048}],5050:[.705,.022,1.101,{ic:.117}],5051:[.683,0,.75,{ic:.112}],5052:[.66,.012,.753,{ic:.012}],5053:[.683,.022,.75,{ic:.126}],5054:[.705,.022,.777,{ic:.024}],5055:[.683,.195,.705],5056:[.705,.022,.785,{ic:.028}],5057:[.683,.014,.818],5058:[.68,.003,.742],5059:[.683,0,.611,{ic:.086}],5060:[.696,.001,.649],5061:[.705,.022,1.178,{ic:.046}],5062:[.677,0,.722,{ic:.089}],5063:[.671,.011,.945,{ic:.036}],5064:[.683,.014,.902],5065:[.683,.022,1.278,{ic:.072}],5066:[.671,.011,.945,{ic:.036}],5067:[.702,.012,.53,{ic:.068}],5068:[.683,.022,.75,{ic:.112}],5069:[.671,.011,1.03,{ic:.036}],5070:[.677,0,.5,{ic:.014}],5071:[.683,0,.708],5072:[.683,0,.661,{ic:.019}],5073:[.689,.016,.8,{ic:.066}],5074:[.683,0,.778],5075:[.673,.002,.595,{ic:.011}],5076:[.683,.022,1.039,{ic:.126}],5077:[.705,.022,.676,{ic:.054}],5078:[.677,.011,.786,{ic:.025}],5079:[.683,.012,.658,{ic:.111}],5080:[.69,.012,.726,{ic:.1}],5081:[.683,.022,.75,{ic:.125}],5082:[.705,.022,.556,{ic:.063}],5083:[.683,.014,.997,{ic:.065}],5084:[.679,.011,.748,{ic:.001}],5085:[.682,0,.625,{ic:.013}],5086:[.683,0,.625,{ic:.013}],5087:[.705,.022,.722,{ic:.09}],5088:[.683,.022,.906,{ic:.113}],5089:[.683,.014,.902,{ic:.061}],5090:[.683,0,.681,{ic:.053}],5091:[.705,.022,.786,{ic:.056}],5092:[.685,.022,.924,{ic:.113}],5093:[.68,.003,.868,{ic:.03}],5094:[.684,0,.778,{ic:.09}],5095:[.686,.011,.562,{ic:.112}],5096:[.705,.023,.919,{ic:.082}],5097:[.705,.022,.931],5098:[.683,.014,1.078,{ic:.049}],5099:[.705,.022,.778,{ic:.023}],5100:[.683,.014,.894,{ic:.065}],5101:[.704,.011,.569,{ic:.048}],5102:[.666,.011,.562,{ic:.031}],5103:[.671,.011,.945,{ic:.036}],5104:[.705,.244,.67,{ic:.057}],5105:[.66,.012,.753,{ic:.013}],5106:[.676,.003,.742],5107:[.705,.022,.919,{ic:.084}],5108:[.683,0,.708,{ic:.028}],5109:[.705,.022,.789,{ic:.011}],5110:[.564,.195,.536,{ic:.044}],5111:[.528,.01,.602,{ic:.009}],5112:[.541,.002,.593],5113:[.564,.018,.735,{ic:.066}],5114:[.546,0,.566,{ic:.023}],5115:[.564,.018,.631,{ic:.008}]}},CHBI:{"bold-italic":{5024:[.683,0,.764,{ic:.023}],5025:[.683,.022,.736,{ic:.019}],5026:[.677,.001,.722,{ic:.088}],5027:[.661,.011,.769,{ic:.027}],5028:[.705,.022,1.099,{ic:.045}],5029:[.657,.001,.278,{ic:.049}],5030:[.705,.022,.651,{ic:.033}],5031:[.705,.022,.778,{ic:.024}],5032:[.685,.002,.685,{ic:.048}],5033:[.686,.022,.75,{ic:.127}],5034:[.716,.002,.75],5035:[.685,.022,.514,{ic:.096}],5036:[.68,0,.681,{ic:.061}],5037:[.685,.022,.954,{ic:.059}],5038:[.683,.002,.681,{ic:.051}],5039:[.683,.022,.744,{ic:.032}],5040:[.685,.002,.541,{ic:.054}],5041:[.68,0,.625,{ic:.076}],5042:[.683,.014,.902,{ic:.024}],5043:[.685,.022,1.028,{ic:.126}],5044:[.657,.011,.708,{ic:.077}],5045:[.673,.004,.595,{ic:.067}],5046:[.705,.022,.785,{ic:.026}],5047:[.683,.002,.917,{ic:.107}],5048:[.673,.004,.725],5049:[.661,.022,1,{ic:.046}],5050:[.705,.022,1.101,{ic:.118}],5051:[.685,.002,.75,{ic:.112}],5052:[.661,.012,.753,{ic:.014}],5053:[.684,.022,.75,{ic:.126}],5054:[.705,.022,.777,{ic:.025}],5055:[.683,.195,.705],5056:[.705,.022,.785,{ic:.029}],5057:[.683,.013,.818],5058:[.681,.004,.742],5059:[.683,0,.611,{ic:.083}],5060:[.696,.001,.649],5061:[.705,.022,1.178,{ic:.048}],5062:[.677,0,.722,{ic:.088}],5063:[.671,.011,.945,{ic:.037}],5064:[.683,.014,.902],5065:[.685,.022,1.278,{ic:.071}],5066:[.671,.011,.945,{ic:.037}],5067:[.702,.012,.53,{ic:.07}],5068:[.685,.022,.75,{ic:.111}],5069:[.671,.011,1.03,{ic:.037}],5070:[.677,.001,.5,{ic:.018}],5071:[.684,0,.708],5072:[.685,.002,.661,{ic:.022}],5073:[.689,.016,.8,{ic:.064}],5074:[.683,.002,.778],5075:[.675,.002,.595,{ic:.012}],5076:[.685,.022,1.039,{ic:.126}],5077:[.705,.022,.676,{ic:.054}],5078:[.677,.011,.786,{ic:.029}],5079:[.683,.012,.658,{ic:.11}],5080:[.69,.012,.726,{ic:.1}],5081:[.685,.022,.75,{ic:.125}],5082:[.705,.022,.556,{ic:.062}],5083:[.683,.014,.997,{ic:.064}],5084:[.679,.011,.748,{ic:.002}],5085:[.682,0,.625,{ic:.019}],5086:[.685,0,.625,{ic:.019}],5087:[.705,.022,.722,{ic:.087}],5088:[.684,.022,.906,{ic:.112}],5089:[.683,.014,.902,{ic:.06}],5090:[.683,.002,.681,{ic:.051}],5091:[.705,.022,.786,{ic:.058}],5092:[.685,.022,.924,{ic:.127}],5093:[.681,.004,.868,{ic:.031}],5094:[.685,.002,.778,{ic:.091}],5095:[.687,.011,.562,{ic:.111}],5096:[.705,.023,.919,{ic:.08}],5097:[.705,.022,.931],5098:[.683,.014,1.078,{ic:.047}],5099:[.705,.022,.778,{ic:.024}],5100:[.683,.014,.894,{ic:.063}],5101:[.704,.011,.569,{ic:.047}],5102:[.666,.011,.562,{ic:.031}],5103:[.671,.011,.945,{ic:.037}],5104:[.705,.245,.67,{ic:.056}],5105:[.661,.012,.753,{ic:.014}],5106:[.676,.004,.742],5107:[.705,.022,.919,{ic:.081}],5108:[.683,0,.708,{ic:.028}],5109:[.705,.022,.789,{ic:.012}],5110:[.564,.197,.536,{ic:.043}],5111:[.529,.01,.602,{ic:.012}],5112:[.541,.003,.593],5113:[.564,.018,.735,{ic:.064}],5114:[.546,0,.566,{ic:.023}],5115:[.564,.018,.631,{ic:.01}]}}},"NCM"),{},["MJX-NCM-CH","MJX-NCM-CHB","MJX-NCM-CHI","MJX-NCM-CHBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-newcm]/chtml/dynamic/cherokee","4.0.0-beta.7","dynamic-font")})();