(()=>{"use strict";const i=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","sans-serif",i({SS:{normal:{8513:[.705,.022,.638],120224:[.694,0,.667],120225:[.694,0,.667,{sk:-.077}],120226:[.706,.011,.639,{sk:.091}],120227:[.694,0,.722,{sk:-.109}],120228:[.691,0,.597,{sk:.018}],120229:[.691,0,.569,{sk:.025}],120230:[.706,.011,.667,{sk:.073}],120231:[.694,0,.708],120232:[.694,0,.278],120233:[.694,.022,.472,{sk:.109}],120234:[.694,0,.694,{sk:.02}],120235:[.694,0,.542,{sk:-.132}],120236:[.694,0,.875],120237:[.694,0,.708],120238:[.716,.022,.736],120239:[.694,0,.639,{sk:-.06}],120240:[.716,.125,.736],120241:[.694,0,.646,{sk:-.066}],120242:[.716,.022,.556,{sk:.015}],120243:[.688,0,.681],120244:[.694,.022,.688],120245:[.694,0,.667],120246:[.694,0,.944],120247:[.694,0,.667,{sk:-.015}],120248:[.694,0,.667],120249:[.694,0,.611],120250:[.461,.011,.481,{sk:-.013}],120251:[.694,.011,.517,{sk:-.139}],120252:[.461,.011,.444,{sk:.042}],120253:[.694,.011,.517,{sk:.139}],120254:[.461,.011,.444,{sk:.016}],120255:[.705,0,.306,{ic:.041,sk:.096}],120256:[.455,.206,.5,{sk:.074}],120257:[.694,0,.517,{sk:-.14}],120258:[.655,0,.239],120259:[.655,.205,.267],120260:[.694,0,.489,{sk:-.124}],120261:[.694,0,.239],120262:[.455,0,.794,{sk:-.038}],120263:[.455,0,.517,{sk:-.038}],120264:[.461,.011,.5],120265:[.455,.194,.517,{sk:-.03}],120266:[.455,.194,.517,{sk:.041}],120267:[.455,0,.342,{sk:.034}],120268:[.461,.011,.383,{sk:.022}],120269:[.571,.011,.361,{sk:-.04}],120270:[.444,.011,.517],120271:[.444,0,.461],120272:[.444,0,.683],120273:[.444,0,.461],120274:[.444,.205,.461],120275:[.444,0,.435],120802:[.678,.022,.5],120803:[.678,0,.5,{sk:.01}],120804:[.678,0,.5],120805:[.678,.022,.5],120806:[.656,0,.5,{sk:.073}],120807:[.656,.022,.5],120808:[.678,.022,.5,{sk:.084}],120809:[.656,.011,.5],120810:[.678,.022,.5],120811:[.678,.022,.5]},"sans-serif":{305:[.444,0,.239],567:[.444,.205,.267,{sk:.013}],32:[0,0,.333],33:[.694,0,.319],34:[.716,-.43,.434],35:[.694,.194,.833],36:[.75,.056,.5],37:[.75,.056,.833],38:[.716,.022,.758],39:[.716,-.43,.278],40:[.75,.25,.389],41:[.75,.25,.389],42:[.75,-.306,.5],43:[.583,.083,.778],44:[.083,.125,.278],45:[.251,-.193,.333],46:[.083,0,.278],47:[.75,.25,.5],58:[.444,0,.278],59:[.444,.125,.278],60:[.54,.04,.778],61:[.37,-.13,.778],62:[.54,.04,.778],63:[.705,0,.472],64:[.705,.011,.667],91:[.75,.25,.289],92:[.75,.25,.5],93:[.75,.25,.289],94:[.744,-.562,.556,{ic:.005}],95:[-.086,.164,.667],96:[.694,-.527,.5],123:[.75,.25,.5],124:[.75,.25,.278],125:[.75,.25,.5],126:[.307,-.193,.556],160:[0,0,.5],163:[.694,.022,.667],165:[.694,0,.667],167:[.712,.212,.528],168:[.651,-.571,.5],172:[.37,-.13,.778],175:[.643,-.579,.5],176:[.694,-.417,.375],177:[.666,0,.778],180:[.694,-.527,.5],181:[.444,.194,.517],182:[.694,.194,.611],183:[.389,-.306,.778],215:[.492,-.009,.778],240:[.723,.011,.5],247:[.414,-.085,.778],710:[.697,-.525,.5],711:[.697,-.525,.5],713:[.643,-.579,.5],714:[.694,-.527,.5],715:[.694,-.527,.5],728:[.7,-.522,.5],729:[.655,-.566,.278],730:[.685,-.517,.667],732:[.67,-.551,.5],768:[.694,-.527,0,{dx:.276}],769:[.694,-.527,0,{dx:.204}],770:[.697,-.525,0,{dx:.24}],771:[.67,-.551,0,{dx:.239}],772:[.643,-.579,0,{dx:.239}],774:[.7,-.522,0,{dx:.239}],775:[.655,-.566,0,{dx:.24}],776:[.651,-.571,0,{dx:.24}],778:[.685,-.517,0,{dx:.241}],780:[.697,-.525,0,{dx:.24}],913:[.694,0,.667],914:[.694,0,.667],915:[.691,0,.542],916:[.694,0,.833],917:[.691,0,.597],918:[.694,0,.611],919:[.694,0,.708],920:[.716,.022,.778],921:[.694,0,.278],922:[.694,0,.694],923:[.694,0,.611],924:[.694,0,.875],925:[.694,0,.708],926:[.688,0,.667],927:[.716,.022,.736],928:[.691,0,.708],929:[.694,0,.639],931:[.694,0,.722],932:[.688,0,.681],933:[.716,0,.778],934:[.694,0,.722],935:[.694,0,.667],936:[.694,0,.778],937:[.716,0,.722],8208:[.251,-.193,.333],8209:[.251,-.193,.333],8211:[.305,-.244,.5],8212:[.305,-.244,1],8214:[.75,.25,.398],8216:[.694,-.486,.278],8217:[.694,-.486,.278],8220:[.694,-.486,.472],8221:[.694,-.486,.472],8224:[.705,.216,.444],8225:[.705,.205,.444],8230:[.083,0,.626],8260:[.674,-.02,.558],8364:[.716,.022,.599],8486:[.716,0,.722],8487:[.694,.022,.722],8592:[.511,.009,1],8593:[.694,.194,.5],8594:[.511,.009,1],8595:[.694,.194,.5],8722:[.27,-.23,.778],8734:[.468,.024,1]}},SSB:{normal:{120276:[.694,0,.733],120277:[.694,0,.733,{sk:-.07}],120278:[.706,.011,.703,{sk:.082}],120279:[.694,0,.794,{sk:-.096}],120280:[.691,0,.642,{sk:.015}],120281:[.691,0,.611,{sk:.022}],120282:[.706,.011,.733,{sk:.061}],120283:[.694,0,.794],120284:[.694,0,.331],120285:[.694,.022,.519,{sk:.097}],120286:[.694,0,.764,{sk:.016}],120287:[.694,0,.581,{sk:-.126}],120288:[.694,0,.978],120289:[.694,0,.794],120290:[.716,.022,.794],120291:[.694,0,.703,{sk:-.064}],120292:[.716,.106,.794],120293:[.694,0,.703,{sk:-.056}],120294:[.716,.022,.611],120295:[.688,0,.733],120296:[.694,.022,.764],120297:[.694,0,.733],120298:[.694,0,1.039],120299:[.694,0,.733,{sk:-.015}],120300:[.694,0,.733],120301:[.694,0,.672],120302:[.475,.011,.525],120303:[.694,.011,.561,{sk:-.152}],120304:[.475,.011,.489,{sk:.041}],120305:[.694,.011,.561,{sk:.152}],120306:[.475,.011,.511,{sk:.011}],120307:[.705,0,.336,{ic:.045,sk:.087}],120308:[.469,.206,.55,{sk:.065}],120309:[.694,0,.561,{sk:-.153}],120310:[.673,0,.256],120311:[.673,.205,.286],120312:[.694,0,.531,{sk:-.138}],120313:[.694,0,.256],120314:[.469,0,.867,{sk:-.028}],120315:[.469,0,.561,{sk:-.028}],120316:[.475,.011,.55],120317:[.469,.194,.561,{sk:-.043}],120318:[.469,.194,.561,{sk:.061}],120319:[.469,0,.372,{sk:.03}],120320:[.475,.011,.422,{sk:.015}],120321:[.589,.011,.404,{sk:-.047}],120322:[.458,.011,.561],120323:[.458,0,.5],120324:[.458,0,.744],120325:[.458,0,.5],120326:[.458,.205,.5],120327:[.458,0,.476],120812:[.716,.022,.55],120813:[.716,0,.55],120814:[.716,0,.55,{sk:-.016}],120815:[.716,.022,.55],120816:[.694,0,.55,{sk:.068}],120817:[.694,.022,.55],120818:[.716,.022,.55,{sk:.056}],120819:[.695,.011,.55],120820:[.716,.022,.55],120821:[.716,.022,.55,{sk:-.016}]},"bold-sans-serif":{305:[.458,0,.256],567:[.458,.205,.286,{sk:.015}],32:[0,0,.367],33:[.694,0,.367],34:[.716,-.43,.578],35:[.694,.194,.917],36:[.75,.056,.55],37:[.75,.056,1.029],38:[.716,.022,.831],39:[.716,-.43,.306],40:[.75,.25,.428],41:[.75,.25,.428],42:[.75,-.292,.55],43:[.617,.117,.856],44:[.131,.106,.306],45:[.265,-.193,.367],46:[.131,0,.306],47:[.75,.25,.55],58:[.458,0,.306],59:[.458,.106,.306],60:[.587,.086,.894],61:[.407,-.093,.856],62:[.587,.086,.894],63:[.705,0,.519],64:[.705,.011,.733],91:[.75,.25,.343],92:[.75,.25,.575],93:[.75,.25,.343],94:[.744,-.562,.556,{ic:.005}],95:[-.057,.193,.733],96:[.694,-.537,.55],123:[.75,.25,.575],124:[.75,.25,.319],125:[.75,.25,.575],126:[.307,-.193,.556],160:[0,0,.55],163:[.694,.022,.733],165:[.694,0,.733],167:[.709,.209,.566],168:[.667,-.564,.55],172:[.407,-.093,.856],175:[.658,-.573,.55],176:[.694,-.389,.413],177:[.694,0,.856],180:[.694,-.537,.55],181:[.458,.194,.561],182:[.694,.194,.703],183:[.413,-.282,.856],215:[.523,.022,.856],240:[.723,.011,.55],247:[.532,.033,.856],710:[.697,-.535,.55],711:[.697,-.535,.55],713:[.658,-.573,.55],714:[.694,-.537,.55],715:[.694,-.537,.55],728:[.687,-.545,.55],729:[.673,-.558,.306],730:[.684,-.526,.733],732:[.681,-.55,.55],768:[.694,-.537,0,{dx:.301}],769:[.694,-.537,0,{dx:.223}],770:[.697,-.535,0,{dx:.262}],771:[.681,-.55,0,{dx:.262}],772:[.658,-.573,0,{dx:.262}],774:[.687,-.545,0,{dx:.262}],775:[.673,-.558,0,{dx:.263}],776:[.667,-.564,0,{dx:.262}],778:[.684,-.526,0,{dx:.262}],780:[.697,-.535,0,{dx:.261}],8208:[.265,-.193,.367],8209:[.265,-.193,.367],8211:[.319,-.247,.55],8212:[.319,-.247,1.1],8214:[.75,.25,.571],8216:[.694,-.457,.306],8217:[.694,-.457,.306],8220:[.694,-.457,.529],8221:[.694,-.457,.529],8224:[.702,.211,.511],8225:[.702,.202,.511],8230:[.131,0,.768],8260:[.716,.022,.646],8364:[.716,.022,.658],8486:[.716,0,.794],8487:[.694,.022,.794],8592:[.547,.046,1.1],8593:[.694,.194,.622],8594:[.548,.045,1.1],8595:[.694,.194,.622],8722:[.292,-.208,.856],8734:[.492,.034,1.1]}},SSI:{normal:{120328:[.694,0,.667,{sk:.147}],120329:[.694,0,.667,{ic:.03,sk:.047}],120330:[.706,.011,.639,{ic:.079,sk:.18}],120331:[.694,0,.722,{ic:.024,sk:.021}],120332:[.691,0,.597,{ic:.09,sk:.098}],120333:[.691,0,.569,{ic:.104,sk:.095}],120334:[.706,.011,.667,{ic:.062,sk:.174}],120335:[.694,0,.708,{ic:.053,sk:.108}],120336:[.694,0,.278,{ic:.053,sk:.108}],120337:[.694,.022,.472,{ic:.064,sk:.209}],120338:[.694,0,.694,{ic:.091,sk:.1}],120339:[.694,0,.542,{sk:.016}],120340:[.694,0,.875,{ic:.047,sk:.112}],120341:[.694,0,.708,{ic:.051,sk:.11}],120342:[.716,.022,.736,{ic:.026,sk:.131}],120343:[.694,0,.639,{ic:.052,sk:.048}],120344:[.716,.125,.736,{ic:.026,sk:.131}],120345:[.694,0,.646,{ic:.054,sk:.04}],120346:[.716,.022,.556,{ic:.051,sk:.126}],120347:[.688,0,.681,{ic:.109,sk:.062}],120348:[.694,.022,.688,{ic:.053,sk:.106}],120349:[.694,0,.667,{ic:.133,sk:.048}],120350:[.694,0,.944,{ic:.133,sk:.048}],120351:[.694,0,.667,{ic:.091,sk:.065}],120352:[.694,0,.667,{ic:.144,sk:.04}],120353:[.694,0,.611,{ic:.091,sk:.084}],120354:[.461,.011,.481,{sk:.084}],120355:[.694,.011,.517,{ic:.018}],120356:[.461,.011,.444,{ic:.055,sk:.096}],120357:[.694,.011,.517,{ic:.065,sk:.237}],120358:[.461,.011,.444,{ic:.028,sk:.09}],120359:[.705,0,.306,{ic:.189,sk:.133}],120360:[.455,.206,.5,{ic:.071,sk:.116}],120361:[.694,0,.517],120362:[.655,0,.239,{ic:.064,sk:.09}],120363:[.655,.205,.267,{ic:.056,sk:.102}],120364:[.694,0,.489,{ic:.054,sk:-.017}],120365:[.694,0,.239,{ic:.065,sk:.099}],120366:[.455,0,.794,{sk:.057}],120367:[.455,0,.517,{sk:.057}],120368:[.461,.011,.5,{ic:.022,sk:.079}],120369:[.455,.194,.517,{ic:.018,sk:.05}],120370:[.455,.194,.517,{ic:.014,sk:.126}],120371:[.455,0,.342,{ic:.082,sk:.069}],120372:[.461,.011,.383,{ic:.051,sk:.08}],120373:[.571,.011,.361,{ic:.049,sk:.043}],120374:[.444,.011,.517,{ic:.012,sk:.084}],120375:[.444,0,.461,{ic:.079,sk:.034}],120376:[.444,0,.683,{ic:.079,sk:.034}],120377:[.444,0,.461,{ic:.076,sk:.032}],120378:[.444,.205,.461,{ic:.079,sk:.034}],120379:[.444,0,.435,{ic:.059,sk:.052}]},"sans-serif-italic":{305:[.444,0,.239,{ic:.011,sk:.083}],567:[.444,.205,.267,{ic:.011,sk:.097}],32:[0,0,.333],33:[.694,0,.319,{ic:.03}],34:[.716,-.43,.434,{ic:.052}],35:[.694,.194,.833,{ic:.018}],36:[.75,.056,.5,{ic:.065}],37:[.75,.056,.833],38:[.716,.022,.758],39:[.716,-.43,.278,{ic:.052}],40:[.75,.25,.389,{ic:.102}],41:[.75,.25,.389],42:[.75,-.306,.5,{ic:.068}],43:[.583,.083,.778],44:[.083,.125,.278],45:[.251,-.193,.333],46:[.083,0,.278],47:[.75,.25,.5,{ic:.099}],48:[.678,.022,.5,{ic:.047}],49:[.678,0,.5],50:[.678,0,.5,{ic:.053}],51:[.678,.022,.5,{ic:.045}],52:[.656,0,.5,{ic:.02}],53:[.656,.022,.5,{ic:.055}],54:[.678,.022,.5,{ic:.055}],55:[.656,.011,.5,{ic:.096}],56:[.678,.022,.5,{ic:.053}],57:[.678,.022,.5,{ic:.046}],58:[.444,0,.278],59:[.444,.125,.278],60:[.54,.04,.778,{ic:.027}],61:[.37,-.13,.778,{ic:.018}],62:[.54,.04,.778],63:[.705,0,.472,{ic:.064}],64:[.705,.011,.667,{ic:.039}],91:[.75,.25,.289,{ic:.136}],92:[.75,.25,.5],93:[.75,.25,.289,{ic:.056}],94:[.744,-.562,.556,{ic:.129}],95:[-.086,.164,.667],96:[.694,-.527,.5],123:[.75,.25,.5,{ic:.084}],124:[.75,.25,.278,{ic:.035}],125:[.75,.25,.5],126:[.307,-.193,.556,{ic:.061}],160:[0,0,.5],163:[.694,.022,.667],165:[.694,0,.667,{ic:.144}],167:[.712,.212,.528,{ic:.044}],168:[.651,-.571,.5,{ic:.001}],172:[.37,-.13,.778,{ic:.018}],175:[.643,-.579,.5,{ic:.068}],176:[.694,-.417,.375,{ic:.072}],177:[.666,0,.778,{ic:.015}],180:[.694,-.527,.5,{ic:.039}],181:[.444,.194,.517,{ic:.012}],182:[.694,.194,.611,{ic:.115}],183:[.389,-.306,.778],215:[.492,-.009,.778],240:[.723,.011,.5,{ic:.034}],247:[.414,-.085,.778],710:[.697,-.525,.5,{ic:.017}],711:[.697,-.525,.5,{ic:.053}],713:[.643,-.579,.5,{ic:.068}],714:[.694,-.527,.5,{ic:.039}],715:[.694,-.527,.5],728:[.7,-.522,.5,{ic:.068}],729:[.655,-.566,.278,{ic:.044}],730:[.685,-.517,.738],732:[.67,-.551,.5,{ic:.059}],768:[.694,-.527,0,{dx:.145}],769:[.694,-.527,0,{dx:.074}],770:[.697,-.525,0,{dx:.128}],771:[.67,-.551,0,{dx:.11}],772:[.643,-.579,0,{dx:.11}],774:[.7,-.522,0,{dx:.092}],775:[.655,-.566,0,{dx:.111}],776:[.651,-.571,0,{dx:.111}],778:[.685,-.517,0,{dx:.113}],780:[.697,-.525,0,{dx:.092}],913:[.694,0,.667],914:[.694,0,.667,{ic:.03}],915:[.691,0,.542,{ic:.104}],916:[.694,0,.833],917:[.691,0,.597,{ic:.09}],918:[.694,0,.611,{ic:.091}],919:[.694,0,.708,{ic:.053}],920:[.716,.022,.778,{ic:.026}],921:[.694,0,.278,{ic:.053}],922:[.694,0,.694,{ic:.091}],923:[.694,0,.611],924:[.694,0,.875,{ic:.047}],925:[.694,0,.708,{ic:.051}],926:[.688,0,.667,{ic:.098}],927:[.716,.022,.736,{ic:.026}],928:[.691,0,.708,{ic:.052}],929:[.694,0,.639,{ic:.052}],931:[.694,0,.722,{ic:.091}],932:[.688,0,.681,{ic:.109}],933:[.716,0,.778,{ic:.064}],934:[.694,0,.722,{ic:.021}],935:[.694,0,.667,{ic:.091}],936:[.694,0,.778,{ic:.073}],937:[.716,0,.722,{ic:.046}],8208:[.251,-.193,.333,{ic:.019}],8209:[.251,-.193,.333,{ic:.019}],8211:[.305,-.244,.5,{ic:.064}],8212:[.305,-.244,1,{ic:.064}],8214:[.75,.25,.398,{ic:.035}],8216:[.694,-.486,.278,{ic:.05}],8217:[.694,-.486,.278,{ic:.05}],8220:[.694,-.486,.472,{ic:.05}],8221:[.694,-.486,.472,{ic:.051}],8224:[.705,.216,.444,{ic:.035}],8225:[.705,.205,.444,{ic:.045}],8230:[.083,0,.626],8260:[.674,-.02,.558,{ic:.087}],8364:[.716,.022,.599,{ic:.084}],8486:[.716,0,.722,{ic:.046}],8487:[.694,.022,.722,{ic:.104}],8592:[.511,.009,1],8593:[.694,.194,.5,{ic:.071}],8594:[.511,.009,1],8595:[.694,.194,.5],8722:[.27,-.23,.778],8734:[.468,.024,1]}},SSBI:{normal:{120380:[.694,0,.733,{sk:.144}],120381:[.694,0,.733,{ic:.021,sk:.061}],120382:[.706,.011,.703,{ic:.071,sk:.176}],120383:[.694,0,.794,{ic:.024,sk:.032}],120384:[.691,0,.642,{ic:.077,sk:.102}],120385:[.691,0,.611,{ic:.092,sk:.098}],120386:[.706,.011,.733,{ic:.053,sk:.169}],120387:[.694,0,.794,{ic:.048,sk:.11}],120388:[.694,0,.331,{ic:.048,sk:.111}],120389:[.694,.022,.519,{ic:.048,sk:.206}],120390:[.694,0,.764,{ic:.076,sk:.104}],120391:[.694,0,.581,{sk:.021}],120392:[.694,0,.978,{ic:.048,sk:.11}],120393:[.694,0,.794,{ic:.048,sk:.11}],120394:[.716,.022,.794,{ic:.026,sk:.13}],120395:[.694,0,.703,{ic:.047,sk:.047}],120396:[.716,.106,.794,{ic:.026,sk:.13}],120397:[.694,0,.703,{ic:.049,sk:.052}],120398:[.716,.022,.611,{ic:.041,sk:.124}],120399:[.688,0,.733,{ic:.098,sk:.071}],120400:[.694,.022,.764,{ic:.048,sk:.11}],120401:[.694,0,.733,{ic:.115,sk:.058}],120402:[.694,0,1.039,{ic:.117,sk:.056}],120403:[.694,0,.733,{ic:.06,sk:.086}],120404:[.694,0,.733,{ic:.119,sk:.055}],120405:[.694,0,.672,{ic:.079,sk:.094}],120406:[.475,.011,.525,{ic:.021,sk:.085}],120407:[.694,.011,.561,{ic:.022,sk:-.023}],120408:[.475,.011,.489,{ic:.052,sk:.1}],120409:[.694,.011,.561,{ic:.078,sk:.239}],120410:[.475,.011,.511,{ic:.031,sk:.086}],120411:[.705,0,.336,{ic:.189,sk:.127}],120412:[.469,.206,.55,{ic:.071,sk:.108}],120413:[.694,0,.561,{ic:.014,sk:-.018}],120414:[.673,0,.256,{ic:.081,sk:.08}],120415:[.673,.205,.286,{ic:.074,sk:.094}],120416:[.694,0,.531,{ic:.045,sk:-.025}],120417:[.694,0,.256,{ic:.078,sk:.088}],120418:[.469,0,.867,{ic:.014,sk:.059}],120419:[.469,0,.561,{ic:.014,sk:.059}],120420:[.475,.011,.55,{ic:.025,sk:.079}],120421:[.469,.194,.561,{ic:.022,sk:.038}],120422:[.469,.194,.561,{ic:.03,sk:.137}],120423:[.469,0,.372,{ic:.081,sk:.066}],120424:[.475,.011,.422,{ic:.047,sk:.079}],120425:[.589,.011,.404,{ic:.042,sk:.045}],120426:[.458,.011,.561,{ic:.029,sk:.074}],120427:[.458,0,.5,{ic:.065,sk:.044}],120428:[.458,0,.744,{ic:.066,sk:.044}],120429:[.458,0,.5,{ic:.05,sk:.052}],120430:[.458,.205,.5,{ic:.066,sk:.046}],120431:[.458,0,.476,{ic:.052,sk:.059}]},"sans-serif-bold-italic":{305:[.458,0,.256,{ic:.028,sk:.074}],567:[.458,.205,.286,{ic:.028,sk:.089}],32:[0,0,.367],33:[.694,0,.367,{ic:.027}],34:[.716,-.43,.578,{ic:.059}],35:[.694,.194,.917,{ic:.017}],36:[.75,.056,.55,{ic:.055}],37:[.75,.056,1.029],38:[.716,.022,.831],39:[.716,-.43,.306,{ic:.059}],40:[.75,.25,.428,{ic:.095}],41:[.75,.25,.428],42:[.75,-.292,.55,{ic:.061}],43:[.617,.117,.856],44:[.131,.106,.306],45:[.265,-.193,.367],46:[.131,0,.306],47:[.75,.25,.55,{ic:.089}],48:[.716,.022,.55,{ic:.055}],49:[.716,0,.55],50:[.716,0,.55,{ic:.053}],51:[.716,.022,.55,{ic:.043}],52:[.694,0,.55,{ic:.018}],53:[.694,.022,.55,{ic:.047}],54:[.716,.022,.55,{ic:.026}],55:[.695,.011,.55,{ic:.093}],56:[.716,.022,.55,{ic:.055}],57:[.716,.022,.55,{ic:.05}],58:[.458,0,.306,{ic:.006}],59:[.458,.106,.306,{ic:.006}],60:[.587,.086,.894,{ic:.022}],61:[.407,-.093,.856,{ic:.017}],62:[.587,.086,.894],63:[.705,0,.519,{ic:.06}],64:[.705,.011,.733,{ic:.037}],91:[.75,.25,.343,{ic:.128}],92:[.75,.25,.575],93:[.75,.25,.343,{ic:.064}],94:[.744,-.562,.556,{ic:.129}],95:[-.057,.193,.733],96:[.694,-.537,.55],123:[.75,.25,.575,{ic:.078}],124:[.75,.25,.319,{ic:.024}],125:[.75,.25,.575],126:[.307,-.193,.556,{ic:.061}],160:[0,0,.55],163:[.694,.022,.733],165:[.694,0,.733,{ic:.119}],167:[.709,.209,.566,{ic:.065}],168:[.667,-.564,.55,{ic:.047}],172:[.407,-.093,.856,{ic:.017}],175:[.658,-.573,.55,{ic:.061}],176:[.694,-.389,.413,{ic:.065}],177:[.694,0,.856,{ic:.013}],180:[.694,-.537,.55,{ic:.026}],181:[.458,.194,.561,{ic:.029}],182:[.694,.194,.703,{ic:.109}],183:[.413,-.282,.856],215:[.523,.022,.856],240:[.723,.011,.55,{ic:.033}],247:[.532,.033,.856],710:[.697,-.535,.55,{ic:.006}],711:[.697,-.535,.55,{ic:.035}],713:[.658,-.573,.55,{ic:.061}],714:[.694,-.537,.55,{ic:.026}],715:[.694,-.537,.55],728:[.687,-.545,.55,{ic:.053}],729:[.673,-.558,.306,{ic:.056}],730:[.684,-.526,.8],732:[.681,-.55,.55,{ic:.05}],768:[.694,-.537,0,{dx:.17}],769:[.694,-.537,0,{dx:.092}],770:[.697,-.535,0,{dx:.145}],771:[.681,-.55,0,{dx:.13}],772:[.658,-.573,0,{dx:.131}],774:[.687,-.545,0,{dx:.124}],775:[.673,-.558,0,{dx:.131}],776:[.667,-.564,0,{dx:.131}],778:[.684,-.526,0,{dx:.134}],780:[.697,-.535,0,{dx:.116}],8208:[.265,-.193,.367,{ic:.013}],8209:[.265,-.193,.367,{ic:.013}],8211:[.319,-.247,.55,{ic:.06}],8212:[.319,-.247,1.1,{ic:.06}],8214:[.75,.25,.571,{ic:.024}],8216:[.694,-.457,.306,{ic:.057}],8217:[.694,-.457,.306,{ic:.057}],8220:[.694,-.457,.529,{ic:.057}],8221:[.694,-.457,.529,{ic:.058}],8224:[.702,.211,.511,{ic:.031}],8225:[.702,.202,.511,{ic:.038}],8230:[.131,0,.768],8260:[.716,.022,.646,{ic:.083}],8364:[.716,.022,.658,{ic:.076}],8486:[.716,0,.794,{ic:.042}],8487:[.694,.022,.794,{ic:.091}],8592:[.547,.046,1.1],8593:[.694,.194,.622,{ic:.058}],8594:[.548,.045,1.1],8595:[.694,.194,.622],8722:[.292,-.208,.856],8734:[.492,.034,1.1,{ic:.006}]}}},"MM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/sans-serif","4.0.0-beta.7","dynamic-font")})();