(()=>{"use strict";const c=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","shapes",c({SH:{normal:{9472:[.27,-.23,.666,{ic:.02}],9474:[.77,.27,.666],9484:[.274,.27,.666,{ic:.02}],9488:[.274,.27,.666],9492:[.77,-.226,.666,{ic:.02}],9496:[.77,-.226,.666],9500:[.77,-.23,.666,{ic:.02}],9508:[.27,.27,.666],9516:[.27,.27,.666,{ic:.02}],9524:[.77,-.23,.666],9532:[.77,.27,.666,{ic:.02}],9585:[.694,.194,.888],9586:[.694,.194,.888],9601:[.083,0,.664],9608:[.664,0,.664],9617:[.664,0,.664],9618:[.664,0,.664],9619:[.664,0,.664],9644:[.417,-.084,.778],9645:[.417,-.084,.778],9733:[.693,.111,.944],9824:[.727,.13,.778],9825:[.716,.033,.778],9826:[.727,.163,.778],9827:[.727,.13,.778],9828:[.727,.13,.778],9829:[.716,.033,.778],9830:[.727,.163,.778],9831:[.727,.13,.778],9834:[.695,.029,.611],9837:[.75,.022,.388],9838:[.728,.217,.388],9839:[.716,.216,.388],9901:[.467,-.036,.5,{ic:.077}],9902:[.606,.104,.5,{ic:.188}],10003:[.699,.027,.833],10016:[.684,0,.796],10145:[.468,-.032,.977],11034:[.64,.24,.96]}},SHB:{bold:{9834:[.695,.036,.703],9901:[.474,-.028,.575,{ic:.1}],9902:[.615,.113,.575,{ic:.228}]}},SHI:{italic:{9834:[.695,.03,.562],9901:[.466,-.036,.511,{ic:.117}],9902:[.607,.105,.511,{ic:.22}]}},SHBI:{"bold-italic":{9834:[.695,.04,.648],9901:[.474,-.029,.591,{ic:.133}],9902:[.616,.113,.591,{ic:.252}]}}},"MM"),{},["MJX-MM-SH","MJX-MM-SHB","MJX-MM-SHI","MJX-MM-SHBI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/shapes","4.0.0-beta.7","dynamic-font")})();