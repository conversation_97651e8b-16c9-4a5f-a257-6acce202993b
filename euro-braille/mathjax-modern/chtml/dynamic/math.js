(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","math",t({MM:{normal:{8714:[.447,-.054,.547],8717:[.447,-.054,.547],8762:[.504,.004,.778],8763:[.536,.036,.773],8782:[.49,-.01,.778],8783:[.49,-.133,.778],8785:[.601,.101,.778],8786:[.601,.101,.778],8787:[.601,.101,.778],8790:[.367,-.133,.778],8791:[.73,-.133,.778],8792:[.619,-.133,.778],8793:[.752,-.133,.778],8794:[.752,-.133,.778],8795:[.81,-.133,.778],8796:[.81,-.133,.778],8798:[.684,-.133,.778],8844:[.604,.02,.667],8886:[.4,-.1,1.078],8887:[.4,-.1,1.078],8888:[.4,-.1,.948],8889:[.603,.103,.818],8891:[.568,.136,.642],8892:[.684,.02,.642],8893:[.684,.018,.642],8894:[.679,.109,.9],8895:[.679,-.013,.778],8903:[.586,.086,.802],8912:[.543,.043,.698],8913:[.543,.043,.698],8914:[.604,.02,.658],8915:[.604,.02,.658],8916:[.736,.022,.666],8917:[.75,.25,.778],8918:[.547,.047,.778],8919:[.547,.047,.778],8920:[.547,.047,1.285],8921:[.547,.047,1.285],8922:[.849,.349,.778],8923:[.849,.349,.778],8924:[.631,.119,.778],8925:[.631,.119,.778],8926:[.639,.139,.738],8927:[.639,.139,.738],8928:[.73,.23,.738],8929:[.73,.23,.738],8932:[.627,.211,.778],8933:[.627,.211,.778],8934:[.668,.241,.776],8935:[.636,.209,.776],8936:[.682,.254,.773],8937:[.682,.254,.773],10178:[.684,0,.778],10202:[.684,0,1.026],10203:[.684,0,1.026],10204:[.4,-.1,.948],10208:[.61,.11,.572],10209:[.501,.001,.614],10210:[.501,.001,.73],10211:[.501,.001,.73],10731:[.716,.133,.666],10742:[.75,.25,.5]}}},"MM"),{},["MJX-MM-MM"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/math","4.0.0-beta.7","dynamic-font")})();