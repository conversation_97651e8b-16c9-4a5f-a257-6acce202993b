(()=>{"use strict";const d=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","accents-b-i",d({AB:{bold:{184:[.005,.2,.511],702:[.656,-.314,.456],703:[.656,-.314,.456],731:[.005,.206,.575],733:[.704,-.509,.575],777:[.703,-.51,0,{dx:.28}],779:[.704,-.509,0,{dx:.252}],783:[.704,-.509,0,{dx:.307}],785:[.7,-.513,0,{dx:.279}],803:[-.044,.2,0,{dx:.279}],806:[-.033,.295,0,{dx:.28}],814:[-.028,.215,0,{dx:.279}],815:[-.028,.215,0,{dx:.279}],816:[-.068,.176,0,{dx:.279}],817:[-.096,.148,0,{dx:.279}],818:[-.096,.148,0,{dx:.279}]}},AI:{italic:{184:[.01,.192,.46],702:[.667,-.33,.386,{ic:.079}],703:[.667,-.33,.386,{ic:.055}],731:[.005,.211,.511],733:[.696,-.506,.511,{ic:.065}],777:[.705,-.496,0,{dx:.095}],779:[.696,-.506,0,{dx:.088}],783:[.696,-.506,0,{dx:.126}],785:[.686,-.516,0,{dx:.12}],803:[-.094,.2,0,{dx:.292}],806:[-.066,.29,0,{dx:.3}],814:[-.062,.232,0,{dx:.277}],815:[-.062,.232,0,{dx:.307}],816:[-.096,.197,0,{dx:.292}],817:[-.132,.162,0,{dx:.292}],818:[-.132,.162,0,{dx:.292}]}},ABI:{"bold-italic":{184:[.005,.2,.532],702:[.656,-.314,.457,{ic:.068}],703:[.656,-.314,.457,{ic:.038}],731:[.005,.204,.591],733:[.699,-.503,.591,{ic:.049}],777:[.702,-.5,0,{dx:.136}],779:[.699,-.503,0,{dx:.129}],783:[.699,-.503,0,{dx:.168}],785:[.69,-.511,0,{dx:.161}],803:[-.053,.2,0,{dx:.327}],806:[-.051,.307,0,{dx:.338}],814:[-.036,.215,0,{dx:.31}],815:[-.036,.215,0,{dx:.342}],816:[-.066,.185,0,{dx:.327}],817:[-.101,.151,0,{dx:.326}],818:[-.101,.151,0,{dx:.326}]}}},"MM"),{},["MJX-MM-AB","MJX-MM-AI","MJX-MM-ABI"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/accents-b-i","4.0.0-beta.7","dynamic-font")})();