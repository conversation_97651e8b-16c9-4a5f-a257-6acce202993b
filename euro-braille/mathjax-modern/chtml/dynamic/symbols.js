(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds,a=MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont,o=MathJax._.output.common.Direction,n=(o.DIRECTION,o.V,o.H);a.dynamicSetup("","symbols",t({SY:{normal:{161:[.5,.216,.278],162:[.476,.045,.444],164:[.492,-.009,.778],166:[.75,.25,.278],169:[.683,0,.683],170:[.705,-.333,.449],171:[.483,0,.556],173:[.245,-.187,.333],174:[.683,0,.683],186:[.705,-.333,.419],187:[.483,0,.556],188:[.705,0,.825],189:[.705,0,.825],190:[.705,0,.825],191:[.5,.205,.472],3647:[.728,.045,.708],8204:[0,0,0],8205:[0,0,0],8215:[-.103,.293,.504],8218:[.104,.195,.278],8222:[.104,.195,.472],8226:[.445,-.055,.5],8240:[.75,.056,1.14],8241:[.75,.056,1.457],8249:[.483,0,.389],8250:[.483,0,.389],8251:[.492,-.009,.778],8253:[.756,0,.472],8255:[-.067,.194,.637],8256:[.666,-.539,.637],8261:[.751,.252,.361],8262:[.751,.249,.361],8274:[.751,-.001,.5],8276:[-.067,.194,.637],8353:[.728,.045,.722],8358:[.683,0,.75],8361:[.683,.022,1.028],8363:[.694,.03,.556],8369:[.683,0,.681],8451:[.705,.022,1.031],8457:[.683,0,.98],8470:[.695,.01,.916],8471:[.683,0,.683],8478:[.683,.022,.736],8480:[.683,-.247,.883],8482:[.687,-.277,.983],8494:[.701,.01,.676],8960:[.596,.096,.778],8965:[.27,.155,.778],8966:[.367,.252,.778],9250:[.694,.011,.556],9251:[.249,.105,.5],11800:[.5,.256,.472],12310:[.77,.27,.458],12311:[.77,.27,.458],64256:[.705,0,.583,{ic:.045}],64257:[.705,0,.556],64258:[.705,0,.556],64259:[.705,0,.833],64260:[.705,0,.833],65279:[0,0,0]}}},"MM"),{8215:{dir:n,stretch:[0,8215],HDW:[-.103,.293,.504],hd:[-.103,.293]}},["MJX-MM-SY"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/symbols","4.0.0-beta.7","dynamic-font")})();