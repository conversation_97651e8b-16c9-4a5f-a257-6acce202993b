(()=>{"use strict";const t=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","monospace-l",t({ML:{monospace:{192:[.807,0,.525],193:[.807,0,.525],194:[.765,0,.525],195:[.783,0,.525],196:[.753,0,.525],197:[.767,0,.525],198:[.611,0,.525],199:[.622,.208,.525],200:[.807,0,.525],201:[.807,0,.525],202:[.765,0,.525],203:[.753,0,.525],204:[.807,0,.525],205:[.807,0,.525],206:[.765,0,.525],207:[.753,0,.525],208:[.611,0,.525],209:[.783,0,.525],210:[.807,.011,.525],211:[.807,.011,.525],212:[.765,.011,.525],213:[.783,.011,.525],214:[.753,.011,.525],216:[.696,.085,.525],217:[.807,.011,.525],218:[.807,.011,.525],219:[.765,.011,.525],220:[.753,.011,.525],221:[.807,0,.525],222:[.611,0,.525],223:[.617,.006,.525],224:[.622,.006,.525],225:[.622,.006,.525],226:[.623,.006,.525],227:[.615,.006,.525],228:[.6,.006,.525],229:[.636,.006,.525],230:[.44,.006,.525],231:[.44,.208,.525],232:[.622,.006,.525],233:[.622,.006,.525],234:[.623,.006,.525],235:[.6,.006,.525],236:[.622,0,.525],237:[.622,0,.525],238:[.623,0,.525],239:[.6,0,.525],241:[.615,0,.525],242:[.622,.006,.525],243:[.622,.006,.525],244:[.623,.006,.525],245:[.615,.006,.525],246:[.6,.006,.525],248:[.571,.14,.525],249:[.622,.006,.525],250:[.622,.006,.525],251:[.623,.006,.525],252:[.6,.006,.525],253:[.622,.228,.525],254:[.611,.222,.525],255:[.6,.228,.525],256:[.746,0,.525],257:[.587,.006,.525],258:[.786,0,.525],259:[.612,.006,.525],260:[.623,.203,.525],261:[.44,.203,.525],262:[.807,.011,.525],263:[.622,.006,.525],264:[.765,.011,.525],265:[.623,.006,.525],266:[.774,.011,.525],267:[.605,.006,.525],268:[.765,.011,.525],269:[.622,.006,.525],270:[.765,0,.525],271:[.611,.006,.525,{ic:.066}],272:[.611,0,.525],273:[.611,.006,.525],274:[.746,0,.525],275:[.587,.006,.525],276:[.786,0,.525],277:[.612,.006,.525],278:[.774,0,.525],279:[.605,.006,.525],280:[.611,.203,.525],281:[.44,.203,.525],282:[.765,0,.525],283:[.622,.006,.525],284:[.765,.011,.525],285:[.623,.229,.525],286:[.786,.011,.525],287:[.612,.229,.525],288:[.774,.011,.525],289:[.605,.229,.525],290:[.622,.231,.525],291:[.665,.229,.525],292:[.765,0,.525],293:[.765,0,.525],294:[.611,0,.525],295:[.611,0,.525],296:[.783,0,.525],297:[.615,0,.525],298:[.746,0,.525],299:[.587,0,.525],300:[.786,0,.525],301:[.612,0,.525],302:[.611,.203,.525],303:[.612,.203,.525],304:[.774,0,.525],308:[.765,.011,.525],309:[.623,.228,.525],310:[.611,.231,.525],311:[.611,.231,.525],313:[.807,0,.525],314:[.807,0,.525],315:[.611,.231,.525],316:[.611,.231,.525],317:[.611,0,.525],318:[.611,0,.525],319:[.611,0,.525],320:[.611,0,.525,{ic:.05}],321:[.611,0,.525],322:[.611,0,.525],323:[.807,0,.525],324:[.622,0,.525],325:[.611,.231,.525],326:[.437,.231,.525],327:[.765,0,.525],328:[.622,0,.525],330:[.622,.011,.525],331:[.437,.233,.525],332:[.746,.011,.525],333:[.587,.006,.525],334:[.786,.011,.525],335:[.612,.006,.525],336:[.813,.011,.525],337:[.625,.006,.525],338:[.622,.011,.525],339:[.44,.006,.525],340:[.807,.011,.525],341:[.622,0,.525],342:[.611,.231,.525],343:[.437,.231,.525],344:[.765,.011,.525],345:[.622,0,.525],346:[.807,.011,.525],347:[.622,.006,.525],348:[.765,.011,.525],349:[.623,.006,.525],350:[.622,.219,.525],351:[.44,.208,.525],352:[.765,.011,.525],353:[.622,.006,.525],354:[.611,.211,.525],355:[.554,.211,.525],356:[.765,0,.525],357:[.667,.006,.525],360:[.783,.011,.525],361:[.615,.006,.525],362:[.746,.011,.525],363:[.587,.006,.525],364:[.786,.011,.525],365:[.612,.006,.525],366:[.767,.011,.525],367:[.636,.006,.525],368:[.813,.011,.525],369:[.625,.006,.525],370:[.611,.203,.525],371:[.431,.203,.525,{ic:.007}],372:[.765,.008,.525],373:[.623,.004,.525],374:[.765,0,.525],375:[.623,.228,.525],376:[.753,0,.525],377:[.807,0,.525],378:[.622,0,.525],379:[.774,0,.525],380:[.605,0,.525],381:[.765,0,.525],382:[.622,0,.525],383:[.617,0,.525],398:[.611,0,.525],402:[.617,0,.525],416:[.711,.011,.525],417:[.53,.006,.525],431:[.711,.011,.525],432:[.53,.006,.525],461:[.765,0,.525],462:[.622,.006,.525],463:[.765,0,.525],464:[.622,0,.525],465:[.765,.011,.525],466:[.622,.006,.525],467:[.765,.011,.525],468:[.622,.006,.525],471:[.948,.011,.525],472:[.807,.006,.525],473:[.906,.011,.525],474:[.765,.006,.525],475:[.948,.011,.525],476:[.807,.006,.525],477:[.44,.006,.525],486:[.765,.011,.525],487:[.622,.229,.525],490:[.622,.203,.525],491:[.44,.203,.525],496:[.622,.228,.525],500:[.807,.011,.525],501:[.622,.229,.525],506:[.921,0,.525],507:[.79,.006,.525],508:[.807,0,.525],509:[.622,.006,.525],510:[.807,.085,.525],511:[.622,.14,.525],512:[.813,0,.525],513:[.625,.006,.525],516:[.813,0,.525],517:[.625,.006,.525],520:[.813,0,.525],521:[.625,0,.525],524:[.813,.011,.525],525:[.625,.006,.525],528:[.813,.011,.525],529:[.625,0,.525],532:[.813,.011,.525],533:[.625,.006,.525],536:[.622,.231,.525],537:[.44,.231,.525],538:[.611,.231,.525],539:[.554,.231,.525],7692:[.611,.2,.525],7693:[.611,.2,.525],7694:[.611,.181,.525],7695:[.611,.181,.525],7716:[.611,.2,.525],7717:[.611,.2,.525],7718:[.753,0,.525],7719:[.753,0,.525],7722:[.611,.206,.525],7723:[.611,.206,.525],7726:[.948,0,.525],7727:[.807,0,.525],7734:[.611,.2,.525],7735:[.611,.2,.525],7736:[.746,.2,.525],7737:[.746,.2,.525],7746:[.611,.2,.525],7747:[.437,.2,.525],7748:[.763,0,.525],7749:[.605,0,.525],7750:[.611,.2,.525],7751:[.437,.2,.525],7768:[.774,.011,.525],7769:[.605,0,.525],7770:[.611,.2,.525],7771:[.437,.2,.525],7772:[.746,.2,.525],7773:[.587,.2,.525],7778:[.622,.2,.525],7779:[.44,.2,.525],7788:[.611,.2,.525],7789:[.554,.2,.525],7790:[.611,.181,.525],7791:[.554,.181,.525],7808:[.807,.008,.525],7809:[.622,.004,.525],7810:[.807,.008,.525],7811:[.622,.004,.525],7812:[.753,.008,.525],7813:[.6,.004,.525],7826:[.611,.2,.525],7827:[.431,.2,.525],7831:[.695,.006,.525],7840:[.623,.2,.525],7841:[.44,.2,.525],7842:[.904,0,.525],7843:[.67,.006,.525],7844:[.919,0,.525],7845:[.743,.006,.525],7846:[.919,0,.525],7847:[.743,.006,.525],7848:[.995,0,.525],7849:[.819,.006,.525],7850:[.925,0,.525],7851:[.749,.006,.525],7852:[.765,.2,.525],7853:[.623,.2,.525],7854:[.94,0,.525],7855:[.766,.006,.525],7856:[.94,0,.525],7857:[.766,.006,.525],7858:[1.016,0,.525],7859:[.842,.006,.525],7860:[.946,0,.525],7861:[.772,.006,.525],7862:[.786,.2,.525],7863:[.612,.2,.525],7864:[.611,.2,.525],7865:[.44,.2,.525],7866:[.904,0,.525],7867:[.67,.006,.525],7868:[.783,0,.525],7869:[.615,.006,.525],7870:[.919,0,.525],7871:[.743,.006,.525],7872:[.919,0,.525],7873:[.743,.006,.525],7874:[.995,0,.525],7875:[.819,.006,.525],7876:[.925,0,.525],7877:[.749,.006,.525],7878:[.765,.2,.525],7879:[.623,.2,.525],7880:[.904,0,.525],7881:[.67,0,.525],7882:[.611,.2,.525],7883:[.605,.2,.525],7884:[.622,.2,.525],7885:[.44,.2,.525],7886:[.904,.011,.525],7887:[.67,.006,.525],7888:[.919,.011,.525],7889:[.743,.006,.525],7890:[.919,.011,.525],7891:[.743,.006,.525],7892:[.995,.011,.525],7893:[.819,.006,.525],7894:[.925,.011,.525],7895:[.749,.006,.525],7896:[.765,.2,.525],7897:[.623,.2,.525],7898:[.807,.011,.525],7899:[.622,.006,.525],7900:[.807,.011,.525],7901:[.622,.006,.525],7902:[.904,.011,.525],7903:[.67,.006,.525],7904:[.783,.011,.525],7905:[.615,.006,.525],7906:[.711,.2,.525],7907:[.53,.2,.525],7908:[.611,.2,.525],7909:[.431,.2,.525],7910:[.904,.011,.525],7911:[.67,.006,.525],7912:[.807,.011,.525],7913:[.622,.006,.525],7914:[.807,.011,.525],7915:[.622,.006,.525],7916:[.904,.011,.525],7917:[.67,.006,.525],7918:[.783,.011,.525],7919:[.615,.006,.525],7920:[.711,.2,.525],7921:[.53,.2,.525],7922:[.807,0,.525],7923:[.622,.228,.525],7924:[.611,.2,.525],7925:[.431,.228,.525],7926:[.904,0,.525],7927:[.67,.228,.525],7928:[.783,0,.525],7929:[.615,.228,.525]}}},"MM"),{},["MJX-MM-ML"]);MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/monospace-l","4.0.0-beta.7","dynamic-font")})();