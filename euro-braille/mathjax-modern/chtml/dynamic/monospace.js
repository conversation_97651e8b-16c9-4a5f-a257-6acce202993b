(()=>{"use strict";const s=MathJax._.output.chtml.DynamicFonts.AddFontIds;MathJax._.output.fonts["mathjax-modern"].chtml_ts.MathJaxModernFont.dynamicSetup("","monospace",s({M:{normal:{120432:[.623,0,.525],120433:[.611,0,.525,{sk:-.073}],120434:[.622,.011,.525,{sk:.1}],120435:[.611,0,.525,{sk:-.098}],120436:[.611,0,.525],120437:[.611,0,.525],120438:[.622,.011,.525,{sk:.072}],120439:[.611,0,.525],120440:[.611,0,.525],120441:[.611,.011,.525,{sk:.085}],120442:[.611,0,.525],120443:[.611,0,.525,{sk:-.11}],120444:[.611,0,.525],120445:[.611,0,.525],120446:[.622,.011,.525],120447:[.611,0,.525,{sk:-.07}],120448:[.622,.139,.525],120449:[.611,.011,.525,{sk:-.097}],120450:[.622,.011,.525,{sk:.046}],120451:[.611,0,.525],120452:[.611,.011,.525],120453:[.611,.008,.525],120454:[.611,.008,.525],120455:[.611,0,.525],120456:[.611,0,.525],120457:[.611,0,.525],120458:[.44,.006,.525,{sk:-.048}],120459:[.611,.006,.525,{sk:-.172}],120460:[.44,.006,.525,{sk:.043}],120461:[.611,.006,.525,{sk:.088}],120462:[.44,.006,.525],120463:[.617,0,.525,{sk:.077}],120464:[.442,.229,.525,{sk:.07}],120465:[.611,0,.525,{sk:-.172}],120466:[.605,0,.525],120467:[.605,.228,.525,{sk:.057}],120468:[.611,0,.525,{sk:-.169}],120469:[.611,0,.525,{sk:-.085}],120470:[.437,0,.525,{sk:-.052}],120471:[.437,0,.525,{sk:-.071}],120472:[.44,.006,.525],120473:[.437,.222,.525,{sk:-.074}],120474:[.437,.222,.525,{ic:.012,sk:.044}],120475:[.437,0,.525,{sk:-.018}],120476:[.44,.006,.525,{sk:.04}],120477:[.554,.006,.525,{sk:-.074}],120478:[.431,.006,.525,{sk:-.043}],120479:[.431,.004,.525],120480:[.431,.004,.525],120481:[.431,0,.525],120482:[.431,.228,.525],120483:[.431,0,.525],120822:[.622,.011,.525],120823:[.622,0,.525,{sk:.015}],120824:[.622,0,.525,{sk:-.011}],120825:[.622,.011,.525,{sk:-.015}],120826:[.623,0,.525,{sk:.06}],120827:[.611,.011,.525],120828:[.622,.011,.525,{sk:.054}],120829:[.627,.011,.525,{sk:-.184}],120830:[.622,.011,.525],120831:[.622,.011,.525]},monospace:{305:[.431,0,.525],567:[.431,.228,.525],32:[0,0,.525],33:[.622,0,.525],34:[.622,-.328,.525],35:[.611,0,.525],36:[.694,.083,.525],37:[.694,.083,.525],38:[.622,.011,.525],39:[.622,-.328,.525],40:[.694,.082,.525],41:[.694,.082,.525],42:[.521,-.09,.525],43:[.531,-.081,.525],44:[.125,.139,.525],45:[.341,-.271,.525],46:[.125,0,.525],47:[.694,.083,.525],58:[.431,0,.525],59:[.431,.139,.525],60:[.556,-.056,.525],61:[.417,-.195,.525],62:[.556,-.056,.525],63:[.617,0,.525],64:[.617,.006,.525],91:[.694,.083,.525],92:[.694,.083,.525],93:[.694,.083,.525],94:[.624,-.486,.525],95:[-.097,.167,.525],96:[.622,-.488,.525],123:[.694,.083,.525],124:[.694,.083,.525],125:[.694,.083,.525],126:[.366,-.246,.525],160:[0,0,.525],163:[.611,.011,.525],165:[.611,0,.525],167:[.622,.122,.525],168:[.6,-.51,.525],172:[.417,-.195,.525],175:[.587,-.524,.525],176:[.611,-.319,.525],177:[.45,0,.525],180:[.622,-.488,.525],181:[.43,.222,.525],182:[.694,.194,.525],183:[.368,-.243,.525],215:[.476,-.136,.525],240:[.611,.006,.525],247:[.58,-.031,.525],710:[.623,-.488,.525],711:[.622,-.487,.525],728:[.612,-.499,.525],729:[.605,-.505,.525],730:[.636,-.492,.525],732:[.615,-.495,.525],768:[.622,-.488,0,{dx:.263}],769:[.622,-.488,0,{dx:.262}],770:[.623,-.488,0,{dx:.262}],771:[.615,-.495,0,{dx:.262}],772:[.587,-.524,0,{dx:.262}],774:[.612,-.499,0,{dx:.262}],775:[.605,-.505,0,{dx:.262}],776:[.6,-.51,0,{dx:.263}],778:[.636,-.492,0,{dx:.262}],780:[.622,-.487,0,{dx:.262}],913:[.623,0,.525],914:[.611,0,.525],915:[.611,0,.525],916:[.623,0,.525],917:[.611,0,.525],918:[.611,0,.525],919:[.611,0,.525],920:[.622,.011,.525],921:[.611,0,.525],922:[.611,0,.525],923:[.623,0,.525],924:[.611,0,.525],925:[.611,0,.525],926:[.611,0,.525],927:[.622,.011,.525],928:[.611,0,.525],929:[.611,0,.525],931:[.611,0,.525],932:[.611,0,.525],933:[.622,0,.525],934:[.611,0,.525],935:[.611,0,.525],936:[.611,0,.525],937:[.622,0,.525],8208:[.341,-.271,.525],8209:[.341,-.271,.525],8211:[.341,-.271,.525],8212:[.341,-.271,.525],8214:[.694,.083,.525],8216:[.681,-.372,.525],8217:[.611,-.302,.525],8220:[.622,-.328,.525],8221:[.622,-.328,.525],8224:[.705,.216,.525],8225:[.705,.205,.525],8230:[.125,0,.525],8260:[.626,.015,.525],8364:[.622,.011,.525],8486:[.622,0,.525],8487:[.612,.01,.525],8592:[.481,-.131,.525],8593:[.611,0,.525],8594:[.481,-.131,.525],8595:[.611,0,.525],8722:[.341,-.271,.525],8734:[.435,.004,.525]}}},"MM"));MathJax.loader&&MathJax.loader.checkVersion("[mathjax-modern]/chtml/dynamic/monospace","4.0.0-beta.7","dynamic-font")})();