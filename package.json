{"name": "MathJax-demos-web", "version": "4.0.0", "description": "Demos using MathJax version 4 in web pages", "dependencies": {"@mathjax/mathjax-modern-font": "^4.0.0", "@mathjax/src": "^4.0.0", "yargs": "^18.0.0"}, "devDependencies": {"terser-webpack-plugin": "^5.3.14", "webpack": "^5.100.2", "webpack-cli": "^6.0.1"}, "scripts": {"make-custom-tex-extension": "cd custom/custom-tex-extension && node ../../node_modules/@mathjax/src/components/bin/pack", "make-custom-component": "cd custom/custom-component && node ../../node_modules/@mathjax/src/components/bin/pack", "make-custom-build": "cd custom/custom-build && node ../../node_modules/@mathjax/src/components/bin/pack"}, "repository": {"type": "git", "url": "https://github.com/mathjax/MathJax-demos-web/"}, "keywords": ["MathJax", "examples", "browser"], "license": "Apache-2.0"}