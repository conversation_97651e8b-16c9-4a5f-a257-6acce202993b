<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width">
  <link rel="stylesheet" href="../styles/globals.css">
  <title>MathJax v4: Using Equation References</title>
  <script class="show">
  MathJax = {
    tex: {
      tags: 'all'
    }
  };
  </script>
  <script class="show" defer src="https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js"></script>
</head>
<body>

<div id="frame">

<h1>Using Equation References</h1>

<div class="inset">

<p>This example shows the usage of <code>\label</code> and
<code>\ref</code>, and related macros.  Use the "Show Source" button
at the bottom to see the TeX that produced it.</p>

</div>

<div class="show display">
 
  <p>
  Here is a labeled equation:

  $$x+1\over\sqrt{1-x^2}\label{ref1}$$

  with a reference to ref1: \ref{ref1},
  and another numbered one with no label:

  $$x+1\over\sqrt{1-x^2}$$
  </p>

  <p>
  This one uses \nonumber:
  $$x+1\over\sqrt{1-x^2}\nonumber$$
  </p>

  <hr/>

  <p>
  Here's one using the equation environment:

  \begin{equation}
  x+1\over\sqrt{1-x^2}
  \end{equation}

  and one with equation* environment:

  \begin{equation*}
  x+1\over\sqrt{1-x^2}
  \end{equation*}
  </p>

  <hr/>

  <p>
  This is a forward reference [\ref{ref2}] and another \eqref{ref2} for the 
  following equation:
  $$x+1\over\sqrt{1-x^2}\label{ref2}$$
  </p>

  <p>
  More math:
  $$x+1\over\sqrt{1-x^2}$$
  </p>

  <p>Here is a ref inside math: \(\ref{ref2}+1\) and text after it.</p>

  \begin{align} 
    x & = y_1-y_2+y_3-y_5+y_8-\dots 
      && \text{by \eqref{ref1}} \\ 
      & = y'\circ y^* && \text{(by \eqref{ref3})} \\
      & = y(0)\, y' && \text {by Axiom 1.} 
  \end{align} 

  <p>Here's a bad ref [\ref{ref4}] to a nonexistent label.</p>

  <hr/>

  <p>
  An alignment:

  \begin{align}
    a & = b\label{ref3} \\
      & = c+d
  \end{align}

  and a starred one:

  \begin{align*}
   a &= b \\
     &= c + d
  \end{align*}
  </p>
 
</div>
</div>

<script src="../scripts/source.js"></script>

</body>
</html>
